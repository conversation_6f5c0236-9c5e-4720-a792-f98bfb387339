{"name": "generateblocks-pro", "version": "2.2.0", "private": true, "description": "GenerateBlocks Pro adds more great features to GenerateBlocks without sacrificing usability or performance.", "author": "<PERSON>", "license": "GPL-2.0-or-later", "keywords": ["WordPress", "editor"], "homepage": "https://generateblocks.com", "repository": {"type": "git", "url": "git+https://github.com/tomusborne/generateblocks-pro.git"}, "bugs": {"url": "https://generateblocks.com/support"}, "files": ["dist", "includes", "init.php", "languages", "package.json", "plugin.php", "readme.txt"], "dependencies": {"@dnd-kit/sortable": "^8.0.0", "@edge22/block-styles": "^1.3.0", "@edge22/components": "^1.2.0", "@edge22/styles-builder": "^1.4.0", "@tanstack/react-query": "^5.76.1", "@wordpress/icons": "9.30.0", "classnames": "^2.5.1", "css-color-extractor": "^1.1.4", "cssbeautify": "^0.3.1", "deep-object-diff": "^1.1.9", "dompurify": "^2.5.8", "parsel-js": "^1.2.1", "react-copy-to-clipboard": "^5.1.0", "react-lazyload": "3.2.0", "react-masonry-component": "6.3.0", "react-select": "^5.10.1", "react-syntax-highlighter": "^15.6.1", "react-use": "^17.6.0", "use-debounce": "^9.0.4", "uuid": "^9.0.1"}, "devDependencies": {"@wordpress/scripts": "^27.9.0", "colord": "^2.9.3", "copy-webpack-plugin": "^11.0.0", "eslint-import-resolver-custom-alias": "^1.3.2", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-webpack-plugin": "^4.2.0", "file-saver": "^2.0.5", "lodash": "4.17.21", "react-colorful": "^5.6.1", "semver": "^7.7.2"}, "overrides": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "scripts": {"build": "wp-scripts build", "start": "wp-scripts start", "lint:js": "wp-scripts lint-js", "lint:pkg-json": "wp-scripts lint-pkg-json", "test:unit": "wp-scripts test-unit-js --passWithNoTests", "plugin-zip": "wp-scripts plugin-zip", "clean": "git checkout -- dist/ && git clean -fd dist/"}}