(()=>{"use strict";const e=window.React,t=window.wp.editPost,n=window.wp.plugins,r=window.wp.i18n,i=window.wp.element,o=window.wp.data,s=window.wp.components;(0,n.registerPlugin)("generateblocks-pattern-library",{render:function(){const{toggleEditorPanelOpened:n}=(0,o.useDispatch)(t.store),{editPost:a}=(0,o.useDispatch)("core/editor"),d=(0,o.useSelect)((e=>e("core/editor").getEditedPostAttribute("meta"))),l=(0,o.useSelect)((e=>e(t.store).getPreference("panels")));return(0,i.useEffect)((()=>{const e=l?.["generateblocks-pattern-library/pattern-settings"]?.opened;e||n("generateblocks-pattern-library/pattern-settings")}),[]),(0,i.useEffect)((()=>{let e=d?._editor_width;const t=document?.querySelector(".editor-styles-wrapper");e&&t&&(e="100"===e?"100%":e+"%",t.style.setProperty("--editor-width",e))}),[d?._editor_width]),(0,e.createElement)(t.PluginDocumentSettingPanel,{name:"pattern-settings",title:(0,r.__)("Pattern","generateblocks-pro"),initialOpen:!0},(0,e.createElement)(s.RangeControl,{label:(0,r.__)("Editor Width","generateblocks-pro"),help:(0,r.__)("Set the screen width of the editor (%)","generateblocks-pro"),value:d?._editor_width?parseInt(d?._editor_width):100,onChange:e=>{a({meta:{...d,_editor_width:e.toString()}})},min:"10",max:"100"}))}})})();