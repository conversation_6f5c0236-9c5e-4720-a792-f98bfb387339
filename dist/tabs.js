(()=>{const t=t=>{const e=t.closest(".gb-tabs");if(!e)return;const s=[...t.parentElement.children].filter((t=>t.classList.contains("gb-tabs__button")||t.classList.contains("gb-tabs__menu-item"))).indexOf(t),n=e.querySelector(".gb-tabs__items").querySelectorAll(":scope > .gb-tabs__item"),a=n[s];if(!a||a.classList.contains("gb-tabs__item-open"))return;const r=e.querySelector(".gb-tabs__menu")||e.querySelector(".gb-tabs__buttons"),b=r.querySelectorAll(".gb-tabs__menu-item").length?r.querySelectorAll(".gb-tabs__menu-item"):r.querySelectorAll(".gb-tabs__button");n.forEach((t=>{t.classList.remove("gb-tabs__item-open")})),b.forEach((t=>{t.setAttribute("aria-selected",!1),t.classList.remove("gb-block-is-current")}));const c=b[s];a.classList.add("gb-tabs__item-open"),a.classList.add("gb-tabs__item-transition"),setTimeout((()=>a.classList.remove("gb-tabs__item-transition")),100),e.setAttribute("data-opened-tab",s+1),c.setAttribute("aria-selected",!0),c.classList.add("gb-block-is-current")};function e(){const t=window.location.hash;if(t){const e=document.getElementById(String(t).replace("#",""));if(e&&e.classList.contains("gb-tabs__item")){const t=e.closest(".gb-tabs"),s=t.querySelector(".gb-tabs__menu")||t.querySelector(".gb-tabs__buttons"),n=[...e.parentElement.children].indexOf(e);s&&s.children&&s.children[n]&&(s.children[n].click(),s.scrollIntoView())}}}document.addEventListener("click",(e=>{const s=e.target.closest(".gb-tabs__menu-item")||e.target.closest(".gb-tabs__button");s&&t(s)})),document.addEventListener("keydown",(e=>{const s=e.target.closest(".gb-tabs__menu-item")||e.target.closest(".gb-tabs__button");s&&"BUTTON"!==s.tagName.toUpperCase()&&(" "!==e.key&&"Enter"!==e.key&&"Spacebar"!==e.key||(e.preventDefault(),t(s)))})),document.addEventListener("DOMContentLoaded",e),window.addEventListener("hashchange",e);const s=document.querySelectorAll(".gb-tabs__item");s&&s.forEach((t=>{const e=t.closest(".gb-tabs"),s=e.querySelector(".gb-tabs__menu")||e.querySelector(".gb-tabs__buttons"),n=(s.querySelectorAll(".gb-tabs__menu-item").length?s.querySelectorAll(".gb-tabs__menu-item"):s.querySelectorAll(".gb-tabs__button"))[[...t.parentElement.children].indexOf(t)];if(!n)return;const a=n.getAttribute("id");a&&t.setAttribute("aria-labelledby",a);const r=t.getAttribute("id");r&&n.setAttribute("aria-controls",r),t.classList.contains("gb-tabs__item-open")&&n?n.setAttribute("aria-selected",!0):n&&n.setAttribute("aria-selected",!1)}))})();