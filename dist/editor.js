(()=>{"use strict";var e={n:t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return e.d(r,{a:r}),r},d:(t,r)=>{for(var o in r)e.o(r,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:r[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.wp.data,r=window.gbp.stylesBuilder,o=(0,t.createReduxStore)("gbp-block-styles-current-style",{reducer:r.currentStyleReducer,actions:r.currentStyleActions,selectors:r.currentStyleSelectors}),s=(0,t.createReduxStore)("gbp-block-styles-at-rule",{reducer:r.atRuleReducer,actions:r.atRuleActions,selectors:r.atRuleSelectors}),n=(0,t.createReduxStore)("gbp-block-styles-nested-rule",{reducer:r.nestedRuleReducer,actions:r.nestedRuleActions,selectors:r.nestedRuleSelectors}),a=(0,t.createReduxStore)("gbp-menu-toggle-state",{reducer:function(e=!1,t){return"SET_DATA"===t.type?t.payload:e},actions:{setMenuToggleState:e=>({type:"SET_DATA",payload:e})},selectors:{menuToggleState:e=>e}});(0,t.register)(o),(0,t.register)(s),(0,t.register)(n),(0,t.register)(a);const l=window.React,c=window.wp.hooks,i=window.wp.compose,u=window.wp.blockEditor,d=window.wp.i18n,p=window.wp.components,g=window.wp.element,b=window.wp.primitives,m=(0,g.createElement)(b.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,g.createElement)(b.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})),y=(0,g.createElement)(b.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,g.createElement)(b.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})),k={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let h;const _=new Uint8Array(16);function f(){if(!h&&(h="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!h))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return h(_)}const w=[];for(let e=0;e<256;++e)w.push((e+256).toString(16).slice(1));const S=function(e,t,r){if(k.randomUUID&&!t&&!e)return k.randomUUID();const o=(e=e||{}).random||(e.rng||f)();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=o[e];return t}return function(e,t=0){return w[e[t+0]]+w[e[t+1]]+w[e[t+2]]+w[e[t+3]]+"-"+w[e[t+4]]+w[e[t+5]]+"-"+w[e[t+6]]+w[e[t+7]]+"-"+w[e[t+8]]+w[e[t+9]]+"-"+w[e[t+10]]+w[e[t+11]]+w[e[t+12]]+w[e[t+13]]+w[e[t+14]]+w[e[t+15]]}(o)},v=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"],E=e=>e.replace(/[^a-zA-Z0-9-_\.]/g,"");function C({items:e={},onAdd:t,onRemove:r,onChange:o,label:s=(0,d.__)("HTML Attributes","generateblocks")}){const[n,a]=(0,g.useState)(""),[c,i]=(0,g.useState)(""),[u,b]=(0,g.useState)(""),[k,h]=(0,g.useState)(null);return(0,l.createElement)(p.BaseControl,{label:s,id:S()},(0,l.createElement)("div",{className:"gb-html-attributes"},Object.entries(e).map((([t,s],n)=>(0,l.createElement)("div",{className:"gb-html-attributes__item",key:n},(0,l.createElement)(p.TextControl,{type:"text",value:k&&k.oldKey===t?k.key:t,onChange:e=>h({oldKey:t,key:e,value:s}),title:t}),(0,l.createElement)(p.TextControl,{type:"text",value:k&&k.oldKey===t?k.value:s,onChange:e=>h({oldKey:t,key:t,value:e}),title:s}),k&&k.oldKey===t?(0,l.createElement)(p.Button,{onClick:()=>(t=>{const r=k.key.startsWith("data-");if(!k.value&&!v.includes(k.key)&&!r&&"alt"!==k.key)return void b("Attribute value is required.");if("class"===k.key)return void b("Class attribute is not allowed.");if(!k.key)return void b("Attribute name is required.");const s={...e};delete s[t],s[E(k.key)]=k.value,o(s),h(null),b("")})(t),icon:m,size:"small",iconSize:"20",variant:"primary"}):(0,l.createElement)(p.Button,{onClick:()=>(t=>{const o={...e};delete o[t],r(o)})(t),icon:y,size:"small",iconSize:"20",isDestructive:!0})))),(0,l.createElement)("div",{className:"gb-html-attributes__item"},(0,l.createElement)(p.TextControl,{type:"text",value:n,onChange:e=>a(e),placeholder:(0,d.__)("Name","generateblocks-pro")}),(0,l.createElement)(p.TextControl,{type:"text",value:c,onChange:e=>i(e),placeholder:(0,d.__)("Value","generateblocks-pro")}),(0,l.createElement)(p.Button,{onClick:()=>{const r=n.startsWith("data-");c||v.includes(n)||r||"alt"===n?"class"!==n?n?(t({...e,[E(n)]:c}),a(""),i(""),b("")):b("Attribute name is required."):b("Class attribute is not allowed."):b("Attribute value is required.")},icon:m,size:"small",iconSize:"20",disabled:!n,variant:"primary",style:{display:n||Object.keys(e).length?"":"none",opacity:!n&&Object.keys(e).length?0:1}})),u&&(0,l.createElement)("div",{className:"error-message"},u)))}const A=["generateblocks/button-container","generateblocks/button","generateblocks/container","generateblocks/headline","generateblocks/image","generateblocks/grid","generateblocks/query-loop"],P=["generateblocks/query-no-results","generateblocks-pro/classic-menu","generateblocks-pro/classic-menu-item","generateblocks-pro/classic-sub-menu"],R=(0,i.createHigherOrderComponent)((e=>t=>{const{isSelected:r,attributes:o,setAttributes:s}=t,{htmlAttributes:n}=o,a=t.name.startsWith("generateblocks")&&!P.includes(t.name)&&!A.includes(t.name);return r&&a?(0,l.createElement)(l.Fragment,null,(0,l.createElement)(e,{...t}),(0,l.createElement)(u.InspectorAdvancedControls,null,(0,l.createElement)(C,{items:n,onAdd:e=>s({htmlAttributes:e}),onRemove:e=>s({htmlAttributes:e}),onChange:e=>s({htmlAttributes:e})}))):(0,l.createElement)(e,{...t})}),"withHtmlAttributesControl");(0,c.addFilter)("editor.BlockEdit","generateblocks-pro/html-attributes/add-control",R),(0,c.addFilter)("generateblocks.blockSettings.afterImageUrlControls","generateblocks-pro/add-image-link-attributes-control",(function(e,t){const{attributes:r,setAttributes:o}=t,{linkHtmlAttributes:s,tagName:n}=r;return s?.href&&"img"===n?(0,l.createElement)(l.Fragment,null,e,(0,l.createElement)(C,{label:(0,d.__)("Link Attributes","generateblocks"),items:s,onAdd:e=>o({linkHtmlAttributes:e}),onRemove:e=>o({linkHtmlAttributes:e}),onChange:e=>o({linkHtmlAttributes:e})})):e}));const T=window.gbp.blockStyles,x=(0,g.createElement)(b.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,g.createElement)(b.Path,{d:"M12 4c-4.4 0-8 3.6-8 8v.1c0 4.1 3.2 7.5 7.2 7.9h.8c4.4 0 8-3.6 8-8s-3.6-8-8-8zm0 15V5c3.9 0 7 3.1 7 7s-3.1 7-7 7z"}));function I({styles:e={},onPaste:r,clientId:o,onClear:s}){const{updateBlockAttributes:n}=(0,t.useDispatch)("core/block-editor"),{getMultiSelectedBlockClientIds:a,hasMultiSelection:c}=(0,t.useSelect)("core/block-editor"),i="generateblocksLocalStyles",u=JSON.parse(sessionStorage.getItem(i)),b=(0,g.useMemo)((()=>Object.keys(e).length),[e]);return b||u?(0,l.createElement)(p.ToolbarGroup,{isCollapsed:!0,icon:x,label:(0,d.__)("Styles","generateblocks-pro"),controls:[{title:(0,d.__)("Copy Styles","generateblocks-pro"),onClick:()=>{sessionStorage.setItem(i,JSON.stringify(e))},isDisabled:c()||!b},{title:(0,d.__)("Paste Styles","generateblocks-pro"),onClick:()=>{const e=JSON.parse(sessionStorage.getItem(i));e&&r(e)},isDisabled:!u},{title:(0,d.__)("Clear Styles","generateblocks-pro"),onClick:()=>{if(window.confirm(c()?(0,d.__)("This will remove all styling from these blocks.","generateblocks-pro"):(0,d.__)("This will remove all styling from this block.","generateblocks-pro"))){const e=c()?a():[o],t={};e.forEach((e=>{t[e]={styles:{}}})),n(e,t,!0),s&&s()}},isDisabled:!b}]}):null}const F=["generateblocks/query-no-results"],q=(0,i.createHigherOrderComponent)((e=>t=>{const{name:o,attributes:s,clientId:n}=t,{styles:a}=s,c=(0,T.useSetStyles)(t,{cleanStylesObject:r.cleanStylesObject});return!o.startsWith("generateblocks")||F.includes(t.name)||A.includes(t.name)?(0,l.createElement)(e,{...t}):(0,l.createElement)(l.Fragment,null,(0,l.createElement)(e,{...t}),(0,l.createElement)(u.BlockControls,null,(0,l.createElement)(I,{styles:a,clientId:n,onPaste:c})))}),"withCopyStylesToolbar");(0,c.addFilter)("editor.BlockEdit","generateblocks-pro/copy-paste-styles",q);const B=window.wp.plugins;(0,t.createReduxStore)("gbp-current-style",{reducer:r.currentStyleReducer,actions:r.currentStyleActions,selectors:r.currentStyleSelectors}),(0,t.createReduxStore)("gbp-styles",{reducer:r.styleReducer,actions:r.styleActions,selectors:r.styleSelectors}),(0,t.createReduxStore)("gbp-styles-at-rule",{reducer:r.atRuleReducer,actions:r.atRuleActions,selectors:r.atRuleSelectors}),(0,t.createReduxStore)("gbp-styles-nested-rule",{reducer:r.nestedRuleReducer,actions:r.nestedRuleActions,selectors:r.nestedRuleSelectors});const O=window.wp.apiFetch;var M=e.n(O);window.wp.notices;const D=window.wp.url;function L(){return(0,t.useSelect)((()=>{const e=(0,D.getPath)(window.location.href)?.includes("site-editor.php"),r=(0,t.select)(e?"core/edit-site":"core/editor");return{getSettings:e?r.getSettings:r.getEditorSettings}}),[])}function N(){const e=(0,D.getPath)(window.location.href)?.includes("site-editor.php"),r=(0,t.useDispatch)(e?"core/edit-site":"core/editor");return{updateSettings:e?r.updateSettings:r.updateEditorSettings}}function V(){const{updateSettings:e}=N(),{getSettings:t}=L();return async(r,o="")=>{if(!o){const e=await M()({path:(0,D.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_css",{selector:r}),method:"GET"});!0===e.success&&(o=e.response.data.toString())}const s=t(),n=s?.styles?.find((e=>"gb_class:"+r===e.source));e(n?{...s,styles:s?.styles.map((e=>"gb_class:"+r!==e.source?e:{...e,css:o}))}:{...s,styles:[...s?.styles,{css:o,source:"gb_class:"+r}]})}}const G=window.wp.coreData;window.lodash,"undefined"!=typeof gbGlobalStylePermissions&&gbGlobalStylePermissions,window.wp.editPost,(0,B.registerPlugin)("generateblocks-pro-update-style-selectors",{render:function(){const{getSettings:e}=L(),{updateSettings:t}=N(),r=e(),{styles:o}=r;return(0,g.useEffect)((()=>{if(!Array.isArray(o))return;const e=o.filter((e=>e.source?.startsWith("gb_class:")||"generateblocks-pro/class-menu-style"===e.source)).map((e=>{const t=(0,u.transformStyles)([{css:e.css}],".editor-styles-wrapper")?.[0];return t?{css:t,source:e.source}:null})).filter(Boolean);e.length>0&&t({...r,styles:r.styles.map((t=>{const r=e.find((e=>e.source===t.source));return r?{...t,css:r.css}:t}))})}),[]),null}}),(0,c.addFilter)("generateblocks.editor.SelectMetaKeys.options","generateblocks-pro/dynamic-tags/post-meta",(function(e,{post:t,term:r,user:o,type:s}){var n,a,l;const c="1"===generateBlocksPro?.isACFActive,i=!t&&!r&&!o&&"option"!==s,u=function(e=!0){const[t,r]=(0,g.useState)([]);return(0,g.useEffect)((()=>{e?(async()=>{try{const e=await M()({path:"/generateblocks-pro/v1/get-acf-option-fields",method:"GET"});r(e)}catch(e){console.error("Failed to fetch ACF option fields:",e),r([])}})():r([])}),[e]),t}(c&&"option"===s&&!i);if(i||!c)return e;let p={};switch(s){case"post":p=null!==(n=t?.acf)&&void 0!==n?n:{};break;case"term":p=null!==(a=r?.acf)&&void 0!==a?a:{};break;case"author":case"user":p=null!==(l=o?.acf)&&void 0!==l?l:{};break;case"option":p=u;break;default:return e}const b=Object.keys(p);if(0===b.length)return e;const m=function(e){const t=[],r=e.sort(((e,t)=>e.localeCompare(t))),o=[];r.forEach((e=>{r.forEach((t=>{const r=new RegExp(`^${e}_\\d_`,"gi");t!==e&&t.startsWith(e)&&t.match(r)&&o.push(t)}))}));const s=[];return r.filter((e=>!t.includes(e)&&!o.includes(e))).forEach((e=>{s.push({label:e,value:e})})),s}(b);return[...e,{id:"acf",label:(0,d.__)("ACF Fields","generateblocks-pro"),items:m}]})),(0,c.addFilter)("generateblocks.editor.SelectMetaKeys.keys","generateblocks-pro/dynamic-tags/acf",(function(e,t){const{acf:r={}}=t,o=Object.keys(r);return e.filter((e=>!o.includes(e.value)))})),(0,c.addFilter)("generateblocks.dynamicTags.sourceOptions","generateblocks-pro/dynamicTags/set-adjacent-post-options",(function(e,{dynamicTagType:t}){return"post"!==t&&"author"!==t||(e.push({value:"next-post",label:(0,d.__)("Next Post","generateblocks-pro")}),e.push({value:"previous-post",label:(0,d.__)("Previous Post","generateblocks-pro")})),e})),(0,c.addFilter)("generateblocks.dynamicTags.sourcesInOptions","generateblocks-pro/dynamicTags/set-adjacent-sources-in-options",(function(e){return e.push("next-post"),e.push("previous-post"),e}));const H=window.wp.editor,j=window.gbp.components;function K({value:e,setParameter:t}){return(0,l.createElement)(p.TextControl,{label:(0,d.__)("Per Page","generateblocks-pro"),value:e,onChange:e=>t("posts_per_page",e),type:"number",min:-1})}function z({value:e,setParameter:t}){return(0,l.createElement)(p.TextControl,{label:(0,d.__)("Offset","generateblocks-pro"),value:e,onChange:e=>t("offset",e),type:"number",min:0,help:(0,d.__)("Offset is ignored if per_page is set to -1.","generateblocks")})}(0,c.addFilter)("generateblocks.editor.query.queryTypes","generateblocks-pro/query/addTypes",(function(e){return[...e,{label:(0,d.__)("Post Meta","generateblocks-pro"),value:"post_meta",help:(0,d.__)("Loop over post meta array value.","generateblocks-pro")},{label:(0,d.__)("Option","generateblocks-pro"),value:"option",help:(0,d.__)("Loop over option array value.","generateblocks-pro")}]})),(0,c.addFilter)("generateblocks.editor.query.inspectorControls","generateblocks-pro/query/inspector-controls",(function(e,r){var o;const{queryType:s,queryState:n,setParameter:a,removeParameter:i,queryClient:u}=r,p=(0,c.applyFilters)("generateblocks.editor.preview.context",null!==(o=r?.context)&&void 0!==o?o:{},{queryType:s,queryState:n,otherControls:e}),b=(0,t.useSelect)((e=>{const{postId:t}=p;if(t)return parseInt(t,10);const{getCurrentPostId:r}=e(H.store);return r?r():null}),[p]),{meta_key:m="",meta_key_id:y="current",posts_per_page:k=10,offset:h=0}=n,_=(0,g.useMemo)((()=>{const e=[];return"post_meta"===s&&e.push("post"),{load:e,options:{},postId:"current"!==y&&y?parseInt(y,10):b}}),[s,y,b]),{record:f}=(0,j.usePostRecord)(_);return(0,g.useEffect)((()=>{y&&"post_meta"!==s&&i("meta_key_id"),m&&!["option","post_meta"].includes(s)&&i("meta_key")}),[s,y,m]),(0,l.createElement)(l.Fragment,null,e,"post_meta"===s&&(0,l.createElement)(l.Fragment,null,(0,l.createElement)(j.SelectPost,{value:y.toString(),onChange:function(e){var t;return a("meta_key_id",null!==(t=e?.value)&&void 0!==t?t:"")},onClear:function(){a("meta_key_id","current")},help:(0,d.__)("Type to search for posts.","generateblocks-pro"),currentPostId:b,placeholder:(0,d.__)("Current Post","generateblocks-pro"),queryClient:u}),(0,l.createElement)(j.SelectMeta,{onSelect:e=>{e?.value?a("meta_key",e.value):i("meta_key")},onClear:()=>i("meta_key"),onEnter:e=>a("meta_key",e),onAdd:({inputValue:e})=>a("meta_key",e),help:(0,d.__)("Meta value must be an array","generateblocks-pro"),value:m,post:f,type:"post"}),(0,l.createElement)(K,{value:k,setParameter:a}),(0,l.createElement)(z,{value:h,setParameter:a})),"option"===s&&(0,l.createElement)(l.Fragment,null,(0,l.createElement)(j.SelectMeta,{onSelect:e=>{e?.value?a("meta_key",e.value):i("meta_key")},onClear:()=>i("meta_key"),onEnter:e=>a("meta_key",e),onAdd:({inputValue:e})=>a("meta_key",e),help:(0,d.__)("Meta value must be an array","generateblocks-pro"),value:m,type:"option"}),(0,l.createElement)(K,{value:k,setParameter:a}),(0,l.createElement)(z,{value:h,setParameter:a})))})),(0,c.addFilter)("generateblocks.editor.looper.query","generateblocks-pro/looper/custom-query",(function(e,{query:r,queryType:o,context:s}){const n=(0,t.useSelect)((e=>{const{postId:t}=s;if(t)return parseInt(t,10);const{getCurrentPostId:r}=e("core/editor");return r?r():null}),[s]),{meta_key:a,meta_key_id:l="current"}=r,c="current"!==l&&l?parseInt(l,10):n,i=(0,j.usePostMeta)({shouldRequest:"post_meta"===o,id:c,key:a}),u=(0,j.useOption)({shouldRequest:"option"===o,key:a});return"post_meta"===o&&i.data?i:"option"===o&&u.data?u:e}),10,2);const U=(0,g.createElement)(b.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,g.createElement)(b.Path,{d:"M18 11.2h-5.2V6h-1.6v5.2H6v1.6h5.2V18h1.6v-5.2H18z"}));(0,c.addAction)("generateblocks.patterns.patternsList","generateblocks.patterns.setGlobalStyleCSS",(({activeLibrary:e,setGlobalStyleCSS:t,setGlobalStyleData:r,isLocal:o,cacheIsClearing:s})=>{(0,g.useEffect)((()=>{!async function(){if(o)t(""),r([]);else{var s,n;const{data:o}=await async function(e){try{const t=await M()({path:(0,D.addQueryArgs)("/generateblocks-pro/v1/pattern-library/get-global-style-data",{url:e?.domain,id:e?.id,publicKey:e?.publicKey}),method:"GET",headers:{"X-GB-Public-Key":e.publicKey}});if(t)return t.response}catch(e){return[]}}(e);t(null!==(s=o?.css)&&void 0!==s?s:""),r(null!==(n=o?.styles)&&void 0!==n?n:[])}}()}),[e?.id,s])})),(0,c.addFilter)("generateblocks.patterns.insertPatternButton","generateblocks.patterns.insertGlobalStyleButton",((e,{onClick:o,label:s,patterns:n,globalStyleData:a,className:c,disabled:i})=>{const u=V(),d=function(){const{editEntityRecord:e}=(0,t.useDispatch)(G.store),o=V();return async(t,s,n)=>{const a=await(0,r.getCss)(t,n),l=["postType","gblocks_styles",s];await e(...l,{title:t,gb_style_selector:t,gb_style_data:n,gb_style_css:a});const c=await(0,r.getCss)(t,n,"editor");o(t,c)}}(),[b,m]=(0,g.useState)(!1),{getEntityRecordEdits:y,getEntityRecord:k}=(0,t.useSelect)(G.store);return(0,l.createElement)(p.Button,{variant:"primary",className:c,icon:U,isBusy:b,disabled:i,onClick:async e=>{m(!0);const t=n.map((e=>e.globalStyleSelectors)).flat();if(!t.length)return void o(e);const r=[];if(t.forEach((e=>{const t=a.find((t=>t.className===e));t&&!r.includes(t)&&r.push(t)})),r.length)try{const t=await M()({path:"/generateblocks-pro/v1/pattern-library/import-styles",method:"POST",data:{styles:r}}),s=t?.response?.data?.imported,n=t?.response?.data?.existing;if(n?.length){const e=n.find((e=>".gbp-section__inner"===e.selector));e&&await async function(e,t=[]){const r=a.find((t=>t.className===e.selector)),o=JSON.parse(r.styles),s=await y("postType","gblocks_styles",e.id),n={...e.styles},l={...s?.gb_style_data||n};let c=!1;for(const e of t)!l[e]&&o[e]&&(l[e]=o[e],c=!0);c&&await async function(e,t,r=10){return new Promise((o=>{let s=0;const n=()=>{const a=k(e,"gblocks_styles",t);a?o(a):s<r&&(s++,setTimeout(n,100))};n()}))}("postType",e.id)&&await d(e.selector,e.id,l)}(e,["maxWidth"])}s.length&&s.forEach((e=>{const t=a.find((t=>t.className===e))?.css;u(e,t)})),o(e),m(!1)}catch(t){o(e),m(!1)}else o(e)}},s)})),(0,c.addFilter)("generateblocks.patternLibrary.addLibraryContent","generateblocks.patternLibrary.addRemoteLibrary",((e,{setShowAddLibrary:t,setLibraries:r})=>{const[o,s]=(0,g.useState)(""),[n,a]=(0,g.useState)(""),[c,i]=(0,g.useState)(""),[u,b]=(0,g.useState)(!1);return(0,l.createElement)(l.Fragment,null,(0,l.createElement)(p.TextControl,{label:(0,d.__)("Domain","generateblocks-pro"),value:n,onChange:a,type:"url",placeholder:"https://yourdomain.com",onBlur:()=>{""===n||n.startsWith("http")?n&&s(""):a("https://"+n.replace(/^\/\//,""))}}),(0,l.createElement)(p.TextControl,{label:(0,d.__)("Public key","generateblocks-pro"),value:c,onChange:i,onBlur:()=>{c&&s("")}}),(0,l.createElement)(p.Button,{disabled:!!o,variant:"primary",isBusy:u,onClick:async()=>{if(b(!0),s(""),""===n)return s((0,d.__)("Please enter a domain.","generateblocks-pro")),void b(!1);if(""===c)return s((0,d.__)("Public key is required.","generateblocks-pro")),void b(!1);const e={id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),domain:n,publicKey:c,isEnabled:!0,isDefault:!1,isLocal:!1};try{const o=await M()({path:"/generateblocks-pro/v1/pattern-library/add-library",method:"POST",data:{domain:n,publicKey:c,data:e}});if(!o?.success)throw new Error(o?.response);await r(),t(!1),b(!1)}catch(e){s(e?.message||(0,d.__)("Error adding library.","generateblocks-pro")),b(!1)}}},(0,d.__)("Add","generateblocks-pro")),o&&(0,l.createElement)(p.Notice,{status:"error",isDismissible:!1,className:"gb-add-library__error"},o))})),(0,c.addFilter)("generateblocks.editor.allowCustomAtRule","generateblocks-pro/editor/allowCustomAtRule",(()=>!0)),(0,c.addFilter)("generateblocks.editor.allowCustomAdvancedSelector","generateblocks-pro/editor/allowCustomAdvancedSelector",(()=>!0));const W=(0,c.applyFilters)("generateblocks-pro.deviceVisibilityOptions",[{label:(0,d.__)("Hide on Desktop","generateblocks-pro"),ruleId:"largeWidth"},{label:(0,d.__)("Hide on Tablet","generateblocks-pro"),ruleId:"mediumWidth"},{label:(0,d.__)("Hide on Tablet & Mobile","generateblocks-pro"),ruleId:"mediumSmallWidth"},{label:(0,d.__)("Hide on Mobile","generateblocks-pro"),ruleId:"smallWidth"}]),$=["none","none!important","none !important"];(0,c.addFilter)("generateblocks.blockSettings.openPanel","generateblocks-pro/device-visibility",(function(e,t){const{panelId:o,getStyleValue:s,onStyleChange:n}=t;return"settings"!==o||void 0===s?e:W&&W.length?(0,l.createElement)(l.Fragment,null,e,(0,l.createElement)(p.BaseControl,{label:(0,d.__)("Device Visibility","generateblocks-pro"),id:"gb-device-visibility"},(0,l.createElement)(j.Stack,{gap:"5px"},W.map((e=>{const t=(0,r.getAtRuleValue)(e.ruleId),o=$.includes(s("display",t));return(0,l.createElement)(p.ToggleControl,{key:e.ruleId,label:e.label,checked:o,onChange:e=>{n("display",e?"none !important":"",t)}})}))))):e})),(0,c.addFilter)("generateblocks.editor.blockCss","generateblocks-pro/navigation-editor-css",((e,{clientId:r,name:o})=>{const{getBlock:s}=(0,t.useSelect)((e=>e("core/block-editor")),[]);if("generateblocks-pro/navigation"===o){var n;const t=s(r),{htmlAttributes:a={},uniqueId:l}=t.attributes,c=null!==(n=a?.["data-gb-mobile-breakpoint"])&&void 0!==n?n:"";if(c){const t=(0,T.getSelector)(o,l);let r=`@media (width > ${c}) {.editor-styles-wrapper ${t} .gb-menu-toggle {display: none;}}`;r+=`@media (max-width: ${c}) {.editor-styles-wrapper ${t} .gb-menu-container:not(.gb-menu-container--toggled) {display: none;}`,e+=r}else e+=`.editor-styles-wrapper ${(0,T.getSelector)(o,l)} .gb-menu-toggle {display: none;}`}return e}))})();