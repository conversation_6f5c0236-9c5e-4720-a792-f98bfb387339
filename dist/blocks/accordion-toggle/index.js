(()=>{"use strict";var e={n:t=>{var l=t&&t.__esModule?()=>t.default:()=>t;return e.d(l,{a:l}),l},d:(t,l)=>{for(var s in l)e.o(l,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:l[s]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.React,l=window.wp.blocks,s=window.wp.compose,r=window.wp.i18n,n=window.wp.element,o=window.wp.blockEditor,c=window.gbp.blockStyles,a=window.gbp.components,u=window.gbp.stylesBuilder,i=window.wp.data,d=(0,i.createReduxStore)("gbp-current-style",{reducer:u.currentStyleReducer,actions:u.currentStyleActions,selectors:u.currentStyleSelectors}),b=(0,i.createReduxStore)("gbp-styles",{reducer:u.styleReducer,actions:u.styleActions,selectors:u.styleSelectors}),y=(0,i.createReduxStore)("gbp-styles-at-rule",{reducer:u.atRuleReducer,actions:u.atRuleActions,selectors:u.atRuleSelectors}),g=(0,i.createReduxStore)("gbp-styles-nested-rule",{reducer:u.nestedRuleReducer,actions:u.nestedRuleActions,selectors:u.nestedRuleSelectors}),p=window.wp.apiFetch;var S=e.n(p);const f=window.wp.notices,v=window.wp.url,h=window.wp.coreData;var m;window.lodash;const R="undefined"!=typeof gbGlobalStylePermissions&&null!==(m=gbGlobalStylePermissions?.canManageStyles)&&void 0!==m&&m,w=window.wp.editPost;const C=(0,i.createReduxStore)("gbp-block-styles-current-style",{reducer:u.currentStyleReducer,actions:u.currentStyleActions,selectors:u.currentStyleSelectors}),k=(0,i.createReduxStore)("gbp-block-styles-at-rule",{reducer:u.atRuleReducer,actions:u.atRuleActions,selectors:u.atRuleSelectors}),A=(0,i.createReduxStore)("gbp-block-styles-nested-rule",{reducer:u.nestedRuleReducer,actions:u.nestedRuleActions,selectors:u.nestedRuleSelectors});function E(){const e=(0,i.useSelect)((e=>e(k).getAtRule())),{setAtRule:t}=(0,i.useDispatch)(k),l=(0,i.useSelect)((e=>e(A).getNestedRule())),{setNestedRule:s}=(0,i.useDispatch)(A),n=(0,c.useCurrentAtRule)(u.defaultAtRules),{setCurrentStyle:a}=(0,i.useDispatch)(C),p=(0,i.useSelect)((e=>e(C).currentStyle())),{deviceType:m,setDeviceType:E}=(0,c.useDeviceType)(),L=function(){const{setCurrentStyle:e}=(0,i.useDispatch)(d),{setStyles:t}=(0,i.useDispatch)(b),{createNotice:l,removeAllNotices:s}=(0,i.useDispatch)(f.store),{getEntityRecordEdits:n}=(0,i.useSelect)(h.store),{getSelectedBlock:c}=(0,i.useSelect)((e=>e(o.store)),[]),{setAtRule:a}=(0,i.useDispatch)(y),{setNestedRule:u}=(0,i.useDispatch)(g),{openGeneralSidebar:p}=(0,i.useDispatch)(w.store);return async(o,i={})=>{if(!R)return;const{classStyles:d,classPostId:b}=await async function(e){var t;const l=await S()({path:(0,v.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let s=null!==(t=l?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(s)&&0===s.length&&(s={}),{classStyles:s,classPostId:l?.response?.data?.postId}}(o);if(!b)return s("snackbar"),void l("error",(0,r.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,r.__)("%s does not exist.","generateblocks-pro"),o),{type:"snackbar"});a(""),u(""),p("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:b,name:o,classStyles:d,clientId:c()?.clientId,options:i}),i.nestedRule&&u(i.nestedRule),i.atRule&&a(i.atRule);const y=n("postType","gblocks_styles",b);t(y?.gb_style_data||d),s("snackbar"),l("info",(0,r.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,r.__)("Editing %s.","generateblocks-pro"),o),{type:"snackbar"})}}(),_=function(){const{setCurrentStyle:e}=(0,i.useDispatch)(d),{setStyles:t}=(0,i.useDispatch)(b),{setAtRule:l}=(0,i.useDispatch)(y),{setNestedRule:s}=(0,i.useDispatch)(g);return()=>{e({}),t({}),l(""),s("")}}();return{atRule:e,nestedRule:l,setAtRule:t,currentAtRule:n,setNestedRule:s,setDeviceType:E,deviceType:m,setCurrentStyle:a,currentStyle:p,getPreviewDevice:c.getPreviewDevice,setGlobalStyle:L,cancelEditGlobalStyle:_}}function L({attributes:e,setAttributes:l,shortcuts:s,onStyleChange:r}){const{atRule:n,setAtRule:o,nestedRule:a,setNestedRule:i,setDeviceType:d,getPreviewDevice:b,currentStyle:y,setGlobalStyle:g,cancelEditGlobalStyle:p}=E(),{styles:S,globalClasses:f=[]}=e,v=(0,u.getStylesObject)(S,n,a);return(0,t.createElement)(u.StylesBuilder,{currentSelector:y?.selector,styles:v,allStyles:S,onDeleteStyle:(e,t)=>{const s=(0,u.deleteStylesObjectKey)(S,e,t);l({styles:s})},nestedRule:a,atRule:n,onStyleChange:(e,t=null)=>r(e,t,n,a),onNestedRuleChange:e=>i(e),onAtRuleChange:e=>{o(e);const t=(0,u.getPreviewWidth)(e),l=b(t);l&&d(l)},onUpdateKey:(e,t,s)=>{const r=(0,u.updateStylesObjectKey)(S,e,t,s);l({styles:r})},selectorShortcuts:s.selectorShortcuts,visibleSelectors:s.visibleShortcuts,onEditStyle:g,cancelEditStyle:p,setLocalTab:e=>{sessionStorage.setItem(c.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:f})}const _=window.wp.components,N=window.wp.hooks,I=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function D(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function O(e,t,l=!1){const{styles:s={},uniqueId:r="",globalClasses:n=[]}=t,o=[];return l&&o.push(e),n.length>0&&o.push(...n),Object.keys(s).length>0&&o.push(`${e}-${r}`),o}const T={default:{items:[{label:"Hover",value:"&:hover"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"},{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"}]},interactions:{label:(0,r.__)("Interactions","generateblocks-pro"),items:[{label:"Hover",value:"&:hover"},{label:"Focus",value:"&:focus"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"}]},links:{label:(0,r.__)("Links","generateblocks-pro"),items:[{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"},{label:"Hovered & focused links",value:"a:is(:hover, :focus)"},{label:"Visited links",value:"a:visited"}]},pseudoElements:{label:(0,r.__)("Pseudo Elements","generateblocks-pro"),items:[{label:"Before",value:"&:before"},{label:"After",value:"&:after"}]}};function P({value:e,options:s=[],onChange:n,blockName:o}){var c;const a=null!==(c=(0,l.getBlockType)(o)?.attributes?.tagName?.enum)&&void 0!==c?c:[],u=s.length?s:a.map((e=>({label:e,value:e})));return u.length?(0,t.createElement)(_.SelectControl,{label:(0,r.__)("Tag Name","generateblocks-pro"),value:e,options:u,onChange:n}):null}const j=(0,s.compose)((function(e){return l=>{var s,r,c,a;const{attributes:u,setAttributes:d,context:b}=l,{htmlAttributes:y={},uniqueId:g,className:p,align:S}=u,f=(0,i.useSelect)((e=>e("core/editor").isSavingPost())),{style:v="",href:h,...m}=y,R=Object.keys(m).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(m[t]),e)),{}),[w,C]=(0,n.useState)(v);var k,A,E,L;(0,n.useEffect)((()=>{!async function(){const e=await(0,N.applyFilters)("generateblocks.editor.htmlAttributes.style",v,{...l});C(e)}()}),[v,b,f]),k=()=>{const e=["alignwide","alignfull"],t=(p?.split(" ")||[]).filter((t=>!e.includes(t)));S&&t.push("align"+S),d({className:t.join(" ")})},A=[S],L=(E=(0,t.useRef)(!0)).current?(E.current=!1,!0):E.current,(0,t.useEffect)((function(){if(!L)return k()}),A);const O="string"==typeof w?w.split(";").reduce(((e,t)=>{const l=t.indexOf(":");if(-1===l)return e;let s=t.slice(0,l).trim();const r=t.slice(l+1).trim();return s&&r&&(s.startsWith("--")||(s=s.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[s]=r),e}),{}):"",T={...R,style:O,"data-gb-id":g,"data-context-post-id":null!==(s=null!==(r=b?.postId)&&void 0!==r?r:b?.["generateblocks/loopIndex"])&&void 0!==s?s:0,"data-align":S||void 0},P=(0,n.useMemo)((()=>Array.isArray(y)?{}:y),[JSON.stringify(y)]);return(0,n.useEffect)((()=>{const e={...y};Object.keys(e).forEach((t=>{const l=t.startsWith("data-"),s=e[t];I.includes(t)||""!==s||l||"alt"===t||delete e[t],"string"!=typeof s&&"boolean"!=typeof s&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const l=Object.keys(e),s=Object.keys(t);if(l.length!==s.length)return!1;for(const s of l)if(e[s]!==t[s])return!1;return!0}(e,y)||d({htmlAttributes:e})}),[JSON.stringify(y)]),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(e,{...l,editorHtmlAttributes:T,htmlAttributes:P}),(0,t.createElement)(o.InspectorAdvancedControls,null,(0,t.createElement)(_.TextControl,{label:"HTML ID",value:null!==(c=y.id)&&void 0!==c?c:"",onChange:e=>{d({htmlAttributes:{...y,id:e}})},onBlur:()=>{y.id&&d({htmlAttributes:{...y,id:D(y.id)}})}}),(0,t.createElement)(_.TextControl,{label:"ARIA Label",value:null!==(a=y["aria-label"])&&void 0!==a?a:"",onChange:e=>{d({htmlAttributes:{...y,"aria-label":e}})}})))}}),(function(e){return l=>{const{attributes:s,name:r,setAttributes:o,isSelected:a,clientId:i}=l,{uniqueId:d,styles:b,css:y}=s,{atRule:g,deviceType:p,setAtRule:S,currentStyle:f,setCurrentStyle:v,setNestedRule:h}=E(),m=(0,c.useSetStyles)(l,{cleanStylesObject:u.cleanStylesObject}),R=(0,n.useMemo)((()=>d?(0,c.getSelector)(r,d):""),[r,d]),w=Array.isArray(b)?{}:b;return(0,c.useAtRuleEffect)({deviceType:p,atRule:g,setAtRule:S,defaultAtRules:u.defaultAtRules,isSelected:a,getPreviewWidth:u.getPreviewWidth}),(0,c.useGenerateCSSEffect)({selector:R,styles:w,setAttributes:o,getCss:u.getCss,getSelector:c.getSelector,isSelected:a,blockCss:y,clientId:i}),(0,c.useStyleSelectorEffect)({isSelected:a,currentStyle:f,selector:R,setCurrentStyle:v,setNestedRule:h}),(0,c.useDecodeStyleKeys)({styles:b,setAttributes:o}),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(c.Style,{selector:R,getCss:u.getCss,styles:w,clientId:i,name:r}),(0,t.createElement)(e,{...l,selector:R,onStyleChange:function(e,t="",l="",s=""){const r="object"==typeof e?e:{[e]:t},n=(0,c.buildChangedStylesObject)(r,l,s);m(n)},getStyleValue:function(e,t="",l=""){var s,r,n,o;return l?t?null!==(n=b?.[l]?.[t]?.[e])&&void 0!==n?n:"":null!==(r=b?.[l]?.[e])&&void 0!==r?r:"":t?null!==(o=b?.[t]?.[e])&&void 0!==o?o:"":null!==(s=b?.[e])&&void 0!==s?s:""},styles:w}))}}),c.withUniqueId)((function(e){const{attributes:l,setAttributes:s,context:u,onStyleChange:i,getStyleValue:d,editorHtmlAttributes:b={},htmlAttributes:y,styles:g,name:p,clientId:S}=e,{tagName:f,uniqueId:v}=l,h=O("gb-accordion__toggle",{...l,styles:g},!0);u["generateblocks/accordion/openByDefault"]&&h.push("gb-block-is-current");const m=(0,n.useRef)(),R=(0,o.useBlockProps)({className:h.join(" ").trim(),...b,ref:m}),w=(0,o.useInnerBlocksProps)(R),C=f||"div",k=(0,n.useMemo)((()=>{const e=[{label:(0,r.__)("Current","generateblocks"),value:"&:is(.gb-block-is-current, .gb-block-is-current:hover, .gb-block-is-current:focus)"}];return T.interactions.items&&T.interactions.items.push({label:(0,r.__)("Current","generateblocks"),value:"&:is(.gb-block-is-current, .gb-block-is-current:hover, .gb-block-is-current:focus)"}),{selectorShortcuts:T,visibleShortcuts:e}}),[]);(0,n.useEffect)((()=>{f||s({tagName:"div"})}),[]),(0,n.useEffect)((()=>{var e;if(!v)return;const t=null!==(e=y?.id)&&void 0!==e?e:"",l=t&&t.startsWith("gb-accordion-toggle-")&&t!==`gb-accordion-toggle-${v}`;t&&!l||s({htmlAttributes:{...y,id:`gb-accordion-toggle-${v}`}})}),[v,y?.id]);const A={name:p,attributes:l,setAttributes:s,clientId:S,getStyleValue:d,onStyleChange:i};return(0,t.createElement)(t.Fragment,null,(0,t.createElement)(o.InspectorControls,null,(0,t.createElement)(c.BlockStyles,{settingsTab:(0,t.createElement)(a.OpenPanel,{...A,panelId:"settings"},(0,t.createElement)(P,{blockName:p,value:f,onChange:e=>{s({tagName:e})}})),stylesTab:(0,t.createElement)(L,{attributes:l,setAttributes:s,shortcuts:k,onStyleChange:i})})),(0,t.createElement)(C,{...w}))})),x=JSON.parse('{"UU":"generateblocks-pro/accordion-toggle"}');(0,l.registerBlockType)(x.UU,{edit:j,save:function({attributes:e}){const{tagName:l,htmlAttributes:s={}}=e,r=O("gb-accordion__toggle",e,!0),n=o.useBlockProps.save({className:r.join(" ").trim(),...s});return(0,t.createElement)(l,{...o.useInnerBlocksProps.save(n)})},icon:function(){return(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",className:"gblocks-block-icon","aria-hidden":"true",focusable:"false"},(0,t.createElement)("path",{d:"M21.376,21.918L2.626,21.918C2.282,21.918 2,21.636 2,21.292C2,20.949 2.282,20.667 2.626,20.667L21.377,20.667C21.719,20.667 22.002,20.95 22.002,21.293C22.002,21.636 21.719,21.918 21.376,21.918C21.376,21.918 21.376,21.918 21.376,21.918ZM21.376,18.765L2.626,18.765C2.283,18.765 2.001,18.483 2.001,18.14C2.001,17.797 2.283,17.515 2.626,17.515L21.377,17.515C21.719,17.515 22.001,17.797 22.001,18.14C22.001,18.483 21.719,18.765 21.376,18.765C21.376,18.765 21.376,18.765 21.376,18.765ZM21.376,15.613L2.626,15.613C2.283,15.613 2.001,15.331 2.001,14.988C2.001,14.645 2.283,14.363 2.626,14.363L21.377,14.363C21.719,14.363 22.001,14.645 22.001,14.988C22.001,15.331 21.719,15.613 21.376,15.613C21.376,15.613 21.376,15.613 21.376,15.613Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M21.376,12.137L2.626,12.137C2.283,12.137 2,11.855 2,11.512C2,11.512 2,11.511 2.001,11.511L2.001,5.695C2.001,5.35 2.281,5.07 2.626,5.07L21.377,5.07C21.721,5.07 22.001,5.35 22.001,5.695L22.001,11.511C22.001,11.857 21.721,12.137 21.376,12.137ZM3.25,10.886L20.751,10.886L20.751,6.32L3.25,6.32L3.25,10.886Z",style:{fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M17.831,9.944C17.693,9.944 17.561,9.889 17.463,9.791L15.901,8.229L16.638,7.492L17.831,8.686L19.026,7.492L19.762,8.229L18.2,9.792C18.102,9.889 17.969,9.944 17.831,9.944Z",style:{fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M19.352,2L19.764,2.412L18.201,3.975C18.103,4.072 17.97,4.127 17.833,4.127C17.694,4.127 17.562,4.072 17.465,3.974L15.902,2.412L16.314,2L16.964,2L17.833,2.869L18.702,2L19.352,2Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M22.002,2L22.002,5.694C22.002,6.04 21.722,6.32 21.377,6.32L2.627,6.32C2.284,6.32 2.001,6.038 2.001,5.695C2.001,5.695 2.001,5.694 2.002,5.694L2.002,2L3.251,2L3.251,5.069L20.752,5.069L20.752,2L22.002,2Z",style:{fillOpacity:.3,fillRule:"nonzero"}}))}})})();