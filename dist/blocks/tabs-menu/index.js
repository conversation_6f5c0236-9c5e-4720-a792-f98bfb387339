(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var l in s)e.o(s,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:s[l]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.React,s=window.wp.blocks,l=window.wp.compose,r=window.wp.element,n=window.wp.blockEditor,o=window.gbp.blockStyles,a=window.gbp.stylesBuilder,c=window.wp.data,u=(0,c.createReduxStore)("gbp-current-style",{reducer:a.currentStyleReducer,actions:a.currentStyleActions,selectors:a.currentStyleSelectors}),i=(0,c.createReduxStore)("gbp-styles",{reducer:a.styleReducer,actions:a.styleActions,selectors:a.styleSelectors}),d=(0,c.createReduxStore)("gbp-styles-at-rule",{reducer:a.atRuleReducer,actions:a.atRuleActions,selectors:a.atRuleSelectors}),y=(0,c.createReduxStore)("gbp-styles-nested-rule",{reducer:a.nestedRuleReducer,actions:a.nestedRuleActions,selectors:a.nestedRuleSelectors}),b=window.wp.i18n,p=window.wp.apiFetch;var g=e.n(p);const S=window.wp.notices,f=window.wp.url,m=window.wp.coreData;var v;window.lodash;const h="undefined"!=typeof gbGlobalStylePermissions&&null!==(v=gbGlobalStylePermissions?.canManageStyles)&&void 0!==v&&v,R=window.wp.editPost;const w=(0,c.createReduxStore)("gbp-block-styles-current-style",{reducer:a.currentStyleReducer,actions:a.currentStyleActions,selectors:a.currentStyleSelectors}),A=(0,c.createReduxStore)("gbp-block-styles-at-rule",{reducer:a.atRuleReducer,actions:a.atRuleActions,selectors:a.atRuleSelectors}),C=(0,c.createReduxStore)("gbp-block-styles-nested-rule",{reducer:a.nestedRuleReducer,actions:a.nestedRuleActions,selectors:a.nestedRuleSelectors});function k(){const e=(0,c.useSelect)((e=>e(A).getAtRule())),{setAtRule:t}=(0,c.useDispatch)(A),s=(0,c.useSelect)((e=>e(C).getNestedRule())),{setNestedRule:l}=(0,c.useDispatch)(C),r=(0,o.useCurrentAtRule)(a.defaultAtRules),{setCurrentStyle:p}=(0,c.useDispatch)(w),v=(0,c.useSelect)((e=>e(w).currentStyle())),{deviceType:k,setDeviceType:L}=(0,o.useDeviceType)(),E=function(){const{setCurrentStyle:e}=(0,c.useDispatch)(u),{setStyles:t}=(0,c.useDispatch)(i),{createNotice:s,removeAllNotices:l}=(0,c.useDispatch)(S.store),{getEntityRecordEdits:r}=(0,c.useSelect)(m.store),{getSelectedBlock:o}=(0,c.useSelect)((e=>e(n.store)),[]),{setAtRule:a}=(0,c.useDispatch)(d),{setNestedRule:p}=(0,c.useDispatch)(y),{openGeneralSidebar:v}=(0,c.useDispatch)(R.store);return async(n,c={})=>{if(!h)return;const{classStyles:u,classPostId:i}=await async function(e){var t;const s=await g()({path:(0,f.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let l=null!==(t=s?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(l)&&0===l.length&&(l={}),{classStyles:l,classPostId:s?.response?.data?.postId}}(n);if(!i)return l("snackbar"),void s("error",(0,b.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,b.__)("%s does not exist.","generateblocks-pro"),n),{type:"snackbar"});a(""),p(""),v("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:i,name:n,classStyles:u,clientId:o()?.clientId,options:c}),c.nestedRule&&p(c.nestedRule),c.atRule&&a(c.atRule);const d=r("postType","gblocks_styles",i);t(d?.gb_style_data||u),l("snackbar"),s("info",(0,b.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,b.__)("Editing %s.","generateblocks-pro"),n),{type:"snackbar"})}}(),N=function(){const{setCurrentStyle:e}=(0,c.useDispatch)(u),{setStyles:t}=(0,c.useDispatch)(i),{setAtRule:s}=(0,c.useDispatch)(d),{setNestedRule:l}=(0,c.useDispatch)(y);return()=>{e({}),t({}),s(""),l("")}}();return{atRule:e,nestedRule:s,setAtRule:t,currentAtRule:r,setNestedRule:l,setDeviceType:L,deviceType:k,setCurrentStyle:p,currentStyle:v,getPreviewDevice:o.getPreviewDevice,setGlobalStyle:E,cancelEditGlobalStyle:N}}function L({attributes:e,setAttributes:s,shortcuts:l,onStyleChange:r}){const{atRule:n,setAtRule:c,nestedRule:u,setNestedRule:i,setDeviceType:d,getPreviewDevice:y,currentStyle:b,setGlobalStyle:p,cancelEditGlobalStyle:g}=k(),{styles:S,globalClasses:f=[]}=e,m=(0,a.getStylesObject)(S,n,u);return(0,t.createElement)(a.StylesBuilder,{currentSelector:b?.selector,styles:m,allStyles:S,onDeleteStyle:(e,t)=>{const l=(0,a.deleteStylesObjectKey)(S,e,t);s({styles:l})},nestedRule:u,atRule:n,onStyleChange:(e,t=null)=>r(e,t,n,u),onNestedRuleChange:e=>i(e),onAtRuleChange:e=>{c(e);const t=(0,a.getPreviewWidth)(e),s=y(t);s&&d(s)},onUpdateKey:(e,t,l)=>{const r=(0,a.updateStylesObjectKey)(S,e,t,l);s({styles:r})},selectorShortcuts:l.selectorShortcuts,visibleSelectors:l.visibleShortcuts,onEditStyle:p,cancelEditStyle:g,setLocalTab:e=>{sessionStorage.setItem(o.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:f})}const E=window.wp.components,N=window.wp.hooks,I=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function _(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function D(e,t,s=!1){const{styles:l={},uniqueId:r="",globalClasses:n=[]}=t,o=[];return s&&o.push(e),n.length>0&&o.push(...n),Object.keys(l).length>0&&o.push(`${e}-${r}`),o}const O=window.gbp.components,T={default:{items:[{label:"Hover",value:"&:hover"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"},{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"}]},interactions:{label:(0,b.__)("Interactions","generateblocks-pro"),items:[{label:"Hover",value:"&:hover"},{label:"Focus",value:"&:focus"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"}]},links:{label:(0,b.__)("Links","generateblocks-pro"),items:[{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"},{label:"Hovered & focused links",value:"a:is(:hover, :focus)"},{label:"Visited links",value:"a:visited"}]},pseudoElements:{label:(0,b.__)("Pseudo Elements","generateblocks-pro"),items:[{label:"Before",value:"&:before"},{label:"After",value:"&:after"}]}};function P({value:e,options:l=[],onChange:r,blockName:n}){var o;const a=null!==(o=(0,s.getBlockType)(n)?.attributes?.tagName?.enum)&&void 0!==o?o:[],c=l.length?l:a.map((e=>({label:e,value:e})));return c.length?(0,t.createElement)(E.SelectControl,{label:(0,b.__)("Tag Name","generateblocks-pro"),value:e,options:c,onChange:r}):null}const j=(0,l.compose)((function(e){return s=>{var l,o,a,u;const{attributes:i,setAttributes:d,context:y}=s,{htmlAttributes:b={},uniqueId:p,className:g,align:S}=i,f=(0,c.useSelect)((e=>e("core/editor").isSavingPost())),{style:m="",href:v,...h}=b,R=Object.keys(h).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(h[t]),e)),{}),[w,A]=(0,r.useState)(m);var C,k,L,D;(0,r.useEffect)((()=>{!async function(){const e=await(0,N.applyFilters)("generateblocks.editor.htmlAttributes.style",m,{...s});A(e)}()}),[m,y,f]),C=()=>{const e=["alignwide","alignfull"],t=(g?.split(" ")||[]).filter((t=>!e.includes(t)));S&&t.push("align"+S),d({className:t.join(" ")})},k=[S],D=(L=(0,t.useRef)(!0)).current?(L.current=!1,!0):L.current,(0,t.useEffect)((function(){if(!D)return C()}),k);const O="string"==typeof w?w.split(";").reduce(((e,t)=>{const s=t.indexOf(":");if(-1===s)return e;let l=t.slice(0,s).trim();const r=t.slice(s+1).trim();return l&&r&&(l.startsWith("--")||(l=l.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[l]=r),e}),{}):"",T={...R,style:O,"data-gb-id":p,"data-context-post-id":null!==(l=null!==(o=y?.postId)&&void 0!==o?o:y?.["generateblocks/loopIndex"])&&void 0!==l?l:0,"data-align":S||void 0},P=(0,r.useMemo)((()=>Array.isArray(b)?{}:b),[JSON.stringify(b)]);return(0,r.useEffect)((()=>{const e={...b};Object.keys(e).forEach((t=>{const s=t.startsWith("data-"),l=e[t];I.includes(t)||""!==l||s||"alt"===t||delete e[t],"string"!=typeof l&&"boolean"!=typeof l&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const s=Object.keys(e),l=Object.keys(t);if(s.length!==l.length)return!1;for(const l of s)if(e[l]!==t[l])return!1;return!0}(e,b)||d({htmlAttributes:e})}),[JSON.stringify(b)]),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(e,{...s,editorHtmlAttributes:T,htmlAttributes:P}),(0,t.createElement)(n.InspectorAdvancedControls,null,(0,t.createElement)(E.TextControl,{label:"HTML ID",value:null!==(a=b.id)&&void 0!==a?a:"",onChange:e=>{d({htmlAttributes:{...b,id:e}})},onBlur:()=>{b.id&&d({htmlAttributes:{...b,id:_(b.id)}})}}),(0,t.createElement)(E.TextControl,{label:"ARIA Label",value:null!==(u=b["aria-label"])&&void 0!==u?u:"",onChange:e=>{d({htmlAttributes:{...b,"aria-label":e}})}})))}}),(function(e){return s=>{const{attributes:l,name:n,setAttributes:c,isSelected:u,clientId:i}=s,{uniqueId:d,styles:y,css:b}=l,{atRule:p,deviceType:g,setAtRule:S,currentStyle:f,setCurrentStyle:m,setNestedRule:v}=k(),h=(0,o.useSetStyles)(s,{cleanStylesObject:a.cleanStylesObject}),R=(0,r.useMemo)((()=>d?(0,o.getSelector)(n,d):""),[n,d]),w=Array.isArray(y)?{}:y;return(0,o.useAtRuleEffect)({deviceType:g,atRule:p,setAtRule:S,defaultAtRules:a.defaultAtRules,isSelected:u,getPreviewWidth:a.getPreviewWidth}),(0,o.useGenerateCSSEffect)({selector:R,styles:w,setAttributes:c,getCss:a.getCss,getSelector:o.getSelector,isSelected:u,blockCss:b,clientId:i}),(0,o.useStyleSelectorEffect)({isSelected:u,currentStyle:f,selector:R,setCurrentStyle:m,setNestedRule:v}),(0,o.useDecodeStyleKeys)({styles:y,setAttributes:c}),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(o.Style,{selector:R,getCss:a.getCss,styles:w,clientId:i,name:n}),(0,t.createElement)(e,{...s,selector:R,onStyleChange:function(e,t="",s="",l=""){const r="object"==typeof e?e:{[e]:t},n=(0,o.buildChangedStylesObject)(r,s,l);h(n)},getStyleValue:function(e,t="",s=""){var l,r,n,o;return s?t?null!==(n=y?.[s]?.[t]?.[e])&&void 0!==n?n:"":null!==(r=y?.[s]?.[e])&&void 0!==r?r:"":t?null!==(o=y?.[t]?.[e])&&void 0!==o?o:"":null!==(l=y?.[e])&&void 0!==l?l:""},styles:w}))}}),o.withUniqueId)((function(e){const{attributes:s,setAttributes:l,onStyleChange:a,getStyleValue:c,editorHtmlAttributes:u={},styles:i,name:d,clientId:y}=e,{tagName:b}=s,p=D("gb-tabs__menu",{...s,styles:i},!0),g=(0,r.useRef)(),S=(0,n.useBlockProps)({className:p.filter(Boolean).join(" ").trim(),...u,ref:g}),f=(0,n.useInnerBlocksProps)(S,{allowedBlocks:["generateblocks-pro/tab-menu-item"],renderAppender:!1}),m=b||"div",v=(0,r.useMemo)((()=>({selectorShortcuts:T,visibleShortcuts:[]})),[]);(0,r.useEffect)((()=>{b||l({tagName:"div"})}),[b]);const h={name:d,attributes:s,setAttributes:l,clientId:y,getStyleValue:c,onStyleChange:a};return(0,t.createElement)(t.Fragment,null,(0,t.createElement)(n.InspectorControls,null,(0,t.createElement)(o.BlockStyles,{settingsTab:(0,t.createElement)(O.OpenPanel,{...h,panelId:"settings"},(0,t.createElement)(P,{blockName:d,value:b,onChange:e=>{l({tagName:e})}})),stylesTab:(0,t.createElement)(L,{attributes:s,setAttributes:l,shortcuts:v,onStyleChange:a})})),(0,t.createElement)(m,{...f}))})),x=JSON.parse('{"UU":"generateblocks-pro/tabs-menu"}');(0,s.registerBlockType)(x.UU,{edit:j,save:function({attributes:e}){const{tagName:s,htmlAttributes:l={}}=e,r=D("gb-tabs__menu",e,!0),o=n.useBlockProps.save({className:r.join(" ").trim(),...l});return(0,t.createElement)(s,{...n.useInnerBlocksProps.save(o)})},icon:function(){return(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",className:"gblocks-block-icon","aria-hidden":"true",focusable:"false"},(0,t.createElement)("path",{d:"M18.23,13.26L5.77,13.26C5.43,13.26 5.15,13.54 5.15,13.88C5.15,14.22 5.43,14.5 5.77,14.5L18.23,14.5C18.57,14.5 18.85,14.22 18.85,13.88C18.85,13.54 18.58,13.26 18.23,13.26ZM18.23,16.29L5.77,16.29C5.43,16.29 5.15,16.57 5.15,16.91C5.15,17.25 5.43,17.53 5.77,17.53L18.23,17.53C18.57,17.53 18.85,17.25 18.85,16.91L18.85,16.9C18.85,16.565 18.575,16.29 18.24,16.29L18.23,16.29ZM5.14,10.85C5.14,11.19 5.42,11.47 5.76,11.47L18.22,11.47C18.56,11.47 18.84,11.19 18.84,10.85C18.84,10.51 18.56,10.23 18.22,10.23L5.77,10.23L5.753,10.23C5.417,10.23 5.14,10.506 5.14,10.843L5.14,10.85Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M21.38,2L2.62,2C2.28,2 2,2.28 2,2.62L2,6.997L21.99,6.997L21.99,2.62L21.99,2.608C21.99,2.274 21.716,2 21.382,2L21.38,2ZM9.5,3.25L14.5,3.25L14.5,5.76L9.5,5.76L9.5,3.25ZM20.75,7.01L3.25,6.997L3.25,6.35L3.25,5.76L3.25,3.25L8.25,3.25L8.25,6.39C8.25,6.73 8.53,7.01 8.87,7.01L20.75,7.01ZM20.75,5.76L15.75,5.76L15.75,3.25L20.75,3.25L20.75,5.76Z",style:{fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M21.99,6.997L21.99,21.38C21.99,21.72 21.71,22 21.37,22L2.62,22C2.28,22 2,21.72 2,21.37L2,6.997L3.25,6.997L3.25,20.75L20.75,20.75L20.75,7.01L8.87,7.01C8.827,7.01 8.785,7.006 8.744,6.997L21.99,6.997Z",style:{fillOpacity:.3,fillRule:"nonzero"}}))}})})();