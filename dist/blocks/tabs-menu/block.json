{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "generateblocks-pro/tabs-menu", "title": "Tabs Menu", "category": "generateblocks", "parent": ["generateblocks-pro/tabs"], "icon": "star", "description": "Contains the menu items for a set of tabs.", "keywords": ["alert", "message"], "version": "1.0.0", "textdomain": "generateblocks-pro", "attributes": {"uniqueId": {"type": "string", "default": ""}, "tagName": {"type": "string", "default": "", "enum": ["div", "section", "aside", "nav", "ul", "ol", "li"]}, "styles": {"type": "object", "default": {}}, "css": {"type": "string", "default": ""}, "globalClasses": {"type": "array", "default": []}, "htmlAttributes": {"type": "object", "default": {}}}, "supports": {"align": false, "className": false}, "editorScript": ["file:./index.js"]}