{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "generateblocks-pro/accordion-content", "title": "Accordion Content", "category": "generateblocks", "parent": ["generateblocks-pro/accordion-item"], "icon": "star", "description": "The accordion content wrapper. Accordion transitions are applied to this block.", "keywords": ["accordion"], "version": "1.0.0", "textdomain": "generateblocks-pro", "attributes": {"uniqueId": {"type": "string", "default": ""}, "tagName": {"type": "string", "default": "", "enum": ["div", "section", "aside", "ul", "ol"]}, "styles": {"type": "object", "default": {}}, "css": {"type": "string", "default": ""}, "globalClasses": {"type": "array", "default": []}, "htmlAttributes": {"type": "object", "default": {}}}, "supports": {"align": false, "className": false}, "editorStyle": ["file:./index.css"], "editorScript": "file:./index.js"}