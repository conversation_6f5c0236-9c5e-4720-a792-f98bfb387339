(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var l in s)e.o(s,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:s[l]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.React,s=window.wp.blocks,l=window.wp.blockEditor,n=window.wp.compose,r=window.wp.element,o=window.wp.i18n,c=window.wp.components,a=window.gbp.blockStyles,i=window.gbp.components;function u(e,t,s=!1){const{styles:l={},uniqueId:n="",globalClasses:r=[]}=t,o=[];return s&&o.push(e),r.length>0&&o.push(...r),Object.keys(l).length>0&&o.push(`${e}-${n}`),o}const d=window.wp.data,y=window.wp.hooks,p=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function g(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}const b=window.gbp.stylesBuilder,S=(0,d.createReduxStore)("gbp-current-style",{reducer:b.currentStyleReducer,actions:b.currentStyleActions,selectors:b.currentStyleSelectors}),m=(0,d.createReduxStore)("gbp-styles",{reducer:b.styleReducer,actions:b.styleActions,selectors:b.styleSelectors}),f=(0,d.createReduxStore)("gbp-styles-at-rule",{reducer:b.atRuleReducer,actions:b.atRuleActions,selectors:b.atRuleSelectors}),h=(0,d.createReduxStore)("gbp-styles-nested-rule",{reducer:b.nestedRuleReducer,actions:b.nestedRuleActions,selectors:b.nestedRuleSelectors}),R=window.wp.apiFetch;var w=e.n(R);const v=window.wp.notices,C=window.wp.url,A=window.wp.coreData;var E;window.lodash;const k="undefined"!=typeof gbGlobalStylePermissions&&null!==(E=gbGlobalStylePermissions?.canManageStyles)&&void 0!==E&&E,L=window.wp.editPost;const N=(0,d.createReduxStore)("gbp-block-styles-current-style",{reducer:b.currentStyleReducer,actions:b.currentStyleActions,selectors:b.currentStyleSelectors}),I=(0,d.createReduxStore)("gbp-block-styles-at-rule",{reducer:b.atRuleReducer,actions:b.atRuleActions,selectors:b.atRuleSelectors}),D=(0,d.createReduxStore)("gbp-block-styles-nested-rule",{reducer:b.nestedRuleReducer,actions:b.nestedRuleActions,selectors:b.nestedRuleSelectors});function O(){const e=(0,d.useSelect)((e=>e(I).getAtRule())),{setAtRule:t}=(0,d.useDispatch)(I),s=(0,d.useSelect)((e=>e(D).getNestedRule())),{setNestedRule:n}=(0,d.useDispatch)(D),r=(0,a.useCurrentAtRule)(b.defaultAtRules),{setCurrentStyle:c}=(0,d.useDispatch)(N),i=(0,d.useSelect)((e=>e(N).currentStyle())),{deviceType:u,setDeviceType:y}=(0,a.useDeviceType)(),p=function(){const{setCurrentStyle:e}=(0,d.useDispatch)(S),{setStyles:t}=(0,d.useDispatch)(m),{createNotice:s,removeAllNotices:n}=(0,d.useDispatch)(v.store),{getEntityRecordEdits:r}=(0,d.useSelect)(A.store),{getSelectedBlock:c}=(0,d.useSelect)((e=>e(l.store)),[]),{setAtRule:a}=(0,d.useDispatch)(f),{setNestedRule:i}=(0,d.useDispatch)(h),{openGeneralSidebar:u}=(0,d.useDispatch)(L.store);return async(l,d={})=>{if(!k)return;const{classStyles:y,classPostId:p}=await async function(e){var t;const s=await w()({path:(0,C.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let l=null!==(t=s?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(l)&&0===l.length&&(l={}),{classStyles:l,classPostId:s?.response?.data?.postId}}(l);if(!p)return n("snackbar"),void s("error",(0,o.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,o.__)("%s does not exist.","generateblocks-pro"),l),{type:"snackbar"});a(""),i(""),u("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:p,name:l,classStyles:y,clientId:c()?.clientId,options:d}),d.nestedRule&&i(d.nestedRule),d.atRule&&a(d.atRule);const g=r("postType","gblocks_styles",p);t(g?.gb_style_data||y),n("snackbar"),s("info",(0,o.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,o.__)("Editing %s.","generateblocks-pro"),l),{type:"snackbar"})}}(),g=function(){const{setCurrentStyle:e}=(0,d.useDispatch)(S),{setStyles:t}=(0,d.useDispatch)(m),{setAtRule:s}=(0,d.useDispatch)(f),{setNestedRule:l}=(0,d.useDispatch)(h);return()=>{e({}),t({}),s(""),l("")}}();return{atRule:e,nestedRule:s,setAtRule:t,currentAtRule:r,setNestedRule:n,setDeviceType:y,deviceType:u,setCurrentStyle:c,currentStyle:i,getPreviewDevice:a.getPreviewDevice,setGlobalStyle:p,cancelEditGlobalStyle:g}}function _({attributes:e,setAttributes:s,shortcuts:l,onStyleChange:n}){const{atRule:r,setAtRule:o,nestedRule:c,setNestedRule:i,setDeviceType:u,getPreviewDevice:d,currentStyle:y,setGlobalStyle:p,cancelEditGlobalStyle:g}=O(),{styles:S,globalClasses:m=[]}=e,f=(0,b.getStylesObject)(S,r,c);return(0,t.createElement)(b.StylesBuilder,{currentSelector:y?.selector,styles:f,allStyles:S,onDeleteStyle:(e,t)=>{const l=(0,b.deleteStylesObjectKey)(S,e,t);s({styles:l})},nestedRule:c,atRule:r,onStyleChange:(e,t=null)=>n(e,t,r,c),onNestedRuleChange:e=>i(e),onAtRuleChange:e=>{o(e);const t=(0,b.getPreviewWidth)(e),s=d(t);s&&u(s)},onUpdateKey:(e,t,l)=>{const n=(0,b.updateStylesObjectKey)(S,e,t,l);s({styles:n})},selectorShortcuts:l.selectorShortcuts,visibleSelectors:l.visibleShortcuts,onEditStyle:p,cancelEditStyle:g,setLocalTab:e=>{sessionStorage.setItem(a.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:m})}function T({value:e,options:l=[],onChange:n,blockName:r}){var a;const i=null!==(a=(0,s.getBlockType)(r)?.attributes?.tagName?.enum)&&void 0!==a?a:[],u=l.length?l:i.map((e=>({label:e,value:e})));return u.length?(0,t.createElement)(c.SelectControl,{label:(0,o.__)("Tag Name","generateblocks-pro"),value:e,options:u,onChange:n}):null}const P=(0,n.compose)((function(e){return s=>{var n,o,a,i;const{attributes:u,setAttributes:b,context:S}=s,{htmlAttributes:m={},uniqueId:f,className:h,align:R}=u,w=(0,d.useSelect)((e=>e("core/editor").isSavingPost())),{style:v="",href:C,...A}=m,E=Object.keys(A).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(A[t]),e)),{}),[k,L]=(0,r.useState)(v);var N,I,D,O;(0,r.useEffect)((()=>{!async function(){const e=await(0,y.applyFilters)("generateblocks.editor.htmlAttributes.style",v,{...s});L(e)}()}),[v,S,w]),N=()=>{const e=["alignwide","alignfull"],t=(h?.split(" ")||[]).filter((t=>!e.includes(t)));R&&t.push("align"+R),b({className:t.join(" ")})},I=[R],O=(D=(0,t.useRef)(!0)).current?(D.current=!1,!0):D.current,(0,t.useEffect)((function(){if(!O)return N()}),I);const _="string"==typeof k?k.split(";").reduce(((e,t)=>{const s=t.indexOf(":");if(-1===s)return e;let l=t.slice(0,s).trim();const n=t.slice(s+1).trim();return l&&n&&(l.startsWith("--")||(l=l.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[l]=n),e}),{}):"",T={...E,style:_,"data-gb-id":f,"data-context-post-id":null!==(n=null!==(o=S?.postId)&&void 0!==o?o:S?.["generateblocks/loopIndex"])&&void 0!==n?n:0,"data-align":R||void 0},P=(0,r.useMemo)((()=>Array.isArray(m)?{}:m),[JSON.stringify(m)]);return(0,r.useEffect)((()=>{const e={...m};Object.keys(e).forEach((t=>{const s=t.startsWith("data-"),l=e[t];p.includes(t)||""!==l||s||"alt"===t||delete e[t],"string"!=typeof l&&"boolean"!=typeof l&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const s=Object.keys(e),l=Object.keys(t);if(s.length!==l.length)return!1;for(const l of s)if(e[l]!==t[l])return!1;return!0}(e,m)||b({htmlAttributes:e})}),[JSON.stringify(m)]),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(e,{...s,editorHtmlAttributes:T,htmlAttributes:P}),(0,t.createElement)(l.InspectorAdvancedControls,null,(0,t.createElement)(c.TextControl,{label:"HTML ID",value:null!==(a=m.id)&&void 0!==a?a:"",onChange:e=>{b({htmlAttributes:{...m,id:e}})},onBlur:()=>{m.id&&b({htmlAttributes:{...m,id:g(m.id)}})}}),(0,t.createElement)(c.TextControl,{label:"ARIA Label",value:null!==(i=m["aria-label"])&&void 0!==i?i:"",onChange:e=>{b({htmlAttributes:{...m,"aria-label":e}})}})))}}),(function(e){return s=>{const{attributes:l,name:n,setAttributes:o,isSelected:c,clientId:i}=s,{uniqueId:u,styles:d,css:y}=l,{atRule:p,deviceType:g,setAtRule:S,currentStyle:m,setCurrentStyle:f,setNestedRule:h}=O(),R=(0,a.useSetStyles)(s,{cleanStylesObject:b.cleanStylesObject}),w=(0,r.useMemo)((()=>u?(0,a.getSelector)(n,u):""),[n,u]),v=Array.isArray(d)?{}:d;return(0,a.useAtRuleEffect)({deviceType:g,atRule:p,setAtRule:S,defaultAtRules:b.defaultAtRules,isSelected:c,getPreviewWidth:b.getPreviewWidth}),(0,a.useGenerateCSSEffect)({selector:w,styles:v,setAttributes:o,getCss:b.getCss,getSelector:a.getSelector,isSelected:c,blockCss:y,clientId:i}),(0,a.useStyleSelectorEffect)({isSelected:c,currentStyle:m,selector:w,setCurrentStyle:f,setNestedRule:h}),(0,a.useDecodeStyleKeys)({styles:d,setAttributes:o}),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(a.Style,{selector:w,getCss:b.getCss,styles:v,clientId:i,name:n}),(0,t.createElement)(e,{...s,selector:w,onStyleChange:function(e,t="",s="",l=""){const n="object"==typeof e?e:{[e]:t},r=(0,a.buildChangedStylesObject)(n,s,l);R(r)},getStyleValue:function(e,t="",s=""){var l,n,r,o;return s?t?null!==(r=d?.[s]?.[t]?.[e])&&void 0!==r?r:"":null!==(n=d?.[s]?.[e])&&void 0!==n?n:"":t?null!==(o=d?.[t]?.[e])&&void 0!==o?o:"":null!==(l=d?.[e])&&void 0!==l?l:""},styles:v}))}}),a.withUniqueId)((function(e){const{attributes:s,editorHtmlAttributes:n={},htmlAttributes:d,styles:y,setAttributes:p,onStyleChange:g,getStyleValue:b,clientId:S,name:m}=e,{tagName:f,uniqueId:h}=s,R=u("gb-accordion__content",{...s,styles:y},!0),w=(0,l.useBlockProps)({className:R.join(" ").trim(),...n}),v=(0,l.useInnerBlocksProps)(w),C=f||"div";(0,r.useEffect)((()=>{f||p({tagName:"div"})}),[f]),(0,r.useEffect)((()=>{var e;if(!h)return;const t=null!==(e=d?.id)&&void 0!==e?e:"",s=t&&t.startsWith("gb-accordion-content-")&&t!==`gb-accordion-content-${h}`;t&&!s||p({htmlAttributes:{...d,id:`gb-accordion-content-${h}`}})}),[h,d?.id]);const A={name:m,attributes:s,setAttributes:p,clientId:S,getStyleValue:b,onStyleChange:g};return(0,t.createElement)(t.Fragment,null,(0,t.createElement)(l.InspectorControls,null,(0,t.createElement)(a.BlockStyles,{settingsTab:(0,t.createElement)(t.Fragment,null,(0,t.createElement)(c.PanelBody,null,(0,t.createElement)(c.Notice,{status:"info",isDismissible:!1},(0,t.createElement)("p",null,(0,o.__)("This block is necessary for the structure and transitions of the Accordion block.","generateblocks-pro")),(0,t.createElement)("p",{style:{marginBottom:0}},(0,o.__)("If using transitions, avoid adding any padding, margin or borders to this block.","generateblocks-pro")))),(0,t.createElement)(i.OpenPanel,{...A,panelId:"settings"},(0,t.createElement)(T,{blockName:m,value:f,onChange:e=>{p({tagName:e})}}))),stylesTab:(0,t.createElement)(_,{attributes:s,setAttributes:p,shortcuts:{},onStyleChange:g})})),(0,t.createElement)(C,{...v}))})),j=JSON.parse('{"UU":"generateblocks-pro/accordion-content"}');(0,s.registerBlockType)(j.UU,{edit:P,save:function({attributes:e}){const{tagName:s,htmlAttributes:n={}}=e,r=u("gb-accordion__content",e,!0),o=l.useBlockProps.save({className:r.join(" ").trim(),...n});return(0,t.createElement)(s,{...l.useInnerBlocksProps.save(o)})},icon:function(){return(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",className:"gblocks-block-icon","aria-hidden":"true",focusable:"false"},(0,t.createElement)("path",{d:"M21.376,21.918L2.626,21.918C2.282,21.918 2,21.636 2,21.292C2,20.949 2.282,20.667 2.626,20.667L21.377,20.667C21.719,20.667 22.002,20.95 22.002,21.293C22.002,21.636 21.719,21.918 21.376,21.918C21.376,21.918 21.376,21.918 21.376,21.918ZM21.376,18.765L2.626,18.765C2.283,18.765 2.001,18.483 2.001,18.14C2.001,17.797 2.283,17.515 2.626,17.515L21.377,17.515C21.719,17.515 22.001,17.797 22.001,18.14C22.001,18.483 21.719,18.765 21.376,18.765C21.376,18.765 21.376,18.765 21.376,18.765ZM21.376,15.613L2.626,15.613C2.283,15.613 2.001,15.331 2.001,14.988C2.001,14.645 2.283,14.363 2.626,14.363L21.377,14.363C21.719,14.363 22.001,14.645 22.001,14.988C22.001,15.331 21.719,15.613 21.376,15.613C21.376,15.613 21.376,15.613 21.376,15.613Z",style:{fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M17.831,9.944C17.693,9.944 17.561,9.889 17.463,9.791L15.901,8.229L16.638,7.492L17.831,8.686L19.026,7.492L19.762,8.229L18.2,9.792C18.102,9.889 17.969,9.944 17.831,9.944Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M19.352,2L19.764,2.412L18.201,3.975C18.103,4.072 17.97,4.127 17.833,4.127C17.694,4.127 17.562,4.072 17.465,3.974L15.902,2.412L16.314,2L16.964,2L17.833,2.869L18.702,2L19.352,2Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M2.002,5.66L2.002,2L3.251,2L3.251,5.069L20.752,5.069L20.752,2L22.002,2L22.002,5.694C22.002,5.706 22.001,5.718 22.001,5.73L22.001,11.511C22.001,11.857 21.721,12.137 21.376,12.137L2.626,12.137C2.283,12.137 2,11.855 2,11.512C2,11.512 2,11.511 2.001,11.511L2.001,5.695C2.001,5.683 2.001,5.671 2.002,5.66ZM20.751,6.32L3.25,6.32L3.25,10.886L20.751,10.886L20.751,6.32Z",style:{fillOpacity:.3,fillRule:"nonzero"}}))}})})();