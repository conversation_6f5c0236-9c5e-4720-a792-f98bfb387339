(()=>{var e={46942:(e,t)=>{var l;!function(){"use strict";var o={}.hasOwnProperty;function n(){for(var e="",t=0;t<arguments.length;t++){var l=arguments[t];l&&(e=s(e,r(l)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var l in e)o.call(e,l)&&e[l]&&(t=s(t,l));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n.default=n,e.exports=n):void 0===(l=function(){return n}.apply(t,[]))||(e.exports=l)}()}},t={};function l(o){var n=t[o];if(void 0!==n)return n.exports;var r=t[o]={exports:{}};return e[o](r,r.exports,l),r.exports}l.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return l.d(t,{a:t}),t},l.d=(e,t)=>{for(var o in t)l.o(t,o)&&!l.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.wp.blocks,t=window.wp.i18n,o=window.React,n=(wp.element.createElement,window.wp.hooks),r=((0,n.applyFilters)("generateblocks.utils.noStyleAttributes",["accordionItemOpen","accordionMultipleOpen","alt","anchor","ariaLabel","blockLabel","blockVersion","buttonType","className","columns","content","cssClasses","customIcon","dateReplacePublished","dateType","defaultOpenedTab","dynamicContentType","dynamicLinkType","dynamicSource","element","elementId","globalClasses","globalStyleId","globalStyleLabel","gridId","hasIcon","hasUrl","hiddenLinkAriaLabel","href","htmlAttributes","icon","iconLocation","isDynamic","isDynamic","isGlobalStyle","isGrid","isPagination","isQueryLoop","isQueryLoopItem","linkMetaFieldName","linkType","lock","marginSyncUnits","mediaId","mediaUrl","metaFieldName","multipleCommentsText","noCommentsText","openInNewWindow","paddingSyncUnits","postId","postType","query","relNoFollow","relSponsored","singleCommentsText","sizeSlug","tabItemOpen","tagName","target","termSeparator","termTaxonomy","text","title","uniqueId","url","useDynamicData","useGlobalStyle","useInnerContainer","variantRole"]),window.wp.compose),s=window.wp.element,a=window.wp.blockEditor,c=window.wp.components,i=window.gbp.blockStyles,u=window.wp.data;var d=l(46942),p=l.n(d);function b({name:e,clientId:t,align:l,children:o}){const{getBlockRootClientId:r}=(0,u.useSelect)((e=>e("core/block-editor")),[]),c=(0,u.useSelect)((e=>{const{getSettings:t}=e(a.store);return t().supportsLayout||!1}),[]),i=e.toString().replace("/","-"),d={className:p()({"wp-block":!0,"gb-is-root-block":!0,[`gb-root-block-${i}`]:!0,[`align${l}`]:c}),"data-align":l&&!c?l:null,"data-block":t},b=r(t);return(0,n.applyFilters)("generateblocks.rootElement.disable",b,{name:e})?o:(0,s.createElement)("div",d,o)}const g=window.gbp.stylesBuilder,y=(0,u.createReduxStore)("gbp-current-style",{reducer:g.currentStyleReducer,actions:g.currentStyleActions,selectors:g.currentStyleSelectors}),m=(0,u.createReduxStore)("gbp-styles",{reducer:g.styleReducer,actions:g.styleActions,selectors:g.styleSelectors}),h=(0,u.createReduxStore)("gbp-styles-at-rule",{reducer:g.atRuleReducer,actions:g.atRuleActions,selectors:g.atRuleSelectors}),f=(0,u.createReduxStore)("gbp-styles-nested-rule",{reducer:g.nestedRuleReducer,actions:g.nestedRuleActions,selectors:g.nestedRuleSelectors}),v=window.wp.apiFetch;var S=l.n(v);const k=window.wp.notices,w=window.wp.url,C=window.wp.coreData;var L;window.lodash;const R="undefined"!=typeof gbGlobalStylePermissions&&null!==(L=gbGlobalStylePermissions?.canManageStyles)&&void 0!==L&&L,A=window.wp.editPost;const E=(0,u.createReduxStore)("gbp-block-styles-current-style",{reducer:g.currentStyleReducer,actions:g.currentStyleActions,selectors:g.currentStyleSelectors}),I=(0,u.createReduxStore)("gbp-block-styles-at-rule",{reducer:g.atRuleReducer,actions:g.atRuleActions,selectors:g.atRuleSelectors}),T=(0,u.createReduxStore)("gbp-block-styles-nested-rule",{reducer:g.nestedRuleReducer,actions:g.nestedRuleActions,selectors:g.nestedRuleSelectors});function _(){const e=(0,u.useSelect)((e=>e(I).getAtRule())),{setAtRule:l}=(0,u.useDispatch)(I),o=(0,u.useSelect)((e=>e(T).getNestedRule())),{setNestedRule:n}=(0,u.useDispatch)(T),r=(0,i.useCurrentAtRule)(g.defaultAtRules),{setCurrentStyle:s}=(0,u.useDispatch)(E),c=(0,u.useSelect)((e=>e(E).currentStyle())),{deviceType:d,setDeviceType:p}=(0,i.useDeviceType)(),b=function(){const{setCurrentStyle:e}=(0,u.useDispatch)(y),{setStyles:l}=(0,u.useDispatch)(m),{createNotice:o,removeAllNotices:n}=(0,u.useDispatch)(k.store),{getEntityRecordEdits:r}=(0,u.useSelect)(C.store),{getSelectedBlock:s}=(0,u.useSelect)((e=>e(a.store)),[]),{setAtRule:c}=(0,u.useDispatch)(h),{setNestedRule:i}=(0,u.useDispatch)(f),{openGeneralSidebar:d}=(0,u.useDispatch)(A.store);return async(a,u={})=>{if(!R)return;const{classStyles:p,classPostId:b}=await async function(e){var t;const l=await S()({path:(0,w.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let o=null!==(t=l?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(o)&&0===o.length&&(o={}),{classStyles:o,classPostId:l?.response?.data?.postId}}(a);if(!b)return n("snackbar"),void o("error",(0,t.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,t.__)("%s does not exist.","generateblocks-pro"),a),{type:"snackbar"});c(""),i(""),d("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:b,name:a,classStyles:p,clientId:s()?.clientId,options:u}),u.nestedRule&&i(u.nestedRule),u.atRule&&c(u.atRule);const g=r("postType","gblocks_styles",b);l(g?.gb_style_data||p),n("snackbar"),o("info",(0,t.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,t.__)("Editing %s.","generateblocks-pro"),a),{type:"snackbar"})}}(),v=function(){const{setCurrentStyle:e}=(0,u.useDispatch)(y),{setStyles:t}=(0,u.useDispatch)(m),{setAtRule:l}=(0,u.useDispatch)(h),{setNestedRule:o}=(0,u.useDispatch)(f);return()=>{e({}),t({}),l(""),o("")}}();return{atRule:e,nestedRule:o,setAtRule:l,currentAtRule:r,setNestedRule:n,setDeviceType:p,deviceType:d,setCurrentStyle:s,currentStyle:c,getPreviewDevice:i.getPreviewDevice,setGlobalStyle:b,cancelEditGlobalStyle:v}}const x=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function B(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function N({attributes:e,setAttributes:t,shortcuts:l,onStyleChange:n}){const{atRule:r,setAtRule:s,nestedRule:a,setNestedRule:c,setDeviceType:u,getPreviewDevice:d,currentStyle:p,setGlobalStyle:b,cancelEditGlobalStyle:y}=_(),{styles:m,globalClasses:h=[]}=e,f=(0,g.getStylesObject)(m,r,a);return(0,o.createElement)(g.StylesBuilder,{currentSelector:p?.selector,styles:f,allStyles:m,onDeleteStyle:(e,l)=>{const o=(0,g.deleteStylesObjectKey)(m,e,l);t({styles:o})},nestedRule:a,atRule:r,onStyleChange:(e,t=null)=>n(e,t,r,a),onNestedRuleChange:e=>c(e),onAtRuleChange:e=>{s(e);const t=(0,g.getPreviewWidth)(e),l=d(t);l&&u(l)},onUpdateKey:(e,l,o)=>{const n=(0,g.updateStylesObjectKey)(m,e,l,o);t({styles:n})},selectorShortcuts:l.selectorShortcuts,visibleSelectors:l.visibleShortcuts,onEditStyle:b,cancelEditStyle:y,setLocalTab:e=>{sessionStorage.setItem(i.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:h})}function D(e,t,l=!1){const{styles:o={},uniqueId:n="",globalClasses:r=[]}=t,s=[];return l&&s.push(e),r.length>0&&s.push(...r),Object.keys(o).length>0&&s.push(`${e}-${n}`),s}const O=window.gbp.components,j={default:{items:[{label:"Hover",value:"&:hover"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"},{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"}]},interactions:{label:(0,t.__)("Interactions","generateblocks-pro"),items:[{label:"Hover",value:"&:hover"},{label:"Focus",value:"&:focus"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"}]},links:{label:(0,t.__)("Links","generateblocks-pro"),items:[{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"},{label:"Hovered & focused links",value:"a:is(:hover, :focus)"},{label:"Visited links",value:"a:visited"}]},pseudoElements:{label:(0,t.__)("Pseudo Elements","generateblocks-pro"),items:[{label:"Before",value:"&:before"},{label:"After",value:"&:after"}]}};function M({value:l,options:n=[],onChange:r,blockName:s}){var a;const i=null!==(a=(0,e.getBlockType)(s)?.attributes?.tagName?.enum)&&void 0!==a?a:[],u=n.length?n:i.map((e=>({label:e,value:e})));return u.length?(0,o.createElement)(c.SelectControl,{label:(0,t.__)("Tag Name","generateblocks-pro"),value:l,options:u,onChange:r}):null}const P=(0,r.compose)((function(e){return t=>{var l,r,i,d;const{attributes:p,setAttributes:b,context:g}=t,{htmlAttributes:y={},uniqueId:m,className:h,align:f}=p,v=(0,u.useSelect)((e=>e("core/editor").isSavingPost())),{style:S="",href:k,...w}=y,C=Object.keys(w).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(w[t]),e)),{}),[L,R]=(0,s.useState)(S);var A,E,I,T;(0,s.useEffect)((()=>{!async function(){const e=await(0,n.applyFilters)("generateblocks.editor.htmlAttributes.style",S,{...t});R(e)}()}),[S,g,v]),A=()=>{const e=["alignwide","alignfull"],t=(h?.split(" ")||[]).filter((t=>!e.includes(t)));f&&t.push("align"+f),b({className:t.join(" ")})},E=[f],T=(I=(0,o.useRef)(!0)).current?(I.current=!1,!0):I.current,(0,o.useEffect)((function(){if(!T)return A()}),E);const _="string"==typeof L?L.split(";").reduce(((e,t)=>{const l=t.indexOf(":");if(-1===l)return e;let o=t.slice(0,l).trim();const n=t.slice(l+1).trim();return o&&n&&(o.startsWith("--")||(o=o.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[o]=n),e}),{}):"",N={...C,style:_,"data-gb-id":m,"data-context-post-id":null!==(l=null!==(r=g?.postId)&&void 0!==r?r:g?.["generateblocks/loopIndex"])&&void 0!==l?l:0,"data-align":f||void 0},D=(0,s.useMemo)((()=>Array.isArray(y)?{}:y),[JSON.stringify(y)]);return(0,s.useEffect)((()=>{const e={...y};Object.keys(e).forEach((t=>{const l=t.startsWith("data-"),o=e[t];x.includes(t)||""!==o||l||"alt"===t||delete e[t],"string"!=typeof o&&"boolean"!=typeof o&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const l=Object.keys(e),o=Object.keys(t);if(l.length!==o.length)return!1;for(const o of l)if(e[o]!==t[o])return!1;return!0}(e,y)||b({htmlAttributes:e})}),[JSON.stringify(y)]),(0,o.createElement)(o.Fragment,null,(0,o.createElement)(e,{...t,editorHtmlAttributes:N,htmlAttributes:D}),(0,o.createElement)(a.InspectorAdvancedControls,null,(0,o.createElement)(c.TextControl,{label:"HTML ID",value:null!==(i=y.id)&&void 0!==i?i:"",onChange:e=>{b({htmlAttributes:{...y,id:e}})},onBlur:()=>{y.id&&b({htmlAttributes:{...y,id:B(y.id)}})}}),(0,o.createElement)(c.TextControl,{label:"ARIA Label",value:null!==(d=y["aria-label"])&&void 0!==d?d:"",onChange:e=>{b({htmlAttributes:{...y,"aria-label":e}})}})))}}),(function(e){return t=>{const{attributes:l,name:n,setAttributes:r,isSelected:a,clientId:c}=t,{uniqueId:u,styles:d,css:p}=l,{atRule:b,deviceType:y,setAtRule:m,currentStyle:h,setCurrentStyle:f,setNestedRule:v}=_(),S=(0,i.useSetStyles)(t,{cleanStylesObject:g.cleanStylesObject}),k=(0,s.useMemo)((()=>u?(0,i.getSelector)(n,u):""),[n,u]),w=Array.isArray(d)?{}:d;return(0,i.useAtRuleEffect)({deviceType:y,atRule:b,setAtRule:m,defaultAtRules:g.defaultAtRules,isSelected:a,getPreviewWidth:g.getPreviewWidth}),(0,i.useGenerateCSSEffect)({selector:k,styles:w,setAttributes:r,getCss:g.getCss,getSelector:i.getSelector,isSelected:a,blockCss:p,clientId:c}),(0,i.useStyleSelectorEffect)({isSelected:a,currentStyle:h,selector:k,setCurrentStyle:f,setNestedRule:v}),(0,i.useDecodeStyleKeys)({styles:d,setAttributes:r}),(0,o.createElement)(o.Fragment,null,(0,o.createElement)(i.Style,{selector:k,getCss:g.getCss,styles:w,clientId:c,name:n}),(0,o.createElement)(e,{...t,selector:k,onStyleChange:function(e,t="",l="",o=""){const n="object"==typeof e?e:{[e]:t},r=(0,i.buildChangedStylesObject)(n,l,o);S(r)},getStyleValue:function(e,t="",l=""){var o,n,r,s;return l?t?null!==(r=d?.[l]?.[t]?.[e])&&void 0!==r?r:"":null!==(n=d?.[l]?.[e])&&void 0!==n?n:"":t?null!==(s=d?.[t]?.[e])&&void 0!==s?s:"":null!==(o=d?.[e])&&void 0!==o?o:""},styles:w}))}}),i.withUniqueId)((function(e){var l,n;const{attributes:r,setAttributes:u,name:d,clientId:p,onStyleChange:g,getStyleValue:y,editorHtmlAttributes:m={},htmlAttributes:h={},styles:f={}}=e,{tagName:v}=r,S=D("gb-accordion",{...r,styles:f},!0),k=(0,s.useRef)(),w=(0,a.useBlockProps)({className:S.filter(Boolean).join(" ").trim(),...m,ref:k}),C=(0,a.useInnerBlocksProps)(w,{allowedBlocks:["generateblocks-pro/accordion-item"],renderAppender:!1}),L=v||"div",R=(0,s.useMemo)((()=>{const e=[{label:(0,t.__)("Links","generateblocks"),value:"a"}];return{selectorShortcuts:j,visibleShortcuts:e}}),[]);(0,s.useEffect)((()=>{v||u({tagName:"div"})}),[v]);const A={name:d,attributes:r,setAttributes:u,clientId:p,getStyleValue:y,onStyleChange:g};return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(a.InspectorControls,null,(0,o.createElement)(i.BlockStyles,{settingsTab:(0,o.createElement)(O.OpenPanel,{...A,panelId:"settings"},(0,o.createElement)(c.SelectControl,{label:(0,t.__)("Transition","generateblocks-pro"),value:null!==(l=h?.["data-transition"])&&void 0!==l?l:"",options:[{label:(0,t.__)("None","generateblocks-pro"),value:""},{label:(0,t.__)("Fade","generateblocks-pro"),value:"fade"},{label:(0,t.__)("Slide","generateblocks-pro"),value:"slide"}],onChange:e=>{const t={...h};e?t["data-transition"]=e:delete t["data-transition"],u({htmlAttributes:t})}}),(0,o.createElement)(c.ToggleControl,{label:(0,t.__)("Keep multiple items open","generateblocks-pro"),checked:null!==(n=!!h?.["data-accordion-multiple-open"])&&void 0!==n&&n,onChange:e=>{const t={...h};e?t["data-accordion-multiple-open"]=e:delete t["data-accordion-multiple-open"],u({htmlAttributes:t})}}),(0,o.createElement)(M,{blockName:d,value:v,onChange:e=>{u({tagName:e})}})),stylesTab:(0,o.createElement)(N,{attributes:r,setAttributes:u,shortcuts:R,onStyleChange:g})})),(0,o.createElement)(b,{name:d,clientId:p},(0,o.createElement)(L,{...C})))})),F=JSON.parse('{"UU":"generateblocks-pro/accordion"}'),H=[["generateblocks-pro/accordion-item",{styles:{paddingTop:"1em",paddingRight:"1em",paddingBottom:"1em",paddingLeft:"1em",marginBottom:"1em",borderTopWidth:"1px",borderTopStyle:"solid",borderTopColor:"#000000",borderRightWidth:"1px",borderRightStyle:"solid",borderRightColor:"#000000",borderBottomWidth:"1px",borderBottomStyle:"solid",borderBottomColor:"#000000",borderLeftWidth:"1px",borderLeftStyle:"solid",borderLeftColor:"#000000"}},[["generateblocks-pro/accordion-toggle",{styles:{display:"flex",alignItems:"center",justifyContent:"space-between",color:"#000000",textAlign:"left",columnGap:"1em","&:is(:hover, :focus)":{color:"currentColor"},"&:is(.gb-block-is-current, .gb-block-is-current:hover, .gb-block-is-current:focus)":{fontWeight:"600"}}},[["generateblocks/text",{content:(0,t.__)("Accordion title","generateblocks-pro"),tagName:"span"}],["generateblocks-pro/accordion-toggle-icon",{openIcon:'<svg aria-hidden="true" viewBox="0 0 256 256"><rect width="256" height="256" fill="none" /><polyline points="208 96 128 176 48 96" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16" /></svg>',closeIcon:'<svg aria-hidden="true" viewBox="0 0 256 256"><rect width="256" height="256" fill="none" /><polyline points="48 160 128 80 208 160" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16" /></svg>',styles:{svg:{width:"1em",height:"1em"}}}]]],["generateblocks-pro/accordion-content",{},[["generateblocks/element",{styles:{marginTop:"1em"}},[["core/paragraph",{content:(0,t.__)("Accordion content","generateblocks-pro")}]]]]]]]];function G(){return(0,o.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,o.createElement)("path",{d:"M22.006,22.006L20.665,22.006L20.665,17.629L22.006,17.629L22.006,22.006ZM22.006,14.814L20.665,14.814L20.665,9.185L22.006,9.185L22.006,14.814ZM22.006,6.372L20.672,6.372L20.672,3.328L17.628,3.328L17.628,1.994L21.38,1.994C21.725,1.994 22.006,2.274 22.006,2.619L22.006,6.372ZM6.371,1.994L6.371,3.331L1.994,3.331L1.994,1.994L6.371,1.994ZM14.814,3.331L9.186,3.331L9.186,1.994L14.814,1.994L14.814,3.331Z",style:{fillOpacity:.5}}),(0,o.createElement)("path",{d:"M14,6.5L16.5,6.5L16.5,4L17.5,4L17.5,6.5L20,6.5L20,7.5L17.5,7.5L17.5,10L16.5,10L16.5,7.5L14,7.5L14,6.5Z"}),(0,o.createElement)("path",{d:"M15.556,14.99L2.432,14.99C2.192,14.99 1.994,14.791 1.994,14.55C1.994,14.55 1.994,14.55 1.995,14.55L1.995,10.462C1.995,10.219 2.191,10.022 2.432,10.022L15.556,10.022C15.797,10.022 15.993,10.219 15.993,10.462L15.993,14.55C15.993,14.793 15.797,14.99 15.556,14.99ZM2.869,14.11L15.118,14.11L15.118,10.901L2.869,10.901L2.869,14.11ZM15.556,21.865L2.432,21.865C2.191,21.865 1.994,21.666 1.994,21.425C1.994,21.183 2.191,20.985 2.432,20.985L15.556,20.985C15.796,20.985 15.994,21.184 15.994,21.425C15.994,21.666 15.796,21.865 15.556,21.865L15.556,21.865ZM15.556,19.648L2.432,19.648C2.192,19.648 1.995,19.45 1.995,19.209C1.995,18.968 2.192,18.77 2.432,18.77L15.556,18.77C15.796,18.77 15.993,18.968 15.993,19.209C15.993,19.45 15.796,19.648 15.556,19.648L15.556,19.648ZM15.556,17.433L2.432,17.433C2.192,17.433 1.995,17.235 1.995,16.994C1.995,16.753 2.192,16.554 2.432,16.554L15.556,16.554C15.796,16.554 15.993,16.753 15.993,16.994C15.993,17.235 15.796,17.433 15.556,17.433L15.556,17.433Z",style:{fillRule:"nonzero"}}),(0,o.createElement)("path",{d:"M13.074,13.448C12.978,13.448 12.886,13.41 12.817,13.341L11.724,12.243L12.239,11.725L13.074,12.564L13.911,11.725L14.426,12.243L13.333,13.341C13.264,13.41 13.171,13.448 13.074,13.448Z",style:{fillRule:"nonzero"}}))}function z(l){const{name:n,clientId:r,Component:s=c.ToolbarButton}=l,{getBlockParentsByBlockName:i,getBlock:d}=(0,u.useSelect)((e=>e(a.store)),[]),{insertBlocks:p}=(0,u.useDispatch)(a.store);return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(s,{icon:G,label:(0,t.__)("Add Accordion Item","generateblocks-pro"),onClick:()=>{var t;const l="generateblocks-pro/accordion"===n?r:null!==(t=i(r,"generateblocks-pro/accordion",!0)?.[0])&&void 0!==t?t:"";if(!l)return;const o=function(){let e="";const t=d(r);if("generateblocks-pro/accordion"===n){const l=t.innerBlocks,o=Object.keys(l),n=o[o.length-1];void 0!==l[n]&&(e=l[n].clientId)}var l;return"generateblocks-pro/accordion-item"===n&&(e=r),("generateblocks-pro/accordion-toggle"===n||"generateblocks-pro/accordion-content"===n)&&(e=null!==(l=i(r,"generateblocks-pro/accordion-item",!0)?.[0])&&void 0!==l?l:""),e}();if(o){const t=d(o),n=(0,e.cloneBlock)(t,{uniqueId:"",openByDefault:!1});p(n,void 0,l)}else p((0,e.createBlocksFromInnerBlocksTemplate)(H),void 0,l)},showTooltip:!0}))}const Z=["generateblocks-pro/accordion","generateblocks-pro/accordion-content","generateblocks-pro/accordion-item","generateblocks-pro/accordion-toggle"];(0,n.addFilter)("generateblocks.editor.toolbarAppenders","generateblocks.accordion.addToolbarAppenders",(function(e,{clientId:t,name:l}){return Z.includes(l)?(0,o.createElement)(o.Fragment,null,e,(0,o.createElement)(z,{clientId:t,name:l})):e})),(0,e.registerBlockType)(F.UU,{edit:P,save:function({attributes:e}){const{tagName:t,htmlAttributes:l={}}=e,n=D("gb-accordion",e,!0),r=a.useBlockProps.save({className:n.join(" ").trim(),...l});return(0,o.createElement)(t,{...a.useInnerBlocksProps.save(r)})},icon:(0,o.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"gblocks-block-icon"},(0,o.createElement)("path",{style:{fill:"none"},d:"M0 0h24v24H0z"}),(0,o.createElement)("path",{d:"M21.375 9.067H2.625A.625.625 0 0 1 2 8.441V2.625C2 2.28 2.28 2 2.625 2h18.751c.344 0 .624.28.624.625v5.816c0 .346-.28.626-.625.626zM3.249 7.816H20.75V3.25H3.249v4.566zm18.126 11.032H2.625a.625.625 0 0 1 0-1.251h18.751a.625.625 0 1 1-.001 1.251zm0 3.152H2.625a.625.625 0 0 1 0-1.25h18.751a.625.625 0 1 1-.001 1.25zm0-6.305H2.625a.625.625 0 0 1 0-1.25h18.751a.625.625 0 1 1-.001 1.25zm0-3.152H2.625a.625.625 0 0 1 0-1.25h18.751a.625.625 0 1 1-.001 1.25z"}),(0,o.createElement)("path",{d:"M17.831 6.874a.517.517 0 0 1-.368-.153L15.9 5.159l.737-.737 1.194 1.194 1.194-1.194.737.737-1.563 1.563a.522.522 0 0 1-.368.152z"}))}),(0,e.registerBlockVariation)("generateblocks-pro/accordion",{name:"generateblocks/accordion",title:(0,t.__)("Accordion","generateblocks-pro"),isDefault:!0,innerBlocks:H})})()})();