(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var l in s)e.o(s,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:s[l]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.React,s=window.wp.blocks,l=window.wp.compose,r=window.wp.element,n=window.wp.blockEditor,o=window.wp.i18n,a=window.gbp.blockStyles,c=window.gbp.stylesBuilder,u=window.wp.data,i=(0,u.createReduxStore)("gbp-current-style",{reducer:c.currentStyleReducer,actions:c.currentStyleActions,selectors:c.currentStyleSelectors}),d=(0,u.createReduxStore)("gbp-styles",{reducer:c.styleReducer,actions:c.styleActions,selectors:c.styleSelectors}),y=(0,u.createReduxStore)("gbp-styles-at-rule",{reducer:c.atRuleReducer,actions:c.atRuleActions,selectors:c.atRuleSelectors}),b=(0,u.createReduxStore)("gbp-styles-nested-rule",{reducer:c.nestedRuleReducer,actions:c.nestedRuleActions,selectors:c.nestedRuleSelectors}),p=window.wp.apiFetch;var g=e.n(p);const S=window.wp.notices,f=window.wp.url,m=window.wp.coreData;var v;window.lodash;const h="undefined"!=typeof gbGlobalStylePermissions&&null!==(v=gbGlobalStylePermissions?.canManageStyles)&&void 0!==v&&v,R=window.wp.editPost;const w=(0,u.createReduxStore)("gbp-block-styles-current-style",{reducer:c.currentStyleReducer,actions:c.currentStyleActions,selectors:c.currentStyleSelectors}),C=(0,u.createReduxStore)("gbp-block-styles-at-rule",{reducer:c.atRuleReducer,actions:c.atRuleActions,selectors:c.atRuleSelectors}),A=(0,u.createReduxStore)("gbp-block-styles-nested-rule",{reducer:c.nestedRuleReducer,actions:c.nestedRuleActions,selectors:c.nestedRuleSelectors});function k(){const e=(0,u.useSelect)((e=>e(C).getAtRule())),{setAtRule:t}=(0,u.useDispatch)(C),s=(0,u.useSelect)((e=>e(A).getNestedRule())),{setNestedRule:l}=(0,u.useDispatch)(A),r=(0,a.useCurrentAtRule)(c.defaultAtRules),{setCurrentStyle:p}=(0,u.useDispatch)(w),v=(0,u.useSelect)((e=>e(w).currentStyle())),{deviceType:k,setDeviceType:E}=(0,a.useDeviceType)(),L=function(){const{setCurrentStyle:e}=(0,u.useDispatch)(i),{setStyles:t}=(0,u.useDispatch)(d),{createNotice:s,removeAllNotices:l}=(0,u.useDispatch)(S.store),{getEntityRecordEdits:r}=(0,u.useSelect)(m.store),{getSelectedBlock:a}=(0,u.useSelect)((e=>e(n.store)),[]),{setAtRule:c}=(0,u.useDispatch)(y),{setNestedRule:p}=(0,u.useDispatch)(b),{openGeneralSidebar:v}=(0,u.useDispatch)(R.store);return async(n,u={})=>{if(!h)return;const{classStyles:i,classPostId:d}=await async function(e){var t;const s=await g()({path:(0,f.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let l=null!==(t=s?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(l)&&0===l.length&&(l={}),{classStyles:l,classPostId:s?.response?.data?.postId}}(n);if(!d)return l("snackbar"),void s("error",(0,o.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,o.__)("%s does not exist.","generateblocks-pro"),n),{type:"snackbar"});c(""),p(""),v("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:d,name:n,classStyles:i,clientId:a()?.clientId,options:u}),u.nestedRule&&p(u.nestedRule),u.atRule&&c(u.atRule);const y=r("postType","gblocks_styles",d);t(y?.gb_style_data||i),l("snackbar"),s("info",(0,o.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,o.__)("Editing %s.","generateblocks-pro"),n),{type:"snackbar"})}}(),N=function(){const{setCurrentStyle:e}=(0,u.useDispatch)(i),{setStyles:t}=(0,u.useDispatch)(d),{setAtRule:s}=(0,u.useDispatch)(y),{setNestedRule:l}=(0,u.useDispatch)(b);return()=>{e({}),t({}),s(""),l("")}}();return{atRule:e,nestedRule:s,setAtRule:t,currentAtRule:r,setNestedRule:l,setDeviceType:E,deviceType:k,setCurrentStyle:p,currentStyle:v,getPreviewDevice:a.getPreviewDevice,setGlobalStyle:L,cancelEditGlobalStyle:N}}function E({attributes:e,setAttributes:s,shortcuts:l,onStyleChange:r}){const{atRule:n,setAtRule:o,nestedRule:u,setNestedRule:i,setDeviceType:d,getPreviewDevice:y,currentStyle:b,setGlobalStyle:p,cancelEditGlobalStyle:g}=k(),{styles:S,globalClasses:f=[]}=e,m=(0,c.getStylesObject)(S,n,u);return(0,t.createElement)(c.StylesBuilder,{currentSelector:b?.selector,styles:m,allStyles:S,onDeleteStyle:(e,t)=>{const l=(0,c.deleteStylesObjectKey)(S,e,t);s({styles:l})},nestedRule:u,atRule:n,onStyleChange:(e,t=null)=>r(e,t,n,u),onNestedRuleChange:e=>i(e),onAtRuleChange:e=>{o(e);const t=(0,c.getPreviewWidth)(e),s=y(t);s&&d(s)},onUpdateKey:(e,t,l)=>{const r=(0,c.updateStylesObjectKey)(S,e,t,l);s({styles:r})},selectorShortcuts:l.selectorShortcuts,visibleSelectors:l.visibleShortcuts,onEditStyle:p,cancelEditStyle:g,setLocalTab:e=>{sessionStorage.setItem(a.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:f})}const L=window.wp.components,N=window.wp.hooks,_=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function I(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function D(e,t,s=!1){const{styles:l={},uniqueId:r="",globalClasses:n=[]}=t,o=[];return s&&o.push(e),n.length>0&&o.push(...n),Object.keys(l).length>0&&o.push(`${e}-${r}`),o}const O=window.gbp.components,T={default:{items:[{label:"Hover",value:"&:hover"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"},{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"}]},interactions:{label:(0,o.__)("Interactions","generateblocks-pro"),items:[{label:"Hover",value:"&:hover"},{label:"Focus",value:"&:focus"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"}]},links:{label:(0,o.__)("Links","generateblocks-pro"),items:[{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"},{label:"Hovered & focused links",value:"a:is(:hover, :focus)"},{label:"Visited links",value:"a:visited"}]},pseudoElements:{label:(0,o.__)("Pseudo Elements","generateblocks-pro"),items:[{label:"Before",value:"&:before"},{label:"After",value:"&:after"}]}};function P({value:e,options:l=[],onChange:r,blockName:n}){var a;const c=null!==(a=(0,s.getBlockType)(n)?.attributes?.tagName?.enum)&&void 0!==a?a:[],u=l.length?l:c.map((e=>({label:e,value:e})));return u.length?(0,t.createElement)(L.SelectControl,{label:(0,o.__)("Tag Name","generateblocks-pro"),value:e,options:u,onChange:r}):null}const j=(0,l.compose)((function(e){return s=>{var l,o,a,c;const{attributes:i,setAttributes:d,context:y}=s,{htmlAttributes:b={},uniqueId:p,className:g,align:S}=i,f=(0,u.useSelect)((e=>e("core/editor").isSavingPost())),{style:m="",href:v,...h}=b,R=Object.keys(h).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(h[t]),e)),{}),[w,C]=(0,r.useState)(m);var A,k,E,D;(0,r.useEffect)((()=>{!async function(){const e=await(0,N.applyFilters)("generateblocks.editor.htmlAttributes.style",m,{...s});C(e)}()}),[m,y,f]),A=()=>{const e=["alignwide","alignfull"],t=(g?.split(" ")||[]).filter((t=>!e.includes(t)));S&&t.push("align"+S),d({className:t.join(" ")})},k=[S],D=(E=(0,t.useRef)(!0)).current?(E.current=!1,!0):E.current,(0,t.useEffect)((function(){if(!D)return A()}),k);const O="string"==typeof w?w.split(";").reduce(((e,t)=>{const s=t.indexOf(":");if(-1===s)return e;let l=t.slice(0,s).trim();const r=t.slice(s+1).trim();return l&&r&&(l.startsWith("--")||(l=l.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[l]=r),e}),{}):"",T={...R,style:O,"data-gb-id":p,"data-context-post-id":null!==(l=null!==(o=y?.postId)&&void 0!==o?o:y?.["generateblocks/loopIndex"])&&void 0!==l?l:0,"data-align":S||void 0},P=(0,r.useMemo)((()=>Array.isArray(b)?{}:b),[JSON.stringify(b)]);return(0,r.useEffect)((()=>{const e={...b};Object.keys(e).forEach((t=>{const s=t.startsWith("data-"),l=e[t];_.includes(t)||""!==l||s||"alt"===t||delete e[t],"string"!=typeof l&&"boolean"!=typeof l&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const s=Object.keys(e),l=Object.keys(t);if(s.length!==l.length)return!1;for(const l of s)if(e[l]!==t[l])return!1;return!0}(e,b)||d({htmlAttributes:e})}),[JSON.stringify(b)]),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(e,{...s,editorHtmlAttributes:T,htmlAttributes:P}),(0,t.createElement)(n.InspectorAdvancedControls,null,(0,t.createElement)(L.TextControl,{label:"HTML ID",value:null!==(a=b.id)&&void 0!==a?a:"",onChange:e=>{d({htmlAttributes:{...b,id:e}})},onBlur:()=>{b.id&&d({htmlAttributes:{...b,id:I(b.id)}})}}),(0,t.createElement)(L.TextControl,{label:"ARIA Label",value:null!==(c=b["aria-label"])&&void 0!==c?c:"",onChange:e=>{d({htmlAttributes:{...b,"aria-label":e}})}})))}}),(function(e){return s=>{const{attributes:l,name:n,setAttributes:o,isSelected:u,clientId:i}=s,{uniqueId:d,styles:y,css:b}=l,{atRule:p,deviceType:g,setAtRule:S,currentStyle:f,setCurrentStyle:m,setNestedRule:v}=k(),h=(0,a.useSetStyles)(s,{cleanStylesObject:c.cleanStylesObject}),R=(0,r.useMemo)((()=>d?(0,a.getSelector)(n,d):""),[n,d]),w=Array.isArray(y)?{}:y;return(0,a.useAtRuleEffect)({deviceType:g,atRule:p,setAtRule:S,defaultAtRules:c.defaultAtRules,isSelected:u,getPreviewWidth:c.getPreviewWidth}),(0,a.useGenerateCSSEffect)({selector:R,styles:w,setAttributes:o,getCss:c.getCss,getSelector:a.getSelector,isSelected:u,blockCss:b,clientId:i}),(0,a.useStyleSelectorEffect)({isSelected:u,currentStyle:f,selector:R,setCurrentStyle:m,setNestedRule:v}),(0,a.useDecodeStyleKeys)({styles:y,setAttributes:o}),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(a.Style,{selector:R,getCss:c.getCss,styles:w,clientId:i,name:n}),(0,t.createElement)(e,{...s,selector:R,onStyleChange:function(e,t="",s="",l=""){const r="object"==typeof e?e:{[e]:t},n=(0,a.buildChangedStylesObject)(r,s,l);h(n)},getStyleValue:function(e,t="",s=""){var l,r,n,o;return s?t?null!==(n=y?.[s]?.[t]?.[e])&&void 0!==n?n:"":null!==(r=y?.[s]?.[e])&&void 0!==r?r:"":t?null!==(o=y?.[t]?.[e])&&void 0!==o?o:"":null!==(l=y?.[e])&&void 0!==l?l:""},styles:w}))}}),a.withUniqueId)((function(e){const{attributes:s,setAttributes:l,onStyleChange:c,getStyleValue:u,editorHtmlAttributes:i={},styles:d,name:y,clientId:b}=e,{tagName:p}=s,g=D("gb-tabs__items",{...s,styles:d},!0),S=(0,r.useRef)(),f=(0,n.useBlockProps)({className:g.filter(Boolean).join(" ").trim(),...i,ref:S}),m=(0,n.useInnerBlocksProps)(f,{allowedBlocks:["generateblocks-pro/tab-item"],renderAppender:!1}),v=p||"div",h=(0,r.useMemo)((()=>{const e=[{label:(0,o.__)("Links","generateblocks-pro"),value:"a"}];return{selectorShortcuts:T,visibleShortcuts:e}}),[]);(0,r.useEffect)((()=>{p||l({tagName:"div"})}),[p]);const R={name:y,attributes:s,setAttributes:l,clientId:b,getStyleValue:u,onStyleChange:c};return(0,t.createElement)(t.Fragment,null,(0,t.createElement)(n.InspectorControls,null,(0,t.createElement)(a.BlockStyles,{settingsTab:(0,t.createElement)(O.OpenPanel,{...R,panelId:"settings"},(0,t.createElement)(P,{blockName:y,value:p,onChange:e=>{l({tagName:e})}})),stylesTab:(0,t.createElement)(E,{attributes:s,setAttributes:l,shortcuts:h,onStyleChange:c})})),(0,t.createElement)(v,{...m}))})),x=JSON.parse('{"UU":"generateblocks-pro/tab-items"}');(0,s.registerBlockType)(x.UU,{edit:j,save:function({attributes:e}){const{tagName:s,htmlAttributes:l={}}=e,r=D("gb-tabs__items",e,!0),o=n.useBlockProps.save({className:r.join(" ").trim(),...l});return(0,t.createElement)(s,{...n.useInnerBlocksProps.save(o)})},icon:function(){return(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",className:"gblocks-block-icon","aria-hidden":"true",focusable:"false"},(0,t.createElement)("path",{d:"M18.23,13.26L5.77,13.26C5.43,13.26 5.15,13.54 5.15,13.88C5.15,14.22 5.43,14.5 5.77,14.5L18.23,14.5C18.57,14.5 18.85,14.22 18.85,13.88C18.85,13.54 18.58,13.26 18.23,13.26ZM18.23,16.29L5.77,16.29C5.43,16.29 5.15,16.57 5.15,16.91C5.15,17.25 5.43,17.53 5.77,17.53L18.23,17.53C18.57,17.53 18.85,17.25 18.85,16.91L18.85,16.9C18.85,16.565 18.575,16.29 18.24,16.29L18.23,16.29ZM5.14,10.85C5.14,11.19 5.42,11.47 5.76,11.47L18.22,11.47C18.56,11.47 18.84,11.19 18.84,10.85C18.84,10.51 18.56,10.23 18.22,10.23L5.77,10.23L5.753,10.23C5.417,10.23 5.14,10.506 5.14,10.843L5.14,10.85Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M8.849,7.001L8.87,7.01L8.849,7.001ZM8.839,6.997C8.487,6.965 8.25,6.703 8.25,6.39L8.25,3.25L9.5,3.25L9.5,5.76L14.5,5.76L14.505,3.25L15.75,3.25L15.745,5.76L20.75,5.76L20.75,6.997L8.839,6.997Z",style:{fillOpacity:.3}}),(0,t.createElement)("path",{d:"M18.23,13.26L5.77,13.26C5.43,13.26 5.15,13.54 5.15,13.88C5.15,14.22 5.43,14.5 5.77,14.5L18.23,14.5C18.57,14.5 18.85,14.22 18.85,13.88C18.85,13.54 18.58,13.26 18.23,13.26ZM18.23,16.29L5.77,16.29C5.43,16.29 5.15,16.57 5.15,16.91C5.15,17.25 5.43,17.53 5.77,17.53L18.23,17.53C18.57,17.53 18.85,17.25 18.85,16.91C18.85,16.907 18.85,16.903 18.85,16.9C18.85,16.565 18.575,16.29 18.24,16.29C18.237,16.29 18.233,16.29 18.23,16.29ZM5.14,10.85C5.14,11.19 5.42,11.47 5.76,11.47L18.22,11.47C18.56,11.47 18.84,11.19 18.84,10.85C18.84,10.51 18.56,10.23 18.22,10.23L5.77,10.23C5.764,10.23 5.759,10.23 5.753,10.23C5.417,10.23 5.14,10.506 5.14,10.843C5.14,10.845 5.14,10.848 5.14,10.85Z",style:{fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M21.38,2L21.382,2C21.716,2 21.99,2.274 21.99,2.608C21.99,2.612 21.99,2.616 21.99,2.62L21.99,21.38C21.99,21.72 21.71,22 21.37,22L2.62,22C2.28,22 2,21.72 2,21.37L2,2.62C2,2.28 2.28,2 2.62,2L21.38,2ZM20.75,3.25L3.25,3.25L3.25,20.75L20.75,20.75L20.75,3.25Z",style:{fillRule:"nonzero"}}))}})})();