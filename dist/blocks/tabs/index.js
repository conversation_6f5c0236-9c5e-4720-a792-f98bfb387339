(()=>{var e={46942:(e,t)=>{var l;!function(){"use strict";var n={}.hasOwnProperty;function a(){for(var e="",t=0;t<arguments.length;t++){var l=arguments[t];l&&(e=s(e,r(l)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return a.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var l in e)n.call(e,l)&&e[l]&&(t=s(t,l));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(a.default=a,e.exports=a):void 0===(l=function(){return a}.apply(t,[]))||(e.exports=l)}()}},t={};function l(n){var a=t[n];if(void 0!==a)return a.exports;var r=t[n]={exports:{}};return e[n](r,r.exports,l),r.exports}l.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return l.d(t,{a:t}),t},l.d=(e,t)=>{for(var n in t)l.o(t,n)&&!l.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.wp.blocks,t=window.React,n=window.wp.compose,a=window.wp.data,r=window.wp.element,s=window.wp.blockEditor,o=window.wp.components,c=window.wp.i18n,i=window.gbp.blockStyles;var u=l(46942),d=l.n(u);const b=window.wp.hooks;function p({name:e,clientId:t,align:l,children:n}){const{getBlockRootClientId:o}=(0,a.useSelect)((e=>e("core/block-editor")),[]),c=(0,a.useSelect)((e=>{const{getSettings:t}=e(s.store);return t().supportsLayout||!1}),[]),i=e.toString().replace("/","-"),u={className:d()({"wp-block":!0,"gb-is-root-block":!0,[`gb-root-block-${i}`]:!0,[`align${l}`]:c}),"data-align":l&&!c?l:null,"data-block":t},p=o(t);return(0,b.applyFilters)("generateblocks.rootElement.disable",p,{name:e})?n:(0,r.createElement)("div",u,n)}function m({clientId:l,setAttributes:n,label:r,instructions:i,templates:u}){const{replaceInnerBlocks:d,removeBlock:b,selectBlock:p}=(0,a.useDispatch)(s.store);return(0,t.createElement)("div",{className:"wp-block"},(0,t.createElement)(o.Placeholder,{label:r,instructions:i,className:"gbp-select-variation"},(0,t.createElement)("div",{className:"gbp-variation-selector"},u&&u.map((a=>(0,t.createElement)(o.Button,{key:`template-${a.id}`,className:"gbp-variation-selector__button",onClick:()=>{d(l,(0,e.createBlocksFromInnerBlocksTemplate)(a.innerBlocks)),a.attributes&&n(a.attributes),"function"==typeof a.onClick&&a.onClick(),n({showTemplateSelector:!1}),p(l)}},a.icon,(0,t.createElement)("span",null,a.label))))),(0,t.createElement)("div",{className:"gbp-select-variation__actions"},(0,t.createElement)(o.Button,{className:"is-small",onClick:()=>b(l),variant:"secondary"},(0,c.__)("Cancel","generateblocks")))))}const g=wp.element.createElement;function h(e){return"effects"===e?g("svg",{width:20,height:20,viewBox:"0 0 113 113",fillRule:"evenodd"},g("path",{d:"M106.283 6.217c8.289 8.29 8.289 91.776 0 100.066-8.29 8.289-91.776 8.289-100.066 0-8.289-8.29-8.289-91.776 0-100.066 8.29-8.289 91.776-8.289 100.066 0zM96.276 16.224c6.632 6.632 6.632 73.42 0 80.052-6.632 6.632-73.42 6.632-80.052 0-6.632-6.632-6.632-73.42 0-80.052 6.632-6.632 73.42-6.632 80.052 0z"}),g("path",{d:"M48.418 58.413H33.304c-.71 0-1.019-.577-.687-1.288l16.637-35.68c.332-.71 1.178-1.287 1.889-1.287h22.962c.711 0 1.02.577.687 1.288l-10.934 23.45h15.74c.796 0 1.14.647.77 1.443L37.959 91.72c-2.42 2.448-5.343.578-3.83-2.666l14.289-30.642z"})):"colors"===e?g("svg",{width:20,height:20,viewBox:"0 0 113 113",fillRule:"evenodd"},g("path",{d:"M106.283,6.217c8.289,8.29 8.289,91.776 0,100.066c-8.29,8.289 -91.776,8.289 -100.066,0c-8.289,-8.29 -8.289,-91.776 0,-100.066c8.29,-8.289 91.776,-8.289 100.066,0Zm-50.033,12.818c-20.551,0 -37.215,16.664 -37.215,37.215c0,20.551 16.664,37.215 37.215,37.215c3.432,0 6.202,-2.77 6.202,-6.203c0,-1.612 -0.62,-3.059 -1.612,-4.176c-0.951,-1.075 -1.571,-2.522 -1.571,-4.094c0,-3.432 2.77,-6.202 6.202,-6.202l7.319,0c11.413,0 20.675,-9.262 20.675,-20.675c0,-18.277 -16.664,-33.08 -37.215,-33.08Zm-22.742,37.215c-3.433,0 -6.203,-2.77 -6.203,-6.202c0,-3.433 2.77,-6.203 6.203,-6.203c3.432,0 6.202,2.77 6.202,6.203c0,3.432 -2.77,6.202 -6.202,6.202Zm45.484,0c-3.432,0 -6.202,-2.77 -6.202,-6.202c0,-3.433 2.77,-6.203 6.202,-6.203c3.433,0 6.203,2.77 6.203,6.203c0,3.432 -2.77,6.202 -6.203,6.202Zm-33.079,-16.54c-3.433,0 -6.203,-2.77 -6.203,-6.202c0,-3.433 2.77,-6.203 6.203,-6.203c3.432,0 6.202,2.77 6.202,6.203c0,3.432 -2.77,6.202 -6.202,6.202Zm20.674,0c-3.432,0 -6.202,-2.77 -6.202,-6.202c0,-3.433 2.77,-6.203 6.202,-6.203c3.433,0 6.203,2.77 6.203,6.203c0,3.432 -2.77,6.202 -6.203,6.202Z"})):"gradient"===e?g("svg",{width:24,height:24,viewBox:"0 0 24 24",fillRule:"evenodd"},g("path",{d:"M17.66 8L12 2.35L6.34 8A8.02 8.02 0 0 0 4 13.64c0 2 .78 4.11 2.34 5.67a7.99 7.99 0 0 0 11.32 0c1.56-1.56 2.34-3.67 2.34-5.67S19.22 9.56 17.66 8zM6 14c.01-2 .62-3.27 1.76-4.4L12 5.27l4.24 4.38C17.38 10.77 17.99 12 18 14H6z"})):"copy"===e?(0,t.createElement)("svg",{strokeWidth:"1.2",fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,t.createElement)("path",{d:"M19.4 20H9.6a.6.6 0 0 1-.6-.6V9.6a.6.6 0 0 1 .6-.6h9.8a.6.6 0 0 1 .6.6v9.8a.6.6 0 0 1-.6.6Z",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),(0,t.createElement)("path",{d:"M15 9V4.6a.6.6 0 0 0-.6-.6H4.6a.6.6 0 0 0-.6.6v9.8a.6.6 0 0 0 .6.6H9",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"})):"link"===e?(0,t.createElement)("svg",{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",role:"img","aria-hidden":"true",focusable:"false"},(0,t.createElement)("path",{d:"M15.6 7.2H14v1.5h1.6c2 0 3.7 1.7 3.7 3.7s-1.7 3.7-3.7 3.7H14v1.5h1.6c2.8 0 5.2-2.3 5.2-5.2 0-2.9-2.3-5.2-5.2-5.2zM4.7 12.4c0-2 1.7-3.7 3.7-3.7H10V7.2H8.4c-2.9 0-5.2 2.3-5.2 5.2 0 2.9 2.3 5.2 5.2 5.2H10v-1.5H8.4c-2 0-3.7-1.7-3.7-3.7zm4.6.9h5.3v-1.5H9.3v1.5z"})):"globe"===e?(0,t.createElement)("svg",{width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024",role:"img","aria-hidden":"true",focusable:"false"},(0,t.createElement)("path",{d:"M512 1024q-104 0-199-40.5t-163.5-109T40.5 711 0 512t40.5-199 109-163.5T313 40.5 512 0t199 40.5 163.5 109 109 163.5 40.5 199-40.5 199-109 163.5-163.5 109-199 40.5zM293 293l-48-42q-11 0-26 2.5t-27 2.5q-1-1-18-37Q64 346 64 512q0 3 .5 8t.5 7q6 6 29.5 22.5T128 576h64q3-2 5.5-3t5.5-2q-10-11-29.5-32.5T144 507q4-23 11-69.5t10-69.5q86-36 128-53v-22zm201-163q-14-6-26-11-3-8-4-12-6 19-19 57 4 1 11.5 2t11.5 2h26v-38zm-4 471q-5 5-7 8-12 21-34 64t-33 64q14 21 42.5 64t42.5 64q130 8 197 12 2 25 16 34 91-46 154-127.5T951 601q-19-4-41.5-11t-32.5-9.5-39.5-5T776 579q-12 1-15.5-15.5t-3.5-34-4-18.5q-22-4-89 7.5t-89 7.5q-13 12-85 75zm59-501q-3 20-10.5 60T527 221q5-1 16.5-2.5T560 217q-3-2-7-4 15-5 22-8-17-70-26-105zm116-9q-2 11-2 31t-10 53q1 2 4 4 20-2 67-7 0-21 21-42-38-23-80-39zm125 70q-2 4-7 11 19 3 25 5-12-11-18-16zm27 24q-3 6-9.5 18t-9.5 18q-29 1-78 3l-4-34q-2 1-7 2.5t-8 1.5v49q-21 2-64.5 6t-64.5 6q-7 10-15 22 27 58 41 87-20 5-82 22v34q0 2 1.5 6t2.5 6q17 8 53 24t54 25l22-27q-1-10-5-31.5t-6-32.5q3-2 9.5-5.5t9.5-5.5q27-8 41-11 13 21 36.5 60t29.5 49q9-8 25-24.5t24-24.5q-54-38-71-49 1-8 4-23h37q56 48 115 98 1 0 2-1.5t2-1.5q-4-8-26-49 0-1 3-4l4-4h41q1-1 17-9-34-116-124-200z"})):"info"===e?(0,t.createElement)("svg",{width:"1em",height:"1em",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024","aria-hidden":"true"},(0,t.createElement)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}),(0,t.createElement)("path",{d:"M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"})):"wrench"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},(0,t.createElement)("rect",{x:"0",fill:"none",width:"20",height:"20"}),(0,t.createElement)("g",null,(0,t.createElement)("path",{d:"M16.68 9.77c-1.34 1.34-3.3 1.67-4.95.99l-5.41 6.52c-.99.99-2.59.99-3.58 0s-.99-2.59 0-3.57l6.52-5.42c-.68-1.65-.35-3.61.99-4.95 1.28-1.28 3.12-1.62 4.72-1.06l-2.89 2.89 2.82 2.82 2.86-2.87c.53 1.58.18 3.39-1.08 4.65zM3.81 16.21c.4.39 1.04.39 1.43 0 .4-.4.4-1.04 0-1.43-.39-.4-1.03-.4-1.43 0-.39.39-.39 1.03 0 1.43z"}))):"x"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},(0,t.createElement)("rect",{x:"0",fill:"none",width:"20",height:"20"}),(0,t.createElement)("g",null,(0,t.createElement)("path",{d:"M14.95 6.46L11.41 10l3.54 3.54-1.41 1.41L10 11.42l-3.53 3.53-1.42-1.42L8.58 10 5.05 6.47l1.42-1.42L10 8.58l3.54-3.53z"}))):"trash"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",style:{fill:"none"},stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,t.createElement)("path",{d:"M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2M10 11v6M14 11v6"})):"question"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",style:{fill:"none"},stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,t.createElement)("circle",{cx:"12",cy:"12",r:"10"}),(0,t.createElement)("path",{d:"M9.09 9a3 3 0 015.83 1c0 2-3 3-3 3M12 17h.01"})):"lock"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",style:{fill:"none"},stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,t.createElement)("rect",{x:"3",y:"11",width:"18",height:"11",rx:"2",ry:"2"}),(0,t.createElement)("path",{d:"M7 11V7a5 5 0 0110 0v4"})):"template-library"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"gblocks-block-icon"},(0,t.createElement)("path",{style:{fill:"none"},d:"M0 0h24v24H0z"}),(0,t.createElement)("path",{d:"M21.375 12.625H2.625A.625.625 0 0 1 2 12V2.625C2 2.28 2.28 2 2.625 2h18.75c.345 0 .625.28.625.625V12c0 .345-.28.625-.625.625zM3.25 11.375h17.5V3.25H3.25v8.125zM10.283 22H2.625A.625.625 0 0 1 2 21.375V15.31c0-.345.28-.625.625-.625h7.658c.345 0 .625.28.625.625v6.065c0 .345-.28.625-.625.625zM3.25 20.75h6.407v-4.815H3.25v4.815zm18.125-1.783h-7.892a.625.625 0 0 1 0-1.25h7.892a.625.625 0 0 1 0 1.25zm0 3.033h-7.892a.625.625 0 0 1 0-1.25h7.892a.625.625 0 0 1 0 1.25zm0-6.065h-7.892a.625.625 0 0 1 0-1.25h7.892a.625.625 0 0 1 0 1.25z"}),(0,t.createElement)("path",{d:"M12 10.106a.625.625 0 0 1-.625-.625V5.144a.625.625 0 0 1 1.25 0v4.337c0 .346-.28.625-.625.625z"}),(0,t.createElement)("path",{d:"M14.169 7.938H9.831a.625.625 0 0 1 0-1.25h4.337a.625.625 0 0 1 .001 1.25z"})):"accordion"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"gblocks-block-icon"},(0,t.createElement)("path",{style:{fill:"none"},d:"M0 0h24v24H0z"}),(0,t.createElement)("path",{d:"M21.375 9.067H2.625A.625.625 0 0 1 2 8.441V2.625C2 2.28 2.28 2 2.625 2h18.751c.344 0 .624.28.624.625v5.816c0 .346-.28.626-.625.626zM3.249 7.816H20.75V3.25H3.249v4.566zm18.126 11.032H2.625a.625.625 0 0 1 0-1.251h18.751a.625.625 0 1 1-.001 1.251zm0 3.152H2.625a.625.625 0 0 1 0-1.25h18.751a.625.625 0 1 1-.001 1.25zm0-6.305H2.625a.625.625 0 0 1 0-1.25h18.751a.625.625 0 1 1-.001 1.25zm0-3.152H2.625a.625.625 0 0 1 0-1.25h18.751a.625.625 0 1 1-.001 1.25z"}),(0,t.createElement)("path",{d:"M17.831 6.874a.517.517 0 0 1-.368-.153L15.9 5.159l.737-.737 1.194 1.194 1.194-1.194.737.737-1.563 1.563a.522.522 0 0 1-.368.152z"})):"tabs"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"gblocks-block-icon"},(0,t.createElement)("path",{style:{fill:"none"},d:"M0 0h24v24H0V0z"}),(0,t.createElement)("path",{d:"M21.38 2H2.62c-.34 0-.62.28-.62.62v18.75c0 .35.28.63.62.63h18.75c.34 0 .62-.28.62-.62V2.62a.608.608 0 0 0-.61-.62zM9.5 3.25h5v2.51h-5V3.25zm11.25 17.5H3.25V3.25h5v3.14c0 .34.28.62.62.62h11.88v13.74zm0-14.99h-5V3.25h5v2.51z"}),(0,t.createElement)("path",{d:"M18.23 13.26H5.77c-.34 0-.62.28-.62.62s.28.62.62.62h12.46c.34 0 .62-.28.62-.62s-.27-.62-.62-.62zm0 3.03H5.77c-.34 0-.62.28-.62.62 0 .34.28.62.62.62h12.46c.34 0 .62-.28.62-.62a.61.61 0 0 0-.62-.62zM5.14 10.85c0 .34.28.62.62.62h12.46c.34 0 .62-.28.62-.62s-.28-.62-.62-.62H5.77a.613.613 0 0 0-.63.62z"})):"horizontal-tabs"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"50",height:"50",viewBox:"0 0 100 100"},(0,t.createElement)("path",{d:"M83.91 48.67H16.09a1.92 1.92 0 0 0 0 3.84h67.82a1.92 1.92 0 0 0 0-3.84zm0 15.17H16.09a1.92 1.92 0 0 0 0 3.84h67.82a1.92 1.92 0 0 0 0-3.84zm0 15.17H16.09a1.92 1.92 0 0 0 0 3.84h67.82a1.92 1.92 0 0 0 0-3.84zm0-45.51H16.09a1.92 1.92 0 0 0 0 3.84h67.82a1.92 1.92 0 0 0 0-3.84z"}),(0,t.createElement)("path",{d:"M98.08 0H1.92C.86 0 0 .86 0 1.92v96.15C0 99.14.86 100 1.92 100h96.15c1.06 0 1.92-.86 1.92-1.92V1.92C100 .86 99.14 0 98.08 0zm-1.93 15.59-28.2.09V3.85h28.2v11.74zM35.9 3.85h28.21v11.84l-28.21.08V3.85zM3.85 96.15V3.85h28.21V17.7c0 .51.2 1 .57 1.36.36.36.85.56 1.36.56H34l62.17-.19v76.72H3.85z"})):"vertical-tabs"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"50",height:"50",viewBox:"0 0 100 100"},(0,t.createElement)("path",{d:"M33.41 52.51h50.91a1.92 1.92 0 0 0 0-3.84H33.41a1.92 1.92 0 0 0 0 3.84zm0 15.17h50.91a1.92 1.92 0 0 0 0-3.84H33.41a1.92 1.92 0 0 0 0 3.84zm0 15.18h50.91a1.92 1.92 0 0 0 0-3.84H33.41a1.92 1.92 0 0 0 0 3.84zm0-45.52h50.91a1.92 1.92 0 0 0 0-3.84H33.41a1.92 1.92 0 0 0 0 3.84zm0-15.17h50.91a1.92 1.92 0 0 0 0-3.84H33.41a1.92 1.92 0 0 0 0 3.84z"}),(0,t.createElement)("path",{d:"M98.08 0H1.92C.86 0 0 .86 0 1.92v96.15C0 99.14.86 100 1.92 100h96.15c1.06 0 1.92-.86 1.92-1.92V1.92C100 .86 99.14 0 98.08 0zM3.85 52.12h12.52l-.05 19.97H3.85V52.12zm12.53-3.84H3.85V27.99h12.58l-.05 20.29zM3.85 75.94H16.3l-.05 20.21H3.85V75.94zm92.3 20.21H20.09l.19-70.08a1.932 1.932 0 0 0-1.92-1.93H3.85V3.85h92.31v92.3z"})):"button-tabs"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"50",height:"50",viewBox:"0 0 100 100"},(0,t.createElement)("path",{d:"M16.09 55.55h67.82a1.92 1.92 0 0 0 0-3.84H16.09a1.92 1.92 0 0 0 0 3.84zm0 14.22h67.82a1.92 1.92 0 0 0 0-3.84H16.09a1.92 1.92 0 0 0 0 3.84zm0 14.23h67.82a1.92 1.92 0 0 0 0-3.84H16.09a1.92 1.92 0 0 0 0 3.84zm0-42.67h67.82a1.92 1.92 0 0 0 0-3.84H16.09a1.92 1.92 0 0 0 0 3.84z"}),(0,t.createElement)("path",{d:"M98.08 21.47H1.92C.86 21.47 0 22.33 0 23.4v74.68C0 99.14.86 100 1.92 100h96.15c1.06 0 1.92-.86 1.92-1.92V23.4c.01-1.07-.85-1.93-1.91-1.93zm-1.93 74.68H3.85V25.32h92.31v70.83zM1.92 14.9l25 .05c.48 0 .95-.19 1.29-.53.34-.34.54-.81.54-1.29V1.83C28.75.82 27.93 0 26.92 0h-25C.91 0 .1.82.1 1.83v11.24c0 1.01.81 1.82 1.82 1.83zM3.75 3.66H25.1v7.63l-21.35-.05V3.66zM37.5 14.9l25 .05c.48 0 .95-.19 1.29-.53.34-.34.54-.81.54-1.29V1.83C64.33.82 63.51 0 62.5 0h-25c-1.01 0-1.83.82-1.83 1.83v11.24c0 1.01.82 1.82 1.83 1.83zm1.83-11.24h21.35v7.63l-21.35-.05V3.66zM73.07 14.9l25 .05c.48 0 .95-.19 1.29-.53.34-.34.54-.81.54-1.29V1.83C99.9.82 99.09 0 98.08 0h-25c-1.01 0-1.83.82-1.83 1.83v11.24c0 1.01.82 1.82 1.82 1.83zM74.9 3.66h21.35v7.63l-21.35-.05V3.66z"})):"vertical-dots"===e?(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256"},(0,t.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,t.createElement)("circle",{cx:"128",cy:"60",r:"16"}),(0,t.createElement)("circle",{cx:"128",cy:"128",r:"16"}),(0,t.createElement)("circle",{cx:"128",cy:"196",r:"16"})):"generateblocks"===e?(0,t.createElement)("svg",{viewBox:"0 0 50 60.12",xmlns:"http://www.w3.org/2000/svg"},(0,t.createElement)("path",{d:"M6.686 31.622V18.918a.077.077 0 0 1 .05-.072l6.5-2.313 6.5-2.313 9.682-3.445L39.1 7.33a.067.067 0 0 0 .036-.028.074.074 0 0 0 .014-.044V.076a.077.077 0 0 0-.032-.062.076.076 0 0 0-.069-.009l-13 4.625-13 4.625-6.5 2.313-6.5 2.313a.067.067 0 0 0-.036.028.097.097 0 0 0-.013.046V52.067c0 .026.013.048.032.062s.044.018.069.009l3.267-1.163 3.267-1.163c.015-.005.028-.015.036-.028s.014-.028.014-.044V37.999l.001-6.377c-.001 0 0 0 0 0z"}),(0,t.createElement)("path",{d:"m23.949 29.976 13-4.625 13-4.625c.015-.005.028-.015.036-.028s.015-.028.015-.044V8.056a.077.077 0 0 0-.032-.062.076.076 0 0 0-.069-.009l-13 4.625-13 4.625-6.5 2.313-6.5 2.313a.067.067 0 0 0-.036.028.074.074 0 0 0-.014.044V60.045c0 .026.013.048.032.062a.076.076 0 0 0 .069.009l6.475-2.304 6.475-2.304 6.525-2.322 6.525-2.322 6.5-2.313 6.5-2.313c.015-.005.028-.015.036-.028s.014-.025.014-.041V27.193a.077.077 0 0 0-.032-.062.076.076 0 0 0-.069-.009l-6.45 2.295L37 31.711a.067.067 0 0 0-.036.028.074.074 0 0 0-.014.044v6.272a.077.077 0 0 1-.05.072l-6.45 2.295L24 42.715a.075.075 0 0 1-.101-.071V30.046c0-.016.005-.031.014-.044a.08.08 0 0 1 .036-.026z"})):void 0}const v={styles:{paddingTop:"1em",paddingRight:"1em",paddingBottom:"1em",paddingLeft:"1em",backgroundColor:"#ffffff",color:"#000000","&:is(:hover, :focus)":{backgroundColor:"#fafafa",color:"#000000"},"&:is(.gb-block-is-current, .gb-block-is-current:hover, .gb-block-is-current:focus)":{backgroundColor:"#fafafa",color:"#000000"},"@media (max-width:767px)":{flexGrow:1,flexShrink:0}},htmlAttributes:{role:"tab"}},f={styles:{paddingTop:"1em",paddingRight:"1em",paddingBottom:"1em",paddingLeft:"1em"},htmlAttributes:{role:"tabpanel"}},k=["generateblocks-pro/tab-menu-item",{tabItemOpen:!0,...v},[["generateblocks/text",{content:(0,c.__)("Tab 1","generateblocks-pro"),tagName:"span"}]]],w=["generateblocks-pro/tab-item",{tabItemOpen:!0,...f},[["core/paragraph",{content:(0,c.__)("Tab 1 content.","generateblocks-pro")}]]],y=[["generateblocks-pro/tabs-menu",{styles:{display:"inline-flex","@media (max-width:767px)":{overflowX:"auto",maxWidth:"100%"}},htmlAttributes:{role:"tablist"}},[k,["generateblocks-pro/tab-menu-item",{...v},[["generateblocks/text",{content:(0,c.__)("Tab 2","generateblocks-pro"),tagName:"span"}]]]]],["generateblocks-pro/tab-items",{styles:{backgroundColor:"#fafafa"}},[w,["generateblocks-pro/tab-item",f,[["core/paragraph",{content:(0,c.__)("Tab 2 content.","generateblocks-pro")}]]]]]],S=[["generateblocks-pro/tabs-menu",{styles:{display:"flex",justifyContent:"center",columnGap:"10px","@media (max-width:767px)":{overflowX:"auto",maxWidth:"100%"}},htmlAttributes:{role:"tablist"}},[["generateblocks-pro/tab-menu-item",{tabItemOpen:!0,...v},[["generateblocks/text",{content:(0,c.__)("Tab 1","generateblocks-pro"),tagName:"span"}]]],["generateblocks-pro/tab-menu-item",{...v},[["generateblocks/text",{content:(0,c.__)("Tab 2","generateblocks-pro"),tagName:"span"}]]]]],["generateblocks-pro/tab-items",{htmlAttributes:{role:"tabpanel"}},[["generateblocks-pro/tab-item",{tabItemOpen:!0},[["core/paragraph",{content:(0,c.__)("Tab 1 content.","generateblocks-pro")}]]],["generateblocks-pro/tab-item",{},[["core/paragraph",{content:(0,c.__)("Tab 2 content.","generateblocks-pro")}]]]]]],E={styles:{paddingTop:"1em",paddingRight:"1em",paddingBottom:"1em",paddingLeft:"1em"}},x=[["generateblocks-pro/tabs-menu",{styles:{display:"flex",flexDirection:"column",width:"200px","@media (max-width:767px)":{flexDirection:"row",overflowX:"auto",maxWidth:"100%",width:"auto"}},htmlAttributes:{role:"tablist"}},[["generateblocks-pro/tab-menu-item",{tabItemOpen:!0,...v},[["generateblocks/text",{content:(0,c.__)("Tab 1","generateblocks-pro"),tagName:"span"}]]],["generateblocks-pro/tab-menu-item",{...v},[["generateblocks/text",{content:(0,c.__)("Tab 2","generateblocks-pro"),tagName:"span"}]]]]],["generateblocks-pro/tab-items",{styles:{flexGrow:1,backgroundColor:"#fafafa"},htmlAttributes:{role:"tabpanel"}},[["generateblocks-pro/tab-item",{tabItemOpen:!0,...E},[["core/paragraph",{content:(0,c.__)("Tab 1 content.","generateblocks-pro")}]]],["generateblocks-pro/tab-item",E,[["core/paragraph",{content:(0,c.__)("Tab 2 content.","generateblocks-pro")}]]]]]],C=function(e,l){var n,a=(n=(0,t.useRef)(!0)).current?(n.current=!1,!0):n.current;(0,t.useEffect)((function(){if(!a)return e()}),l)},z=window.gbp.stylesBuilder,L=(0,a.createReduxStore)("gbp-current-style",{reducer:z.currentStyleReducer,actions:z.currentStyleActions,selectors:z.currentStyleSelectors}),A=(0,a.createReduxStore)("gbp-styles",{reducer:z.styleReducer,actions:z.styleActions,selectors:z.styleSelectors}),B=(0,a.createReduxStore)("gbp-styles-at-rule",{reducer:z.atRuleReducer,actions:z.atRuleActions,selectors:z.atRuleSelectors}),R=(0,a.createReduxStore)("gbp-styles-nested-rule",{reducer:z.nestedRuleReducer,actions:z.nestedRuleActions,selectors:z.nestedRuleSelectors}),_=window.wp.apiFetch;var M=l.n(_);const H=window.wp.notices,I=window.wp.url,T=window.wp.coreData;var V;window.lodash;const q="undefined"!=typeof gbGlobalStylePermissions&&null!==(V=gbGlobalStylePermissions?.canManageStyles)&&void 0!==V&&V,N=window.wp.editPost;const D=(0,a.createReduxStore)("gbp-block-styles-current-style",{reducer:z.currentStyleReducer,actions:z.currentStyleActions,selectors:z.currentStyleSelectors}),O=(0,a.createReduxStore)("gbp-block-styles-at-rule",{reducer:z.atRuleReducer,actions:z.atRuleActions,selectors:z.atRuleSelectors}),j=(0,a.createReduxStore)("gbp-block-styles-nested-rule",{reducer:z.nestedRuleReducer,actions:z.nestedRuleActions,selectors:z.nestedRuleSelectors});function P(){const e=(0,a.useSelect)((e=>e(O).getAtRule())),{setAtRule:t}=(0,a.useDispatch)(O),l=(0,a.useSelect)((e=>e(j).getNestedRule())),{setNestedRule:n}=(0,a.useDispatch)(j),r=(0,i.useCurrentAtRule)(z.defaultAtRules),{setCurrentStyle:o}=(0,a.useDispatch)(D),u=(0,a.useSelect)((e=>e(D).currentStyle())),{deviceType:d,setDeviceType:b}=(0,i.useDeviceType)(),p=function(){const{setCurrentStyle:e}=(0,a.useDispatch)(L),{setStyles:t}=(0,a.useDispatch)(A),{createNotice:l,removeAllNotices:n}=(0,a.useDispatch)(H.store),{getEntityRecordEdits:r}=(0,a.useSelect)(T.store),{getSelectedBlock:o}=(0,a.useSelect)((e=>e(s.store)),[]),{setAtRule:i}=(0,a.useDispatch)(B),{setNestedRule:u}=(0,a.useDispatch)(R),{openGeneralSidebar:d}=(0,a.useDispatch)(N.store);return async(a,s={})=>{if(!q)return;const{classStyles:b,classPostId:p}=await async function(e){var t;const l=await M()({path:(0,I.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let n=null!==(t=l?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(n)&&0===n.length&&(n={}),{classStyles:n,classPostId:l?.response?.data?.postId}}(a);if(!p)return n("snackbar"),void l("error",(0,c.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,c.__)("%s does not exist.","generateblocks-pro"),a),{type:"snackbar"});i(""),u(""),d("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:p,name:a,classStyles:b,clientId:o()?.clientId,options:s}),s.nestedRule&&u(s.nestedRule),s.atRule&&i(s.atRule);const m=r("postType","gblocks_styles",p);t(m?.gb_style_data||b),n("snackbar"),l("info",(0,c.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,c.__)("Editing %s.","generateblocks-pro"),a),{type:"snackbar"})}}(),m=function(){const{setCurrentStyle:e}=(0,a.useDispatch)(L),{setStyles:t}=(0,a.useDispatch)(A),{setAtRule:l}=(0,a.useDispatch)(B),{setNestedRule:n}=(0,a.useDispatch)(R);return()=>{e({}),t({}),l(""),n("")}}();return{atRule:e,nestedRule:l,setAtRule:t,currentAtRule:r,setNestedRule:n,setDeviceType:b,deviceType:d,setCurrentStyle:o,currentStyle:u,getPreviewDevice:i.getPreviewDevice,setGlobalStyle:p,cancelEditGlobalStyle:m}}function Z({attributes:e,setAttributes:l,shortcuts:n,onStyleChange:a}){const{atRule:r,setAtRule:s,nestedRule:o,setNestedRule:c,setDeviceType:u,getPreviewDevice:d,currentStyle:b,setGlobalStyle:p,cancelEditGlobalStyle:m}=P(),{styles:g,globalClasses:h=[]}=e,v=(0,z.getStylesObject)(g,r,o);return(0,t.createElement)(z.StylesBuilder,{currentSelector:b?.selector,styles:v,allStyles:g,onDeleteStyle:(e,t)=>{const n=(0,z.deleteStylesObjectKey)(g,e,t);l({styles:n})},nestedRule:o,atRule:r,onStyleChange:(e,t=null)=>a(e,t,r,o),onNestedRuleChange:e=>c(e),onAtRuleChange:e=>{s(e);const t=(0,z.getPreviewWidth)(e),l=d(t);l&&u(l)},onUpdateKey:(e,t,n)=>{const a=(0,z.updateStylesObjectKey)(g,e,t,n);l({styles:a})},selectorShortcuts:n.selectorShortcuts,visibleSelectors:n.visibleShortcuts,onEditStyle:p,cancelEditStyle:m,setLocalTab:e=>{sessionStorage.setItem(i.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:h})}const F=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function G(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function W(e,t,l=!1){const{styles:n={},uniqueId:a="",globalClasses:r=[]}=t,s=[];return l&&s.push(e),r.length>0&&s.push(...r),Object.keys(n).length>0&&s.push(`${e}-${a}`),s}const U=window.gbp.components,$={default:{items:[{label:"Hover",value:"&:hover"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"},{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"}]},interactions:{label:(0,c.__)("Interactions","generateblocks-pro"),items:[{label:"Hover",value:"&:hover"},{label:"Focus",value:"&:focus"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"}]},links:{label:(0,c.__)("Links","generateblocks-pro"),items:[{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"},{label:"Hovered & focused links",value:"a:is(:hover, :focus)"},{label:"Visited links",value:"a:visited"}]},pseudoElements:{label:(0,c.__)("Pseudo Elements","generateblocks-pro"),items:[{label:"Before",value:"&:before"},{label:"After",value:"&:after"}]}};function K({value:l,options:n=[],onChange:a,blockName:r}){var s;const i=null!==(s=(0,e.getBlockType)(r)?.attributes?.tagName?.enum)&&void 0!==s?s:[],u=n.length?n:i.map((e=>({label:e,value:e})));return u.length?(0,t.createElement)(o.SelectControl,{label:(0,c.__)("Tag Name","generateblocks-pro"),value:l,options:u,onChange:a}):null}const J=(0,n.compose)((function(e){return l=>{var n,c,i,u;const{attributes:d,setAttributes:p,context:m}=l,{htmlAttributes:g={},uniqueId:h,className:v,align:f}=d,k=(0,a.useSelect)((e=>e("core/editor").isSavingPost())),{style:w="",href:y,...S}=g,E=Object.keys(S).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(S[t]),e)),{}),[x,z]=(0,r.useState)(w);(0,r.useEffect)((()=>{!async function(){const e=await(0,b.applyFilters)("generateblocks.editor.htmlAttributes.style",w,{...l});z(e)}()}),[w,m,k]),C((()=>{const e=["alignwide","alignfull"],t=(v?.split(" ")||[]).filter((t=>!e.includes(t)));f&&t.push("align"+f),p({className:t.join(" ")})}),[f]);const L="string"==typeof x?x.split(";").reduce(((e,t)=>{const l=t.indexOf(":");if(-1===l)return e;let n=t.slice(0,l).trim();const a=t.slice(l+1).trim();return n&&a&&(n.startsWith("--")||(n=n.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[n]=a),e}),{}):"",A={...E,style:L,"data-gb-id":h,"data-context-post-id":null!==(n=null!==(c=m?.postId)&&void 0!==c?c:m?.["generateblocks/loopIndex"])&&void 0!==n?n:0,"data-align":f||void 0},B=(0,r.useMemo)((()=>Array.isArray(g)?{}:g),[JSON.stringify(g)]);return(0,r.useEffect)((()=>{const e={...g};Object.keys(e).forEach((t=>{const l=t.startsWith("data-"),n=e[t];F.includes(t)||""!==n||l||"alt"===t||delete e[t],"string"!=typeof n&&"boolean"!=typeof n&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const l=Object.keys(e),n=Object.keys(t);if(l.length!==n.length)return!1;for(const n of l)if(e[n]!==t[n])return!1;return!0}(e,g)||p({htmlAttributes:e})}),[JSON.stringify(g)]),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(e,{...l,editorHtmlAttributes:A,htmlAttributes:B}),(0,t.createElement)(s.InspectorAdvancedControls,null,(0,t.createElement)(o.TextControl,{label:"HTML ID",value:null!==(i=g.id)&&void 0!==i?i:"",onChange:e=>{p({htmlAttributes:{...g,id:e}})},onBlur:()=>{g.id&&p({htmlAttributes:{...g,id:G(g.id)}})}}),(0,t.createElement)(o.TextControl,{label:"ARIA Label",value:null!==(u=g["aria-label"])&&void 0!==u?u:"",onChange:e=>{p({htmlAttributes:{...g,"aria-label":e}})}})))}}),(function(e){return l=>{const{attributes:n,name:a,setAttributes:s,isSelected:o,clientId:c}=l,{uniqueId:u,styles:d,css:b}=n,{atRule:p,deviceType:m,setAtRule:g,currentStyle:h,setCurrentStyle:v,setNestedRule:f}=P(),k=(0,i.useSetStyles)(l,{cleanStylesObject:z.cleanStylesObject}),w=(0,r.useMemo)((()=>u?(0,i.getSelector)(a,u):""),[a,u]),y=Array.isArray(d)?{}:d;return(0,i.useAtRuleEffect)({deviceType:m,atRule:p,setAtRule:g,defaultAtRules:z.defaultAtRules,isSelected:o,getPreviewWidth:z.getPreviewWidth}),(0,i.useGenerateCSSEffect)({selector:w,styles:y,setAttributes:s,getCss:z.getCss,getSelector:i.getSelector,isSelected:o,blockCss:b,clientId:c}),(0,i.useStyleSelectorEffect)({isSelected:o,currentStyle:h,selector:w,setCurrentStyle:v,setNestedRule:f}),(0,i.useDecodeStyleKeys)({styles:d,setAttributes:s}),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(i.Style,{selector:w,getCss:z.getCss,styles:y,clientId:c,name:a}),(0,t.createElement)(e,{...l,selector:w,onStyleChange:function(e,t="",l="",n=""){const a="object"==typeof e?e:{[e]:t},r=(0,i.buildChangedStylesObject)(a,l,n);k(r)},getStyleValue:function(e,t="",l=""){var n,a,r,s;return l?t?null!==(r=d?.[l]?.[t]?.[e])&&void 0!==r?r:"":null!==(a=d?.[l]?.[e])&&void 0!==a?a:"":t?null!==(s=d?.[t]?.[e])&&void 0!==s?s:"":null!==(n=d?.[e])&&void 0!==n?n:""},styles:y}))}}),i.withUniqueId)((function(e){var l,n;const{attributes:u,setAttributes:d,name:b,clientId:g,onStyleChange:v,getStyleValue:f,editorHtmlAttributes:k={},htmlAttributes:w,styles:E}=e,{tagName:z,showTemplateSelector:L}=u,{getBlock:A}=(0,a.useSelect)((e=>e(s.store)),[]),[B,R]=(0,r.useState)({}),{updateBlockAttributes:_}=(0,a.useDispatch)(s.store),M=(0,r.useMemo)((()=>{var e;return null!==(e=w?.["data-opened-tab"])&&void 0!==e?e:""}),[w]),H=null!==(l=A(g)?.innerBlocks)&&void 0!==l?l:[];(0,r.useEffect)((()=>{const e=[],t=[],l=H.length?H.filter((e=>"generateblocks-pro/tab-items"===e.name)):[];l.length&&l[0].innerBlocks.forEach((t=>e.push(t.clientId)));const n=H.length?H.filter((e=>"generateblocks-pro/tabs-menu"===e.name)):[];n.length&&n[0].innerBlocks.forEach((e=>t.push(e.clientId))),R({...B,tabIds:e,buttonIds:t})}),[H]),C((()=>{const e=M-1,t={},l=B.buttonIds.concat(B.tabIds);B?.tabIds?.forEach(((l,n)=>{t[l]={tabItemOpen:String(n)===String(e)}})),B?.buttonIds?.forEach(((l,n)=>{t[l]={tabItemOpen:String(n)===String(e)}})),_(l,t,!0)}),[M]);const I=[{label:(0,c.__)("Select…","generateblocks-pro"),value:""}];B?.tabIds?.length&&B.tabIds.forEach(((e,t)=>{const l=t+1;I.push({
/* translators: Tab number */
label:(0,c.sprintf)((0,c.__)("Tab %s","generateblocks-pro"),l),value:l})}));const T=W("gb-tabs",{...u,styles:E},!0),V=(0,r.useRef)(),q=(0,s.useBlockProps)({className:T.filter(Boolean).join(" ").trim(),...k,ref:V}),N=(0,s.useInnerBlocksProps)(q,{allowedBlocks:["generateblocks-pro/tabs-menu","generateblocks-pro/tab-items"],renderAppender:!1}),D=z||"div",O=(0,r.useMemo)((()=>{const e=[{label:(0,c.__)("Links","generateblocks"),value:"a"}];return{selectorShortcuts:$,visibleShortcuts:e}}),[]);(0,r.useEffect)((()=>{z||d({tagName:"div"})}),[z]);const j={name:b,attributes:u,setAttributes:d,clientId:g,getStyleValue:f,onStyleChange:v};return L?(0,t.createElement)(m,{clientId:g,setAttributes:d,label:(0,c.__)("Tabs","generateblocks-pro"),instructions:(0,c.__)("Choose a tabs layout to start with.","generateblocks-pro"),templates:[{id:"horizontal-tabs",label:(0,c.__)("Horizontal Tabs","generateblocks-pro"),icon:h("horizontal-tabs"),innerBlocks:y},{id:"vertical-tabs",label:(0,c.__)("Vertical Tabs","generateblocks-pro"),icon:h("vertical-tabs"),innerBlocks:x,attributes:{styles:{display:"flex",flexDirection:"row","@media (max-width: 768px)":{flexDirection:"column"}}}},{id:"button-tabs",label:(0,c.__)("Button Tabs","generateblocks-pro"),icon:h("button-tabs"),innerBlocks:S,attributes:{styles:{display:"flex",flexDirection:"column",columnGap:"20px",rowGap:"20px"}}}]}):(0,t.createElement)(t.Fragment,null,(0,t.createElement)(s.InspectorControls,null,(0,t.createElement)(i.BlockStyles,{settingsTab:(0,t.createElement)(U.OpenPanel,{...j,panelId:"settings"},(0,t.createElement)(o.SelectControl,{label:(0,c.__)("Default opened tab","generateblocks-pro"),options:I,value:M,onChange:e=>{const t={...w};e?t["data-opened-tab"]=e:delete t["data-opened-tab"],d({htmlAttributes:t})}}),(0,t.createElement)(o.SelectControl,{label:(0,c.__)("Transition","generateblocks-pro"),value:null!==(n=w?.["data-transition"])&&void 0!==n?n:"",options:[{label:(0,c.__)("None","generateblocks-pro"),value:""},{label:(0,c.__)("Fade","generateblocks-pro"),value:"fade"}],onChange:e=>{const t={...w};e?t["data-transition"]=e:delete t["data-transition"],d({htmlAttributes:t})}}),(0,t.createElement)(K,{blockName:b,value:z,onChange:e=>{d({tagName:e})}})),stylesTab:(0,t.createElement)(Z,{attributes:u,setAttributes:d,shortcuts:O,onStyleChange:v})})),(0,t.createElement)(p,{name:b,clientId:g},(0,t.createElement)(D,{...N})))})),X=JSON.parse('{"UU":"generateblocks-pro/tabs"}');function Q(e,t){return e.reduce(((e,l)=>{if(l.name&&t===l.name&&e.push(l.clientId),l.innerBlocks){const{clientIds:n}=Q(l.innerBlocks,t);e=e.concat(n)}return e.filter((e=>e))}),[])}function Y(){return(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,t.createElement)("path",{d:"M22.006,22.006L20.665,22.006L20.665,17.629L22.006,17.629L22.006,22.006ZM22.006,14.814L20.665,14.814L20.665,9.185L22.006,9.185L22.006,14.814ZM22.006,6.372L20.672,6.372L20.672,3.328L17.628,3.328L17.628,1.994L21.38,1.994C21.725,1.994 22.006,2.274 22.006,2.619L22.006,6.372ZM6.371,1.994L6.371,3.331L1.994,3.331L1.994,1.994L6.371,1.994ZM14.814,3.331L9.186,3.331L9.186,1.994L14.814,1.994L14.814,3.331Z",style:{fillOpacity:.5}}),(0,t.createElement)("path",{d:"M14,6.5L16.5,6.5L16.5,4L17.5,4L17.5,6.5L20,6.5L20,7.5L17.5,7.5L17.5,10L16.5,10L16.5,7.5L14,7.5L14,6.5Z"}),(0,t.createElement)("path",{d:"M13.361,15.744L4.634,15.744C4.396,15.744 4.2,15.94 4.2,16.178C4.2,16.416 4.396,16.612 4.634,16.612L13.361,16.612C13.599,16.612 13.795,16.416 13.795,16.178C13.795,15.94 13.606,15.744 13.361,15.744ZM13.361,17.866L4.634,17.866C4.396,17.866 4.2,18.062 4.2,18.3C4.2,18.538 4.396,18.734 4.634,18.734L13.361,18.734C13.599,18.734 13.795,18.538 13.795,18.3L13.795,18.293C13.795,18.058 13.602,17.866 13.368,17.866L13.361,17.866ZM4.193,14.056C4.193,14.294 4.389,14.49 4.627,14.49L13.354,14.49C13.592,14.49 13.788,14.294 13.788,14.056C13.788,13.818 13.592,13.621 13.354,13.621L4.634,13.621L4.622,13.621C4.387,13.621 4.193,13.815 4.193,14.051L4.193,14.056Z",style:{fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M10.748,10.491L15.994,10.491L15.994,21.43C15.994,21.668 15.798,21.865 15.56,21.865L2.428,21.865C2.19,21.865 1.994,21.668 1.994,21.423L1.994,8.292C1.994,8.054 2.19,7.858 2.428,7.858L7.247,7.858L7.247,10.491L10.748,10.491ZM6.805,11.366C6.567,11.366 6.371,11.17 6.371,10.932L6.371,8.733L2.869,8.733L2.869,20.989L15.125,20.989L15.125,11.366L6.805,11.366Z",style:{fillRule:"nonzero"}}))}function ee(l){const{clientId:n,name:r,Component:s=o.ToolbarButton}=l,{getBlocks:i,getBlock:u,getBlockParentsByBlockName:d}=(0,a.useSelect)((e=>e("core/block-editor")),[]),{insertBlocks:b}=(0,a.useDispatch)("core/block-editor");return(0,t.createElement)(t.Fragment,null,(0,t.createElement)(s,{icon:Y,label:(0,c.__)("Add Tab Item","generateblocks-pro"),onClick:()=>{const t=function(){var e;if("generateblocks-pro/tabs"===r)return u(n);const t=null!==(e=d(n,"generateblocks-pro/tabs",!0)?.[0])&&void 0!==e?e:"";return t?u(t):null}();if(!t)return;const l=Q(t.innerBlocks,"generateblocks-pro/tabs-menu").find(Boolean),a=Q(t.innerBlocks,"generateblocks-pro/tab-items").find(Boolean),s=i(l),o=i(a),c=s.length-1;if(s){const t=s[c],n=t?(0,e.cloneBlock)(t,{uniqueId:"",tabItemOpen:!1}):(0,e.createBlocksFromInnerBlocksTemplate)([k]);b(n,c+1,l,!1)}if(o){const t=o[c],l=t?(0,e.cloneBlock)(t,{uniqueId:"",tabItemOpen:!1}):(0,e.createBlocksFromInnerBlocksTemplate)([w]);b(l,c+1,a,!1)}},showTooltip:!0}))}function te({clientId:e,name:l}){return(0,t.createElement)(ee,{clientId:e,name:l})}const le=["generateblocks-pro/tabs","generateblocks-pro/tab-item","generateblocks-pro/tab-items","generateblocks-pro/tab-menu-item","generateblocks-pro/tabs-menu"];(0,b.addFilter)("generateblocks.editor.toolbarAppenders","generateblocks.tabs.addToolbarAppenders",(function(e,{clientId:l,name:n}){return le.includes(n)?(0,t.createElement)(t.Fragment,null,e,(0,t.createElement)(te,{clientId:l,name:n})):e})),(0,e.registerBlockType)(X.UU,{edit:J,save:function({attributes:e}){const{tagName:l,htmlAttributes:n={}}=e,a=W("gb-tabs",e,!0),r=s.useBlockProps.save({className:a.join(" ").trim(),...n});return(0,t.createElement)(l,{...s.useInnerBlocksProps.save(r)})},icon:h("tabs")}),(0,e.registerBlockVariation)("generateblocks-pro/tabs",{title:"Tabs",name:"tabs",isDefault:!0,description:"Build a series of tabs.",attributes:{showTemplateSelector:!0,htmlAttributes:{"data-opened-tab":"1"}},innerBlocks:[]})})()})();