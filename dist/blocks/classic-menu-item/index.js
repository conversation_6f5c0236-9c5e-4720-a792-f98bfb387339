(()=>{var e={20493:(e,t,n)=>{"use strict";function l(e){if("Tab"!==e.key&&9!==e.keyCode)return;e.preventDefault();const t=e.currentTarget,n=t.querySelectorAll('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]'),l=Array.from(n).filter((e=>null!==e.offsetParent&&"hidden"!==getComputedStyle(e).visibility&&"none"!==getComputedStyle(e).display));if(0===l.length)return;const o=l[0],r=l[l.length-1],s=document.activeElement;if(t.contains(s))if(e.shiftKey)if(s===o)r.focus();else{const e=l.indexOf(s);e>0&&l[e-1].focus()}else if(s===r)o.focus();else{const e=l.indexOf(s);e<l.length-1&&l[e+1].focus()}else o.focus()}function o(e){const t=e.querySelectorAll(".gb-menu-toggle"),n=e.querySelector(".gb-menu-container"),o=n.querySelector(".gb-menu"),r=o.querySelectorAll(".menu-item"),s=o.querySelectorAll(".menu-item > a"),a=o.querySelectorAll(".gb-submenu-toggle"),c=e.closest("body");requestAnimationFrame((()=>{c.removeAttribute("data-gb-menu-open"),e.classList.remove("gb-navigation--open"),n&&(n.classList.remove("gb-menu-container--toggled"),n.removeEventListener("keydown",l)),t.forEach((e=>{e&&(e.classList.remove("gb-menu-toggle--toggled"),e.ariaExpanded="false",(e.offsetHeight>0||e.offsetWidth>0)&&e.focus())})),r?.length>0&&r.forEach((e=>{e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open")})),s?.length>0&&s.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")})),a?.length>0&&a.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")}))}))}function r(e,t=null){if(!e)return;const n=e.querySelectorAll(".menu-item.gb-sub-menu--open");n&&Array.from(n).filter((e=>!e.contains(t))).forEach((e=>{const t=e.querySelector("a"),n=e.querySelector(".gb-submenu-toggle");e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open"),e.setAttribute("aria-current","false"),t&&t.hasAttribute("aria-expanded")&&t.setAttribute("aria-expanded","false"),n&&n.hasAttribute("aria-expanded")&&n.setAttribute("aria-expanded","false")}))}function s(e,t=!1){if(e){t&&t.preventDefault();const n=e.closest(".gb-navigation"),l=e.closest(".menu-item"),o="true"===e.getAttribute("aria-expanded");r(n,l),e.setAttribute("aria-expanded",o?"false":"true"),l.classList.toggle("current-menu-item"),l.classList.toggle("gb-sub-menu--open")}}function a(e,t=!1){if(e){t&&t.preventDefault();const n=t.type,l=e.closest(".gb-menu-container--toggled"),o=e.closest(".gb-menu--hover");if("click"===n&&o&&!l)return;const r=e.closest(".menu-item"),s="true"===e.getAttribute("aria-expanded");e.setAttribute("aria-expanded",s?"false":"true"),r.classList.toggle("current-menu-item"),r.classList.toggle("gb-sub-menu--open")}}function c(e){e&&e.forEach((e=>{var t;const n=e.querySelector(".gb-menu-toggle"),l=e.querySelector(".gb-menu-container"),r=null!==(t=e.getAttribute("data-gb-mobile-breakpoint"))&&void 0!==t?t:"",s=window.matchMedia(`(max-width: ${r})`);n&&l&&n.setAttribute("aria-controls",l.id),e.classList.toggle("gb-navigation--mobile",s.matches),l.classList.toggle("gb-menu-container--mobile",s.matches),s.addEventListener("change",(t=>{e.classList.toggle("gb-navigation--mobile",t.matches),l.classList.toggle("gb-menu-container--mobile",t.matches),o(e)})),setTimeout((()=>{const t=e.querySelector(".gb-menu");if(t){const e=t.querySelectorAll(".menu-item-has-children");e.length>0&&requestAnimationFrame((()=>{e.forEach((e=>{const n=e.querySelector("a"),l=t.classList.contains("gb-menu--click")?n:e.querySelector(".gb-submenu-toggle");if(l){l.setAttribute("aria-controls",`sub-menu-${e.id}`),l.setAttribute("aria-label",`Toggle submenu for ${n.textContent}`);const t=e.querySelector(".gb-sub-menu");t&&(t.id=`sub-menu-${e.id}`)}}))}))}}),0)}))}function i(){let e=document.querySelectorAll(".gb-navigation");if(!e.length){const t=window.frameElement;if(t&&t.id&&t.id.startsWith("pattern-"))return void new MutationObserver(((t,n)=>{e=document.querySelectorAll(".gb-navigation"),e.length&&(n.disconnect(),c(e))})).observe(document.body,{childList:!0,subtree:!0})}c(e),function(){const e=document.querySelectorAll(".gb-navigation--mobile");e&&e.length&&e.forEach((e=>{e.addEventListener("click",(t=>{const n=t.target.closest('a[href*="#"]');if(!n)return;const l=n.getAttribute("href").match(/#(.+)$/);if(l){const t=l[1];document.getElementById(t)&&setTimeout((()=>{o(e)}),50)}}))}))}()}var u;n.d(t,{Qg:()=>s}),window.myNavigationScriptInitialized||(window.myNavigationScriptInitialized=!0,document.addEventListener("click",(e=>{const t=e.target;!function(e){if(e){var t;const n=e.closest(".gb-navigation");if(!n)return;n.classList.contains("gb-navigation--open")?o(n):function(e){const t=e.querySelectorAll(".gb-menu-toggle"),n=e.querySelector(".gb-menu-container"),o=e.getAttribute("data-gb-mobile-menu-type"),r=n.querySelector(".gb-menu-toggle:not(.gb-menu-toggle--clone)"),s=r||"full-overlay"!==o?null:n.querySelector("*"),a=e.closest("body");let c=!1;requestAnimationFrame((()=>{if(e.classList.add("gb-navigation--open"),a.setAttribute("data-gb-menu-open",o),t.forEach((e=>{if(e&&(e.classList.add("gb-menu-toggle--toggled"),e.ariaExpanded="true",!r&&n&&"full-overlay"===o)){s&&(s.style.opacity="0");const t=e.closest(".editor-styles-wrapper"),l=n.querySelector(".gb-menu-toggle--clone");if(t&&l){const t=e.attributes;for(const e of t)l.setAttribute(e.name,e.value);l.innerHTML=e.innerHTML,l.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),c=!0}else if(!l){const t=e.cloneNode(!0);t.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),n.insertAdjacentElement("afterbegin",t),c=!0}}})),c&&s?requestAnimationFrame((()=>{!function(e,t=()=>{}){const n=e.querySelector(".gb-menu-container .gb-menu-toggle");if(n){var l,o;const r=window.getComputedStyle(n),s=null!==(l=parseInt(r?.top,10))&&void 0!==l?l:0,a=null!==(o=parseInt(r?.height,10))&&void 0!==o?o:0;requestAnimationFrame((()=>{e.style.setProperty("--gb-menu-toggle-offset",a+2*s+"px"),t()}))}}(e,(()=>{s.style.opacity=""}))})):s&&"0"===s.style.opacity&&(s.style.opacity=""),n){n.classList.add("gb-menu-container--toggled");const e=n.querySelector('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]');e?e.focus():n.focus(),n.addEventListener("keydown",l)}})),"partial-overlay"===o&&function(e){const t=(n=function(e){var t;const n=null!==(t=e.getAttribute("data-gb-menu-toggle-anchor"))&&void 0!==t?t:"";let l=".gb-navigation";return n?l=n:e.closest(".gb-site-header")&&(l=".gb-site-header"),e.closest(l)}(e))?n.getBoundingClientRect().bottom:0;var n;requestAnimationFrame((()=>e.style.setProperty("--gb-menu-offset",t+"px")))}(e)}(n);const r=null!==(t=window.frameElement)&&void 0!==t&&t;if(r&&r.id&&r.id.startsWith("pattern-"))if(n.classList.contains("gb-navigation--open")){const e=r.getAttribute("data-gb-original-height");e&&(r.style.height=e)}else r.style.height&&parseInt(r.style.height,10)<800&&(r.setAttribute("data-gb-original-height",r.style.height),requestAnimationFrame((()=>r.style.height="800px")))}}(t.closest(".gb-menu-toggle")),s(t.closest(".gb-menu--click .menu-item-has-children > a"),e),a(t.closest(".gb-submenu-toggle"),e);const n=document.querySelector(".menu-item.gb-sub-menu--open");n&&!n.contains(e.target)&&r(n.closest(".gb-navigation:not(.gb-navigation--open)"))})),document.addEventListener("keydown",(e=>{const t="Escape"===e.key,n="Enter"===e.key,l=" "===e.key,c="Tab"===e.key;if((n||l)&&(a(e.target.closest(".gb-submenu-toggle"),e),s(e.target.closest(".gb-menu--click .menu-item-has-children > a"),e)),c){const e=document.querySelector(".gb-sub-menu--open");e&&setTimeout((()=>{const t=document.activeElement;t.closest(".gb-sub-menu--open")||r(e.closest(".gb-navigation"),t)}),0)}if(t){const t=e.target.closest(".gb-sub-menu--open");if(t){r(t.closest(".gb-navigation"));const e=t.querySelector(".gb-submenu-toggle");e&&e.focus()}else{const e=document.querySelector(".gb-navigation--open");e&&o(e)}}})),window.addEventListener("pagehide",(()=>{const e=document.querySelectorAll(".gb-navigation--open");e.length&&e.forEach((e=>o(e)))})),u=()=>{document.querySelector(".editor-styles-wrapper, .wp-admin")?window.addEventListener("load",i):i()},"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",u):u()))},46942:(e,t)=>{var n;!function(){"use strict";var l={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=s(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)l.call(e,n)&&e[n]&&(t=s(t,n));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},t={};function n(l){var o=t[l];if(void 0!==o)return o.exports;var r=t[l]={exports:{}};return e[l](r,r.exports,n),r.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var l in t)n.o(t,l)&&!n.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React,t=window.wp.blocks,l=window.wp.blockEditor,o=window.wp.i18n,r=window.wp.compose,s=window.wp.element,a=window.wp.data,c=window.gbp.components,i=window.gbp.blockStyles,u=window.gbp.stylesBuilder,g=(0,a.createReduxStore)("gbp-current-style",{reducer:u.currentStyleReducer,actions:u.currentStyleActions,selectors:u.currentStyleSelectors}),d=(0,a.createReduxStore)("gbp-styles",{reducer:u.styleReducer,actions:u.styleActions,selectors:u.styleSelectors}),b=(0,a.createReduxStore)("gbp-styles-at-rule",{reducer:u.atRuleReducer,actions:u.atRuleActions,selectors:u.atRuleSelectors}),m=(0,a.createReduxStore)("gbp-styles-nested-rule",{reducer:u.nestedRuleReducer,actions:u.nestedRuleActions,selectors:u.nestedRuleSelectors}),p=window.wp.apiFetch;var y=n.n(p);const f=window.wp.notices,v=window.wp.url,h=window.wp.coreData;var k;window.lodash;const S="undefined"!=typeof gbGlobalStylePermissions&&null!==(k=gbGlobalStylePermissions?.canManageStyles)&&void 0!==k&&k,E=window.wp.editPost;const _=(0,a.createReduxStore)("gbp-block-styles-current-style",{reducer:u.currentStyleReducer,actions:u.currentStyleActions,selectors:u.currentStyleSelectors}),C=(0,a.createReduxStore)("gbp-block-styles-at-rule",{reducer:u.atRuleReducer,actions:u.atRuleActions,selectors:u.atRuleSelectors}),A=(0,a.createReduxStore)("gbp-block-styles-nested-rule",{reducer:u.nestedRuleReducer,actions:u.nestedRuleActions,selectors:u.nestedRuleSelectors});function w(){const e=(0,a.useSelect)((e=>e(C).getAtRule())),{setAtRule:t}=(0,a.useDispatch)(C),n=(0,a.useSelect)((e=>e(A).getNestedRule())),{setNestedRule:r}=(0,a.useDispatch)(A),s=(0,i.useCurrentAtRule)(u.defaultAtRules),{setCurrentStyle:c}=(0,a.useDispatch)(_),p=(0,a.useSelect)((e=>e(_).currentStyle())),{deviceType:k,setDeviceType:w}=(0,i.useDeviceType)(),L=function(){const{setCurrentStyle:e}=(0,a.useDispatch)(g),{setStyles:t}=(0,a.useDispatch)(d),{createNotice:n,removeAllNotices:r}=(0,a.useDispatch)(f.store),{getEntityRecordEdits:s}=(0,a.useSelect)(h.store),{getSelectedBlock:c}=(0,a.useSelect)((e=>e(l.store)),[]),{setAtRule:i}=(0,a.useDispatch)(b),{setNestedRule:u}=(0,a.useDispatch)(m),{openGeneralSidebar:p}=(0,a.useDispatch)(E.store);return async(l,a={})=>{if(!S)return;const{classStyles:g,classPostId:d}=await async function(e){var t;const n=await y()({path:(0,v.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let l=null!==(t=n?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(l)&&0===l.length&&(l={}),{classStyles:l,classPostId:n?.response?.data?.postId}}(l);if(!d)return r("snackbar"),void n("error",(0,o.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,o.__)("%s does not exist.","generateblocks-pro"),l),{type:"snackbar"});i(""),u(""),p("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:d,name:l,classStyles:g,clientId:c()?.clientId,options:a}),a.nestedRule&&u(a.nestedRule),a.atRule&&i(a.atRule);const b=s("postType","gblocks_styles",d);t(b?.gb_style_data||g),r("snackbar"),n("info",(0,o.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,o.__)("Editing %s.","generateblocks-pro"),l),{type:"snackbar"})}}(),R=function(){const{setCurrentStyle:e}=(0,a.useDispatch)(g),{setStyles:t}=(0,a.useDispatch)(d),{setAtRule:n}=(0,a.useDispatch)(b),{setNestedRule:l}=(0,a.useDispatch)(m);return()=>{e({}),t({}),n(""),l("")}}();return{atRule:e,nestedRule:n,setAtRule:t,currentAtRule:s,setNestedRule:r,setDeviceType:w,deviceType:k,setCurrentStyle:c,currentStyle:p,getPreviewDevice:i.getPreviewDevice,setGlobalStyle:L,cancelEditGlobalStyle:R}}function L({attributes:t,setAttributes:n,shortcuts:l,onStyleChange:o}){const{atRule:r,setAtRule:s,nestedRule:a,setNestedRule:c,setDeviceType:g,getPreviewDevice:d,currentStyle:b,setGlobalStyle:m,cancelEditGlobalStyle:p}=w(),{styles:y,globalClasses:f=[]}=t,v=(0,u.getStylesObject)(y,r,a);return(0,e.createElement)(u.StylesBuilder,{currentSelector:b?.selector,styles:v,allStyles:y,onDeleteStyle:(e,t)=>{const l=(0,u.deleteStylesObjectKey)(y,e,t);n({styles:l})},nestedRule:a,atRule:r,onStyleChange:(e,t=null)=>o(e,t,r,a),onNestedRuleChange:e=>c(e),onAtRuleChange:e=>{s(e);const t=(0,u.getPreviewWidth)(e),n=d(t);n&&g(n)},onUpdateKey:(e,t,l)=>{const o=(0,u.updateStylesObjectKey)(y,e,t,l);n({styles:o})},selectorShortcuts:l.selectorShortcuts,visibleSelectors:l.visibleShortcuts,onEditStyle:m,cancelEditStyle:p,setLocalTab:e=>{sessionStorage.setItem(i.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:f})}const R=window.wp.components;function M(t){const{colors:n,label:l}=t;return(0,e.createElement)(R.BaseControl,{className:"gpp-color-group",label:l,id:""},(0,e.createElement)("div",{className:"gpp-color-group__row"},n.map(((t,n)=>(0,e.createElement)(u.ColorPicker,{key:n,tooltip:t?.tooltip,value:t.value,onChange:t.onChange})))))}const I={default:{items:[{label:(0,o.__)("Hover","generateblocks-pro"),value:"&:is(:hover, :focus-within)"},{label:(0,o.__)("Current","generateblocks-pro"),value:"&:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus-within)"},{label:(0,o.__)("Link","generateblocks-pro"),value:".gb-menu-link"},{label:(0,o.__)("Dropdown button","generateblocks-pro"),value:".gb-submenu-toggle"}]}},B=[{id:"hover",label:(0,o.__)("Hover","generateblocks-pro"),value:"&:is(:hover, :focus-within)"},{label:(0,o.__)("Current","generateblocks-pro"),value:"&:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus-within)"}];function T(t){return n=>{const{attributes:l,name:o,setAttributes:r,isSelected:a,clientId:c}=n,{uniqueId:g,styles:d,css:b}=l,{atRule:m,deviceType:p,setAtRule:y,currentStyle:f,setCurrentStyle:v,setNestedRule:h}=w(),k=(0,i.useSetStyles)(n,{cleanStylesObject:u.cleanStylesObject}),S=(0,s.useMemo)((()=>g?(0,i.getSelector)(o,g):""),[o,g]),E=Array.isArray(d)?{}:d;return(0,i.useAtRuleEffect)({deviceType:p,atRule:m,setAtRule:y,defaultAtRules:u.defaultAtRules,isSelected:a,getPreviewWidth:u.getPreviewWidth}),(0,i.useGenerateCSSEffect)({selector:S,styles:E,setAttributes:r,getCss:u.getCss,getSelector:i.getSelector,isSelected:a,blockCss:b,clientId:c}),(0,i.useStyleSelectorEffect)({isSelected:a,currentStyle:f,selector:S,setCurrentStyle:v,setNestedRule:h}),(0,i.useDecodeStyleKeys)({styles:d,setAttributes:r}),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(i.Style,{selector:S,getCss:u.getCss,styles:E,clientId:c,name:o}),(0,e.createElement)(t,{...n,selector:S,onStyleChange:function(e,t="",n="",l=""){const o="object"==typeof e?e:{[e]:t},r=(0,i.buildChangedStylesObject)(o,n,l);k(r)},getStyleValue:function(e,t="",n=""){var l,o,r,s;return n?t?null!==(r=d?.[n]?.[t]?.[e])&&void 0!==r?r:"":null!==(o=d?.[n]?.[e])&&void 0!==o?o:"":t?null!==(s=d?.[t]?.[e])&&void 0!==s?s:"":null!==(l=d?.[e])&&void 0!==l?l:""},styles:E}))}}const q=(e,t)=>e&&Array.isArray(e)?e.reduce(((e,n)=>{const l=!(t&&t.length>0)||t.includes(n.name),o=q(n.innerBlocks,t);return{total:e.total+1+o.total,allowed:e.allowed+(l?1:0)+o.allowed}}),{total:0,allowed:0}):{total:0,allowed:0},x=(e,t)=>e&&Array.isArray(e)?e.filter((e=>{const{name:n}=e;return!(t&&t.length>0)||t.includes(n)})).map((e=>({...e,innerBlocks:x(e.innerBlocks,t)}))):[],N=(0,s.memo)((({block:n,level:l=0,currentClientId:o,selectBlock:r})=>{var s;const{name:c,innerBlocks:i,clientId:u}=n,{getBlockType:g}=(0,a.useSelect)((e=>e(t.store)),[]),d=g(c);return(0,e.createElement)("div",{className:"gb-block-node","data-level":l},(0,e.createElement)(R.Button,{variant:"tertiary",size:"compact",className:"gb-block-node-button",onClick:()=>r(u),isPressed:o===u,icon:null!==(s=d?.icon?.src)&&void 0!==s?s:null},function(e,t,n="list-view"){const{__experimentalLabel:l,title:o}=e||{};if(!e)return"Unknown Block";const r=l&&l(t,{context:n});return!r||"string"!=typeof r&&"number"!=typeof r?o||"Unnamed Block":r}(d,n.attributes)),i&&i.length>0&&(0,e.createElement)("div",{className:"gb-block-tree-inner-blocks"},i.map((t=>(0,e.createElement)(N,{key:t.clientId,block:t,level:l+1,currentClientId:o,blockType:d,selectBlock:r})))))}));function D({blocks:t,clientId:n,allowedBlocks:r,showAllLabel:c=(0,o.__)("Show all blocks","generateblocks-pro")}){const[i,u]=(0,s.useState)(!1),{selectBlock:g}=(0,a.useDispatch)(l.store),d=(0,s.useMemo)((()=>q(t,r)),[t,r]),b=(0,s.useMemo)((()=>x(t,r)),[t,r]),m=(0,s.useMemo)((()=>x(t,[])),[t]),p=(0,s.useMemo)((()=>i?m:b),[i,m,b]),y=d.total!==d.allowed;return(0,e.createElement)("div",{className:"gb-block-tree"},!!y&&(0,e.createElement)("div",{className:"gb-block-tree__show-all"},(0,e.createElement)(R.ToggleControl,{label:c,checked:i,onChange:e=>u(e)})),p.map((t=>(0,e.createElement)(N,{key:t.clientId,block:t,currentClientId:n,selectBlock:g}))))}const O=(0,a.createReduxStore)("gbp-menu-toggle-state",{reducer:function(e=!1,t){return"SET_DATA"===t.type?t.payload:e},actions:{setMenuToggleState:e=>({type:"SET_DATA",payload:e})},selectors:{menuToggleState:e=>e}});function P(e,t,n=!1){const{styles:l={},uniqueId:o="",globalClasses:r=[]}=t,s=[];return n&&s.push(e),r.length>0&&s.push(...r),Object.keys(l).length>0&&s.push(`${e}-${o}`),s}const F=window.wp.hooks,j=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function U(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function Z({value:t,onChange:n}){const l=(0,s.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus?.map((e=>({label:e.name,value:e.term_id.toString()}))))&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]);return l.length?(0,e.createElement)(R.SelectControl,{label:(0,o.__)("Menu","generateblocks-pro"),value:t,options:l,onChange:n}):null}var G=n(46942),H=n.n(G);function $({name:e,clientId:t,align:n,children:o}){const{getBlockRootClientId:r}=(0,a.useSelect)((e=>e("core/block-editor")),[]),c=(0,a.useSelect)((e=>{const{getSettings:t}=e(l.store);return t().supportsLayout||!1}),[]),i=e.toString().replace("/","-"),u={className:H()({"wp-block":!0,"gb-is-root-block":!0,[`gb-root-block-${i}`]:!0,[`align${n}`]:c}),"data-align":n&&!c?n:null,"data-block":t},g=r(t);return(0,F.applyFilters)("generateblocks.rootElement.disable",g,{name:e})?o:(0,s.createElement)("div",u,o)}const z=window.wp.serverSideRender;var V=n.n(z);const W={},K=[];var Q=n(20493);function J(){var t,n;const l=null!==(t=generateblocksBlockClassicMenu?.menuAdminUrl)&&void 0!==t?t:"",[r,c]=(0,s.useState)(null!==(n=generateblocksBlockClassicMenu?.hasMenuSupport)&&void 0!==n&&n),i=(0,s.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus)&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]),u=(0,a.useSelect)((e=>e(h.store).getEntityRecord("root","site")?.generateblocks_pro_classic_menu_support||!1),[]),{editEntityRecord:g,saveEditedEntityRecord:d}=(0,a.useDispatch)(h.store),[b,m]=(0,s.useState)(u);return r?!l||i.length>0?null:(0,e.createElement)(R.Notice,{isDismissible:!1,status:"warning"},(0,s.createInterpolateElement)((0,o.__)("No menus found. Please <CreateMenuLink />.","generateblocks-pro"),{CreateMenuLink:(0,e.createElement)("a",{href:l,target:"_blank",rel:"noopener noreferrer"},(0,o.__)("create a menu","generateblocks-pro"))})):(0,e.createElement)(R.Notice,{isDismissible:!1,status:"warning"},(0,e.createElement)(R.ToggleControl,{label:(0,o.__)("Enable Menu Support","generateblocks-pro"),checked:b,onChange:async e=>{m(e),c(e);try{await g("root","site",void 0,{generateblocks_pro_classic_menu_support:e}),await d("root","site",void 0)}catch(e){var t;console.error("Save failed:",e),m(u),c(null!==(t=generateblocksBlockClassicMenu?.hasMenuSupport)&&void 0!==t&&t)}},help:(0,o.__)("Your theme does not support the menu system. Enable it here.","generateblocks-pro")}))}(0,r.compose)(T,i.withUniqueId)((function(t){var n;const{attributes:r,setAttributes:u,getStyleValue:g,onStyleChange:d,clientId:b,name:m,context:p,isSelected:y}=t,{menu:f,uniqueId:v}=r,h=(0,s.useRef)(),k=(0,a.useSelect)((e=>e(O).menuToggleState())),{getBlockParentsByBlockName:S,getBlock:E}=(0,a.useSelect)((e=>e(l.store)),[]),{selectBlock:_}=(0,a.useDispatch)(l.store),C=(0,s.useMemo)((()=>{var e;return null!==(e=S(b,"generateblocks-pro/navigation",!0)?.[0])&&void 0!==e?e:""}),[b]),A=(0,s.useMemo)((()=>E(C)),[C,y]),w=(0,s.useMemo)((()=>E(b)),[b,y]),R=(0,s.useMemo)((()=>{var e;return null!==(e=w?.innerBlocks?.find((e=>"generateblocks-pro/classic-menu-item"===e.name))?.clientId)&&void 0!==e?e:""}),[w]),M=(0,s.useMemo)((()=>{var e;return null!==(e=w?.innerBlocks?.find((e=>"generateblocks-pro/classic-sub-menu"===e.name))?.clientId)&&void 0!==e?e:""}),[w]),I=h?.current?.querySelector(".gb-menu"),B=(0,s.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus?.map((e=>({label:e.name,value:e.term_id.toString()}))))&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]);(0,s.useEffect)((()=>{var e;f&&B.find((e=>e.value===f))||u({menu:null!==(e=B[0]?.value)&&void 0!==e?e:""})}),[B.length]);const T=P("gb-menu",r,!0),q=(0,l.useBlockProps)({className:T.filter(Boolean).join(" ").trim(),ref:h}),x=(0,l.useInnerBlocksProps)({},{allowedBlocks:["generateblocks-pro/classic-menu-item","generateblocks-pro/classic-sub-menu"],renderAppender:!1}),N={name:m,attributes:r,setAttributes:u,clientId:b,getStyleValue:g,onStyleChange:d},F={};return F.subMenuType=null!==(n=p?.["generateblocks-pro/subMenuType"])&&void 0!==n?n:"hover",F.disableLinks=!0,(0,s.useEffect)((()=>{if(!h?.current)return;const e=h.current.closest(".gb-menu-container");if(!e)return;const t=e.querySelectorAll(".gb-menu--click .menu-item-has-children > a"),n=[];return t.length>0&&t.forEach((e=>{const t=t=>{(0,Q.Qg)(e,t)};e.addEventListener("click",t),n.push({item:e,handler:t})})),()=>{n.forEach((({item:e,handler:t})=>{e.removeEventListener("click",t)}))}}),[h,I]),(0,s.useEffect)((()=>{if(!h?.current)return;const e=h?.current?.closest(".gb-navigation");if(!e)return;const t=e.querySelectorAll(".gb-submenu-toggle"),n=[];return t.length>0&&t.forEach((e=>{const t=t=>{const n=e.closest(".gb-menu-container--toggled"),l=e.closest(".gb-menu--hover");"click"===t?.type&&l&&!n||(0,Q.Qg)(e,t)};e.addEventListener("click",t),n.push({item:e,handler:t})})),()=>{n.forEach((({item:e,handler:t})=>{e.removeEventListener("click",t)}))}}),[h,k,I]),(0,s.useEffect)((()=>{const e=h?.current;if(e)return e.addEventListener("click",t),()=>{e.removeEventListener("click",t)};function t(e){e.target.closest(".gb-sub-menu")?_(M):e.target.closest(".menu-item")&&_(R)}}),[h,R,I,M]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(i.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.OpenPanel,{title:(0,o.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...N},(0,e.createElement)(D,{blocks:[A],clientId:b,allowedBlocks:Y,showAllLabel:(0,o.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(J,null),(0,e.createElement)(c.OpenPanel,{...N,panelId:"settings"},(0,e.createElement)(Z,{value:f,onChange:e=>u({menu:e})}))),stylesTab:(0,e.createElement)(L,{attributes:r,setAttributes:u,shortcuts:{selectorShortcuts:W,visibleShortcuts:K},onStyleChange:d})})),(0,e.createElement)("div",{...q},B.length&&f?(0,e.createElement)(V(),{key:f+v,block:"generateblocks-pro/classic-menu",attributes:r,urlQueryArgs:F}):(0,e.createElement)(e.Fragment,null,(0,o.__)("No menu found.","generateblocks-pro")),(0,e.createElement)("div",{...x})))}));const Y=["generateblocks-pro/navigation","generateblocks-pro/menu-container","generateblocks-pro/classic-menu","generateblocks-pro/classic-menu-item","generateblocks-pro/classic-sub-menu","generateblocks-pro/menu-toggle"];function X(e){for(const t of e){if("generateblocks-pro/classic-menu"===t.name)return t;if(t.innerBlocks&&t.innerBlocks.length>0){const e=X(t.innerBlocks);if(e)return e}}return null}function ee(){const t=document.querySelector('.gb-block-styles-tab-panel button[id*="styles"]');return t?(0,e.createElement)("div",{className:"gb-more-style-controls"},(0,s.createInterpolateElement)(
// Translators: the at-rule for deletion.
// Translators: the at-rule for deletion.
(0,o.__)("Open the <Styles /> tab for more controls.","generateblocks-pro"),{Styles:(0,e.createElement)(R.Button,{variant:"link",className:"gb-more-style-controls__styles-tab",onClick:()=>t.click()},(0,o.__)("Styles","generateblocks-pro"))})):null}(0,r.compose)((function(t){return n=>{var o,r,c,i;const{attributes:u,setAttributes:g,context:d}=n,{htmlAttributes:b={},uniqueId:m,className:p,align:y}=u,f=(0,a.useSelect)((e=>e("core/editor").isSavingPost())),{style:v="",href:h,...k}=b,S=Object.keys(k).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(k[t]),e)),{}),[E,_]=(0,s.useState)(v);var C,A,w,L;(0,s.useEffect)((()=>{!async function(){const e=await(0,F.applyFilters)("generateblocks.editor.htmlAttributes.style",v,{...n});_(e)}()}),[v,d,f]),C=()=>{const e=["alignwide","alignfull"],t=(p?.split(" ")||[]).filter((t=>!e.includes(t)));y&&t.push("align"+y),g({className:t.join(" ")})},A=[y],L=(w=(0,e.useRef)(!0)).current?(w.current=!1,!0):w.current,(0,e.useEffect)((function(){if(!L)return C()}),A);const M="string"==typeof E?E.split(";").reduce(((e,t)=>{const n=t.indexOf(":");if(-1===n)return e;let l=t.slice(0,n).trim();const o=t.slice(n+1).trim();return l&&o&&(l.startsWith("--")||(l=l.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[l]=o),e}),{}):"",I={...S,style:M,"data-gb-id":m,"data-context-post-id":null!==(o=null!==(r=d?.postId)&&void 0!==r?r:d?.["generateblocks/loopIndex"])&&void 0!==o?o:0,"data-align":y||void 0},B=(0,s.useMemo)((()=>Array.isArray(b)?{}:b),[JSON.stringify(b)]);return(0,s.useEffect)((()=>{const e={...b};Object.keys(e).forEach((t=>{const n=t.startsWith("data-"),l=e[t];j.includes(t)||""!==l||n||"alt"===t||delete e[t],"string"!=typeof l&&"boolean"!=typeof l&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(const l of n)if(e[l]!==t[l])return!1;return!0}(e,b)||g({htmlAttributes:e})}),[JSON.stringify(b)]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(t,{...n,editorHtmlAttributes:I,htmlAttributes:B}),(0,e.createElement)(l.InspectorAdvancedControls,null,(0,e.createElement)(R.TextControl,{label:"HTML ID",value:null!==(c=b.id)&&void 0!==c?c:"",onChange:e=>{g({htmlAttributes:{...b,id:e}})},onBlur:()=>{b.id&&g({htmlAttributes:{...b,id:U(b.id)}})}}),(0,e.createElement)(R.TextControl,{label:"ARIA Label",value:null!==(i=b["aria-label"])&&void 0!==i?i:"",onChange:e=>{g({htmlAttributes:{...b,"aria-label":e}})}})))}}),T,i.withUniqueId)((function(t){var n,r,g,d,b;const{attributes:m,setAttributes:p,editorHtmlAttributes:y,onStyleChange:f,getStyleValue:v,clientId:h,name:k,isSelected:S}=t,{styles:E,htmlAttributes:_,tagName:C,uniqueId:A,subMenuType:w}=m,M=(0,a.useSelect)((e=>e(O).menuToggleState())),I=(0,s.useRef)(),{getBlock:B,getBlocks:T}=(0,a.useSelect)((e=>e(l.store)),[]),{updateBlockAttributes:q}=(0,a.useDispatch)(l.store),x=(0,s.useMemo)((()=>B(h)),[h,S]),[N,F]=(0,s.useState)(null),j=P("gb-navigation",{...m,styles:E},!0);M&&j.push("gb-navigation--open"),j.includes("gb-navigation-"+A)||j.push("gb-navigation-"+A);const U=(0,l.useBlockProps)({className:j.filter((e=>e)).join(" ").trim(),...y,ref:I}),G=(0,l.useInnerBlocksProps)(U),H=C||"nav";(0,s.useEffect)((()=>{C||p({tagName:"nav"})}),[C]),(0,s.useEffect)((()=>{_?.["data-gb-mobile-menu-type"]||p({htmlAttributes:{..._,"data-gb-mobile-menu-type":"full-overlay"}})}),[_?.["data-gb-mobile-menu-type"]]),(0,s.useEffect)((()=>{if(!S)return;const e=X(T(h));e&&F(e)}),[S,h]);const z={name:k,attributes:m,setAttributes:p,clientId:h,getStyleValue:v,onStyleChange:f},V=null!==(n=_?.["data-gb-mobile-menu-transition"])&&void 0!==n?n:"",W=null!==(r=_?.["data-gb-sub-menu-transition"])&&void 0!==r?r:"";return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(i.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.OpenPanel,{title:(0,o.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...z},(0,e.createElement)(D,{blocks:[x],clientId:h,allowedBlocks:Y,showAllLabel:(0,o.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(J,null),(0,e.createElement)(c.OpenPanel,{panelId:"settings",...z},!!N&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Z,{value:N?.attributes?.menu,onChange:e=>{q(N.clientId,{menu:e});const t=X(T(h));t&&F(t)}})),(0,e.createElement)(R.SelectControl,{label:(0,o.__)("Sub-menu type","generateblocks-pro"),value:w,options:[{label:(0,o.__)("Hover","generateblocks-pro"),value:"hover"},{label:(0,o.__)("Click Menu Item","generateblocks-pro"),value:"click"},{label:(0,o.__)("Click Toggle","generateblocks-pro"),value:"click-toggle"}],onChange:e=>p({subMenuType:e})}),(0,e.createElement)(R.SelectControl,{label:(0,o.__)("Sub-menu Transition","generateblocks-pro"),value:W,options:[{label:(0,o.__)("None","generateblocks-pro"),value:""},{label:(0,o.__)("Fade","generateblocks-pro"),value:"fade"},{label:(0,o.__)("Slide & fade up","generateblocks-pro"),value:"fade-slide-up"},{label:(0,o.__)("Slide & fade down","generateblocks-pro"),value:"fade-slide-down"},{label:(0,o.__)("Slide & fade left","generateblocks-pro"),value:"fade-slide-left"},{label:(0,o.__)("Slide & fade right","generateblocks-pro"),value:"fade-slide-right"}],onChange:e=>{const t={..._};e?t["data-gb-sub-menu-transition"]=e:(delete t["data-gb-sub-menu-transition"],f("--sub-menu-transition-speed","")),p({htmlAttributes:t})}}),""!==W&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(u.UnitControl,{units:["ms"],defaultUnitValue:"ms",label:(0,o.__)("Sub-menu Transition Speed","generateblocks-pro"),placeholder:"200ms",value:v("--sub-menu-transition-speed",""),onChange:e=>{f("--sub-menu-transition-speed",e)}}),"fade"!==W&&(0,e.createElement)(u.UnitControl,{label:(0,o.__)("Sub-menu Transition Distance","generateblocks-pro"),placeholder:"5px",value:v("--sub-menu-transition-distance",""),onChange:e=>{f("--sub-menu-transition-distance",e)}})),(0,e.createElement)(u.UnitControl,{label:(0,o.__)("Mobile breakpoint","generateblocks-pro"),value:null!==(g=_?.["data-gb-mobile-breakpoint"])&&void 0!==g?g:"",onChange:e=>{const t={..._};e?t["data-gb-mobile-breakpoint"]=e:delete t["data-gb-mobile-breakpoint"],p({htmlAttributes:t})}}),(0,e.createElement)(R.SelectControl,{label:(0,o.__)("Mobile Menu Type","generateblocks-pro"),value:null!==(d=_?.["data-gb-mobile-menu-type"])&&void 0!==d?d:"",options:[{label:(0,o.__)("Full overlay","generateblocks-pro"),value:"full-overlay"},{label:(0,o.__)("Partial overlay","generateblocks-pro"),value:"partial-overlay"}],onChange:e=>{const t={..._};e?t["data-gb-mobile-menu-type"]=e:delete t["data-gb-mobile-menu-type"],p({htmlAttributes:t})}}),"partial-overlay"===_?.["data-gb-mobile-menu-type"]&&(0,e.createElement)(R.TextControl,{label:(0,o.__)("Mobile Menu Anchor","generateblocks-pro"),help:(0,o.__)("The selector for the element the mobile menu will attach to the bottom of.","generateblocks-pro"),value:null!==(b=_?.["data-gb-menu-toggle-anchor"])&&void 0!==b?b:"",placeholder:"Calculate automatically",onChange:e=>{const t={..._};e?t["data-gb-menu-toggle-anchor"]=e:delete t["data-gb-menu-toggle-anchor"],p({htmlAttributes:t})}}),(0,e.createElement)(R.SelectControl,{label:(0,o.__)("Mobile Menu Transition","generateblocks-pro"),value:V,options:[{label:(0,o.__)("None","generateblocks-pro"),value:""},{label:(0,o.__)("Fade","generateblocks-pro"),value:"fade"},{label:(0,o.__)("Slide left","generateblocks-pro"),value:"slide-left"},{label:(0,o.__)("Slide right","generateblocks-pro"),value:"slide-right"},{label:(0,o.__)("Slide up","generateblocks-pro"),value:"slide-up"},{label:(0,o.__)("Slide down","generateblocks-pro"),value:"slide-down"},{label:(0,o.__)("Slide & fade left","generateblocks-pro"),value:"fade-slide-left"},{label:(0,o.__)("Slide & fade right","generateblocks-pro"),value:"fade-slide-right"},{label:(0,o.__)("Slide & fade up","generateblocks-pro"),value:"fade-slide-up"},{label:(0,o.__)("Slide & fade down","generateblocks-pro"),value:"fade-slide-down"}],onChange:e=>{const t={..._};e?t["data-gb-mobile-menu-transition"]=e:(delete t["data-gb-mobile-menu-transition"],f("--mobile-transition-speed","")),p({htmlAttributes:t})}}),""!==V&&(0,e.createElement)(u.UnitControl,{units:["ms"],defaultUnitValue:"ms",label:(0,o.__)("Mobile Menu Transition Speed","generateblocks-pro"),placeholder:"200ms",value:v("--mobile-transition-speed",""),onChange:e=>{f("--mobile-transition-speed",e)}}))),stylesTab:(0,e.createElement)(L,{attributes:m,setAttributes:p,shortcuts:{},onStyleChange:f})})),(0,e.createElement)($,{name:k,clientId:h},(0,e.createElement)(H,{...G})))}));const te=(0,r.compose)(T,i.withUniqueId)((function(t){const{attributes:n,setAttributes:r,getStyleValue:g,onStyleChange:d,clientId:b,name:m,context:p,isSelected:y}=t,{uniqueId:f}=n,{getBlockParentsByBlockName:v,getBlock:h}=(0,a.useSelect)((e=>e(l.store)),[]),k=(0,s.useMemo)((()=>{var e;return null!==(e=v(b,"generateblocks-pro/navigation",!0)?.[0])&&void 0!==e?e:""}),[b]),S=(0,s.useMemo)((()=>h(k)),[k,y]),E={name:m,attributes:n,setAttributes:r,clientId:b,getStyleValue:g,onStyleChange:d};return(0,s.useEffect)((()=>{const e=p?.["generateblocks-pro/menu-unique-id"],t="mi"+e.slice(2);f&&f===t||r({uniqueId:t})}),[f,p?.["generateblocks-pro/menu-unique-id"]]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(i.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.OpenPanel,{title:(0,o.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...E},(0,e.createElement)(D,{blocks:[S],clientId:b,allowedBlocks:Y,showAllLabel:(0,o.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(J,null),(0,e.createElement)(c.OpenPanel,{...E,panelId:"design",title:(0,o.__)("Style Shortcuts","generateblocks-pro")},(0,e.createElement)(M,{label:(0,o.__)("Background","generateblocks-pro"),colors:[{value:g("backgroundColor",""),onChange:e=>d("backgroundColor",e),tooltip:(0,o.__)("Background","generateblocks-pro")},{value:g("backgroundColor","&:is(:hover, :focus)"),onChange:e=>d("backgroundColor",e,"","&:is(:hover, :focus)"),tooltip:(0,o.__)("Hover","generateblocks-pro")},{value:g("backgroundColor","&:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus)"),onChange:e=>d("backgroundColor",e,"","&:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus)"),tooltip:(0,o.__)("Current","generateblocks-pro")}]}),(0,e.createElement)(M,{label:(0,o.__)("Text","generateblocks-pro"),colors:[{value:g("color",""),onChange:e=>d("color",e,"",""),tooltip:(0,o.__)("Text","generateblocks-pro")},{value:g("color","&:is(:hover, :focus)"),onChange:e=>d("color",e,"","&:is(:hover, :focus)"),tooltip:(0,o.__)("Hover","generateblocks-pro")},{value:g("color","&:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus)"),onChange:e=>d("color",e,"","&:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus)"),tooltip:(0,o.__)("Current","generateblocks-pro")}]}),(0,e.createElement)(u.UnitControl,{label:(0,o.__)("Font size","generateblocks-pro"),value:g("fontSize",""),onChange:e=>d("fontSize",e,"","")}),(0,e.createElement)(u.UnitControl,{label:(0,o.__)("Inline padding","generateblocks-pro"),value:g("paddingLeft",".gb-menu-link"),onChange:e=>{d("paddingLeft",e,"",".gb-menu-link"),d("paddingRight",e,"",".gb-menu-link")}}),(0,e.createElement)(u.UnitControl,{label:(0,o.__)("Block padding","generateblocks-pro"),value:g("paddingTop",".gb-menu-link"),onChange:e=>{d("paddingTop",e,"",".gb-menu-link"),d("paddingBottom",e,"",".gb-menu-link")}}),(0,e.createElement)(ee,null))),stylesTab:(0,e.createElement)(L,{attributes:n,setAttributes:r,shortcuts:{selectorShortcuts:I,visibleShortcuts:B},onStyleChange:d})})))})),ne=JSON.parse('{"UU":"generateblocks-pro/classic-menu-item"}');(0,t.registerBlockType)(ne.UU,{edit:te,save:()=>null,icon:(0,e.createElement)((function(){return(0,e.createElement)("svg",{viewBox:"0 0 24 24",className:"gblocks-block-icon",style:{fillRule:"evenodd",clipRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:2}},(0,e.createElement)("path",{d:"M8.55,5.705C8.55,6.037 8.281,6.305 7.95,6.305L5.15,6.305C4.819,6.305 4.55,6.037 4.55,5.705C4.55,5.375 4.819,5.106 5.15,5.106L7.95,5.106C8.281,5.106 8.55,5.375 8.55,5.705Z"}),(0,e.createElement)("path",{d:"M14.03,5.705C14.03,6.037 13.761,6.305 13.43,6.305L10.63,6.305C10.299,6.305 10.03,6.037 10.03,5.705C10.03,5.375 10.299,5.106 10.63,5.106L13.43,5.106C13.761,5.106 14.03,5.375 14.03,5.705Z"}),(0,e.createElement)("path",{d:"M19.509,5.705C19.509,6.037 19.24,6.305 18.909,6.305L16.109,6.305C15.778,6.305 15.509,6.037 15.509,5.705C15.509,5.375 15.778,5.106 16.109,5.106L18.909,5.106C19.24,5.106 19.509,5.375 19.509,5.705Z"}),(0,e.createElement)("path",{d:"M2.03,12.35L2.03,2.797C2.03,2.452 2.31,2.172 2.655,2.172L21.406,2.172C21.75,2.172 22.03,2.452 22.03,2.797L22.03,12.35L20.78,12.35L20.78,9.239L3.28,9.239L3.28,12.35L2.03,12.35ZM20.778,7.975L20.78,3.422L3.279,3.422L3.261,7.975L20.778,7.975ZM21.405,22.172L17.655,22.172L17.655,20.922L20.78,20.922L20.78,17.797L22.03,17.797L22.03,21.547C22.03,21.892 21.75,22.172 21.405,22.172ZM6.405,22.172L2.655,22.172C2.312,22.172 2.03,21.89 2.03,21.547L2.03,17.797L3.28,17.797L3.28,20.922L6.405,20.922L6.405,22.172Z",style:{fillOpacity:.3}}),(0,e.createElement)("path",{d:"M18.235,14.454L5.775,14.454C5.435,14.454 5.155,14.734 5.155,15.074C5.155,15.414 5.435,15.694 5.775,15.694L18.235,15.694C18.575,15.694 18.855,15.414 18.855,15.074C18.855,14.734 18.585,14.454 18.235,14.454ZM18.245,17.484L5.775,17.484C5.435,17.484 5.155,17.764 5.155,18.104C5.155,18.444 5.435,18.724 5.775,18.724L18.235,18.724C18.575,18.724 18.855,18.444 18.855,18.104L18.855,18.094C18.855,17.759 18.58,17.484 18.245,17.484ZM5.145,12.044C5.145,12.384 5.425,12.664 5.765,12.664L18.225,12.664C18.565,12.664 18.845,12.384 18.845,12.044C18.845,11.704 18.565,11.424 18.225,11.424L5.754,11.424C5.422,11.424 5.145,11.7 5.145,12.037L5.145,12.044Z",style:{fillOpacity:.3,fillRule:"nonzero"}}))}),null)})})()})();