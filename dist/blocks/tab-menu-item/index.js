(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var l in s)e.o(s,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:s[l]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.React,s=window.wp.blocks,l=window.wp.compose,r=window.wp.element,n=window.wp.blockEditor,o=window.wp.i18n,c=window.gbp.blockStyles,a=window.gbp.stylesBuilder,u=window.wp.data,i=(0,u.createReduxStore)("gbp-current-style",{reducer:a.currentStyleReducer,actions:a.currentStyleActions,selectors:a.currentStyleSelectors}),d=(0,u.createReduxStore)("gbp-styles",{reducer:a.styleReducer,actions:a.styleActions,selectors:a.styleSelectors}),b=(0,u.createReduxStore)("gbp-styles-at-rule",{reducer:a.atRuleReducer,actions:a.atRuleActions,selectors:a.atRuleSelectors}),y=(0,u.createReduxStore)("gbp-styles-nested-rule",{reducer:a.nestedRuleReducer,actions:a.nestedRuleActions,selectors:a.nestedRuleSelectors}),p=window.wp.apiFetch;var g=e.n(p);const f=window.wp.notices,m=window.wp.url,S=window.wp.coreData;var v;window.lodash;const h="undefined"!=typeof gbGlobalStylePermissions&&null!==(v=gbGlobalStylePermissions?.canManageStyles)&&void 0!==v&&v,w=window.wp.editPost;const R=(0,u.createReduxStore)("gbp-block-styles-current-style",{reducer:a.currentStyleReducer,actions:a.currentStyleActions,selectors:a.currentStyleSelectors}),A=(0,u.createReduxStore)("gbp-block-styles-at-rule",{reducer:a.atRuleReducer,actions:a.atRuleActions,selectors:a.atRuleSelectors}),k=(0,u.createReduxStore)("gbp-block-styles-nested-rule",{reducer:a.nestedRuleReducer,actions:a.nestedRuleActions,selectors:a.nestedRuleSelectors});function C(){const e=(0,u.useSelect)((e=>e(A).getAtRule())),{setAtRule:t}=(0,u.useDispatch)(A),s=(0,u.useSelect)((e=>e(k).getNestedRule())),{setNestedRule:l}=(0,u.useDispatch)(k),r=(0,c.useCurrentAtRule)(a.defaultAtRules),{setCurrentStyle:p}=(0,u.useDispatch)(R),v=(0,u.useSelect)((e=>e(R).currentStyle())),{deviceType:C,setDeviceType:L}=(0,c.useDeviceType)(),E=function(){const{setCurrentStyle:e}=(0,u.useDispatch)(i),{setStyles:t}=(0,u.useDispatch)(d),{createNotice:s,removeAllNotices:l}=(0,u.useDispatch)(f.store),{getEntityRecordEdits:r}=(0,u.useSelect)(S.store),{getSelectedBlock:c}=(0,u.useSelect)((e=>e(n.store)),[]),{setAtRule:a}=(0,u.useDispatch)(b),{setNestedRule:p}=(0,u.useDispatch)(y),{openGeneralSidebar:v}=(0,u.useDispatch)(w.store);return async(n,u={})=>{if(!h)return;const{classStyles:i,classPostId:d}=await async function(e){var t;const s=await g()({path:(0,m.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let l=null!==(t=s?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(l)&&0===l.length&&(l={}),{classStyles:l,classPostId:s?.response?.data?.postId}}(n);if(!d)return l("snackbar"),void s("error",(0,o.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,o.__)("%s does not exist.","generateblocks-pro"),n),{type:"snackbar"});a(""),p(""),v("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:d,name:n,classStyles:i,clientId:c()?.clientId,options:u}),u.nestedRule&&p(u.nestedRule),u.atRule&&a(u.atRule);const b=r("postType","gblocks_styles",d);t(b?.gb_style_data||i),l("snackbar"),s("info",(0,o.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,o.__)("Editing %s.","generateblocks-pro"),n),{type:"snackbar"})}}(),_=function(){const{setCurrentStyle:e}=(0,u.useDispatch)(i),{setStyles:t}=(0,u.useDispatch)(d),{setAtRule:s}=(0,u.useDispatch)(b),{setNestedRule:l}=(0,u.useDispatch)(y);return()=>{e({}),t({}),s(""),l("")}}();return{atRule:e,nestedRule:s,setAtRule:t,currentAtRule:r,setNestedRule:l,setDeviceType:L,deviceType:C,setCurrentStyle:p,currentStyle:v,getPreviewDevice:c.getPreviewDevice,setGlobalStyle:E,cancelEditGlobalStyle:_}}function L({attributes:e,setAttributes:s,shortcuts:l,onStyleChange:r}){const{atRule:n,setAtRule:o,nestedRule:u,setNestedRule:i,setDeviceType:d,getPreviewDevice:b,currentStyle:y,setGlobalStyle:p,cancelEditGlobalStyle:g}=C(),{styles:f,globalClasses:m=[]}=e,S=(0,a.getStylesObject)(f,n,u);return(0,t.createElement)(a.StylesBuilder,{currentSelector:y?.selector,styles:S,allStyles:f,onDeleteStyle:(e,t)=>{const l=(0,a.deleteStylesObjectKey)(f,e,t);s({styles:l})},nestedRule:u,atRule:n,onStyleChange:(e,t=null)=>r(e,t,n,u),onNestedRuleChange:e=>i(e),onAtRuleChange:e=>{o(e);const t=(0,a.getPreviewWidth)(e),s=b(t);s&&d(s)},onUpdateKey:(e,t,l)=>{const r=(0,a.updateStylesObjectKey)(f,e,t,l);s({styles:r})},selectorShortcuts:l.selectorShortcuts,visibleSelectors:l.visibleShortcuts,onEditStyle:p,cancelEditStyle:g,setLocalTab:e=>{sessionStorage.setItem(c.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:m})}const E=window.wp.components,_=window.wp.hooks,N=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function O(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function I(e,t,s=!1){const{styles:l={},uniqueId:r="",globalClasses:n=[]}=t,o=[];return s&&o.push(e),n.length>0&&o.push(...n),Object.keys(l).length>0&&o.push(`${e}-${r}`),o}const D=window.gbp.components,T={default:{items:[{label:"Hover",value:"&:hover"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"},{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"}]},interactions:{label:(0,o.__)("Interactions","generateblocks-pro"),items:[{label:"Hover",value:"&:hover"},{label:"Focus",value:"&:focus"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"}]},links:{label:(0,o.__)("Links","generateblocks-pro"),items:[{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"},{label:"Hovered & focused links",value:"a:is(:hover, :focus)"},{label:"Visited links",value:"a:visited"}]},pseudoElements:{label:(0,o.__)("Pseudo Elements","generateblocks-pro"),items:[{label:"Before",value:"&:before"},{label:"After",value:"&:after"}]}};function P({value:e,options:l=[],onChange:r,blockName:n}){var c;const a=null!==(c=(0,s.getBlockType)(n)?.attributes?.tagName?.enum)&&void 0!==c?c:[],u=l.length?l:a.map((e=>({label:e,value:e})));return u.length?(0,t.createElement)(E.SelectControl,{label:(0,o.__)("Tag Name","generateblocks-pro"),value:e,options:u,onChange:r}):null}const j=(0,l.compose)((function(e){return s=>{var l,o,c,a;const{attributes:i,setAttributes:d,context:b}=s,{htmlAttributes:y={},uniqueId:p,className:g,align:f}=i,m=(0,u.useSelect)((e=>e("core/editor").isSavingPost())),{style:S="",href:v,...h}=y,w=Object.keys(h).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(h[t]),e)),{}),[R,A]=(0,r.useState)(S);var k,C,L,I;(0,r.useEffect)((()=>{!async function(){const e=await(0,_.applyFilters)("generateblocks.editor.htmlAttributes.style",S,{...s});A(e)}()}),[S,b,m]),k=()=>{const e=["alignwide","alignfull"],t=(g?.split(" ")||[]).filter((t=>!e.includes(t)));f&&t.push("align"+f),d({className:t.join(" ")})},C=[f],I=(L=(0,t.useRef)(!0)).current?(L.current=!1,!0):L.current,(0,t.useEffect)((function(){if(!I)return k()}),C);const D="string"==typeof R?R.split(";").reduce(((e,t)=>{const s=t.indexOf(":");if(-1===s)return e;let l=t.slice(0,s).trim();const r=t.slice(s+1).trim();return l&&r&&(l.startsWith("--")||(l=l.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[l]=r),e}),{}):"",T={...w,style:D,"data-gb-id":p,"data-context-post-id":null!==(l=null!==(o=b?.postId)&&void 0!==o?o:b?.["generateblocks/loopIndex"])&&void 0!==l?l:0,"data-align":f||void 0},P=(0,r.useMemo)((()=>Array.isArray(y)?{}:y),[JSON.stringify(y)]);return(0,r.useEffect)((()=>{const e={...y};Object.keys(e).forEach((t=>{const s=t.startsWith("data-"),l=e[t];N.includes(t)||""!==l||s||"alt"===t||delete e[t],"string"!=typeof l&&"boolean"!=typeof l&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const s=Object.keys(e),l=Object.keys(t);if(s.length!==l.length)return!1;for(const l of s)if(e[l]!==t[l])return!1;return!0}(e,y)||d({htmlAttributes:e})}),[JSON.stringify(y)]),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(e,{...s,editorHtmlAttributes:T,htmlAttributes:P}),(0,t.createElement)(n.InspectorAdvancedControls,null,(0,t.createElement)(E.TextControl,{label:"HTML ID",value:null!==(c=y.id)&&void 0!==c?c:"",onChange:e=>{d({htmlAttributes:{...y,id:e}})},onBlur:()=>{y.id&&d({htmlAttributes:{...y,id:O(y.id)}})}}),(0,t.createElement)(E.TextControl,{label:"ARIA Label",value:null!==(a=y["aria-label"])&&void 0!==a?a:"",onChange:e=>{d({htmlAttributes:{...y,"aria-label":e}})}})))}}),(function(e){return s=>{const{attributes:l,name:n,setAttributes:o,isSelected:u,clientId:i}=s,{uniqueId:d,styles:b,css:y}=l,{atRule:p,deviceType:g,setAtRule:f,currentStyle:m,setCurrentStyle:S,setNestedRule:v}=C(),h=(0,c.useSetStyles)(s,{cleanStylesObject:a.cleanStylesObject}),w=(0,r.useMemo)((()=>d?(0,c.getSelector)(n,d):""),[n,d]),R=Array.isArray(b)?{}:b;return(0,c.useAtRuleEffect)({deviceType:g,atRule:p,setAtRule:f,defaultAtRules:a.defaultAtRules,isSelected:u,getPreviewWidth:a.getPreviewWidth}),(0,c.useGenerateCSSEffect)({selector:w,styles:R,setAttributes:o,getCss:a.getCss,getSelector:c.getSelector,isSelected:u,blockCss:y,clientId:i}),(0,c.useStyleSelectorEffect)({isSelected:u,currentStyle:m,selector:w,setCurrentStyle:S,setNestedRule:v}),(0,c.useDecodeStyleKeys)({styles:b,setAttributes:o}),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(c.Style,{selector:w,getCss:a.getCss,styles:R,clientId:i,name:n}),(0,t.createElement)(e,{...s,selector:w,onStyleChange:function(e,t="",s="",l=""){const r="object"==typeof e?e:{[e]:t},n=(0,c.buildChangedStylesObject)(r,s,l);h(n)},getStyleValue:function(e,t="",s=""){var l,r,n,o;return s?t?null!==(n=b?.[s]?.[t]?.[e])&&void 0!==n?n:"":null!==(r=b?.[s]?.[e])&&void 0!==r?r:"":t?null!==(o=b?.[t]?.[e])&&void 0!==o?o:"":null!==(l=b?.[e])&&void 0!==l?l:""},styles:R}))}}),c.withUniqueId)((function(e){const{attributes:s,setAttributes:l,onStyleChange:a,getStyleValue:u,editorHtmlAttributes:i={},htmlAttributes:d,styles:b,name:y,clientId:p}=e,{tagName:g,uniqueId:f}=s,m=(0,r.useRef)(),[S,v]=(0,r.useState)(0),[h,w]=(0,r.useState)(!1);(0,r.useEffect)((()=>{const e=m.current?.closest(".gb-tabs");if(!e)return;const t=()=>{v(e.getAttribute("data-opened-tab"))},s=new MutationObserver(t);return s.observe(e,{attributes:!0,attributeFilter:["data-opened-tab"]}),t(),()=>{s.disconnect()}}),[m]),(0,r.useEffect)((()=>{const e=m.current;if(!e)return;const t=()=>{e.classList.contains("is-selected")||e.classList.contains("has-child-selected")?w(!0):w(!1)},s=new MutationObserver(t);return s.observe(e,{attributes:!0,attributeFilter:["class"]}),t(),()=>{s.disconnect()}}),[m]),(0,r.useEffect)((()=>{if(!h||!S)return;const e=m.current?.closest(".gb-tabs");e&&e.getAttribute("data-opened-tab")!==R&&e.setAttribute("data-opened-tab",R)}),[h]);const R=(0,r.useMemo)((()=>{if(!m?.current)return;const e=m.current.closest(".gb-tabs__menu");return e?(Array.from(e.children).filter((e=>e.classList.contains("gb-tabs__menu-item"))).indexOf(m.current)+1).toString():-1}),[m?.current]),A=I("gb-tabs__menu-item",{...s,styles:b},!0),k=(0,r.useMemo)((()=>{let e=A;return S===R?e.push("gb-block-is-current"):e=e.filter((e=>"gb-block-is-current"!==e)),e}),[JSON.stringify(A),S,R]),C=(0,n.useBlockProps)({className:k.filter(Boolean).join(" ").trim(),...i,ref:m}),E=(0,n.useInnerBlocksProps)(C),_=g||"div",N=(0,r.useMemo)((()=>{const e=[{label:(0,o.__)("Current","generateblocks-pro"),value:"&:is(.gb-block-is-current, .gb-block-is-current:hover, .gb-block-is-current:focus)"}];return T.interactions.items&&T.interactions.items.push({label:(0,o.__)("Current","generateblocks-pro"),value:"&:is(.gb-block-is-current, .gb-block-is-current:hover, .gb-block-is-current:focus)"}),{selectorShortcuts:T,visibleShortcuts:e}}),[]);(0,r.useEffect)((()=>{g||l({tagName:"div"})}),[g]),(0,r.useEffect)((()=>{var e;if(!f)return;const t=null!==(e=d?.id)&&void 0!==e?e:"",s=t&&t.startsWith("gb-tab-menu-item-")&&t!==`gb-tab-menu-item-${f}`;t&&!s||l({htmlAttributes:{...d,id:`gb-tab-menu-item-${f}`}})}),[f,d?.id]);const O={name:y,attributes:s,setAttributes:l,clientId:p,getStyleValue:u,onStyleChange:a};return(0,t.createElement)(t.Fragment,null,(0,t.createElement)(n.InspectorControls,null,(0,t.createElement)(c.BlockStyles,{settingsTab:(0,t.createElement)(D.OpenPanel,{...O,panelId:"settings"},(0,t.createElement)(P,{blockName:y,value:g,onChange:e=>{l({tagName:e})}})),stylesTab:(0,t.createElement)(L,{attributes:s,setAttributes:l,shortcuts:N,onStyleChange:a})})),(0,t.createElement)(_,{...E}))})),x=JSON.parse('{"UU":"generateblocks-pro/tab-menu-item"}');(0,s.registerBlockType)(x.UU,{edit:j,save:function({attributes:e}){const{tagName:s,htmlAttributes:l={}}=e,r=I("gb-tabs__menu-item",e,!0),o=n.useBlockProps.save({className:r.join(" ").trim(),...l});return(0,t.createElement)(s,{...n.useInnerBlocksProps.save(o)})},icon:function(){return(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",className:"gblocks-block-icon","aria-hidden":"true",focusable:"false"},(0,t.createElement)("path",{d:"M18.23,13.26L5.77,13.26C5.43,13.26 5.15,13.54 5.15,13.88C5.15,14.22 5.43,14.5 5.77,14.5L18.23,14.5C18.57,14.5 18.85,14.22 18.85,13.88C18.85,13.54 18.58,13.26 18.23,13.26ZM18.23,16.29L5.77,16.29C5.43,16.29 5.15,16.57 5.15,16.91C5.15,17.25 5.43,17.53 5.77,17.53L18.23,17.53C18.57,17.53 18.85,17.25 18.85,16.91L18.85,16.9C18.85,16.565 18.575,16.29 18.24,16.29L18.23,16.29ZM5.14,10.85C5.14,11.19 5.42,11.47 5.76,11.47L18.22,11.47C18.56,11.47 18.84,11.19 18.84,10.85C18.84,10.51 18.56,10.23 18.22,10.23L5.77,10.23L5.753,10.23C5.417,10.23 5.14,10.506 5.14,10.843L5.14,10.85Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M21.99,5.76L21.99,6.997L8.839,6.997L8.849,7.001L20.75,7.01L8.87,7.01L8.849,7.001L3.25,6.997L8.839,6.997C8.487,6.965 8.25,6.703 8.25,6.39L8.25,3.25L3.25,3.25L3.25,6.997L2,6.997L2,2.62C2,2.307 2.237,2.045 2.62,2L9.5,2L9.5,5.76L21.99,5.76Z"}),(0,t.createElement)("path",{d:"M21.99,6.997L21.99,21.38C21.99,21.72 21.71,22 21.37,22L2.62,22C2.28,22 2,21.72 2,21.37L2,6.997L3.25,6.997L3.25,20.75L20.75,20.75L20.75,7.01L8.87,7.01C8.827,7.01 8.785,7.006 8.744,6.997L21.99,6.997Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M9.5,2L21.382,2C21.758,2.044 21.99,2.3 21.99,2.608L21.99,5.76L20.75,5.76L20.75,3.25L15.75,3.25L15.75,5.76L14.5,5.76L14.5,3.25L9.5,3.25L9.5,2Z",style:{fillOpacity:.3}}))}})})();