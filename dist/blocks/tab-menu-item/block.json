{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "generateblocks-pro/tab-menu-item", "title": "Tab Menu <PERSON>em", "category": "generateblocks", "icon": "star", "description": "The clickable menu item for a tab.", "keywords": ["alert", "message"], "version": "1.0.0", "textdomain": "generateblocks-pro", "attributes": {"uniqueId": {"type": "string", "default": ""}, "tagName": {"type": "string", "default": "", "enum": ["div", "button", "ul", "ol", "li"]}, "styles": {"type": "object", "default": {}}, "css": {"type": "string", "default": ""}, "globalClasses": {"type": "array", "default": []}, "htmlAttributes": {"type": "object", "default": {}}, "tabItemOpen": {"type": "boolean", "default": false}}, "supports": {"align": false, "className": false}, "parent": ["generateblocks-pro/tabs-menu"], "editorScript": ["file:./index.js"]}