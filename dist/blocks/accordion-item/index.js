(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var l in s)e.o(s,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:s[l]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.React,s=window.wp.blocks,l=window.wp.compose,r=window.wp.components,n=window.wp.i18n,o=window.wp.element,c=window.wp.blockEditor,a=window.gbp.blockStyles,i=window.gbp.stylesBuilder,u=window.wp.data,d=(0,u.createReduxStore)("gbp-current-style",{reducer:i.currentStyleReducer,actions:i.currentStyleActions,selectors:i.currentStyleSelectors}),b=(0,u.createReduxStore)("gbp-styles",{reducer:i.styleReducer,actions:i.styleActions,selectors:i.styleSelectors}),p=(0,u.createReduxStore)("gbp-styles-at-rule",{reducer:i.atRuleReducer,actions:i.atRuleActions,selectors:i.atRuleSelectors}),y=(0,u.createReduxStore)("gbp-styles-nested-rule",{reducer:i.nestedRuleReducer,actions:i.nestedRuleActions,selectors:i.nestedRuleSelectors}),g=window.wp.apiFetch;var S=e.n(g);const f=window.wp.notices,m=window.wp.url,v=window.wp.coreData;var h;window.lodash;const R="undefined"!=typeof gbGlobalStylePermissions&&null!==(h=gbGlobalStylePermissions?.canManageStyles)&&void 0!==h&&h,w=window.wp.editPost;const C=(0,u.createReduxStore)("gbp-block-styles-current-style",{reducer:i.currentStyleReducer,actions:i.currentStyleActions,selectors:i.currentStyleSelectors}),k=(0,u.createReduxStore)("gbp-block-styles-at-rule",{reducer:i.atRuleReducer,actions:i.atRuleActions,selectors:i.atRuleSelectors}),A=(0,u.createReduxStore)("gbp-block-styles-nested-rule",{reducer:i.nestedRuleReducer,actions:i.nestedRuleActions,selectors:i.nestedRuleSelectors});function E(){const e=(0,u.useSelect)((e=>e(k).getAtRule())),{setAtRule:t}=(0,u.useDispatch)(k),s=(0,u.useSelect)((e=>e(A).getNestedRule())),{setNestedRule:l}=(0,u.useDispatch)(A),r=(0,a.useCurrentAtRule)(i.defaultAtRules),{setCurrentStyle:o}=(0,u.useDispatch)(C),g=(0,u.useSelect)((e=>e(C).currentStyle())),{deviceType:h,setDeviceType:E}=(0,a.useDeviceType)(),L=function(){const{setCurrentStyle:e}=(0,u.useDispatch)(d),{setStyles:t}=(0,u.useDispatch)(b),{createNotice:s,removeAllNotices:l}=(0,u.useDispatch)(f.store),{getEntityRecordEdits:r}=(0,u.useSelect)(v.store),{getSelectedBlock:o}=(0,u.useSelect)((e=>e(c.store)),[]),{setAtRule:a}=(0,u.useDispatch)(p),{setNestedRule:i}=(0,u.useDispatch)(y),{openGeneralSidebar:g}=(0,u.useDispatch)(w.store);return async(c,u={})=>{if(!R)return;const{classStyles:d,classPostId:b}=await async function(e){var t;const s=await S()({path:(0,m.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let l=null!==(t=s?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(l)&&0===l.length&&(l={}),{classStyles:l,classPostId:s?.response?.data?.postId}}(c);if(!b)return l("snackbar"),void s("error",(0,n.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,n.__)("%s does not exist.","generateblocks-pro"),c),{type:"snackbar"});a(""),i(""),g("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:b,name:c,classStyles:d,clientId:o()?.clientId,options:u}),u.nestedRule&&i(u.nestedRule),u.atRule&&a(u.atRule);const p=r("postType","gblocks_styles",b);t(p?.gb_style_data||d),l("snackbar"),s("info",(0,n.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,n.__)("Editing %s.","generateblocks-pro"),c),{type:"snackbar"})}}(),_=function(){const{setCurrentStyle:e}=(0,u.useDispatch)(d),{setStyles:t}=(0,u.useDispatch)(b),{setAtRule:s}=(0,u.useDispatch)(p),{setNestedRule:l}=(0,u.useDispatch)(y);return()=>{e({}),t({}),s(""),l("")}}();return{atRule:e,nestedRule:s,setAtRule:t,currentAtRule:r,setNestedRule:l,setDeviceType:E,deviceType:h,setCurrentStyle:o,currentStyle:g,getPreviewDevice:a.getPreviewDevice,setGlobalStyle:L,cancelEditGlobalStyle:_}}function L({attributes:e,setAttributes:s,shortcuts:l,onStyleChange:r}){const{atRule:n,setAtRule:o,nestedRule:c,setNestedRule:u,setDeviceType:d,getPreviewDevice:b,currentStyle:p,setGlobalStyle:y,cancelEditGlobalStyle:g}=E(),{styles:S,globalClasses:f=[]}=e,m=(0,i.getStylesObject)(S,n,c);return(0,t.createElement)(i.StylesBuilder,{currentSelector:p?.selector,styles:m,allStyles:S,onDeleteStyle:(e,t)=>{const l=(0,i.deleteStylesObjectKey)(S,e,t);s({styles:l})},nestedRule:c,atRule:n,onStyleChange:(e,t=null)=>r(e,t,n,c),onNestedRuleChange:e=>u(e),onAtRuleChange:e=>{o(e);const t=(0,i.getPreviewWidth)(e),s=b(t);s&&d(s)},onUpdateKey:(e,t,l)=>{const r=(0,i.updateStylesObjectKey)(S,e,t,l);s({styles:r})},selectorShortcuts:l.selectorShortcuts,visibleSelectors:l.visibleShortcuts,onEditStyle:y,cancelEditStyle:g,setLocalTab:e=>{sessionStorage.setItem(a.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:f})}const _=window.wp.hooks,N=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function D(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function I(e,t,s=!1){const{styles:l={},uniqueId:r="",globalClasses:n=[]}=t,o=[];return s&&o.push(e),n.length>0&&o.push(...n),Object.keys(l).length>0&&o.push(`${e}-${r}`),o}const O=window.gbp.components,T={default:{items:[{label:"Hover",value:"&:hover"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"},{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"}]},interactions:{label:(0,n.__)("Interactions","generateblocks-pro"),items:[{label:"Hover",value:"&:hover"},{label:"Focus",value:"&:focus"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"}]},links:{label:(0,n.__)("Links","generateblocks-pro"),items:[{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"},{label:"Hovered & focused links",value:"a:is(:hover, :focus)"},{label:"Visited links",value:"a:visited"}]},pseudoElements:{label:(0,n.__)("Pseudo Elements","generateblocks-pro"),items:[{label:"Before",value:"&:before"},{label:"After",value:"&:after"}]}};function P({value:e,options:l=[],onChange:o,blockName:c}){var a;const i=null!==(a=(0,s.getBlockType)(c)?.attributes?.tagName?.enum)&&void 0!==a?a:[],u=l.length?l:i.map((e=>({label:e,value:e})));return u.length?(0,t.createElement)(r.SelectControl,{label:(0,n.__)("Tag Name","generateblocks-pro"),value:e,options:u,onChange:o}):null}const j=(0,l.compose)((function(e){return s=>{var l,n,a,i;const{attributes:d,setAttributes:b,context:p}=s,{htmlAttributes:y={},uniqueId:g,className:S,align:f}=d,m=(0,u.useSelect)((e=>e("core/editor").isSavingPost())),{style:v="",href:h,...R}=y,w=Object.keys(R).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(R[t]),e)),{}),[C,k]=(0,o.useState)(v);var A,E,L,I;(0,o.useEffect)((()=>{!async function(){const e=await(0,_.applyFilters)("generateblocks.editor.htmlAttributes.style",v,{...s});k(e)}()}),[v,p,m]),A=()=>{const e=["alignwide","alignfull"],t=(S?.split(" ")||[]).filter((t=>!e.includes(t)));f&&t.push("align"+f),b({className:t.join(" ")})},E=[f],I=(L=(0,t.useRef)(!0)).current?(L.current=!1,!0):L.current,(0,t.useEffect)((function(){if(!I)return A()}),E);const O="string"==typeof C?C.split(";").reduce(((e,t)=>{const s=t.indexOf(":");if(-1===s)return e;let l=t.slice(0,s).trim();const r=t.slice(s+1).trim();return l&&r&&(l.startsWith("--")||(l=l.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[l]=r),e}),{}):"",T={...w,style:O,"data-gb-id":g,"data-context-post-id":null!==(l=null!==(n=p?.postId)&&void 0!==n?n:p?.["generateblocks/loopIndex"])&&void 0!==l?l:0,"data-align":f||void 0},P=(0,o.useMemo)((()=>Array.isArray(y)?{}:y),[JSON.stringify(y)]);return(0,o.useEffect)((()=>{const e={...y};Object.keys(e).forEach((t=>{const s=t.startsWith("data-"),l=e[t];N.includes(t)||""!==l||s||"alt"===t||delete e[t],"string"!=typeof l&&"boolean"!=typeof l&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const s=Object.keys(e),l=Object.keys(t);if(s.length!==l.length)return!1;for(const l of s)if(e[l]!==t[l])return!1;return!0}(e,y)||b({htmlAttributes:e})}),[JSON.stringify(y)]),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(e,{...s,editorHtmlAttributes:T,htmlAttributes:P}),(0,t.createElement)(c.InspectorAdvancedControls,null,(0,t.createElement)(r.TextControl,{label:"HTML ID",value:null!==(a=y.id)&&void 0!==a?a:"",onChange:e=>{b({htmlAttributes:{...y,id:e}})},onBlur:()=>{y.id&&b({htmlAttributes:{...y,id:D(y.id)}})}}),(0,t.createElement)(r.TextControl,{label:"ARIA Label",value:null!==(i=y["aria-label"])&&void 0!==i?i:"",onChange:e=>{b({htmlAttributes:{...y,"aria-label":e}})}})))}}),(function(e){return s=>{const{attributes:l,name:r,setAttributes:n,isSelected:c,clientId:u}=s,{uniqueId:d,styles:b,css:p}=l,{atRule:y,deviceType:g,setAtRule:S,currentStyle:f,setCurrentStyle:m,setNestedRule:v}=E(),h=(0,a.useSetStyles)(s,{cleanStylesObject:i.cleanStylesObject}),R=(0,o.useMemo)((()=>d?(0,a.getSelector)(r,d):""),[r,d]),w=Array.isArray(b)?{}:b;return(0,a.useAtRuleEffect)({deviceType:g,atRule:y,setAtRule:S,defaultAtRules:i.defaultAtRules,isSelected:c,getPreviewWidth:i.getPreviewWidth}),(0,a.useGenerateCSSEffect)({selector:R,styles:w,setAttributes:n,getCss:i.getCss,getSelector:a.getSelector,isSelected:c,blockCss:p,clientId:u}),(0,a.useStyleSelectorEffect)({isSelected:c,currentStyle:f,selector:R,setCurrentStyle:m,setNestedRule:v}),(0,a.useDecodeStyleKeys)({styles:b,setAttributes:n}),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(a.Style,{selector:R,getCss:i.getCss,styles:w,clientId:u,name:r}),(0,t.createElement)(e,{...s,selector:R,onStyleChange:function(e,t="",s="",l=""){const r="object"==typeof e?e:{[e]:t},n=(0,a.buildChangedStylesObject)(r,s,l);h(n)},getStyleValue:function(e,t="",s=""){var l,r,n,o;return s?t?null!==(n=b?.[s]?.[t]?.[e])&&void 0!==n?n:"":null!==(r=b?.[s]?.[e])&&void 0!==r?r:"":t?null!==(o=b?.[t]?.[e])&&void 0!==o?o:"":null!==(l=b?.[e])&&void 0!==l?l:""},styles:w}))}}),a.withUniqueId)((function(e){var s;const{attributes:l,setAttributes:i,context:u,onStyleChange:d,getStyleValue:b,editorHtmlAttributes:p={},styles:y,name:g,clientId:S}=e,{openByDefault:f,tagName:m}=l,v=(0,o.useRef)(),h=null!==(s=u["generateblocks/accordion/htmlAttributes"]?.["data-accordion-multiple-open"])&&void 0!==s&&s,R=I("gb-accordion__item",{...l,styles:y},!0);f&&R.push("gb-accordion__item-open"),(0,o.useEffect)((()=>{const e=v.current;if(!e)return;const t=()=>{if(e.classList.contains("is-selected")||e.classList.contains("has-child-selected")){const e=v.current,t=e.parentElement.querySelectorAll(".gb-accordion__item");e.setAttribute("data-accordion-is-open","true"),e.querySelector(".gb-accordion__toggle").classList.add("gb-block-is-current"),h||t.forEach((t=>{t!==e&&(t.removeAttribute("data-accordion-is-open"),t.classList.remove("gb-accordion__item-open"),t.querySelector(".gb-accordion__toggle").classList.remove("gb-block-is-current"))}))}},s=new MutationObserver(t);return s.observe(e,{attributes:!0,attributeFilter:["class"]}),t(),()=>{s.disconnect()}}),[v,h]);const w=(0,c.useBlockProps)({className:R.filter((e=>e)).join(" ").trim(),...p,ref:v}),C=(0,c.useInnerBlocksProps)(w,{allowedBlocks:["generateblocks-pro/accordion-item-content","generateblocks-pro/accordion-toggle"],renderAppender:!1}),k=m||"div",A=(0,o.useMemo)((()=>{const e=[{label:(0,n.__)("Links","generateblocks"),value:"a"}];return{selectorShortcuts:T,visibleShortcuts:e}}),[]);(0,o.useEffect)((()=>{m||i({tagName:"div"})}),[m]);const E={name:g,attributes:l,setAttributes:i,clientId:S,getStyleValue:b,onStyleChange:d};return(0,t.createElement)(t.Fragment,null,(0,t.createElement)(c.InspectorControls,null,(0,t.createElement)(a.BlockStyles,{settingsTab:(0,t.createElement)(O.OpenPanel,{...E,panelId:"settings"},(0,t.createElement)(r.ToggleControl,{label:(0,n.__)("Open by default","generateblocks-pro"),checked:!!f,onChange:e=>i({openByDefault:e})}),(0,t.createElement)(P,{blockName:g,value:m,onChange:e=>{i({tagName:e})}})),stylesTab:(0,t.createElement)(L,{attributes:l,setAttributes:i,shortcuts:A,onStyleChange:d})})),(0,t.createElement)(k,{...C}))})),B=JSON.parse('{"UU":"generateblocks-pro/accordion-item"}');(0,s.registerBlockType)(B.UU,{edit:j,save:function({attributes:e}){const{tagName:s,htmlAttributes:l={},openByDefault:r}=e,n=I("gb-accordion__item",e,!0);r&&n.push("gb-accordion__item-open");const o=c.useBlockProps.save({className:n.join(" ").trim(),...l});return(0,t.createElement)(s,{...c.useInnerBlocksProps.save(o)})},icon:function(){return(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",className:"gblocks-block-icon","aria-hidden":"true",focusable:"false"},(0,t.createElement)("path",{d:"M21.376,12.137L2.626,12.137C2.283,12.137 2,11.855 2,11.512C2,11.512 2,11.511 2.001,11.511L2.001,5.695C2.001,5.35 2.281,5.07 2.626,5.07L21.377,5.07C21.721,5.07 22.001,5.35 22.001,5.695L22.001,11.511C22.001,11.857 21.721,12.137 21.376,12.137ZM3.25,10.886L20.751,10.886L20.751,6.32L3.25,6.32L3.25,10.886ZM21.376,21.918L2.626,21.918C2.282,21.918 2,21.636 2,21.292C2,20.949 2.282,20.667 2.626,20.667L21.377,20.667C21.719,20.667 22.002,20.95 22.002,21.293C22.002,21.636 21.719,21.918 21.376,21.918C21.376,21.918 21.376,21.918 21.376,21.918ZM21.376,18.765L2.626,18.765C2.283,18.765 2.001,18.483 2.001,18.14C2.001,17.797 2.283,17.515 2.626,17.515L21.377,17.515C21.719,17.515 22.001,17.797 22.001,18.14C22.001,18.483 21.719,18.765 21.376,18.765C21.376,18.765 21.376,18.765 21.376,18.765ZM21.376,15.613L2.626,15.613C2.283,15.613 2.001,15.331 2.001,14.988C2.001,14.645 2.283,14.363 2.626,14.363L21.377,14.363C21.719,14.363 22.001,14.645 22.001,14.988C22.001,15.331 21.719,15.613 21.376,15.613C21.376,15.613 21.376,15.613 21.376,15.613Z",style:{fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M17.831,9.944C17.693,9.944 17.561,9.889 17.463,9.791L15.901,8.229L16.638,7.492L17.831,8.686L19.026,7.492L19.762,8.229L18.2,9.792C18.102,9.889 17.969,9.944 17.831,9.944Z",style:{fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M19.352,2L19.764,2.412L18.201,3.975C18.103,4.072 17.97,4.127 17.833,4.127C17.694,4.127 17.562,4.072 17.465,3.974L15.902,2.412L16.314,2L16.964,2L17.833,2.869L18.702,2L19.352,2Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M22.002,2L22.002,5.694C22.002,6.04 21.722,6.32 21.377,6.32L2.627,6.32C2.284,6.32 2.001,6.038 2.001,5.695C2.001,5.695 2.001,5.694 2.002,5.694L2.002,2L3.251,2L3.251,5.069L20.752,5.069L20.752,2L22.002,2Z",style:{fillOpacity:.3,fillRule:"nonzero"}}))}})})();