(()=>{var e={20493:(e,t,l)=>{"use strict";function r(e){if("Tab"!==e.key&&9!==e.keyCode)return;e.preventDefault();const t=e.currentTarget,l=t.querySelectorAll('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]'),r=Array.from(l).filter((e=>null!==e.offsetParent&&"hidden"!==getComputedStyle(e).visibility&&"none"!==getComputedStyle(e).display));if(0===r.length)return;const n=r[0],o=r[r.length-1],a=document.activeElement;if(t.contains(a))if(e.shiftKey)if(a===n)o.focus();else{const e=r.indexOf(a);e>0&&r[e-1].focus()}else if(a===o)n.focus();else{const e=r.indexOf(a);e<r.length-1&&r[e+1].focus()}else n.focus()}function n(e){const t=e.querySelectorAll(".gb-menu-toggle"),l=e.querySelector(".gb-menu-container"),n=l.querySelector(".gb-menu"),o=n.querySelectorAll(".menu-item"),a=n.querySelectorAll(".menu-item > a"),i=n.querySelectorAll(".gb-submenu-toggle"),c=e.closest("body");requestAnimationFrame((()=>{c.removeAttribute("data-gb-menu-open"),e.classList.remove("gb-navigation--open"),l&&(l.classList.remove("gb-menu-container--toggled"),l.removeEventListener("keydown",r)),t.forEach((e=>{e&&(e.classList.remove("gb-menu-toggle--toggled"),e.ariaExpanded="false",(e.offsetHeight>0||e.offsetWidth>0)&&e.focus())})),o?.length>0&&o.forEach((e=>{e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open")})),a?.length>0&&a.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")})),i?.length>0&&i.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")}))}))}function o(e){const t=e.querySelectorAll(".gb-menu-toggle"),l=e.querySelector(".gb-menu-container"),n=e.getAttribute("data-gb-mobile-menu-type"),o=l.querySelector(".gb-menu-toggle:not(.gb-menu-toggle--clone)"),a=o||"full-overlay"!==n?null:l.querySelector("*"),i=e.closest("body");let c=!1;requestAnimationFrame((()=>{if(e.classList.add("gb-navigation--open"),i.setAttribute("data-gb-menu-open",n),t.forEach((e=>{if(e&&(e.classList.add("gb-menu-toggle--toggled"),e.ariaExpanded="true",!o&&l&&"full-overlay"===n)){a&&(a.style.opacity="0");const t=e.closest(".editor-styles-wrapper"),r=l.querySelector(".gb-menu-toggle--clone");if(t&&r){const t=e.attributes;for(const e of t)r.setAttribute(e.name,e.value);r.innerHTML=e.innerHTML,r.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),c=!0}else if(!r){const t=e.cloneNode(!0);t.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),l.insertAdjacentElement("afterbegin",t),c=!0}}})),c&&a?requestAnimationFrame((()=>{!function(e,t=()=>{}){const l=e.querySelector(".gb-menu-container .gb-menu-toggle");if(l){var r,n;const o=window.getComputedStyle(l),a=null!==(r=parseInt(o?.top,10))&&void 0!==r?r:0,i=null!==(n=parseInt(o?.height,10))&&void 0!==n?n:0;requestAnimationFrame((()=>{e.style.setProperty("--gb-menu-toggle-offset",i+2*a+"px"),t()}))}}(e,(()=>{a.style.opacity=""}))})):a&&"0"===a.style.opacity&&(a.style.opacity=""),l){l.classList.add("gb-menu-container--toggled");const e=l.querySelector('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]');e?e.focus():l.focus(),l.addEventListener("keydown",r)}})),"partial-overlay"===n&&function(e){const t=(l=function(e){var t;const l=null!==(t=e.getAttribute("data-gb-menu-toggle-anchor"))&&void 0!==t?t:"";let r=".gb-navigation";return l?r=l:e.closest(".gb-site-header")&&(r=".gb-site-header"),e.closest(r)}(e))?l.getBoundingClientRect().bottom:0;var l;requestAnimationFrame((()=>e.style.setProperty("--gb-menu-offset",t+"px")))}(e)}function a(e,t=null){if(!e)return;const l=e.querySelectorAll(".menu-item.gb-sub-menu--open");l&&Array.from(l).filter((e=>!e.contains(t))).forEach((e=>{const t=e.querySelector("a"),l=e.querySelector(".gb-submenu-toggle");e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open"),e.setAttribute("aria-current","false"),t&&t.hasAttribute("aria-expanded")&&t.setAttribute("aria-expanded","false"),l&&l.hasAttribute("aria-expanded")&&l.setAttribute("aria-expanded","false")}))}function i(e,t=!1){if(e){t&&t.preventDefault();const l=e.closest(".gb-navigation"),r=e.closest(".menu-item"),n="true"===e.getAttribute("aria-expanded");a(l,r),e.setAttribute("aria-expanded",n?"false":"true"),r.classList.toggle("current-menu-item"),r.classList.toggle("gb-sub-menu--open")}}function c(e,t=!1){if(e){t&&t.preventDefault();const l=t.type,r=e.closest(".gb-menu-container--toggled"),n=e.closest(".gb-menu--hover");if("click"===l&&n&&!r)return;const o=e.closest(".menu-item"),a="true"===e.getAttribute("aria-expanded");e.setAttribute("aria-expanded",a?"false":"true"),o.classList.toggle("current-menu-item"),o.classList.toggle("gb-sub-menu--open")}}function s(e){e&&e.forEach((e=>{var t;const l=e.querySelector(".gb-menu-toggle"),r=e.querySelector(".gb-menu-container"),o=null!==(t=e.getAttribute("data-gb-mobile-breakpoint"))&&void 0!==t?t:"",a=window.matchMedia(`(max-width: ${o})`);l&&r&&l.setAttribute("aria-controls",r.id),e.classList.toggle("gb-navigation--mobile",a.matches),r.classList.toggle("gb-menu-container--mobile",a.matches),a.addEventListener("change",(t=>{e.classList.toggle("gb-navigation--mobile",t.matches),r.classList.toggle("gb-menu-container--mobile",t.matches),n(e)})),setTimeout((()=>{const t=e.querySelector(".gb-menu");if(t){const e=t.querySelectorAll(".menu-item-has-children");e.length>0&&requestAnimationFrame((()=>{e.forEach((e=>{const l=e.querySelector("a"),r=t.classList.contains("gb-menu--click")?l:e.querySelector(".gb-submenu-toggle");if(r){r.setAttribute("aria-controls",`sub-menu-${e.id}`),r.setAttribute("aria-label",`Toggle submenu for ${l.textContent}`);const t=e.querySelector(".gb-sub-menu");t&&(t.id=`sub-menu-${e.id}`)}}))}))}}),0)}))}function u(){let e=document.querySelectorAll(".gb-navigation");if(!e.length){const t=window.frameElement;if(t&&t.id&&t.id.startsWith("pattern-"))return void new MutationObserver(((t,l)=>{e=document.querySelectorAll(".gb-navigation"),e.length&&(l.disconnect(),s(e))})).observe(document.body,{childList:!0,subtree:!0})}s(e),function(){const e=document.querySelectorAll(".gb-navigation--mobile");e&&e.length&&e.forEach((e=>{e.addEventListener("click",(t=>{const l=t.target.closest('a[href*="#"]');if(!l)return;const r=l.getAttribute("href").match(/#(.+)$/);if(r){const t=r[1];document.getElementById(t)&&setTimeout((()=>{n(e)}),50)}}))}))}()}var g;l.d(t,{IJ:()=>n,Qg:()=>i,SL:()=>o}),window.myNavigationScriptInitialized||(window.myNavigationScriptInitialized=!0,document.addEventListener("click",(e=>{const t=e.target;!function(e){if(e){var t;const l=e.closest(".gb-navigation");if(!l)return;l.classList.contains("gb-navigation--open")?n(l):o(l);const r=null!==(t=window.frameElement)&&void 0!==t&&t;if(r&&r.id&&r.id.startsWith("pattern-"))if(l.classList.contains("gb-navigation--open")){const e=r.getAttribute("data-gb-original-height");e&&(r.style.height=e)}else r.style.height&&parseInt(r.style.height,10)<800&&(r.setAttribute("data-gb-original-height",r.style.height),requestAnimationFrame((()=>r.style.height="800px")))}}(t.closest(".gb-menu-toggle")),i(t.closest(".gb-menu--click .menu-item-has-children > a"),e),c(t.closest(".gb-submenu-toggle"),e);const l=document.querySelector(".menu-item.gb-sub-menu--open");l&&!l.contains(e.target)&&a(l.closest(".gb-navigation:not(.gb-navigation--open)"))})),document.addEventListener("keydown",(e=>{const t="Escape"===e.key,l="Enter"===e.key,r=" "===e.key,o="Tab"===e.key;if((l||r)&&(c(e.target.closest(".gb-submenu-toggle"),e),i(e.target.closest(".gb-menu--click .menu-item-has-children > a"),e)),o){const e=document.querySelector(".gb-sub-menu--open");e&&setTimeout((()=>{const t=document.activeElement;t.closest(".gb-sub-menu--open")||a(e.closest(".gb-navigation"),t)}),0)}if(t){const t=e.target.closest(".gb-sub-menu--open");if(t){a(t.closest(".gb-navigation"));const e=t.querySelector(".gb-submenu-toggle");e&&e.focus()}else{const e=document.querySelector(".gb-navigation--open");e&&n(e)}}})),window.addEventListener("pagehide",(()=>{const e=document.querySelectorAll(".gb-navigation--open");e.length&&e.forEach((e=>n(e)))})),g=()=>{document.querySelector(".editor-styles-wrapper, .wp-admin")?window.addEventListener("load",u):u()},"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",g):g()))},46942:(e,t)=>{var l;!function(){"use strict";var r={}.hasOwnProperty;function n(){for(var e="",t=0;t<arguments.length;t++){var l=arguments[t];l&&(e=a(e,o(l)))}return e}function o(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var l in e)r.call(e,l)&&e[l]&&(t=a(t,l));return t}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n.default=n,e.exports=n):void 0===(l=function(){return n}.apply(t,[]))||(e.exports=l)}()}},t={};function l(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,l),o.exports}l.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return l.d(t,{a:t}),t},l.d=(e,t)=>{for(var r in t)l.o(t,r)&&!l.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React,t=window.wp.blocks,r=window.wp.i18n,n=window.wp.blockEditor,o=window.wp.element,a=window.wp.compose,i=window.wp.data,c=window.wp.components,s=window.gbp.blockStyles,u=window.gbp.stylesBuilder;function g(t){const{colors:l,label:r}=t;return(0,e.createElement)(c.BaseControl,{className:"gpp-color-group",label:r,id:""},(0,e.createElement)("div",{className:"gpp-color-group__row"},l.map(((t,l)=>(0,e.createElement)(u.ColorPicker,{key:l,tooltip:t?.tooltip,value:t.value,onChange:t.onChange})))))}const m=(0,i.createReduxStore)("gbp-menu-toggle-state",{reducer:function(e=!1,t){return"SET_DATA"===t.type?t.payload:e},actions:{setMenuToggleState:e=>({type:"SET_DATA",payload:e})},selectors:{menuToggleState:e=>e}});function d(e,t,l=!1){const{styles:r={},uniqueId:n="",globalClasses:o=[]}=t,a=[];return l&&a.push(e),o.length>0&&a.push(...o),Object.keys(r).length>0&&a.push(`${e}-${n}`),a}const h=(0,i.createReduxStore)("gbp-current-style",{reducer:u.currentStyleReducer,actions:u.currentStyleActions,selectors:u.currentStyleSelectors}),b=(0,i.createReduxStore)("gbp-styles",{reducer:u.styleReducer,actions:u.styleActions,selectors:u.styleSelectors}),p=(0,i.createReduxStore)("gbp-styles-at-rule",{reducer:u.atRuleReducer,actions:u.atRuleActions,selectors:u.atRuleSelectors}),f=(0,i.createReduxStore)("gbp-styles-nested-rule",{reducer:u.nestedRuleReducer,actions:u.nestedRuleActions,selectors:u.nestedRuleSelectors}),w=window.wp.apiFetch;var v=l.n(w);const y=window.wp.notices,x=window.wp.url,k=window.wp.coreData;var E;window.lodash;const C="undefined"!=typeof gbGlobalStylePermissions&&null!==(E=gbGlobalStylePermissions?.canManageStyles)&&void 0!==E&&E,S=window.wp.editPost;const _=(0,i.createReduxStore)("gbp-block-styles-current-style",{reducer:u.currentStyleReducer,actions:u.currentStyleActions,selectors:u.currentStyleSelectors}),A=(0,i.createReduxStore)("gbp-block-styles-at-rule",{reducer:u.atRuleReducer,actions:u.atRuleActions,selectors:u.atRuleSelectors}),M=(0,i.createReduxStore)("gbp-block-styles-nested-rule",{reducer:u.nestedRuleReducer,actions:u.nestedRuleActions,selectors:u.nestedRuleSelectors});function L(){const e=(0,i.useSelect)((e=>e(A).getAtRule())),{setAtRule:t}=(0,i.useDispatch)(A),l=(0,i.useSelect)((e=>e(M).getNestedRule())),{setNestedRule:o}=(0,i.useDispatch)(M),a=(0,s.useCurrentAtRule)(u.defaultAtRules),{setCurrentStyle:c}=(0,i.useDispatch)(_),g=(0,i.useSelect)((e=>e(_).currentStyle())),{deviceType:m,setDeviceType:d}=(0,s.useDeviceType)(),w=function(){const{setCurrentStyle:e}=(0,i.useDispatch)(h),{setStyles:t}=(0,i.useDispatch)(b),{createNotice:l,removeAllNotices:o}=(0,i.useDispatch)(y.store),{getEntityRecordEdits:a}=(0,i.useSelect)(k.store),{getSelectedBlock:c}=(0,i.useSelect)((e=>e(n.store)),[]),{setAtRule:s}=(0,i.useDispatch)(p),{setNestedRule:u}=(0,i.useDispatch)(f),{openGeneralSidebar:g}=(0,i.useDispatch)(S.store);return async(n,i={})=>{if(!C)return;const{classStyles:m,classPostId:d}=await async function(e){var t;const l=await v()({path:(0,x.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let r=null!==(t=l?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(r)&&0===r.length&&(r={}),{classStyles:r,classPostId:l?.response?.data?.postId}}(n);if(!d)return o("snackbar"),void l("error",(0,r.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,r.__)("%s does not exist.","generateblocks-pro"),n),{type:"snackbar"});s(""),u(""),g("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:d,name:n,classStyles:m,clientId:c()?.clientId,options:i}),i.nestedRule&&u(i.nestedRule),i.atRule&&s(i.atRule);const h=a("postType","gblocks_styles",d);t(h?.gb_style_data||m),o("snackbar"),l("info",(0,r.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,r.__)("Editing %s.","generateblocks-pro"),n),{type:"snackbar"})}}(),E=function(){const{setCurrentStyle:e}=(0,i.useDispatch)(h),{setStyles:t}=(0,i.useDispatch)(b),{setAtRule:l}=(0,i.useDispatch)(p),{setNestedRule:r}=(0,i.useDispatch)(f);return()=>{e({}),t({}),l(""),r("")}}();return{atRule:e,nestedRule:l,setAtRule:t,currentAtRule:a,setNestedRule:o,setDeviceType:d,deviceType:m,setCurrentStyle:c,currentStyle:g,getPreviewDevice:s.getPreviewDevice,setGlobalStyle:w,cancelEditGlobalStyle:E}}function B({attributes:t,setAttributes:l,shortcuts:r,onStyleChange:n}){const{atRule:o,setAtRule:a,nestedRule:i,setNestedRule:c,setDeviceType:g,getPreviewDevice:m,currentStyle:d,setGlobalStyle:h,cancelEditGlobalStyle:b}=L(),{styles:p,globalClasses:f=[]}=t,w=(0,u.getStylesObject)(p,o,i);return(0,e.createElement)(u.StylesBuilder,{currentSelector:d?.selector,styles:w,allStyles:p,onDeleteStyle:(e,t)=>{const r=(0,u.deleteStylesObjectKey)(p,e,t);l({styles:r})},nestedRule:i,atRule:o,onStyleChange:(e,t=null)=>n(e,t,o,i),onNestedRuleChange:e=>c(e),onAtRuleChange:e=>{a(e);const t=(0,u.getPreviewWidth)(e),l=m(t);l&&g(l)},onUpdateKey:(e,t,r)=>{const n=(0,u.updateStylesObjectKey)(p,e,t,r);l({styles:n})},selectorShortcuts:r.selectorShortcuts,visibleSelectors:r.visibleShortcuts,onEditStyle:h,cancelEditStyle:b,setLocalTab:e=>{sessionStorage.setItem(s.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:f})}function I(t){return l=>{const{attributes:r,name:n,setAttributes:a,isSelected:i,clientId:c}=l,{uniqueId:g,styles:m,css:d}=r,{atRule:h,deviceType:b,setAtRule:p,currentStyle:f,setCurrentStyle:w,setNestedRule:v}=L(),y=(0,s.useSetStyles)(l,{cleanStylesObject:u.cleanStylesObject}),x=(0,o.useMemo)((()=>g?(0,s.getSelector)(n,g):""),[n,g]),k=Array.isArray(m)?{}:m;return(0,s.useAtRuleEffect)({deviceType:b,atRule:h,setAtRule:p,defaultAtRules:u.defaultAtRules,isSelected:i,getPreviewWidth:u.getPreviewWidth}),(0,s.useGenerateCSSEffect)({selector:x,styles:k,setAttributes:a,getCss:u.getCss,getSelector:s.getSelector,isSelected:i,blockCss:d,clientId:c}),(0,s.useStyleSelectorEffect)({isSelected:i,currentStyle:f,selector:x,setCurrentStyle:w,setNestedRule:v}),(0,s.useDecodeStyleKeys)({styles:m,setAttributes:a}),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(s.Style,{selector:x,getCss:u.getCss,styles:k,clientId:c,name:n}),(0,e.createElement)(t,{...l,selector:x,onStyleChange:function(e,t="",l="",r=""){const n="object"==typeof e?e:{[e]:t},o=(0,s.buildChangedStylesObject)(n,l,r);y(o)},getStyleValue:function(e,t="",l=""){var r,n,o,a;return l?t?null!==(o=m?.[l]?.[t]?.[e])&&void 0!==o?o:"":null!==(n=m?.[l]?.[e])&&void 0!==n?n:"":t?null!==(a=m?.[t]?.[e])&&void 0!==a?a:"":null!==(r=m?.[e])&&void 0!==r?r:""},styles:k}))}}const R=window.wp.hooks,T=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function N(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function q(t){return l=>{var r,a,s,u;const{attributes:g,setAttributes:m,context:d}=l,{htmlAttributes:h={},uniqueId:b,className:p,align:f}=g,w=(0,i.useSelect)((e=>e("core/editor").isSavingPost())),{style:v="",href:y,...x}=h,k=Object.keys(x).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(x[t]),e)),{}),[E,C]=(0,o.useState)(v);var S,_,A,M;(0,o.useEffect)((()=>{!async function(){const e=await(0,R.applyFilters)("generateblocks.editor.htmlAttributes.style",v,{...l});C(e)}()}),[v,d,w]),S=()=>{const e=["alignwide","alignfull"],t=(p?.split(" ")||[]).filter((t=>!e.includes(t)));f&&t.push("align"+f),m({className:t.join(" ")})},_=[f],M=(A=(0,e.useRef)(!0)).current?(A.current=!1,!0):A.current,(0,e.useEffect)((function(){if(!M)return S()}),_);const L="string"==typeof E?E.split(";").reduce(((e,t)=>{const l=t.indexOf(":");if(-1===l)return e;let r=t.slice(0,l).trim();const n=t.slice(l+1).trim();return r&&n&&(r.startsWith("--")||(r=r.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[r]=n),e}),{}):"",B={...k,style:L,"data-gb-id":b,"data-context-post-id":null!==(r=null!==(a=d?.postId)&&void 0!==a?a:d?.["generateblocks/loopIndex"])&&void 0!==r?r:0,"data-align":f||void 0},I=(0,o.useMemo)((()=>Array.isArray(h)?{}:h),[JSON.stringify(h)]);return(0,o.useEffect)((()=>{const e={...h};Object.keys(e).forEach((t=>{const l=t.startsWith("data-"),r=e[t];T.includes(t)||""!==r||l||"alt"===t||delete e[t],"string"!=typeof r&&"boolean"!=typeof r&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const l=Object.keys(e),r=Object.keys(t);if(l.length!==r.length)return!1;for(const r of l)if(e[r]!==t[r])return!1;return!0}(e,h)||m({htmlAttributes:e})}),[JSON.stringify(h)]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(t,{...l,editorHtmlAttributes:B,htmlAttributes:I}),(0,e.createElement)(n.InspectorAdvancedControls,null,(0,e.createElement)(c.TextControl,{label:"HTML ID",value:null!==(s=h.id)&&void 0!==s?s:"",onChange:e=>{m({htmlAttributes:{...h,id:e}})},onBlur:()=>{h.id&&m({htmlAttributes:{...h,id:N(h.id)}})}}),(0,e.createElement)(c.TextControl,{label:"ARIA Label",value:null!==(u=h["aria-label"])&&void 0!==u?u:"",onChange:e=>{m({htmlAttributes:{...h,"aria-label":e}})}})))}}const D=window.gbp.components,O=(e,t)=>e&&Array.isArray(e)?e.reduce(((e,l)=>{const r=!(t&&t.length>0)||t.includes(l.name),n=O(l.innerBlocks,t);return{total:e.total+1+n.total,allowed:e.allowed+(r?1:0)+n.allowed}}),{total:0,allowed:0}):{total:0,allowed:0},P=(e,t)=>e&&Array.isArray(e)?e.filter((e=>{const{name:l}=e;return!(t&&t.length>0)||t.includes(l)})).map((e=>({...e,innerBlocks:P(e.innerBlocks,t)}))):[],j=(0,o.memo)((({block:l,level:r=0,currentClientId:n,selectBlock:o})=>{var a;const{name:s,innerBlocks:u,clientId:g}=l,{getBlockType:m}=(0,i.useSelect)((e=>e(t.store)),[]),d=m(s);return(0,e.createElement)("div",{className:"gb-block-node","data-level":r},(0,e.createElement)(c.Button,{variant:"tertiary",size:"compact",className:"gb-block-node-button",onClick:()=>o(g),isPressed:n===g,icon:null!==(a=d?.icon?.src)&&void 0!==a?a:null},function(e,t,l="list-view"){const{__experimentalLabel:r,title:n}=e||{};if(!e)return"Unknown Block";const o=r&&r(t,{context:l});return!o||"string"!=typeof o&&"number"!=typeof o?n||"Unnamed Block":o}(d,l.attributes)),u&&u.length>0&&(0,e.createElement)("div",{className:"gb-block-tree-inner-blocks"},u.map((t=>(0,e.createElement)(j,{key:t.clientId,block:t,level:r+1,currentClientId:n,blockType:d,selectBlock:o})))))}));function F({blocks:t,clientId:l,allowedBlocks:a,showAllLabel:s=(0,r.__)("Show all blocks","generateblocks-pro")}){const[u,g]=(0,o.useState)(!1),{selectBlock:m}=(0,i.useDispatch)(n.store),d=(0,o.useMemo)((()=>O(t,a)),[t,a]),h=(0,o.useMemo)((()=>P(t,a)),[t,a]),b=(0,o.useMemo)((()=>P(t,[])),[t]),p=(0,o.useMemo)((()=>u?b:h),[u,b,h]),f=d.total!==d.allowed;return(0,e.createElement)("div",{className:"gb-block-tree"},!!f&&(0,e.createElement)("div",{className:"gb-block-tree__show-all"},(0,e.createElement)(c.ToggleControl,{label:s,checked:u,onChange:e=>g(e)})),p.map((t=>(0,e.createElement)(j,{key:t.clientId,block:t,currentClientId:l,selectBlock:m}))))}var H=l(20493);function U({value:t,onChange:l}){const n=(0,o.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus?.map((e=>({label:e.name,value:e.term_id.toString()}))))&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]);return n.length?(0,e.createElement)(c.SelectControl,{label:(0,r.__)("Menu","generateblocks-pro"),value:t,options:n,onChange:l}):null}var V=l(46942),Z=l.n(V);function G({name:e,clientId:t,align:l,children:r}){const{getBlockRootClientId:a}=(0,i.useSelect)((e=>e("core/block-editor")),[]),c=(0,i.useSelect)((e=>{const{getSettings:t}=e(n.store);return t().supportsLayout||!1}),[]),s=e.toString().replace("/","-"),u={className:Z()({"wp-block":!0,"gb-is-root-block":!0,[`gb-root-block-${s}`]:!0,[`align${l}`]:c}),"data-align":l&&!c?l:null,"data-block":t},g=a(t);return(0,R.applyFilters)("generateblocks.rootElement.disable",g,{name:e})?r:(0,o.createElement)("div",u,r)}const $=window.wp.serverSideRender;var z=l.n($);const W={},J=[];function K(){var t,l;const n=null!==(t=generateblocksBlockClassicMenu?.menuAdminUrl)&&void 0!==t?t:"",[a,s]=(0,o.useState)(null!==(l=generateblocksBlockClassicMenu?.hasMenuSupport)&&void 0!==l&&l),u=(0,o.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus)&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]),g=(0,i.useSelect)((e=>e(k.store).getEntityRecord("root","site")?.generateblocks_pro_classic_menu_support||!1),[]),{editEntityRecord:m,saveEditedEntityRecord:d}=(0,i.useDispatch)(k.store),[h,b]=(0,o.useState)(g);return a?!n||u.length>0?null:(0,e.createElement)(c.Notice,{isDismissible:!1,status:"warning"},(0,o.createInterpolateElement)((0,r.__)("No menus found. Please <CreateMenuLink />.","generateblocks-pro"),{CreateMenuLink:(0,e.createElement)("a",{href:n,target:"_blank",rel:"noopener noreferrer"},(0,r.__)("create a menu","generateblocks-pro"))})):(0,e.createElement)(c.Notice,{isDismissible:!1,status:"warning"},(0,e.createElement)(c.ToggleControl,{label:(0,r.__)("Enable Menu Support","generateblocks-pro"),checked:h,onChange:async e=>{b(e),s(e);try{await m("root","site",void 0,{generateblocks_pro_classic_menu_support:e}),await d("root","site",void 0)}catch(e){var t;console.error("Save failed:",e),b(g),s(null!==(t=generateblocksBlockClassicMenu?.hasMenuSupport)&&void 0!==t&&t)}},help:(0,r.__)("Your theme does not support the menu system. Enable it here.","generateblocks-pro")}))}(0,a.compose)(I,s.withUniqueId)((function(t){var l;const{attributes:a,setAttributes:c,getStyleValue:u,onStyleChange:g,clientId:h,name:b,context:p,isSelected:f}=t,{menu:w,uniqueId:v}=a,y=(0,o.useRef)(),x=(0,i.useSelect)((e=>e(m).menuToggleState())),{getBlockParentsByBlockName:k,getBlock:E}=(0,i.useSelect)((e=>e(n.store)),[]),{selectBlock:C}=(0,i.useDispatch)(n.store),S=(0,o.useMemo)((()=>{var e;return null!==(e=k(h,"generateblocks-pro/navigation",!0)?.[0])&&void 0!==e?e:""}),[h]),_=(0,o.useMemo)((()=>E(S)),[S,f]),A=(0,o.useMemo)((()=>E(h)),[h,f]),M=(0,o.useMemo)((()=>{var e;return null!==(e=A?.innerBlocks?.find((e=>"generateblocks-pro/classic-menu-item"===e.name))?.clientId)&&void 0!==e?e:""}),[A]),L=(0,o.useMemo)((()=>{var e;return null!==(e=A?.innerBlocks?.find((e=>"generateblocks-pro/classic-sub-menu"===e.name))?.clientId)&&void 0!==e?e:""}),[A]),I=y?.current?.querySelector(".gb-menu"),R=(0,o.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus?.map((e=>({label:e.name,value:e.term_id.toString()}))))&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]);(0,o.useEffect)((()=>{var e;w&&R.find((e=>e.value===w))||c({menu:null!==(e=R[0]?.value)&&void 0!==e?e:""})}),[R.length]);const T=d("gb-menu",a,!0),N=(0,n.useBlockProps)({className:T.filter(Boolean).join(" ").trim(),ref:y}),q=(0,n.useInnerBlocksProps)({},{allowedBlocks:["generateblocks-pro/classic-menu-item","generateblocks-pro/classic-sub-menu"],renderAppender:!1}),O={name:b,attributes:a,setAttributes:c,clientId:h,getStyleValue:u,onStyleChange:g},P={};return P.subMenuType=null!==(l=p?.["generateblocks-pro/subMenuType"])&&void 0!==l?l:"hover",P.disableLinks=!0,(0,o.useEffect)((()=>{if(!y?.current)return;const e=y.current.closest(".gb-menu-container");if(!e)return;const t=e.querySelectorAll(".gb-menu--click .menu-item-has-children > a"),l=[];return t.length>0&&t.forEach((e=>{const t=t=>{(0,H.Qg)(e,t)};e.addEventListener("click",t),l.push({item:e,handler:t})})),()=>{l.forEach((({item:e,handler:t})=>{e.removeEventListener("click",t)}))}}),[y,I]),(0,o.useEffect)((()=>{if(!y?.current)return;const e=y?.current?.closest(".gb-navigation");if(!e)return;const t=e.querySelectorAll(".gb-submenu-toggle"),l=[];return t.length>0&&t.forEach((e=>{const t=t=>{const l=e.closest(".gb-menu-container--toggled"),r=e.closest(".gb-menu--hover");"click"===t?.type&&r&&!l||(0,H.Qg)(e,t)};e.addEventListener("click",t),l.push({item:e,handler:t})})),()=>{l.forEach((({item:e,handler:t})=>{e.removeEventListener("click",t)}))}}),[y,x,I]),(0,o.useEffect)((()=>{const e=y?.current;if(e)return e.addEventListener("click",t),()=>{e.removeEventListener("click",t)};function t(e){e.target.closest(".gb-sub-menu")?C(L):e.target.closest(".menu-item")&&C(M)}}),[y,M,I,L]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.InspectorControls,null,(0,e.createElement)(s.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(D.OpenPanel,{title:(0,r.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...O},(0,e.createElement)(F,{blocks:[_],clientId:h,allowedBlocks:Q,showAllLabel:(0,r.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(K,null),(0,e.createElement)(D.OpenPanel,{...O,panelId:"settings"},(0,e.createElement)(U,{value:w,onChange:e=>c({menu:e})}))),stylesTab:(0,e.createElement)(B,{attributes:a,setAttributes:c,shortcuts:{selectorShortcuts:W,visibleShortcuts:J},onStyleChange:g})})),(0,e.createElement)("div",{...N},R.length&&w?(0,e.createElement)(z(),{key:w+v,block:"generateblocks-pro/classic-menu",attributes:a,urlQueryArgs:P}):(0,e.createElement)(e.Fragment,null,(0,r.__)("No menu found.","generateblocks-pro")),(0,e.createElement)("div",{...q})))}));const Q=["generateblocks-pro/navigation","generateblocks-pro/menu-container","generateblocks-pro/classic-menu","generateblocks-pro/classic-menu-item","generateblocks-pro/classic-sub-menu","generateblocks-pro/menu-toggle"];function Y(e){for(const t of e){if("generateblocks-pro/classic-menu"===t.name)return t;if(t.innerBlocks&&t.innerBlocks.length>0){const e=Y(t.innerBlocks);if(e)return e}}return null}function X(){const t=document.querySelector('.gb-block-styles-tab-panel button[id*="styles"]');return t?(0,e.createElement)("div",{className:"gb-more-style-controls"},(0,o.createInterpolateElement)(
// Translators: the at-rule for deletion.
// Translators: the at-rule for deletion.
(0,r.__)("Open the <Styles /> tab for more controls.","generateblocks-pro"),{Styles:(0,e.createElement)(c.Button,{variant:"link",className:"gb-more-style-controls__styles-tab",onClick:()=>t.click()},(0,r.__)("Styles","generateblocks-pro"))})):null}(0,a.compose)(q,I,s.withUniqueId)((function(t){var l,a,g,h,b;const{attributes:p,setAttributes:f,editorHtmlAttributes:w,onStyleChange:v,getStyleValue:y,clientId:x,name:k,isSelected:E}=t,{styles:C,htmlAttributes:S,tagName:_,uniqueId:A,subMenuType:M}=p,L=(0,i.useSelect)((e=>e(m).menuToggleState())),I=(0,o.useRef)(),{getBlock:R,getBlocks:T}=(0,i.useSelect)((e=>e(n.store)),[]),{updateBlockAttributes:N}=(0,i.useDispatch)(n.store),q=(0,o.useMemo)((()=>R(x)),[x,E]),[O,P]=(0,o.useState)(null),j=d("gb-navigation",{...p,styles:C},!0);L&&j.push("gb-navigation--open"),j.includes("gb-navigation-"+A)||j.push("gb-navigation-"+A);const H=(0,n.useBlockProps)({className:j.filter((e=>e)).join(" ").trim(),...w,ref:I}),V=(0,n.useInnerBlocksProps)(H),Z=_||"nav";(0,o.useEffect)((()=>{_||f({tagName:"nav"})}),[_]),(0,o.useEffect)((()=>{S?.["data-gb-mobile-menu-type"]||f({htmlAttributes:{...S,"data-gb-mobile-menu-type":"full-overlay"}})}),[S?.["data-gb-mobile-menu-type"]]),(0,o.useEffect)((()=>{if(!E)return;const e=Y(T(x));e&&P(e)}),[E,x]);const $={name:k,attributes:p,setAttributes:f,clientId:x,getStyleValue:y,onStyleChange:v},z=null!==(l=S?.["data-gb-mobile-menu-transition"])&&void 0!==l?l:"",W=null!==(a=S?.["data-gb-sub-menu-transition"])&&void 0!==a?a:"";return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.InspectorControls,null,(0,e.createElement)(s.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(D.OpenPanel,{title:(0,r.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...$},(0,e.createElement)(F,{blocks:[q],clientId:x,allowedBlocks:Q,showAllLabel:(0,r.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(K,null),(0,e.createElement)(D.OpenPanel,{panelId:"settings",...$},!!O&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(U,{value:O?.attributes?.menu,onChange:e=>{N(O.clientId,{menu:e});const t=Y(T(x));t&&P(t)}})),(0,e.createElement)(c.SelectControl,{label:(0,r.__)("Sub-menu type","generateblocks-pro"),value:M,options:[{label:(0,r.__)("Hover","generateblocks-pro"),value:"hover"},{label:(0,r.__)("Click Menu Item","generateblocks-pro"),value:"click"},{label:(0,r.__)("Click Toggle","generateblocks-pro"),value:"click-toggle"}],onChange:e=>f({subMenuType:e})}),(0,e.createElement)(c.SelectControl,{label:(0,r.__)("Sub-menu Transition","generateblocks-pro"),value:W,options:[{label:(0,r.__)("None","generateblocks-pro"),value:""},{label:(0,r.__)("Fade","generateblocks-pro"),value:"fade"},{label:(0,r.__)("Slide & fade up","generateblocks-pro"),value:"fade-slide-up"},{label:(0,r.__)("Slide & fade down","generateblocks-pro"),value:"fade-slide-down"},{label:(0,r.__)("Slide & fade left","generateblocks-pro"),value:"fade-slide-left"},{label:(0,r.__)("Slide & fade right","generateblocks-pro"),value:"fade-slide-right"}],onChange:e=>{const t={...S};e?t["data-gb-sub-menu-transition"]=e:(delete t["data-gb-sub-menu-transition"],v("--sub-menu-transition-speed","")),f({htmlAttributes:t})}}),""!==W&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(u.UnitControl,{units:["ms"],defaultUnitValue:"ms",label:(0,r.__)("Sub-menu Transition Speed","generateblocks-pro"),placeholder:"200ms",value:y("--sub-menu-transition-speed",""),onChange:e=>{v("--sub-menu-transition-speed",e)}}),"fade"!==W&&(0,e.createElement)(u.UnitControl,{label:(0,r.__)("Sub-menu Transition Distance","generateblocks-pro"),placeholder:"5px",value:y("--sub-menu-transition-distance",""),onChange:e=>{v("--sub-menu-transition-distance",e)}})),(0,e.createElement)(u.UnitControl,{label:(0,r.__)("Mobile breakpoint","generateblocks-pro"),value:null!==(g=S?.["data-gb-mobile-breakpoint"])&&void 0!==g?g:"",onChange:e=>{const t={...S};e?t["data-gb-mobile-breakpoint"]=e:delete t["data-gb-mobile-breakpoint"],f({htmlAttributes:t})}}),(0,e.createElement)(c.SelectControl,{label:(0,r.__)("Mobile Menu Type","generateblocks-pro"),value:null!==(h=S?.["data-gb-mobile-menu-type"])&&void 0!==h?h:"",options:[{label:(0,r.__)("Full overlay","generateblocks-pro"),value:"full-overlay"},{label:(0,r.__)("Partial overlay","generateblocks-pro"),value:"partial-overlay"}],onChange:e=>{const t={...S};e?t["data-gb-mobile-menu-type"]=e:delete t["data-gb-mobile-menu-type"],f({htmlAttributes:t})}}),"partial-overlay"===S?.["data-gb-mobile-menu-type"]&&(0,e.createElement)(c.TextControl,{label:(0,r.__)("Mobile Menu Anchor","generateblocks-pro"),help:(0,r.__)("The selector for the element the mobile menu will attach to the bottom of.","generateblocks-pro"),value:null!==(b=S?.["data-gb-menu-toggle-anchor"])&&void 0!==b?b:"",placeholder:"Calculate automatically",onChange:e=>{const t={...S};e?t["data-gb-menu-toggle-anchor"]=e:delete t["data-gb-menu-toggle-anchor"],f({htmlAttributes:t})}}),(0,e.createElement)(c.SelectControl,{label:(0,r.__)("Mobile Menu Transition","generateblocks-pro"),value:z,options:[{label:(0,r.__)("None","generateblocks-pro"),value:""},{label:(0,r.__)("Fade","generateblocks-pro"),value:"fade"},{label:(0,r.__)("Slide left","generateblocks-pro"),value:"slide-left"},{label:(0,r.__)("Slide right","generateblocks-pro"),value:"slide-right"},{label:(0,r.__)("Slide up","generateblocks-pro"),value:"slide-up"},{label:(0,r.__)("Slide down","generateblocks-pro"),value:"slide-down"},{label:(0,r.__)("Slide & fade left","generateblocks-pro"),value:"fade-slide-left"},{label:(0,r.__)("Slide & fade right","generateblocks-pro"),value:"fade-slide-right"},{label:(0,r.__)("Slide & fade up","generateblocks-pro"),value:"fade-slide-up"},{label:(0,r.__)("Slide & fade down","generateblocks-pro"),value:"fade-slide-down"}],onChange:e=>{const t={...S};e?t["data-gb-mobile-menu-transition"]=e:(delete t["data-gb-mobile-menu-transition"],v("--mobile-transition-speed","")),f({htmlAttributes:t})}}),""!==z&&(0,e.createElement)(u.UnitControl,{units:["ms"],defaultUnitValue:"ms",label:(0,r.__)("Mobile Menu Transition Speed","generateblocks-pro"),placeholder:"200ms",value:y("--mobile-transition-speed",""),onChange:e=>{v("--mobile-transition-speed",e)}}))),stylesTab:(0,e.createElement)(B,{attributes:p,setAttributes:f,shortcuts:{},onStyleChange:v})})),(0,e.createElement)(G,{name:k,clientId:x},(0,e.createElement)(Z,{...V})))}));const ee={default:{items:[{label:"Hover",value:"&:is(:hover, :focus)"},{label:"Toggled",value:"&:is(.gb-menu-toggle--toggled, .gb-menu-toggle--toggled:hover, .gb-menu-toggle--toggled:focus)"}]}},te=[{label:(0,r.__)("Toggled","generateblocks-pro"),value:"&:is(.gb-menu-toggle--toggled, .gb-menu-toggle--toggled:hover, .gb-menu-toggle--toggled:focus)"}],le={menu1:{label:(0,r._x)("Menu 1","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 14",height:"14",width:"17"},(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"16",x:"0.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"16",y:"6",x:"0.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"16",y:"12",x:"0.5"}))},menu2:{label:(0,r._x)("Menu 2","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 8",height:"8",width:"17"},(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"16",x:"0.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"16",y:"6",x:"0.5"}))},menu3:{label:(0,r._x)("Menu 3","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 14",height:"14",width:"17"},(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"11.6364",x:"0.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"16",y:"6",x:"0.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"11.6364",y:"12",x:"4.86353"}))},menu4:{label:(0,r._x)("Menu 4","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 14",height:"14",width:"17"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1 0 0 -1 0.5 14)",rx:"1",height:"2",width:"11.6364"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1 0 0 -1 0.5 8)",rx:"1",height:"2",width:"16"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1 0 0 -1 4.86353 2)",rx:"1",height:"2",width:"11.6364"}))},menu5:{label:(0,r._x)("Menu 5","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 14",height:"14",width:"17"},(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"11.6364",x:"0.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"2.90909",x:"13.5908"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"16",y:"6",x:"0.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"11.6364",y:"12",x:"4.86377"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"2.90909",y:"12",x:"0.5"}))},menu6:{label:(0,r._x)("Menu 6","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 14",height:"14",width:"17"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16.5 0)",rx:"1",height:"2",width:"11.6364"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 3.40918 0)",rx:"1",height:"2",width:"2.90909"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16.5 6)",rx:"1",height:"2",width:"16"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 12.1362 12)",rx:"1",height:"2",width:"11.6364"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16.5 12)",rx:"1",height:"2",width:"2.90909"}))},menu7:{label:(0,r._x)("Menu 7","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 14",height:"14",width:"17"},(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"13.0909",x:"0.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"16",y:"6",x:"0.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"11.6364",y:"12",x:"0.5"}))},menu8:{label:(0,r._x)("Menu 8","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 14",height:"14",width:"17"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16.5 0)",rx:"1",height:"2",width:"13.0909"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16.5 6)",rx:"1",height:"2",width:"16"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16.5 12)",rx:"1",height:"2",width:"11.6364"}))},menu9:{label:(0,r._x)("Menu 9","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 15",height:"15",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16 6.5)",rx:"1",height:"2",width:"13"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16 0.5)",rx:"1",height:"2",width:"16"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16 12.5)",rx:"1",height:"2",width:"10"}))},menu10:{label:(0,r._x)("Menu 10","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 15",height:"15",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"13",y:"6.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"16",y:"0.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"10",y:"12.5"}))},menu11:{label:(0,r._x)("Menu 11","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 15",height:"15",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"10.1818",y:"0.5",x:"2.90918"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"16",y:"6.5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"1",height:"2",width:"10.1818",y:"12.5",x:"2.90918"}))},menu12:{label:(0,r._x)("Menu 12","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 3 15",height:"15",width:"3"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 3 0)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 3 6)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 3 12)",rx:"1.5",height:"3",width:"3"}))},menu13:{label:(0,r._x)("Menu 13","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 15 3",height:"3",width:"15"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1.19249e-08 -1 -1 -1.19249e-08 15 3)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1.19249e-08 -1 -1 -1.19249e-08 9 3)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1.19249e-08 -1 -1 -1.19249e-08 3 3)",rx:"1.5",height:"3",width:"3"}))},menu14:{label:(0,r._x)("Menu 14","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 15 15",height:"15",width:"15"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 3 0)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 3 6)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 3 12)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 9 0)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 9 6)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 9 12)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 15 0)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 15 6)",rx:"1.5",height:"3",width:"3"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 15 12)",rx:"1.5",height:"3",width:"3"}))},close:{label:(0,r._x)("Close","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 13 13",height:"13",width:"13"},(0,e.createElement)("rect",{fill:"currentColor",transform:"rotate(-45 0 11.4497)",rx:"1",height:"2",width:"16",y:"11.4497"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-0.707107 -0.707107 -0.707107 0.707107 13 11.4497)",rx:"1",height:"2",width:"16"}))}},re={menu1:{label:(0,r._x)("Menu 1","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 13",height:"13",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"16"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"16",y:"6"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"16",y:"12"}))},menu2:{label:(0,r._x)("Menu 2","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 7",height:"7",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"16"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"16",y:"6"}))},menu3:{label:(0,r._x)("Menu 3","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 13",height:"13",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"11"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"16",y:"6"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"11",y:"12",x:"5"}))},menu4:{label:(0,r._x)("Menu 4","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 14",height:"14",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1 0 0 -1 0 14)",rx:"0.5",height:"1",width:"11"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1 0 0 -1 0 8)",rx:"0.5",height:"1",width:"16"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1 0 0 -1 5 2)",rx:"0.5",height:"1",width:"11"}))},menu5:{label:(0,r._x)("Menu 5","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 13",height:"13",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"12"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"2",x:"14"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"16",y:"6"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"11",y:"12",x:"5"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"3",y:"12"}))},menu6:{label:(0,r._x)("Menu 6","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 13",height:"13",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16 0)",rx:"0.5",height:"1",width:"12"}),(0,e.createElement)("path",{fill:"currentColor",d:"M2 0.5C2 0.223858 1.77614 0 1.5 0H0.5C0.223858 0 0 0.223858 0 0.5V0.5C0 0.776142 0.223858 1 0.5 1H1.5C1.77614 1 2 0.776142 2 0.5V0.5Z"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16 6)",rx:"0.5",height:"1",width:"16"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 12 12)",rx:"0.5",height:"1",width:"12"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16 12)",rx:"0.5",height:"1",width:"2"}))},menu7:{label:(0,r._x)("Menu 7","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 13",height:"13",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"13"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"16",y:"6"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"12",y:"12"}))},menu8:{label:(0,r._x)("Menu 8","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 13",height:"13",width:"16"},(0,e.createElement)("path",{fill:"currentColor",d:"M16 0.5C16 0.223858 15.7761 0 15.5 0H2.5C2.22386 0 2 0.223858 2 0.5V0.5C2 0.776142 2.22386 1 2.5 1H15.5C15.7761 1 16 0.776142 16 0.5V0.5Z"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16 6)",rx:"0.5",height:"1",width:"16"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16 12)",rx:"0.5",height:"1",width:"12"}))},menu9:{label:(0,r._x)("Menu 9","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 13",height:"13",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16 6)",rx:"0.5",height:"1",width:"13"}),(0,e.createElement)("path",{fill:"currentColor",d:"M16 0.5C16 0.223858 15.7761 0 15.5 0H0.5C0.223858 0 0 0.223858 0 0.5V0.5C0 0.776142 0.223858 1 0.5 1H15.5C15.7761 1 16 0.776142 16 0.5V0.5Z"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 16 12)",rx:"0.5",height:"1",width:"10"}))},menu10:{label:(0,r._x)("Menu 10","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 13",height:"13",width:"17"},(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"13",y:"6",x:"1"}),(0,e.createElement)("path",{fill:"currentColor",d:"M0.866025 0.5C0.866025 0.223858 1.08988 0 1.36603 0H16.366C16.6422 0 16.866 0.223858 16.866 0.5V0.5C16.866 0.776142 16.6422 1 16.366 1H1.36603C1.08988 1 0.866025 0.776142 0.866025 0.5V0.5Z"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"10",y:"12",x:"1"}))},menu11:{label:(0,r._x)("Menu 11","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 13",height:"13",width:"16"},(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"10",x:"3"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"16",y:"6"}),(0,e.createElement)("rect",{fill:"currentColor",rx:"0.5",height:"1",width:"10",y:"12",x:"3"}))},menu12:{label:(0,r._x)("Menu 12","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 2 14",height:"14",width:"2"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 2 0)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 2 6)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 2 12)",rx:"1",height:"2",width:"2"}))},menu13:{label:(0,r._x)("Menu 13","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 2",height:"2",width:"14"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1.19249e-08 -1 -1 -1.19249e-08 14 2)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1.19249e-08 -1 -1 -1.19249e-08 8 2)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(1.19249e-08 -1 -1 -1.19249e-08 2 2)",rx:"1",height:"2",width:"2"}))},menu14:{label:(0,r._x)("Menu 14","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 14",height:"14",width:"14"},(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 2 0)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 2 6)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 2 12)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 8 0)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 8 6)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 8 12)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 14 0)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 14 6)",rx:"1",height:"2",width:"2"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-1 0 0 1 14 12)",rx:"1",height:"2",width:"2"}))},close:{label:(0,r._x)("Close","label","generateblocks-pro"),icon:(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 12 12",height:"12",width:"12"},(0,e.createElement)("rect",{fill:"currentColor",transform:"rotate(-45 0 11.3137)",rx:"0.5",height:"1",width:"16",y:"11.3137"}),(0,e.createElement)("rect",{fill:"currentColor",transform:"matrix(-0.707107 -0.707107 -0.707107 0.707107 12 11.3137)",rx:"0.5",height:"1",width:"16"}))}};function ne({openIcon:t,closeIcon:l}){return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("span",{className:"gb-menu-open-icon",dangerouslySetInnerHTML:{__html:t}}),(0,e.createElement)("span",{className:"gb-menu-close-icon",dangerouslySetInnerHTML:{__html:l}}))}const oe=(0,a.compose)(q,I,s.withUniqueId)((function(t){const{attributes:l,setAttributes:a,editorHtmlAttributes:u,getStyleValue:h,onStyleChange:b,clientId:p,name:f,mergeBlocks:w,onReplace:v,isSelected:y}=t,{openIcon:x,closeIcon:k,tagName:E,content:C,iconLocation:S,iconOnly:_}=l,A=(0,o.useRef)(),{setMenuToggleState:M}=(0,i.useDispatch)(m),L=(0,i.useSelect)((e=>e(m).menuToggleState())),[I,R]=(0,o.useState)(!1),T=(0,i.useSelect)((e=>{const{getDeviceType:t}=e("core/editor")||{};if("function"==typeof t)return t();const{__experimentalGetPreviewDeviceType:l=()=>""}=e("core/edit-post");return l()}),[]),{getBlockParentsByBlockName:N,getBlock:q}=(0,i.useSelect)((e=>e(n.store)),[]),O=(0,o.useMemo)((()=>{var e;return null!==(e=N(p,"generateblocks-pro/navigation",!0)?.[0])&&void 0!==e?e:""}),[p]),P=(0,o.useMemo)((()=>q(O)),[O,y]),j=d("gb-menu-toggle",l,!0);L&&j.push("gb-menu-toggle--toggled");const U=(0,n.useBlockProps)({className:j.filter((e=>e)).join(" ").trim(),...u,ref:A}),V=E||"button",Z={identifier:"content",value:C,onChange:e=>a({content:e}),onMerge:w,onReplace:v,onRemove:()=>v([]),placeholder:(0,r.__)("Text","generateblocks"),withoutInteractiveFormatting:!0,disableLineBreaks:!0,...o.Platform.isNative&&{deleteEnter:!0}};(0,o.useEffect)((()=>{A.current&&setTimeout((()=>{"none"===window.getComputedStyle(A.current).display&&L&&M(!1)}),500)}),[A.current,T]),(0,o.useEffect)((()=>{x||a({openIcon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256"><rect width="256" height="256" fill="none"/><line x1="40" y1="128" x2="216" y2="128" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="12"/><line x1="40" y1="64" x2="216" y2="64" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="12"/><line x1="40" y1="192" x2="216" y2="192" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="12"/></svg>'}),k||a({closeIcon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256"><rect width="256" height="256" fill="none"/><line x1="200" y1="56" x2="56" y2="200" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><line x1="200" y1="200" x2="56" y2="56" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/></svg>'})}),[]),(0,o.useEffect)((()=>{E||a({tagName:"button"})}),[]),(0,o.useEffect)((()=>{y&&R(!0)}),[y]);const G={clearLabel:(0,r.__)("Clear","generateblocks-pro"),openLabel:(0,r.__)("Open Library","generateblocks-pro"),modalTitle:(0,r.__)("Select Icon","generateblocks-pro"),icons:{mediumToggles:{group:(0,r.__)("Menu Toggles (Medium)","generateblocks-pro"),svgs:le},lightToggles:{group:(0,r.__)("Menu Toggles (Light)","generateblocks-pro"),svgs:re}}},$={name:f,attributes:l,setAttributes:a,clientId:p,getStyleValue:h,onStyleChange:b};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(n.InspectorControls,null,(0,e.createElement)(s.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(D.OpenPanel,{title:(0,r.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...$},(0,e.createElement)(F,{blocks:[P],clientId:p,allowedBlocks:Q,showAllLabel:(0,r.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(K,null),(0,e.createElement)(D.OpenPanel,{panelId:"design",title:(0,r.__)("Style Shortcuts","generateblocks-pro"),...$},(0,e.createElement)(g,{label:(0,r.__)("Background","generateblocks-pro"),colors:[{value:h("backgroundColor"),onChange:e=>b("backgroundColor",e),tooltip:(0,r.__)("Background","generateblocks-pro")},{value:h("backgroundColor","&:is(:hover, :focus)"),onChange:e=>b("backgroundColor",e,"","&:is(:hover, :focus)"),tooltip:(0,r.__)("Hover","generateblocks-pro")},{value:h("backgroundColor","&:is(.gb-menu-toggle--toggled, .gb-menu-toggle--toggled:hover, .gb-menu-toggle--toggled:focus)"),onChange:e=>b("backgroundColor",e,"","&:is(.gb-menu-toggle--toggled, .gb-menu-toggle--toggled:hover, .gb-menu-toggle--toggled:focus)"),tooltip:(0,r.__)("Toggled","generateblocks-pro")}]}),(0,e.createElement)(g,{label:(0,r.__)("Text","generateblocks-pro"),colors:[{value:h("color"),onChange:e=>b("color",e),tooltip:(0,r.__)("Text","generateblocks-pro")},{value:h("color","&:is(:hover, :focus)"),onChange:e=>b("color",e,"","&:is(:hover, :focus)"),tooltip:(0,r.__)("Hover","generateblocks-pro")},{value:h("color","&:is(.gb-menu-toggle--toggled, .gb-menu-toggle--toggled:hover, .gb-menu-toggle--toggled:focus)"),onChange:e=>b("color",e,"","&:is(.gb-menu-toggle--toggled, .gb-menu-toggle--toggled:hover, .gb-menu-toggle--toggled:focus)"),tooltip:(0,r.__)("Toggled","generateblocks-pro")}]}),(0,e.createElement)(X,null)),(0,e.createElement)(D.OpenPanel,{panelId:"settings",...$},(0,e.createElement)(D.IconControl,{label:(0,r.__)("Open Icon","generateblocks-pro"),value:x,onChange:e=>a({openIcon:e}),onClear:()=>a({openIcon:""}),...G}),(0,e.createElement)(D.IconControl,{label:(0,r.__)("Close Icon","generateblocks-pro"),value:k,onChange:e=>a({closeIcon:e}),onClear:()=>a({closeIcon:""}),...G}),!_&&(0,e.createElement)(c.SelectControl,{label:(0,r.__)("Icon Location","generateblocks"),value:S,options:[{label:(0,r.__)("Before","generateblocks"),value:"before"},{label:(0,r.__)("After","generateblocks"),value:"after"}],onChange:e=>a({iconLocation:e})}),(0,e.createElement)(c.BaseControl,{label:(0,r.__)("Icon Display","generateblocks"),id:"gb-icon-only"},(0,e.createElement)(c.ToggleControl,{id:"gb-icon-only",label:(0,r.__)("Show the icon by itself","generateblocks"),checked:!!_,onChange:()=>a({iconOnly:!_})})))),stylesTab:(0,e.createElement)(B,{attributes:l,setAttributes:a,shortcuts:{selectorShortcuts:ee,visibleShortcuts:te},onStyleChange:b})})),(0,e.createElement)(V,{...U,onClick:()=>{var e;if(!I)return;const t=L,l=null!==(e=A?.current.closest(".gb-navigation"))&&void 0!==e?e:"";M(!t),l&&(t?(0,H.IJ)(l):(0,H.SL)(l))}},"before"===S&&(0,e.createElement)(ne,{openIcon:x,closeIcon:k}),!_&&(0,e.createElement)("span",{className:"gb-menu-toggle-text"},(0,e.createElement)(n.RichText,{...Z,tagName:"span"})),"after"===S&&(0,e.createElement)(ne,{openIcon:x,closeIcon:k})))})),ae=JSON.parse('{"UU":"generateblocks-pro/menu-toggle"}');function ie(){return(0,e.createElement)("svg",{viewBox:"0 0 24 24",className:"gblocks-block-icon",style:{fillRule:"evenodd",clipRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:2}},(0,e.createElement)("path",{d:"M2.03,12.35L2.03,2.797C2.03,2.452 2.31,2.172 2.655,2.172L21.406,2.172C21.75,2.172 22.03,2.452 22.03,2.797L22.03,12.35L20.78,12.35L20.78,9.239L3.28,9.239L3.28,12.35L2.03,12.35ZM20.778,7.975L20.78,3.422L3.279,3.422L3.261,7.975L20.778,7.975ZM21.405,22.172L17.655,22.172L17.655,20.922L20.78,20.922L20.78,17.797L22.03,17.797L22.03,21.547C22.03,21.892 21.75,22.172 21.405,22.172ZM6.405,22.172L2.655,22.172C2.312,22.172 2.03,21.89 2.03,21.547L2.03,17.797L3.28,17.797L3.28,20.922L6.405,20.922L6.405,22.172Z",style:{fillOpacity:.3}}),(0,e.createElement)("path",{d:"M18.235,14.454L5.775,14.454C5.435,14.454 5.155,14.734 5.155,15.074C5.155,15.414 5.435,15.694 5.775,15.694L18.235,15.694C18.575,15.694 18.855,15.414 18.855,15.074C18.855,14.734 18.585,14.454 18.235,14.454ZM18.245,17.484L5.775,17.484C5.435,17.484 5.155,17.764 5.155,18.104C5.155,18.444 5.435,18.724 5.775,18.724L18.235,18.724C18.575,18.724 18.855,18.444 18.855,18.104L18.855,18.094C18.855,17.759 18.58,17.484 18.245,17.484ZM5.145,12.044C5.145,12.384 5.425,12.664 5.765,12.664L18.225,12.664C18.565,12.664 18.845,12.384 18.845,12.044C18.845,11.704 18.565,11.424 18.225,11.424L5.758,11.424C5.422,11.424 5.145,11.7 5.145,12.037L5.145,12.044Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,e.createElement)("path",{d:"M19.509,5.705C19.509,5.926 19.33,6.105 19.109,6.105L15.909,6.105C15.688,6.105 15.509,5.926 15.509,5.705C15.509,5.485 15.688,5.305 15.909,5.305L19.109,5.305C19.33,5.305 19.509,5.485 19.509,5.705Z"}),(0,e.createElement)("path",{d:"M19.509,6.905C19.509,7.126 19.33,7.305 19.109,7.305L15.909,7.305C15.688,7.305 15.509,7.126 15.509,6.905C15.509,6.685 15.688,6.505 15.909,6.505L19.109,6.505C19.33,6.505 19.509,6.685 19.509,6.905Z"}),(0,e.createElement)("path",{d:"M19.509,4.505C19.509,4.726 19.33,4.905 19.109,4.905L15.909,4.905C15.688,4.905 15.509,4.726 15.509,4.505C15.509,4.285 15.688,4.105 15.909,4.105L19.109,4.105C19.33,4.105 19.509,4.285 19.509,4.505Z"}))}(0,t.registerBlockType)(ae.UU,{edit:oe,save:function(t){const{attributes:l}=t,{tagName:r,openIcon:o,closeIcon:a,htmlAttributes:i,iconLocation:c,iconOnly:s,content:u}=l,g=d("gb-menu-toggle",l,!0),m=n.useBlockProps.save({className:g.join(" ").trim(),...i});return(0,e.createElement)(r,{...m},"before"===c&&(0,e.createElement)(ne,{openIcon:o,closeIcon:a}),!s&&(0,e.createElement)("span",{className:"gb-menu-toggle-text"},(0,e.createElement)(n.RichText.Content,{value:u})),"after"===c&&(0,e.createElement)(ne,{openIcon:o,closeIcon:a}))},icon:(0,e.createElement)(ie,null)}),(0,t.registerBlockVariation)("generateblocks-pro/menu-toggle",{title:(0,r.__)("Menu Toggle","generateblocks-pro"),name:"menu-toggle",icon:(0,e.createElement)(ie,null),isDefault:!0,attributes:{styles:{display:"flex",alignItems:"center",columnGap:"5px",backgroundColor:"#000000",color:"#ffffff",paddingTop:"10px",paddingRight:"10px",paddingBottom:"10px",paddingLeft:"10px",zIndex:"2",svg:{width:"25px",height:"25px",fill:"currentColor"}},iconOnly:!0}})})()})();