(()=>{var e={20493:(e,t,n)=>{"use strict";function l(e){if("Tab"!==e.key&&9!==e.keyCode)return;e.preventDefault();const t=e.currentTarget,n=t.querySelectorAll('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]'),l=Array.from(n).filter((e=>null!==e.offsetParent&&"hidden"!==getComputedStyle(e).visibility&&"none"!==getComputedStyle(e).display));if(0===l.length)return;const o=l[0],s=l[l.length-1],r=document.activeElement;if(t.contains(r))if(e.shiftKey)if(r===o)s.focus();else{const e=l.indexOf(r);e>0&&l[e-1].focus()}else if(r===s)o.focus();else{const e=l.indexOf(r);e<l.length-1&&l[e+1].focus()}else o.focus()}function o(e){const t=e.querySelectorAll(".gb-menu-toggle"),n=e.querySelector(".gb-menu-container"),o=n.querySelector(".gb-menu"),s=o.querySelectorAll(".menu-item"),r=o.querySelectorAll(".menu-item > a"),a=o.querySelectorAll(".gb-submenu-toggle"),i=e.closest("body");requestAnimationFrame((()=>{i.removeAttribute("data-gb-menu-open"),e.classList.remove("gb-navigation--open"),n&&(n.classList.remove("gb-menu-container--toggled"),n.removeEventListener("keydown",l)),t.forEach((e=>{e&&(e.classList.remove("gb-menu-toggle--toggled"),e.ariaExpanded="false",(e.offsetHeight>0||e.offsetWidth>0)&&e.focus())})),s?.length>0&&s.forEach((e=>{e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open")})),r?.length>0&&r.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")})),a?.length>0&&a.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")}))}))}function s(e,t=null){if(!e)return;const n=e.querySelectorAll(".menu-item.gb-sub-menu--open");n&&Array.from(n).filter((e=>!e.contains(t))).forEach((e=>{const t=e.querySelector("a"),n=e.querySelector(".gb-submenu-toggle");e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open"),e.setAttribute("aria-current","false"),t&&t.hasAttribute("aria-expanded")&&t.setAttribute("aria-expanded","false"),n&&n.hasAttribute("aria-expanded")&&n.setAttribute("aria-expanded","false")}))}function r(e,t=!1){if(e){t&&t.preventDefault();const n=e.closest(".gb-navigation"),l=e.closest(".menu-item"),o="true"===e.getAttribute("aria-expanded");s(n,l),e.setAttribute("aria-expanded",o?"false":"true"),l.classList.toggle("current-menu-item"),l.classList.toggle("gb-sub-menu--open")}}function a(e,t=!1){if(e){t&&t.preventDefault();const n=t.type,l=e.closest(".gb-menu-container--toggled"),o=e.closest(".gb-menu--hover");if("click"===n&&o&&!l)return;const s=e.closest(".menu-item"),r="true"===e.getAttribute("aria-expanded");e.setAttribute("aria-expanded",r?"false":"true"),s.classList.toggle("current-menu-item"),s.classList.toggle("gb-sub-menu--open")}}function i(e){e&&e.forEach((e=>{var t;const n=e.querySelector(".gb-menu-toggle"),l=e.querySelector(".gb-menu-container"),s=null!==(t=e.getAttribute("data-gb-mobile-breakpoint"))&&void 0!==t?t:"",r=window.matchMedia(`(max-width: ${s})`);n&&l&&n.setAttribute("aria-controls",l.id),e.classList.toggle("gb-navigation--mobile",r.matches),l.classList.toggle("gb-menu-container--mobile",r.matches),r.addEventListener("change",(t=>{e.classList.toggle("gb-navigation--mobile",t.matches),l.classList.toggle("gb-menu-container--mobile",t.matches),o(e)})),setTimeout((()=>{const t=e.querySelector(".gb-menu");if(t){const e=t.querySelectorAll(".menu-item-has-children");e.length>0&&requestAnimationFrame((()=>{e.forEach((e=>{const n=e.querySelector("a"),l=t.classList.contains("gb-menu--click")?n:e.querySelector(".gb-submenu-toggle");if(l){l.setAttribute("aria-controls",`sub-menu-${e.id}`),l.setAttribute("aria-label",`Toggle submenu for ${n.textContent}`);const t=e.querySelector(".gb-sub-menu");t&&(t.id=`sub-menu-${e.id}`)}}))}))}}),0)}))}function c(){let e=document.querySelectorAll(".gb-navigation");if(!e.length){const t=window.frameElement;if(t&&t.id&&t.id.startsWith("pattern-"))return void new MutationObserver(((t,n)=>{e=document.querySelectorAll(".gb-navigation"),e.length&&(n.disconnect(),i(e))})).observe(document.body,{childList:!0,subtree:!0})}i(e),function(){const e=document.querySelectorAll(".gb-navigation--mobile");e&&e.length&&e.forEach((e=>{e.addEventListener("click",(t=>{const n=t.target.closest('a[href*="#"]');if(!n)return;const l=n.getAttribute("href").match(/#(.+)$/);if(l){const t=l[1];document.getElementById(t)&&setTimeout((()=>{o(e)}),50)}}))}))}()}var u;n.d(t,{Qg:()=>r}),window.myNavigationScriptInitialized||(window.myNavigationScriptInitialized=!0,document.addEventListener("click",(e=>{const t=e.target;!function(e){if(e){var t;const n=e.closest(".gb-navigation");if(!n)return;n.classList.contains("gb-navigation--open")?o(n):function(e){const t=e.querySelectorAll(".gb-menu-toggle"),n=e.querySelector(".gb-menu-container"),o=e.getAttribute("data-gb-mobile-menu-type"),s=n.querySelector(".gb-menu-toggle:not(.gb-menu-toggle--clone)"),r=s||"full-overlay"!==o?null:n.querySelector("*"),a=e.closest("body");let i=!1;requestAnimationFrame((()=>{if(e.classList.add("gb-navigation--open"),a.setAttribute("data-gb-menu-open",o),t.forEach((e=>{if(e&&(e.classList.add("gb-menu-toggle--toggled"),e.ariaExpanded="true",!s&&n&&"full-overlay"===o)){r&&(r.style.opacity="0");const t=e.closest(".editor-styles-wrapper"),l=n.querySelector(".gb-menu-toggle--clone");if(t&&l){const t=e.attributes;for(const e of t)l.setAttribute(e.name,e.value);l.innerHTML=e.innerHTML,l.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),i=!0}else if(!l){const t=e.cloneNode(!0);t.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),n.insertAdjacentElement("afterbegin",t),i=!0}}})),i&&r?requestAnimationFrame((()=>{!function(e,t=()=>{}){const n=e.querySelector(".gb-menu-container .gb-menu-toggle");if(n){var l,o;const s=window.getComputedStyle(n),r=null!==(l=parseInt(s?.top,10))&&void 0!==l?l:0,a=null!==(o=parseInt(s?.height,10))&&void 0!==o?o:0;requestAnimationFrame((()=>{e.style.setProperty("--gb-menu-toggle-offset",a+2*r+"px"),t()}))}}(e,(()=>{r.style.opacity=""}))})):r&&"0"===r.style.opacity&&(r.style.opacity=""),n){n.classList.add("gb-menu-container--toggled");const e=n.querySelector('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]');e?e.focus():n.focus(),n.addEventListener("keydown",l)}})),"partial-overlay"===o&&function(e){const t=(n=function(e){var t;const n=null!==(t=e.getAttribute("data-gb-menu-toggle-anchor"))&&void 0!==t?t:"";let l=".gb-navigation";return n?l=n:e.closest(".gb-site-header")&&(l=".gb-site-header"),e.closest(l)}(e))?n.getBoundingClientRect().bottom:0;var n;requestAnimationFrame((()=>e.style.setProperty("--gb-menu-offset",t+"px")))}(e)}(n);const s=null!==(t=window.frameElement)&&void 0!==t&&t;if(s&&s.id&&s.id.startsWith("pattern-"))if(n.classList.contains("gb-navigation--open")){const e=s.getAttribute("data-gb-original-height");e&&(s.style.height=e)}else s.style.height&&parseInt(s.style.height,10)<800&&(s.setAttribute("data-gb-original-height",s.style.height),requestAnimationFrame((()=>s.style.height="800px")))}}(t.closest(".gb-menu-toggle")),r(t.closest(".gb-menu--click .menu-item-has-children > a"),e),a(t.closest(".gb-submenu-toggle"),e);const n=document.querySelector(".menu-item.gb-sub-menu--open");n&&!n.contains(e.target)&&s(n.closest(".gb-navigation:not(.gb-navigation--open)"))})),document.addEventListener("keydown",(e=>{const t="Escape"===e.key,n="Enter"===e.key,l=" "===e.key,i="Tab"===e.key;if((n||l)&&(a(e.target.closest(".gb-submenu-toggle"),e),r(e.target.closest(".gb-menu--click .menu-item-has-children > a"),e)),i){const e=document.querySelector(".gb-sub-menu--open");e&&setTimeout((()=>{const t=document.activeElement;t.closest(".gb-sub-menu--open")||s(e.closest(".gb-navigation"),t)}),0)}if(t){const t=e.target.closest(".gb-sub-menu--open");if(t){s(t.closest(".gb-navigation"));const e=t.querySelector(".gb-submenu-toggle");e&&e.focus()}else{const e=document.querySelector(".gb-navigation--open");e&&o(e)}}})),window.addEventListener("pagehide",(()=>{const e=document.querySelectorAll(".gb-navigation--open");e.length&&e.forEach((e=>o(e)))})),u=()=>{document.querySelector(".editor-styles-wrapper, .wp-admin")?window.addEventListener("load",c):c()},"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",u):u()))},46942:(e,t)=>{var n;!function(){"use strict";var l={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=r(e,s(n)))}return e}function s(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)l.call(e,n)&&e[n]&&(t=r(t,n));return t}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},t={};function n(l){var o=t[l];if(void 0!==o)return o.exports;var s=t[l]={exports:{}};return e[l](s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var l in t)n.o(t,l)&&!n.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React,t=window.wp.blocks,l=window.wp.blockEditor,o=window.wp.element,s=window.wp.i18n,r=window.wp.data,a=window.wp.compose,i=window.wp.components,c=window.gbp.components,u=window.gbp.blockStyles,d=window.gbp.stylesBuilder,g=(0,r.createReduxStore)("gbp-menu-toggle-state",{reducer:function(e=!1,t){return"SET_DATA"===t.type?t.payload:e},actions:{setMenuToggleState:e=>({type:"SET_DATA",payload:e})},selectors:{menuToggleState:e=>e}});function b(e,t,n=!1){const{styles:l={},uniqueId:o="",globalClasses:s=[]}=t,r=[];return n&&r.push(e),s.length>0&&r.push(...s),Object.keys(l).length>0&&r.push(`${e}-${o}`),r}const m=(0,r.createReduxStore)("gbp-current-style",{reducer:d.currentStyleReducer,actions:d.currentStyleActions,selectors:d.currentStyleSelectors}),p=(0,r.createReduxStore)("gbp-styles",{reducer:d.styleReducer,actions:d.styleActions,selectors:d.styleSelectors}),f=(0,r.createReduxStore)("gbp-styles-at-rule",{reducer:d.atRuleReducer,actions:d.atRuleActions,selectors:d.atRuleSelectors}),y=(0,r.createReduxStore)("gbp-styles-nested-rule",{reducer:d.nestedRuleReducer,actions:d.nestedRuleActions,selectors:d.nestedRuleSelectors}),v=window.wp.apiFetch;var h=n.n(v);const k=window.wp.notices,S=window.wp.url,E=window.wp.coreData;var A;window.lodash;const w="undefined"!=typeof gbGlobalStylePermissions&&null!==(A=gbGlobalStylePermissions?.canManageStyles)&&void 0!==A&&A,C=window.wp.editPost;const _=(0,r.createReduxStore)("gbp-block-styles-current-style",{reducer:d.currentStyleReducer,actions:d.currentStyleActions,selectors:d.currentStyleSelectors}),x=(0,r.createReduxStore)("gbp-block-styles-at-rule",{reducer:d.atRuleReducer,actions:d.atRuleActions,selectors:d.atRuleSelectors}),L=(0,r.createReduxStore)("gbp-block-styles-nested-rule",{reducer:d.nestedRuleReducer,actions:d.nestedRuleActions,selectors:d.nestedRuleSelectors});function R(){const e=(0,r.useSelect)((e=>e(x).getAtRule())),{setAtRule:t}=(0,r.useDispatch)(x),n=(0,r.useSelect)((e=>e(L).getNestedRule())),{setNestedRule:o}=(0,r.useDispatch)(L),a=(0,u.useCurrentAtRule)(d.defaultAtRules),{setCurrentStyle:i}=(0,r.useDispatch)(_),c=(0,r.useSelect)((e=>e(_).currentStyle())),{deviceType:g,setDeviceType:b}=(0,u.useDeviceType)(),v=function(){const{setCurrentStyle:e}=(0,r.useDispatch)(m),{setStyles:t}=(0,r.useDispatch)(p),{createNotice:n,removeAllNotices:o}=(0,r.useDispatch)(k.store),{getEntityRecordEdits:a}=(0,r.useSelect)(E.store),{getSelectedBlock:i}=(0,r.useSelect)((e=>e(l.store)),[]),{setAtRule:c}=(0,r.useDispatch)(f),{setNestedRule:u}=(0,r.useDispatch)(y),{openGeneralSidebar:d}=(0,r.useDispatch)(C.store);return async(l,r={})=>{if(!w)return;const{classStyles:g,classPostId:b}=await async function(e){var t;const n=await h()({path:(0,S.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let l=null!==(t=n?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(l)&&0===l.length&&(l={}),{classStyles:l,classPostId:n?.response?.data?.postId}}(l);if(!b)return o("snackbar"),void n("error",(0,s.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,s.__)("%s does not exist.","generateblocks-pro"),l),{type:"snackbar"});c(""),u(""),d("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:b,name:l,classStyles:g,clientId:i()?.clientId,options:r}),r.nestedRule&&u(r.nestedRule),r.atRule&&c(r.atRule);const m=a("postType","gblocks_styles",b);t(m?.gb_style_data||g),o("snackbar"),n("info",(0,s.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,s.__)("Editing %s.","generateblocks-pro"),l),{type:"snackbar"})}}(),A=function(){const{setCurrentStyle:e}=(0,r.useDispatch)(m),{setStyles:t}=(0,r.useDispatch)(p),{setAtRule:n}=(0,r.useDispatch)(f),{setNestedRule:l}=(0,r.useDispatch)(y);return()=>{e({}),t({}),n(""),l("")}}();return{atRule:e,nestedRule:n,setAtRule:t,currentAtRule:a,setNestedRule:o,setDeviceType:b,deviceType:g,setCurrentStyle:i,currentStyle:c,getPreviewDevice:u.getPreviewDevice,setGlobalStyle:v,cancelEditGlobalStyle:A}}function B({attributes:t,setAttributes:n,shortcuts:l,onStyleChange:o}){const{atRule:s,setAtRule:r,nestedRule:a,setNestedRule:i,setDeviceType:c,getPreviewDevice:g,currentStyle:b,setGlobalStyle:m,cancelEditGlobalStyle:p}=R(),{styles:f,globalClasses:y=[]}=t,v=(0,d.getStylesObject)(f,s,a);return(0,e.createElement)(d.StylesBuilder,{currentSelector:b?.selector,styles:v,allStyles:f,onDeleteStyle:(e,t)=>{const l=(0,d.deleteStylesObjectKey)(f,e,t);n({styles:l})},nestedRule:a,atRule:s,onStyleChange:(e,t=null)=>o(e,t,s,a),onNestedRuleChange:e=>i(e),onAtRuleChange:e=>{r(e);const t=(0,d.getPreviewWidth)(e),n=g(t);n&&c(n)},onUpdateKey:(e,t,l)=>{const o=(0,d.updateStylesObjectKey)(f,e,t,l);n({styles:o})},selectorShortcuts:l.selectorShortcuts,visibleSelectors:l.visibleShortcuts,onEditStyle:m,cancelEditStyle:p,setLocalTab:e=>{sessionStorage.setItem(u.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:y})}function M(t){return n=>{const{attributes:l,name:s,setAttributes:r,isSelected:a,clientId:i}=n,{uniqueId:c,styles:g,css:b}=l,{atRule:m,deviceType:p,setAtRule:f,currentStyle:y,setCurrentStyle:v,setNestedRule:h}=R(),k=(0,u.useSetStyles)(n,{cleanStylesObject:d.cleanStylesObject}),S=(0,o.useMemo)((()=>c?(0,u.getSelector)(s,c):""),[s,c]),E=Array.isArray(g)?{}:g;return(0,u.useAtRuleEffect)({deviceType:p,atRule:m,setAtRule:f,defaultAtRules:d.defaultAtRules,isSelected:a,getPreviewWidth:d.getPreviewWidth}),(0,u.useGenerateCSSEffect)({selector:S,styles:E,setAttributes:r,getCss:d.getCss,getSelector:u.getSelector,isSelected:a,blockCss:b,clientId:i}),(0,u.useStyleSelectorEffect)({isSelected:a,currentStyle:y,selector:S,setCurrentStyle:v,setNestedRule:h}),(0,u.useDecodeStyleKeys)({styles:g,setAttributes:r}),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(u.Style,{selector:S,getCss:d.getCss,styles:E,clientId:i,name:s}),(0,e.createElement)(t,{...n,selector:S,onStyleChange:function(e,t="",n="",l=""){const o="object"==typeof e?e:{[e]:t},s=(0,u.buildChangedStylesObject)(o,n,l);k(s)},getStyleValue:function(e,t="",n=""){var l,o,s,r;return n?t?null!==(s=g?.[n]?.[t]?.[e])&&void 0!==s?s:"":null!==(o=g?.[n]?.[e])&&void 0!==o?o:"":t?null!==(r=g?.[t]?.[e])&&void 0!==r?r:"":null!==(l=g?.[e])&&void 0!==l?l:""},styles:E}))}}const T=window.wp.hooks,I=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function q(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}const N=(e,t)=>e&&Array.isArray(e)?e.reduce(((e,n)=>{const l=!(t&&t.length>0)||t.includes(n.name),o=N(n.innerBlocks,t);return{total:e.total+1+o.total,allowed:e.allowed+(l?1:0)+o.allowed}}),{total:0,allowed:0}):{total:0,allowed:0},D=(e,t)=>e&&Array.isArray(e)?e.filter((e=>{const{name:n}=e;return!(t&&t.length>0)||t.includes(n)})).map((e=>({...e,innerBlocks:D(e.innerBlocks,t)}))):[],P=(0,o.memo)((({block:n,level:l=0,currentClientId:o,selectBlock:s})=>{var a;const{name:c,innerBlocks:u,clientId:d}=n,{getBlockType:g}=(0,r.useSelect)((e=>e(t.store)),[]),b=g(c);return(0,e.createElement)("div",{className:"gb-block-node","data-level":l},(0,e.createElement)(i.Button,{variant:"tertiary",size:"compact",className:"gb-block-node-button",onClick:()=>s(d),isPressed:o===d,icon:null!==(a=b?.icon?.src)&&void 0!==a?a:null},function(e,t,n="list-view"){const{__experimentalLabel:l,title:o}=e||{};if(!e)return"Unknown Block";const s=l&&l(t,{context:n});return!s||"string"!=typeof s&&"number"!=typeof s?o||"Unnamed Block":s}(b,n.attributes)),u&&u.length>0&&(0,e.createElement)("div",{className:"gb-block-tree-inner-blocks"},u.map((t=>(0,e.createElement)(P,{key:t.clientId,block:t,level:l+1,currentClientId:o,blockType:b,selectBlock:s})))))}));function O({blocks:t,clientId:n,allowedBlocks:a,showAllLabel:c=(0,s.__)("Show all blocks","generateblocks-pro")}){const[u,d]=(0,o.useState)(!1),{selectBlock:g}=(0,r.useDispatch)(l.store),b=(0,o.useMemo)((()=>N(t,a)),[t,a]),m=(0,o.useMemo)((()=>D(t,a)),[t,a]),p=(0,o.useMemo)((()=>D(t,[])),[t]),f=(0,o.useMemo)((()=>u?p:m),[u,p,m]),y=b.total!==b.allowed;return(0,e.createElement)("div",{className:"gb-block-tree"},!!y&&(0,e.createElement)("div",{className:"gb-block-tree__show-all"},(0,e.createElement)(i.ToggleControl,{label:c,checked:u,onChange:e=>d(e)})),f.map((t=>(0,e.createElement)(P,{key:t.clientId,block:t,currentClientId:n,selectBlock:g}))))}function j({value:t,onChange:n}){const l=(0,o.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus?.map((e=>({label:e.name,value:e.term_id.toString()}))))&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]);return l.length?(0,e.createElement)(i.SelectControl,{label:(0,s.__)("Menu","generateblocks-pro"),value:t,options:l,onChange:n}):null}var F=n(46942),U=n.n(F);function G({name:e,clientId:t,align:n,children:s}){const{getBlockRootClientId:a}=(0,r.useSelect)((e=>e("core/block-editor")),[]),i=(0,r.useSelect)((e=>{const{getSettings:t}=e(l.store);return t().supportsLayout||!1}),[]),c=e.toString().replace("/","-"),u={className:U()({"wp-block":!0,"gb-is-root-block":!0,[`gb-root-block-${c}`]:!0,[`align${n}`]:i}),"data-align":n&&!i?n:null,"data-block":t},d=a(t);return(0,T.applyFilters)("generateblocks.rootElement.disable",d,{name:e})?s:(0,o.createElement)("div",u,s)}const Z=window.wp.serverSideRender;var $=n.n(Z);const z={},W=[];var V=n(20493);function H(){var t,n;const l=null!==(t=generateblocksBlockClassicMenu?.menuAdminUrl)&&void 0!==t?t:"",[a,c]=(0,o.useState)(null!==(n=generateblocksBlockClassicMenu?.hasMenuSupport)&&void 0!==n&&n),u=(0,o.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus)&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]),d=(0,r.useSelect)((e=>e(E.store).getEntityRecord("root","site")?.generateblocks_pro_classic_menu_support||!1),[]),{editEntityRecord:g,saveEditedEntityRecord:b}=(0,r.useDispatch)(E.store),[m,p]=(0,o.useState)(d);return a?!l||u.length>0?null:(0,e.createElement)(i.Notice,{isDismissible:!1,status:"warning"},(0,o.createInterpolateElement)((0,s.__)("No menus found. Please <CreateMenuLink />.","generateblocks-pro"),{CreateMenuLink:(0,e.createElement)("a",{href:l,target:"_blank",rel:"noopener noreferrer"},(0,s.__)("create a menu","generateblocks-pro"))})):(0,e.createElement)(i.Notice,{isDismissible:!1,status:"warning"},(0,e.createElement)(i.ToggleControl,{label:(0,s.__)("Enable Menu Support","generateblocks-pro"),checked:m,onChange:async e=>{p(e),c(e);try{await g("root","site",void 0,{generateblocks_pro_classic_menu_support:e}),await b("root","site",void 0)}catch(e){var t;console.error("Save failed:",e),p(d),c(null!==(t=generateblocksBlockClassicMenu?.hasMenuSupport)&&void 0!==t&&t)}},help:(0,s.__)("Your theme does not support the menu system. Enable it here.","generateblocks-pro")}))}(0,a.compose)(M,u.withUniqueId)((function(t){var n;const{attributes:a,setAttributes:i,getStyleValue:d,onStyleChange:m,clientId:p,name:f,context:y,isSelected:v}=t,{menu:h,uniqueId:k}=a,S=(0,o.useRef)(),E=(0,r.useSelect)((e=>e(g).menuToggleState())),{getBlockParentsByBlockName:A,getBlock:w}=(0,r.useSelect)((e=>e(l.store)),[]),{selectBlock:C}=(0,r.useDispatch)(l.store),_=(0,o.useMemo)((()=>{var e;return null!==(e=A(p,"generateblocks-pro/navigation",!0)?.[0])&&void 0!==e?e:""}),[p]),x=(0,o.useMemo)((()=>w(_)),[_,v]),L=(0,o.useMemo)((()=>w(p)),[p,v]),R=(0,o.useMemo)((()=>{var e;return null!==(e=L?.innerBlocks?.find((e=>"generateblocks-pro/classic-menu-item"===e.name))?.clientId)&&void 0!==e?e:""}),[L]),M=(0,o.useMemo)((()=>{var e;return null!==(e=L?.innerBlocks?.find((e=>"generateblocks-pro/classic-sub-menu"===e.name))?.clientId)&&void 0!==e?e:""}),[L]),T=S?.current?.querySelector(".gb-menu"),I=(0,o.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus?.map((e=>({label:e.name,value:e.term_id.toString()}))))&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]);(0,o.useEffect)((()=>{var e;h&&I.find((e=>e.value===h))||i({menu:null!==(e=I[0]?.value)&&void 0!==e?e:""})}),[I.length]);const q=b("gb-menu",a,!0),N=(0,l.useBlockProps)({className:q.filter(Boolean).join(" ").trim(),ref:S}),D=(0,l.useInnerBlocksProps)({},{allowedBlocks:["generateblocks-pro/classic-menu-item","generateblocks-pro/classic-sub-menu"],renderAppender:!1}),P={name:f,attributes:a,setAttributes:i,clientId:p,getStyleValue:d,onStyleChange:m},F={};return F.subMenuType=null!==(n=y?.["generateblocks-pro/subMenuType"])&&void 0!==n?n:"hover",F.disableLinks=!0,(0,o.useEffect)((()=>{if(!S?.current)return;const e=S.current.closest(".gb-menu-container");if(!e)return;const t=e.querySelectorAll(".gb-menu--click .menu-item-has-children > a"),n=[];return t.length>0&&t.forEach((e=>{const t=t=>{(0,V.Qg)(e,t)};e.addEventListener("click",t),n.push({item:e,handler:t})})),()=>{n.forEach((({item:e,handler:t})=>{e.removeEventListener("click",t)}))}}),[S,T]),(0,o.useEffect)((()=>{if(!S?.current)return;const e=S?.current?.closest(".gb-navigation");if(!e)return;const t=e.querySelectorAll(".gb-submenu-toggle"),n=[];return t.length>0&&t.forEach((e=>{const t=t=>{const n=e.closest(".gb-menu-container--toggled"),l=e.closest(".gb-menu--hover");"click"===t?.type&&l&&!n||(0,V.Qg)(e,t)};e.addEventListener("click",t),n.push({item:e,handler:t})})),()=>{n.forEach((({item:e,handler:t})=>{e.removeEventListener("click",t)}))}}),[S,E,T]),(0,o.useEffect)((()=>{const e=S?.current;if(e)return e.addEventListener("click",t),()=>{e.removeEventListener("click",t)};function t(e){e.target.closest(".gb-sub-menu")?C(M):e.target.closest(".menu-item")&&C(R)}}),[S,R,T,M]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(u.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.OpenPanel,{title:(0,s.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...P},(0,e.createElement)(O,{blocks:[x],clientId:p,allowedBlocks:K,showAllLabel:(0,s.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(H,null),(0,e.createElement)(c.OpenPanel,{...P,panelId:"settings"},(0,e.createElement)(j,{value:h,onChange:e=>i({menu:e})}))),stylesTab:(0,e.createElement)(B,{attributes:a,setAttributes:i,shortcuts:{selectorShortcuts:z,visibleShortcuts:W},onStyleChange:m})})),(0,e.createElement)("div",{...N},I.length&&h?(0,e.createElement)($(),{key:h+k,block:"generateblocks-pro/classic-menu",attributes:a,urlQueryArgs:F}):(0,e.createElement)(e.Fragment,null,(0,s.__)("No menu found.","generateblocks-pro")),(0,e.createElement)("div",{...D})))}));const K=["generateblocks-pro/navigation","generateblocks-pro/menu-container","generateblocks-pro/classic-menu","generateblocks-pro/classic-menu-item","generateblocks-pro/classic-sub-menu","generateblocks-pro/menu-toggle"];function Q(e){for(const t of e){if("generateblocks-pro/classic-menu"===t.name)return t;if(t.innerBlocks&&t.innerBlocks.length>0){const e=Q(t.innerBlocks);if(e)return e}}return null}const J=(0,a.compose)((function(t){return n=>{var s,a,c,u;const{attributes:d,setAttributes:g,context:b}=n,{htmlAttributes:m={},uniqueId:p,className:f,align:y}=d,v=(0,r.useSelect)((e=>e("core/editor").isSavingPost())),{style:h="",href:k,...S}=m,E=Object.keys(S).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(S[t]),e)),{}),[A,w]=(0,o.useState)(h);var C,_,x,L;(0,o.useEffect)((()=>{!async function(){const e=await(0,T.applyFilters)("generateblocks.editor.htmlAttributes.style",h,{...n});w(e)}()}),[h,b,v]),C=()=>{const e=["alignwide","alignfull"],t=(f?.split(" ")||[]).filter((t=>!e.includes(t)));y&&t.push("align"+y),g({className:t.join(" ")})},_=[y],L=(x=(0,e.useRef)(!0)).current?(x.current=!1,!0):x.current,(0,e.useEffect)((function(){if(!L)return C()}),_);const R="string"==typeof A?A.split(";").reduce(((e,t)=>{const n=t.indexOf(":");if(-1===n)return e;let l=t.slice(0,n).trim();const o=t.slice(n+1).trim();return l&&o&&(l.startsWith("--")||(l=l.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[l]=o),e}),{}):"",B={...E,style:R,"data-gb-id":p,"data-context-post-id":null!==(s=null!==(a=b?.postId)&&void 0!==a?a:b?.["generateblocks/loopIndex"])&&void 0!==s?s:0,"data-align":y||void 0},M=(0,o.useMemo)((()=>Array.isArray(m)?{}:m),[JSON.stringify(m)]);return(0,o.useEffect)((()=>{const e={...m};Object.keys(e).forEach((t=>{const n=t.startsWith("data-"),l=e[t];I.includes(t)||""!==l||n||"alt"===t||delete e[t],"string"!=typeof l&&"boolean"!=typeof l&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(const l of n)if(e[l]!==t[l])return!1;return!0}(e,m)||g({htmlAttributes:e})}),[JSON.stringify(m)]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(t,{...n,editorHtmlAttributes:B,htmlAttributes:M}),(0,e.createElement)(l.InspectorAdvancedControls,null,(0,e.createElement)(i.TextControl,{label:"HTML ID",value:null!==(c=m.id)&&void 0!==c?c:"",onChange:e=>{g({htmlAttributes:{...m,id:e}})},onBlur:()=>{m.id&&g({htmlAttributes:{...m,id:q(m.id)}})}}),(0,e.createElement)(i.TextControl,{label:"ARIA Label",value:null!==(u=m["aria-label"])&&void 0!==u?u:"",onChange:e=>{g({htmlAttributes:{...m,"aria-label":e}})}})))}}),M,u.withUniqueId)((function(t){var n,a,m,p,f;const{attributes:y,setAttributes:v,editorHtmlAttributes:h,onStyleChange:k,getStyleValue:S,clientId:E,name:A,isSelected:w}=t,{styles:C,htmlAttributes:_,tagName:x,uniqueId:L,subMenuType:R}=y,M=(0,r.useSelect)((e=>e(g).menuToggleState())),T=(0,o.useRef)(),{getBlock:I,getBlocks:q}=(0,r.useSelect)((e=>e(l.store)),[]),{updateBlockAttributes:N}=(0,r.useDispatch)(l.store),D=(0,o.useMemo)((()=>I(E)),[E,w]),[P,F]=(0,o.useState)(null),U=b("gb-navigation",{...y,styles:C},!0);M&&U.push("gb-navigation--open"),U.includes("gb-navigation-"+L)||U.push("gb-navigation-"+L);const Z=(0,l.useBlockProps)({className:U.filter((e=>e)).join(" ").trim(),...h,ref:T}),$=(0,l.useInnerBlocksProps)(Z),z=x||"nav";(0,o.useEffect)((()=>{x||v({tagName:"nav"})}),[x]),(0,o.useEffect)((()=>{_?.["data-gb-mobile-menu-type"]||v({htmlAttributes:{..._,"data-gb-mobile-menu-type":"full-overlay"}})}),[_?.["data-gb-mobile-menu-type"]]),(0,o.useEffect)((()=>{if(!w)return;const e=Q(q(E));e&&F(e)}),[w,E]);const W={name:A,attributes:y,setAttributes:v,clientId:E,getStyleValue:S,onStyleChange:k},V=null!==(n=_?.["data-gb-mobile-menu-transition"])&&void 0!==n?n:"",J=null!==(a=_?.["data-gb-sub-menu-transition"])&&void 0!==a?a:"";return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(u.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.OpenPanel,{title:(0,s.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...W},(0,e.createElement)(O,{blocks:[D],clientId:E,allowedBlocks:K,showAllLabel:(0,s.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(H,null),(0,e.createElement)(c.OpenPanel,{panelId:"settings",...W},!!P&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(j,{value:P?.attributes?.menu,onChange:e=>{N(P.clientId,{menu:e});const t=Q(q(E));t&&F(t)}})),(0,e.createElement)(i.SelectControl,{label:(0,s.__)("Sub-menu type","generateblocks-pro"),value:R,options:[{label:(0,s.__)("Hover","generateblocks-pro"),value:"hover"},{label:(0,s.__)("Click Menu Item","generateblocks-pro"),value:"click"},{label:(0,s.__)("Click Toggle","generateblocks-pro"),value:"click-toggle"}],onChange:e=>v({subMenuType:e})}),(0,e.createElement)(i.SelectControl,{label:(0,s.__)("Sub-menu Transition","generateblocks-pro"),value:J,options:[{label:(0,s.__)("None","generateblocks-pro"),value:""},{label:(0,s.__)("Fade","generateblocks-pro"),value:"fade"},{label:(0,s.__)("Slide & fade up","generateblocks-pro"),value:"fade-slide-up"},{label:(0,s.__)("Slide & fade down","generateblocks-pro"),value:"fade-slide-down"},{label:(0,s.__)("Slide & fade left","generateblocks-pro"),value:"fade-slide-left"},{label:(0,s.__)("Slide & fade right","generateblocks-pro"),value:"fade-slide-right"}],onChange:e=>{const t={..._};e?t["data-gb-sub-menu-transition"]=e:(delete t["data-gb-sub-menu-transition"],k("--sub-menu-transition-speed","")),v({htmlAttributes:t})}}),""!==J&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(d.UnitControl,{units:["ms"],defaultUnitValue:"ms",label:(0,s.__)("Sub-menu Transition Speed","generateblocks-pro"),placeholder:"200ms",value:S("--sub-menu-transition-speed",""),onChange:e=>{k("--sub-menu-transition-speed",e)}}),"fade"!==J&&(0,e.createElement)(d.UnitControl,{label:(0,s.__)("Sub-menu Transition Distance","generateblocks-pro"),placeholder:"5px",value:S("--sub-menu-transition-distance",""),onChange:e=>{k("--sub-menu-transition-distance",e)}})),(0,e.createElement)(d.UnitControl,{label:(0,s.__)("Mobile breakpoint","generateblocks-pro"),value:null!==(m=_?.["data-gb-mobile-breakpoint"])&&void 0!==m?m:"",onChange:e=>{const t={..._};e?t["data-gb-mobile-breakpoint"]=e:delete t["data-gb-mobile-breakpoint"],v({htmlAttributes:t})}}),(0,e.createElement)(i.SelectControl,{label:(0,s.__)("Mobile Menu Type","generateblocks-pro"),value:null!==(p=_?.["data-gb-mobile-menu-type"])&&void 0!==p?p:"",options:[{label:(0,s.__)("Full overlay","generateblocks-pro"),value:"full-overlay"},{label:(0,s.__)("Partial overlay","generateblocks-pro"),value:"partial-overlay"}],onChange:e=>{const t={..._};e?t["data-gb-mobile-menu-type"]=e:delete t["data-gb-mobile-menu-type"],v({htmlAttributes:t})}}),"partial-overlay"===_?.["data-gb-mobile-menu-type"]&&(0,e.createElement)(i.TextControl,{label:(0,s.__)("Mobile Menu Anchor","generateblocks-pro"),help:(0,s.__)("The selector for the element the mobile menu will attach to the bottom of.","generateblocks-pro"),value:null!==(f=_?.["data-gb-menu-toggle-anchor"])&&void 0!==f?f:"",placeholder:"Calculate automatically",onChange:e=>{const t={..._};e?t["data-gb-menu-toggle-anchor"]=e:delete t["data-gb-menu-toggle-anchor"],v({htmlAttributes:t})}}),(0,e.createElement)(i.SelectControl,{label:(0,s.__)("Mobile Menu Transition","generateblocks-pro"),value:V,options:[{label:(0,s.__)("None","generateblocks-pro"),value:""},{label:(0,s.__)("Fade","generateblocks-pro"),value:"fade"},{label:(0,s.__)("Slide left","generateblocks-pro"),value:"slide-left"},{label:(0,s.__)("Slide right","generateblocks-pro"),value:"slide-right"},{label:(0,s.__)("Slide up","generateblocks-pro"),value:"slide-up"},{label:(0,s.__)("Slide down","generateblocks-pro"),value:"slide-down"},{label:(0,s.__)("Slide & fade left","generateblocks-pro"),value:"fade-slide-left"},{label:(0,s.__)("Slide & fade right","generateblocks-pro"),value:"fade-slide-right"},{label:(0,s.__)("Slide & fade up","generateblocks-pro"),value:"fade-slide-up"},{label:(0,s.__)("Slide & fade down","generateblocks-pro"),value:"fade-slide-down"}],onChange:e=>{const t={..._};e?t["data-gb-mobile-menu-transition"]=e:(delete t["data-gb-mobile-menu-transition"],k("--mobile-transition-speed","")),v({htmlAttributes:t})}}),""!==V&&(0,e.createElement)(d.UnitControl,{units:["ms"],defaultUnitValue:"ms",label:(0,s.__)("Mobile Menu Transition Speed","generateblocks-pro"),placeholder:"200ms",value:S("--mobile-transition-speed",""),onChange:e=>{k("--mobile-transition-speed",e)}}))),stylesTab:(0,e.createElement)(B,{attributes:y,setAttributes:v,shortcuts:{},onStyleChange:k})})),(0,e.createElement)(G,{name:A,clientId:E},(0,e.createElement)(z,{...$})))})),Y=JSON.parse('{"UU":"generateblocks-pro/navigation"}'),X=window.wp.domReady;var ee=n.n(X);const te={paddingTop:"0px",paddingRight:"0px",paddingBottom:"0px",paddingLeft:"0px",marginTop:"0px",marginRight:"0px",marginBottom:"0px",marginLeft:"0px",display:"flex",flexWrap:"wrap",listStyleType:"none"},ne={display:"flex",alignItems:"center",columnGap:"5px",backgroundColor:"#000000",color:"#ffffff",paddingTop:"10px",paddingRight:"10px",paddingBottom:"10px",paddingLeft:"10px",zIndex:"2",svg:{width:"25px",height:"25px",fill:"currentColor"}},le={"&.gb-menu-container--mobile":{backgroundColor:"#000000",color:"#ffffff",paddingBottom:"60px",position:"fixed"},"&.gb-menu-container--mobile .gb-menu":{flexDirection:"column"}},oe={position:"relative",paddingTop:"0px",paddingRight:"0px",paddingBottom:"0px",paddingLeft:"0px",marginTop:"0px",marginRight:"0px",marginBottom:"0px",marginLeft:"0px",listStyleType:"none",".gb-menu-link":{display:"flex",justifyContent:"space-between",alignItems:"center",textDecoration:"none",columnGap:"10px",paddingTop:"1em",paddingBottom:"1em",paddingLeft:"20px",paddingRight:"20px"}},se={width:"200px",paddingTop:"0px",paddingRight:"0px",paddingBottom:"0px",paddingLeft:"0px",marginTop:"0px",marginRight:"0px",marginBottom:"0px",marginLeft:"0px",right:"0px",zIndex:"100",".menu-item":{backgroundColor:"#000000",color:"#ffffff"},".gb-sub-menu":{top:"0px",right:"100%"}};function re(){return(0,e.createElement)("svg",{viewBox:"0 0 24 24",width:"24",height:"24",className:"gblocks-block-icon","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M8.55,5.705C8.55,6.037 8.281,6.305 7.95,6.305L5.15,6.305C4.819,6.305 4.55,6.037 4.55,5.705C4.55,5.375 4.819,5.106 5.15,5.106L7.95,5.106C8.281,5.106 8.55,5.375 8.55,5.705Z"}),(0,e.createElement)("path",{d:"M14.03,5.705C14.03,6.037 13.761,6.305 13.43,6.305L10.63,6.305C10.299,6.305 10.03,6.037 10.03,5.705C10.03,5.375 10.299,5.106 10.63,5.106L13.43,5.106C13.761,5.106 14.03,5.375 14.03,5.705Z"}),(0,e.createElement)("path",{d:"M19.509,5.705C19.509,6.037 19.24,6.305 18.909,6.305L16.109,6.305C15.778,6.305 15.509,6.037 15.509,5.705C15.509,5.375 15.778,5.106 16.109,5.106L18.909,5.106C19.24,5.106 19.509,5.375 19.509,5.705Z"}),(0,e.createElement)("path",{d:"M21.405,22.172L17.655,22.172L17.655,20.922L20.78,20.922L20.78,17.797L22.03,17.797L22.03,21.547C22.03,21.892 21.75,22.172 21.405,22.172ZM6.405,22.172L2.655,22.172C2.312,22.172 2.03,21.89 2.03,21.547L2.03,17.797L3.28,17.797L3.28,20.922L6.405,20.922L6.405,22.172Z",style:{fillRule:"nonzero"}}),(0,e.createElement)("path",{d:"M2.03,12.35L2.03,2.797C2.03,2.452 2.31,2.172 2.655,2.172L21.406,2.172C21.75,2.172 22.03,2.452 22.03,2.797L22.03,12.35L20.78,12.35L20.78,9.239L3.28,9.239L3.28,12.35L2.03,12.35ZM20.778,7.975L20.78,3.422L3.279,3.422L3.261,7.975L20.778,7.975Z"}),(0,e.createElement)("path",{d:"M18.235,14.454L5.775,14.454C5.435,14.454 5.155,14.734 5.155,15.074C5.155,15.414 5.435,15.694 5.775,15.694L18.235,15.694C18.575,15.694 18.855,15.414 18.855,15.074C18.855,14.734 18.585,14.454 18.235,14.454ZM18.245,17.484L5.775,17.484C5.435,17.484 5.155,17.764 5.155,18.104C5.155,18.444 5.435,18.724 5.775,18.724L18.235,18.724C18.575,18.724 18.855,18.444 18.855,18.104L18.855,18.094C18.855,17.759 18.58,17.484 18.245,17.484ZM5.145,12.044C5.145,12.384 5.425,12.664 5.765,12.664L18.225,12.664C18.565,12.664 18.845,12.384 18.845,12.044C18.845,11.704 18.565,11.424 18.225,11.424L5.758,11.424C5.422,11.424 5.145,11.7 5.145,12.037L5.145,12.044Z",style:{fillRule:"nonzero"}}))}(0,t.registerBlockType)(Y.UU,{edit:J,save:function({attributes:t}){const{tagName:n,htmlAttributes:o={},uniqueId:s}=t,r=b("gb-navigation",t,!0);r.includes("gb-navigation-"+s)||r.push("gb-navigation-"+s);const a=l.useBlockProps.save({className:r.join(" ").trim(),...o});return(0,e.createElement)(n,{...l.useInnerBlocksProps.save(a)})},icon:(0,e.createElement)(re,null)}),ee()((()=>{(0,t.registerBlockVariation)("generateblocks-pro/navigation",{title:(0,s.__)("Navigation","generateblocks-pro"),name:"navigation",icon:(0,e.createElement)(re,null),description:(0,s.__)("Build accordions using our Container and Button blocks.","generateblocks-pro"),isDefault:!0,attributes:{htmlAttributes:{"data-gb-mobile-breakpoint":"768px","data-gb-mobile-menu-type":"full-overlay"}},innerBlocks:[["generateblocks-pro/menu-toggle",{styles:ne,iconOnly:!0}],["generateblocks-pro/menu-container",{styles:le},[["generateblocks-pro/classic-menu",{styles:te},[["generateblocks-pro/classic-menu-item",{styles:oe}],["generateblocks-pro/classic-sub-menu",{styles:se}]]]]]]})}))})()})();