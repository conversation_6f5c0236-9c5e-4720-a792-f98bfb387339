.gb-block-tree .gb-block-node{padding-left:20px;position:relative}.gb-block-tree .gb-block-node-button{border:1px solid color-mix(in srgb,var(--wp-components-color-accent,var(--wp-admin-theme-color,#3858e9)) 10%,transparent);font-size:12px;height:auto;margin-bottom:5px;padding:6px;width:100%}.gb-block-tree .gb-block-node-button:not(.is-pressed){color:currentColor}.gb-block-tree .gb-block-node-button svg{height:14px;width:14px}.gb-block-tree .gb-block-tree-inner-blocks{position:relative}.gb-block-tree .gb-block-tree-inner-blocks:empty{display:none}.gb-block-tree .gb-block-node[data-level="0"]{padding-left:0}.gb-block-tree .gb-block-node:not([data-level="0"]):before{border-left:1px solid color-mix(in srgb,var(--wp-components-color-accent,var(--wp-admin-theme-color,#3858e9)) 10%,transparent);bottom:0;content:"";left:5px;position:absolute;top:-5px}.gb-block-tree .gb-block-node:not([data-level="0"]):after{border-top:1px solid color-mix(in srgb,var(--wp-components-color-accent,var(--wp-admin-theme-color,#3858e9)) 10%,transparent);content:"";left:5px;position:absolute;top:9px;width:10px}.gb-block-tree .gb-block-node:last-child:before{bottom:auto;height:14px}.gb-block-tree__show-all{margin-bottom:1em}

.editor-styles-wrapper .wp-block-generateblocks-pro-classic-menu:not(.is-selected),.editor-styles-wrapper .wp-block-generateblocks-pro-classic-menu>div{display:contents}body[data-gb-menu-open] .editor-editor-canvas__post-title-wrapper{display:none}body[data-gb-menu-open] .editor-styles-wrapper{padding:0}body[data-gb-menu-open] .editor-styles-wrapper .wp-block{max-width:none}.gb-navigation:not(.is-selected):not(.has-child-selected) .gb-menu,.gb-navigation:not(.is-selected):not(.has-child-selected) .gb-menu-container{pointer-events:none}
