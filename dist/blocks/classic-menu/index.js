(()=>{var e={20493:(e,t,n)=>{"use strict";function l(e){if("Tab"!==e.key&&9!==e.keyCode)return;e.preventDefault();const t=e.currentTarget,n=t.querySelectorAll('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]'),l=Array.from(n).filter((e=>null!==e.offsetParent&&"hidden"!==getComputedStyle(e).visibility&&"none"!==getComputedStyle(e).display));if(0===l.length)return;const o=l[0],s=l[l.length-1],r=document.activeElement;if(t.contains(r))if(e.shiftKey)if(r===o)s.focus();else{const e=l.indexOf(r);e>0&&l[e-1].focus()}else if(r===s)o.focus();else{const e=l.indexOf(r);e<l.length-1&&l[e+1].focus()}else o.focus()}function o(e){const t=e.querySelectorAll(".gb-menu-toggle"),n=e.querySelector(".gb-menu-container"),o=n.querySelector(".gb-menu"),s=o.querySelectorAll(".menu-item"),r=o.querySelectorAll(".menu-item > a"),a=o.querySelectorAll(".gb-submenu-toggle"),c=e.closest("body");requestAnimationFrame((()=>{c.removeAttribute("data-gb-menu-open"),e.classList.remove("gb-navigation--open"),n&&(n.classList.remove("gb-menu-container--toggled"),n.removeEventListener("keydown",l)),t.forEach((e=>{e&&(e.classList.remove("gb-menu-toggle--toggled"),e.ariaExpanded="false",(e.offsetHeight>0||e.offsetWidth>0)&&e.focus())})),s?.length>0&&s.forEach((e=>{e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open")})),r?.length>0&&r.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")})),a?.length>0&&a.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")}))}))}function s(e,t=null){if(!e)return;const n=e.querySelectorAll(".menu-item.gb-sub-menu--open");n&&Array.from(n).filter((e=>!e.contains(t))).forEach((e=>{const t=e.querySelector("a"),n=e.querySelector(".gb-submenu-toggle");e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open"),e.setAttribute("aria-current","false"),t&&t.hasAttribute("aria-expanded")&&t.setAttribute("aria-expanded","false"),n&&n.hasAttribute("aria-expanded")&&n.setAttribute("aria-expanded","false")}))}function r(e,t=!1){if(e){t&&t.preventDefault();const n=e.closest(".gb-navigation"),l=e.closest(".menu-item"),o="true"===e.getAttribute("aria-expanded");s(n,l),e.setAttribute("aria-expanded",o?"false":"true"),l.classList.toggle("current-menu-item"),l.classList.toggle("gb-sub-menu--open")}}function a(e,t=!1){if(e){t&&t.preventDefault();const n=t.type,l=e.closest(".gb-menu-container--toggled"),o=e.closest(".gb-menu--hover");if("click"===n&&o&&!l)return;const s=e.closest(".menu-item"),r="true"===e.getAttribute("aria-expanded");e.setAttribute("aria-expanded",r?"false":"true"),s.classList.toggle("current-menu-item"),s.classList.toggle("gb-sub-menu--open")}}function c(e){e&&e.forEach((e=>{var t;const n=e.querySelector(".gb-menu-toggle"),l=e.querySelector(".gb-menu-container"),s=null!==(t=e.getAttribute("data-gb-mobile-breakpoint"))&&void 0!==t?t:"",r=window.matchMedia(`(max-width: ${s})`);n&&l&&n.setAttribute("aria-controls",l.id),e.classList.toggle("gb-navigation--mobile",r.matches),l.classList.toggle("gb-menu-container--mobile",r.matches),r.addEventListener("change",(t=>{e.classList.toggle("gb-navigation--mobile",t.matches),l.classList.toggle("gb-menu-container--mobile",t.matches),o(e)})),setTimeout((()=>{const t=e.querySelector(".gb-menu");if(t){const e=t.querySelectorAll(".menu-item-has-children");e.length>0&&requestAnimationFrame((()=>{e.forEach((e=>{const n=e.querySelector("a"),l=t.classList.contains("gb-menu--click")?n:e.querySelector(".gb-submenu-toggle");if(l){l.setAttribute("aria-controls",`sub-menu-${e.id}`),l.setAttribute("aria-label",`Toggle submenu for ${n.textContent}`);const t=e.querySelector(".gb-sub-menu");t&&(t.id=`sub-menu-${e.id}`)}}))}))}}),0)}))}function i(){let e=document.querySelectorAll(".gb-navigation");if(!e.length){const t=window.frameElement;if(t&&t.id&&t.id.startsWith("pattern-"))return void new MutationObserver(((t,n)=>{e=document.querySelectorAll(".gb-navigation"),e.length&&(n.disconnect(),c(e))})).observe(document.body,{childList:!0,subtree:!0})}c(e),function(){const e=document.querySelectorAll(".gb-navigation--mobile");e&&e.length&&e.forEach((e=>{e.addEventListener("click",(t=>{const n=t.target.closest('a[href*="#"]');if(!n)return;const l=n.getAttribute("href").match(/#(.+)$/);if(l){const t=l[1];document.getElementById(t)&&setTimeout((()=>{o(e)}),50)}}))}))}()}var u;n.d(t,{Qg:()=>r}),window.myNavigationScriptInitialized||(window.myNavigationScriptInitialized=!0,document.addEventListener("click",(e=>{const t=e.target;!function(e){if(e){var t;const n=e.closest(".gb-navigation");if(!n)return;n.classList.contains("gb-navigation--open")?o(n):function(e){const t=e.querySelectorAll(".gb-menu-toggle"),n=e.querySelector(".gb-menu-container"),o=e.getAttribute("data-gb-mobile-menu-type"),s=n.querySelector(".gb-menu-toggle:not(.gb-menu-toggle--clone)"),r=s||"full-overlay"!==o?null:n.querySelector("*"),a=e.closest("body");let c=!1;requestAnimationFrame((()=>{if(e.classList.add("gb-navigation--open"),a.setAttribute("data-gb-menu-open",o),t.forEach((e=>{if(e&&(e.classList.add("gb-menu-toggle--toggled"),e.ariaExpanded="true",!s&&n&&"full-overlay"===o)){r&&(r.style.opacity="0");const t=e.closest(".editor-styles-wrapper"),l=n.querySelector(".gb-menu-toggle--clone");if(t&&l){const t=e.attributes;for(const e of t)l.setAttribute(e.name,e.value);l.innerHTML=e.innerHTML,l.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),c=!0}else if(!l){const t=e.cloneNode(!0);t.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),n.insertAdjacentElement("afterbegin",t),c=!0}}})),c&&r?requestAnimationFrame((()=>{!function(e,t=()=>{}){const n=e.querySelector(".gb-menu-container .gb-menu-toggle");if(n){var l,o;const s=window.getComputedStyle(n),r=null!==(l=parseInt(s?.top,10))&&void 0!==l?l:0,a=null!==(o=parseInt(s?.height,10))&&void 0!==o?o:0;requestAnimationFrame((()=>{e.style.setProperty("--gb-menu-toggle-offset",a+2*r+"px"),t()}))}}(e,(()=>{r.style.opacity=""}))})):r&&"0"===r.style.opacity&&(r.style.opacity=""),n){n.classList.add("gb-menu-container--toggled");const e=n.querySelector('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]');e?e.focus():n.focus(),n.addEventListener("keydown",l)}})),"partial-overlay"===o&&function(e){const t=(n=function(e){var t;const n=null!==(t=e.getAttribute("data-gb-menu-toggle-anchor"))&&void 0!==t?t:"";let l=".gb-navigation";return n?l=n:e.closest(".gb-site-header")&&(l=".gb-site-header"),e.closest(l)}(e))?n.getBoundingClientRect().bottom:0;var n;requestAnimationFrame((()=>e.style.setProperty("--gb-menu-offset",t+"px")))}(e)}(n);const s=null!==(t=window.frameElement)&&void 0!==t&&t;if(s&&s.id&&s.id.startsWith("pattern-"))if(n.classList.contains("gb-navigation--open")){const e=s.getAttribute("data-gb-original-height");e&&(s.style.height=e)}else s.style.height&&parseInt(s.style.height,10)<800&&(s.setAttribute("data-gb-original-height",s.style.height),requestAnimationFrame((()=>s.style.height="800px")))}}(t.closest(".gb-menu-toggle")),r(t.closest(".gb-menu--click .menu-item-has-children > a"),e),a(t.closest(".gb-submenu-toggle"),e);const n=document.querySelector(".menu-item.gb-sub-menu--open");n&&!n.contains(e.target)&&s(n.closest(".gb-navigation:not(.gb-navigation--open)"))})),document.addEventListener("keydown",(e=>{const t="Escape"===e.key,n="Enter"===e.key,l=" "===e.key,c="Tab"===e.key;if((n||l)&&(a(e.target.closest(".gb-submenu-toggle"),e),r(e.target.closest(".gb-menu--click .menu-item-has-children > a"),e)),c){const e=document.querySelector(".gb-sub-menu--open");e&&setTimeout((()=>{const t=document.activeElement;t.closest(".gb-sub-menu--open")||s(e.closest(".gb-navigation"),t)}),0)}if(t){const t=e.target.closest(".gb-sub-menu--open");if(t){s(t.closest(".gb-navigation"));const e=t.querySelector(".gb-submenu-toggle");e&&e.focus()}else{const e=document.querySelector(".gb-navigation--open");e&&o(e)}}})),window.addEventListener("pagehide",(()=>{const e=document.querySelectorAll(".gb-navigation--open");e.length&&e.forEach((e=>o(e)))})),u=()=>{document.querySelector(".editor-styles-wrapper, .wp-admin")?window.addEventListener("load",i):i()},"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",u):u()))},46942:(e,t)=>{var n;!function(){"use strict";var l={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=r(e,s(n)))}return e}function s(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)l.call(e,n)&&e[n]&&(t=r(t,n));return t}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},t={};function n(l){var o=t[l];if(void 0!==o)return o.exports;var s=t[l]={exports:{}};return e[l](s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var l in t)n.o(t,l)&&!n.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React,t=window.wp.blocks,l=window.wp.blockEditor,o=window.wp.serverSideRender;var s=n.n(o);const r=window.wp.element,a=window.wp.components,c=window.wp.i18n,i=window.wp.compose,u=window.wp.data,d=window.wp.coreData,g=window.gbp.components,b=window.gbp.blockStyles,m=window.gbp.stylesBuilder,p=(0,u.createReduxStore)("gbp-current-style",{reducer:m.currentStyleReducer,actions:m.currentStyleActions,selectors:m.currentStyleSelectors}),y=(0,u.createReduxStore)("gbp-styles",{reducer:m.styleReducer,actions:m.styleActions,selectors:m.styleSelectors}),f=(0,u.createReduxStore)("gbp-styles-at-rule",{reducer:m.atRuleReducer,actions:m.atRuleActions,selectors:m.atRuleSelectors}),v=(0,u.createReduxStore)("gbp-styles-nested-rule",{reducer:m.nestedRuleReducer,actions:m.nestedRuleActions,selectors:m.nestedRuleSelectors}),h=window.wp.apiFetch;var S=n.n(h);const k=window.wp.notices,E=window.wp.url;var A;window.lodash;const w="undefined"!=typeof gbGlobalStylePermissions&&null!==(A=gbGlobalStylePermissions?.canManageStyles)&&void 0!==A&&A,C=window.wp.editPost;const _=(0,u.createReduxStore)("gbp-block-styles-current-style",{reducer:m.currentStyleReducer,actions:m.currentStyleActions,selectors:m.currentStyleSelectors}),L=(0,u.createReduxStore)("gbp-block-styles-at-rule",{reducer:m.atRuleReducer,actions:m.atRuleActions,selectors:m.atRuleSelectors}),R=(0,u.createReduxStore)("gbp-block-styles-nested-rule",{reducer:m.nestedRuleReducer,actions:m.nestedRuleActions,selectors:m.nestedRuleSelectors});function M(){const e=(0,u.useSelect)((e=>e(L).getAtRule())),{setAtRule:t}=(0,u.useDispatch)(L),n=(0,u.useSelect)((e=>e(R).getNestedRule())),{setNestedRule:o}=(0,u.useDispatch)(R),s=(0,b.useCurrentAtRule)(m.defaultAtRules),{setCurrentStyle:r}=(0,u.useDispatch)(_),a=(0,u.useSelect)((e=>e(_).currentStyle())),{deviceType:i,setDeviceType:g}=(0,b.useDeviceType)(),h=function(){const{setCurrentStyle:e}=(0,u.useDispatch)(p),{setStyles:t}=(0,u.useDispatch)(y),{createNotice:n,removeAllNotices:o}=(0,u.useDispatch)(k.store),{getEntityRecordEdits:s}=(0,u.useSelect)(d.store),{getSelectedBlock:r}=(0,u.useSelect)((e=>e(l.store)),[]),{setAtRule:a}=(0,u.useDispatch)(f),{setNestedRule:i}=(0,u.useDispatch)(v),{openGeneralSidebar:g}=(0,u.useDispatch)(C.store);return async(l,u={})=>{if(!w)return;const{classStyles:d,classPostId:b}=await async function(e){var t;const n=await S()({path:(0,E.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let l=null!==(t=n?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(l)&&0===l.length&&(l={}),{classStyles:l,classPostId:n?.response?.data?.postId}}(l);if(!b)return o("snackbar"),void n("error",(0,c.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,c.__)("%s does not exist.","generateblocks-pro"),l),{type:"snackbar"});a(""),i(""),g("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:b,name:l,classStyles:d,clientId:r()?.clientId,options:u}),u.nestedRule&&i(u.nestedRule),u.atRule&&a(u.atRule);const m=s("postType","gblocks_styles",b);t(m?.gb_style_data||d),o("snackbar"),n("info",(0,c.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,c.__)("Editing %s.","generateblocks-pro"),l),{type:"snackbar"})}}(),A=function(){const{setCurrentStyle:e}=(0,u.useDispatch)(p),{setStyles:t}=(0,u.useDispatch)(y),{setAtRule:n}=(0,u.useDispatch)(f),{setNestedRule:l}=(0,u.useDispatch)(v);return()=>{e({}),t({}),n(""),l("")}}();return{atRule:e,nestedRule:n,setAtRule:t,currentAtRule:s,setNestedRule:o,setDeviceType:g,deviceType:i,setCurrentStyle:r,currentStyle:a,getPreviewDevice:b.getPreviewDevice,setGlobalStyle:h,cancelEditGlobalStyle:A}}function I({attributes:t,setAttributes:n,shortcuts:l,onStyleChange:o}){const{atRule:s,setAtRule:r,nestedRule:a,setNestedRule:c,setDeviceType:i,getPreviewDevice:u,currentStyle:d,setGlobalStyle:g,cancelEditGlobalStyle:p}=M(),{styles:y,globalClasses:f=[]}=t,v=(0,m.getStylesObject)(y,s,a);return(0,e.createElement)(m.StylesBuilder,{currentSelector:d?.selector,styles:v,allStyles:y,onDeleteStyle:(e,t)=>{const l=(0,m.deleteStylesObjectKey)(y,e,t);n({styles:l})},nestedRule:a,atRule:s,onStyleChange:(e,t=null)=>o(e,t,s,a),onNestedRuleChange:e=>c(e),onAtRuleChange:e=>{r(e);const t=(0,m.getPreviewWidth)(e),n=u(t);n&&i(n)},onUpdateKey:(e,t,l)=>{const o=(0,m.updateStylesObjectKey)(y,e,t,l);n({styles:o})},selectorShortcuts:l.selectorShortcuts,visibleSelectors:l.visibleShortcuts,onEditStyle:g,cancelEditStyle:p,setLocalTab:e=>{sessionStorage.setItem(b.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:f})}const T={},B=[];function x(t){return n=>{const{attributes:l,name:o,setAttributes:s,isSelected:a,clientId:c}=n,{uniqueId:i,styles:u,css:d}=l,{atRule:g,deviceType:p,setAtRule:y,currentStyle:f,setCurrentStyle:v,setNestedRule:h}=M(),S=(0,b.useSetStyles)(n,{cleanStylesObject:m.cleanStylesObject}),k=(0,r.useMemo)((()=>i?(0,b.getSelector)(o,i):""),[o,i]),E=Array.isArray(u)?{}:u;return(0,b.useAtRuleEffect)({deviceType:p,atRule:g,setAtRule:y,defaultAtRules:m.defaultAtRules,isSelected:a,getPreviewWidth:m.getPreviewWidth}),(0,b.useGenerateCSSEffect)({selector:k,styles:E,setAttributes:s,getCss:m.getCss,getSelector:b.getSelector,isSelected:a,blockCss:d,clientId:c}),(0,b.useStyleSelectorEffect)({isSelected:a,currentStyle:f,selector:k,setCurrentStyle:v,setNestedRule:h}),(0,b.useDecodeStyleKeys)({styles:u,setAttributes:s}),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(b.Style,{selector:k,getCss:m.getCss,styles:E,clientId:c,name:o}),(0,e.createElement)(t,{...n,selector:k,onStyleChange:function(e,t="",n="",l=""){const o="object"==typeof e?e:{[e]:t},s=(0,b.buildChangedStylesObject)(o,n,l);S(s)},getStyleValue:function(e,t="",n=""){var l,o,s,r;return n?t?null!==(s=u?.[n]?.[t]?.[e])&&void 0!==s?s:"":null!==(o=u?.[n]?.[e])&&void 0!==o?o:"":t?null!==(r=u?.[t]?.[e])&&void 0!==r?r:"":null!==(l=u?.[e])&&void 0!==l?l:""},styles:E}))}}function q(e,t,n=!1){const{styles:l={},uniqueId:o="",globalClasses:s=[]}=t,r=[];return n&&r.push(e),s.length>0&&r.push(...s),Object.keys(l).length>0&&r.push(`${e}-${o}`),r}const N=(e,t)=>e&&Array.isArray(e)?e.reduce(((e,n)=>{const l=!(t&&t.length>0)||t.includes(n.name),o=N(n.innerBlocks,t);return{total:e.total+1+o.total,allowed:e.allowed+(l?1:0)+o.allowed}}),{total:0,allowed:0}):{total:0,allowed:0},D=(e,t)=>e&&Array.isArray(e)?e.filter((e=>{const{name:n}=e;return!(t&&t.length>0)||t.includes(n)})).map((e=>({...e,innerBlocks:D(e.innerBlocks,t)}))):[],O=(0,r.memo)((({block:n,level:l=0,currentClientId:o,selectBlock:s})=>{var r;const{name:c,innerBlocks:i,clientId:d}=n,{getBlockType:g}=(0,u.useSelect)((e=>e(t.store)),[]),b=g(c);return(0,e.createElement)("div",{className:"gb-block-node","data-level":l},(0,e.createElement)(a.Button,{variant:"tertiary",size:"compact",className:"gb-block-node-button",onClick:()=>s(d),isPressed:o===d,icon:null!==(r=b?.icon?.src)&&void 0!==r?r:null},function(e,t,n="list-view"){const{__experimentalLabel:l,title:o}=e||{};if(!e)return"Unknown Block";const s=l&&l(t,{context:n});return!s||"string"!=typeof s&&"number"!=typeof s?o||"Unnamed Block":s}(b,n.attributes)),i&&i.length>0&&(0,e.createElement)("div",{className:"gb-block-tree-inner-blocks"},i.map((t=>(0,e.createElement)(O,{key:t.clientId,block:t,level:l+1,currentClientId:o,blockType:b,selectBlock:s})))))}));function P({blocks:t,clientId:n,allowedBlocks:o,showAllLabel:s=(0,c.__)("Show all blocks","generateblocks-pro")}){const[i,d]=(0,r.useState)(!1),{selectBlock:g}=(0,u.useDispatch)(l.store),b=(0,r.useMemo)((()=>N(t,o)),[t,o]),m=(0,r.useMemo)((()=>D(t,o)),[t,o]),p=(0,r.useMemo)((()=>D(t,[])),[t]),y=(0,r.useMemo)((()=>i?p:m),[i,p,m]),f=b.total!==b.allowed;return(0,e.createElement)("div",{className:"gb-block-tree"},!!f&&(0,e.createElement)("div",{className:"gb-block-tree__show-all"},(0,e.createElement)(a.ToggleControl,{label:s,checked:i,onChange:e=>d(e)})),y.map((t=>(0,e.createElement)(O,{key:t.clientId,block:t,currentClientId:n,selectBlock:g}))))}var j=n(20493);const F=(0,u.createReduxStore)("gbp-menu-toggle-state",{reducer:function(e=!1,t){return"SET_DATA"===t.type?t.payload:e},actions:{setMenuToggleState:e=>({type:"SET_DATA",payload:e})},selectors:{menuToggleState:e=>e}}),U=window.wp.hooks,Z=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function G(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function $({value:t,onChange:n}){const l=(0,r.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus?.map((e=>({label:e.name,value:e.term_id.toString()}))))&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]);return l.length?(0,e.createElement)(a.SelectControl,{label:(0,c.__)("Menu","generateblocks-pro"),value:t,options:l,onChange:n}):null}var z=n(46942),W=n.n(z);function H({name:e,clientId:t,align:n,children:o}){const{getBlockRootClientId:s}=(0,u.useSelect)((e=>e("core/block-editor")),[]),a=(0,u.useSelect)((e=>{const{getSettings:t}=e(l.store);return t().supportsLayout||!1}),[]),c=e.toString().replace("/","-"),i={className:W()({"wp-block":!0,"gb-is-root-block":!0,[`gb-root-block-${c}`]:!0,[`align${n}`]:a}),"data-align":n&&!a?n:null,"data-block":t},d=s(t);return(0,U.applyFilters)("generateblocks.rootElement.disable",d,{name:e})?o:(0,r.createElement)("div",i,o)}const V=["generateblocks-pro/navigation","generateblocks-pro/menu-container","generateblocks-pro/classic-menu","generateblocks-pro/classic-menu-item","generateblocks-pro/classic-sub-menu","generateblocks-pro/menu-toggle"];function K(e){for(const t of e){if("generateblocks-pro/classic-menu"===t.name)return t;if(t.innerBlocks&&t.innerBlocks.length>0){const e=K(t.innerBlocks);if(e)return e}}return null}function Q(){var t,n;const l=null!==(t=generateblocksBlockClassicMenu?.menuAdminUrl)&&void 0!==t?t:"",[o,s]=(0,r.useState)(null!==(n=generateblocksBlockClassicMenu?.hasMenuSupport)&&void 0!==n&&n),i=(0,r.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus)&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]),g=(0,u.useSelect)((e=>e(d.store).getEntityRecord("root","site")?.generateblocks_pro_classic_menu_support||!1),[]),{editEntityRecord:b,saveEditedEntityRecord:m}=(0,u.useDispatch)(d.store),[p,y]=(0,r.useState)(g);return o?!l||i.length>0?null:(0,e.createElement)(a.Notice,{isDismissible:!1,status:"warning"},(0,r.createInterpolateElement)((0,c.__)("No menus found. Please <CreateMenuLink />.","generateblocks-pro"),{CreateMenuLink:(0,e.createElement)("a",{href:l,target:"_blank",rel:"noopener noreferrer"},(0,c.__)("create a menu","generateblocks-pro"))})):(0,e.createElement)(a.Notice,{isDismissible:!1,status:"warning"},(0,e.createElement)(a.ToggleControl,{label:(0,c.__)("Enable Menu Support","generateblocks-pro"),checked:p,onChange:async e=>{y(e),s(e);try{await b("root","site",void 0,{generateblocks_pro_classic_menu_support:e}),await m("root","site",void 0)}catch(e){var t;console.error("Save failed:",e),y(g),s(null!==(t=generateblocksBlockClassicMenu?.hasMenuSupport)&&void 0!==t&&t)}},help:(0,c.__)("Your theme does not support the menu system. Enable it here.","generateblocks-pro")}))}(0,i.compose)((function(t){return n=>{var o,s,c,i;const{attributes:d,setAttributes:g,context:b}=n,{htmlAttributes:m={},uniqueId:p,className:y,align:f}=d,v=(0,u.useSelect)((e=>e("core/editor").isSavingPost())),{style:h="",href:S,...k}=m,E=Object.keys(k).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(k[t]),e)),{}),[A,w]=(0,r.useState)(h);var C,_,L,R;(0,r.useEffect)((()=>{!async function(){const e=await(0,U.applyFilters)("generateblocks.editor.htmlAttributes.style",h,{...n});w(e)}()}),[h,b,v]),C=()=>{const e=["alignwide","alignfull"],t=(y?.split(" ")||[]).filter((t=>!e.includes(t)));f&&t.push("align"+f),g({className:t.join(" ")})},_=[f],R=(L=(0,e.useRef)(!0)).current?(L.current=!1,!0):L.current,(0,e.useEffect)((function(){if(!R)return C()}),_);const M="string"==typeof A?A.split(";").reduce(((e,t)=>{const n=t.indexOf(":");if(-1===n)return e;let l=t.slice(0,n).trim();const o=t.slice(n+1).trim();return l&&o&&(l.startsWith("--")||(l=l.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[l]=o),e}),{}):"",I={...E,style:M,"data-gb-id":p,"data-context-post-id":null!==(o=null!==(s=b?.postId)&&void 0!==s?s:b?.["generateblocks/loopIndex"])&&void 0!==o?o:0,"data-align":f||void 0},T=(0,r.useMemo)((()=>Array.isArray(m)?{}:m),[JSON.stringify(m)]);return(0,r.useEffect)((()=>{const e={...m};Object.keys(e).forEach((t=>{const n=t.startsWith("data-"),l=e[t];Z.includes(t)||""!==l||n||"alt"===t||delete e[t],"string"!=typeof l&&"boolean"!=typeof l&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(const l of n)if(e[l]!==t[l])return!1;return!0}(e,m)||g({htmlAttributes:e})}),[JSON.stringify(m)]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(t,{...n,editorHtmlAttributes:I,htmlAttributes:T}),(0,e.createElement)(l.InspectorAdvancedControls,null,(0,e.createElement)(a.TextControl,{label:"HTML ID",value:null!==(c=m.id)&&void 0!==c?c:"",onChange:e=>{g({htmlAttributes:{...m,id:e}})},onBlur:()=>{m.id&&g({htmlAttributes:{...m,id:G(m.id)}})}}),(0,e.createElement)(a.TextControl,{label:"ARIA Label",value:null!==(i=m["aria-label"])&&void 0!==i?i:"",onChange:e=>{g({htmlAttributes:{...m,"aria-label":e}})}})))}}),x,b.withUniqueId)((function(t){var n,o,s,i,d;const{attributes:p,setAttributes:y,editorHtmlAttributes:f,onStyleChange:v,getStyleValue:h,clientId:S,name:k,isSelected:E}=t,{styles:A,htmlAttributes:w,tagName:C,uniqueId:_,subMenuType:L}=p,R=(0,u.useSelect)((e=>e(F).menuToggleState())),M=(0,r.useRef)(),{getBlock:T,getBlocks:B}=(0,u.useSelect)((e=>e(l.store)),[]),{updateBlockAttributes:x}=(0,u.useDispatch)(l.store),N=(0,r.useMemo)((()=>T(S)),[S,E]),[D,O]=(0,r.useState)(null),j=q("gb-navigation",{...p,styles:A},!0);R&&j.push("gb-navigation--open"),j.includes("gb-navigation-"+_)||j.push("gb-navigation-"+_);const U=(0,l.useBlockProps)({className:j.filter((e=>e)).join(" ").trim(),...f,ref:M}),Z=(0,l.useInnerBlocksProps)(U),G=C||"nav";(0,r.useEffect)((()=>{C||y({tagName:"nav"})}),[C]),(0,r.useEffect)((()=>{w?.["data-gb-mobile-menu-type"]||y({htmlAttributes:{...w,"data-gb-mobile-menu-type":"full-overlay"}})}),[w?.["data-gb-mobile-menu-type"]]),(0,r.useEffect)((()=>{if(!E)return;const e=K(B(S));e&&O(e)}),[E,S]);const z={name:k,attributes:p,setAttributes:y,clientId:S,getStyleValue:h,onStyleChange:v},W=null!==(n=w?.["data-gb-mobile-menu-transition"])&&void 0!==n?n:"",J=null!==(o=w?.["data-gb-sub-menu-transition"])&&void 0!==o?o:"";return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(b.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(g.OpenPanel,{title:(0,c.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...z},(0,e.createElement)(P,{blocks:[N],clientId:S,allowedBlocks:V,showAllLabel:(0,c.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(Q,null),(0,e.createElement)(g.OpenPanel,{panelId:"settings",...z},!!D&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)($,{value:D?.attributes?.menu,onChange:e=>{x(D.clientId,{menu:e});const t=K(B(S));t&&O(t)}})),(0,e.createElement)(a.SelectControl,{label:(0,c.__)("Sub-menu type","generateblocks-pro"),value:L,options:[{label:(0,c.__)("Hover","generateblocks-pro"),value:"hover"},{label:(0,c.__)("Click Menu Item","generateblocks-pro"),value:"click"},{label:(0,c.__)("Click Toggle","generateblocks-pro"),value:"click-toggle"}],onChange:e=>y({subMenuType:e})}),(0,e.createElement)(a.SelectControl,{label:(0,c.__)("Sub-menu Transition","generateblocks-pro"),value:J,options:[{label:(0,c.__)("None","generateblocks-pro"),value:""},{label:(0,c.__)("Fade","generateblocks-pro"),value:"fade"},{label:(0,c.__)("Slide & fade up","generateblocks-pro"),value:"fade-slide-up"},{label:(0,c.__)("Slide & fade down","generateblocks-pro"),value:"fade-slide-down"},{label:(0,c.__)("Slide & fade left","generateblocks-pro"),value:"fade-slide-left"},{label:(0,c.__)("Slide & fade right","generateblocks-pro"),value:"fade-slide-right"}],onChange:e=>{const t={...w};e?t["data-gb-sub-menu-transition"]=e:(delete t["data-gb-sub-menu-transition"],v("--sub-menu-transition-speed","")),y({htmlAttributes:t})}}),""!==J&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(m.UnitControl,{units:["ms"],defaultUnitValue:"ms",label:(0,c.__)("Sub-menu Transition Speed","generateblocks-pro"),placeholder:"200ms",value:h("--sub-menu-transition-speed",""),onChange:e=>{v("--sub-menu-transition-speed",e)}}),"fade"!==J&&(0,e.createElement)(m.UnitControl,{label:(0,c.__)("Sub-menu Transition Distance","generateblocks-pro"),placeholder:"5px",value:h("--sub-menu-transition-distance",""),onChange:e=>{v("--sub-menu-transition-distance",e)}})),(0,e.createElement)(m.UnitControl,{label:(0,c.__)("Mobile breakpoint","generateblocks-pro"),value:null!==(s=w?.["data-gb-mobile-breakpoint"])&&void 0!==s?s:"",onChange:e=>{const t={...w};e?t["data-gb-mobile-breakpoint"]=e:delete t["data-gb-mobile-breakpoint"],y({htmlAttributes:t})}}),(0,e.createElement)(a.SelectControl,{label:(0,c.__)("Mobile Menu Type","generateblocks-pro"),value:null!==(i=w?.["data-gb-mobile-menu-type"])&&void 0!==i?i:"",options:[{label:(0,c.__)("Full overlay","generateblocks-pro"),value:"full-overlay"},{label:(0,c.__)("Partial overlay","generateblocks-pro"),value:"partial-overlay"}],onChange:e=>{const t={...w};e?t["data-gb-mobile-menu-type"]=e:delete t["data-gb-mobile-menu-type"],y({htmlAttributes:t})}}),"partial-overlay"===w?.["data-gb-mobile-menu-type"]&&(0,e.createElement)(a.TextControl,{label:(0,c.__)("Mobile Menu Anchor","generateblocks-pro"),help:(0,c.__)("The selector for the element the mobile menu will attach to the bottom of.","generateblocks-pro"),value:null!==(d=w?.["data-gb-menu-toggle-anchor"])&&void 0!==d?d:"",placeholder:"Calculate automatically",onChange:e=>{const t={...w};e?t["data-gb-menu-toggle-anchor"]=e:delete t["data-gb-menu-toggle-anchor"],y({htmlAttributes:t})}}),(0,e.createElement)(a.SelectControl,{label:(0,c.__)("Mobile Menu Transition","generateblocks-pro"),value:W,options:[{label:(0,c.__)("None","generateblocks-pro"),value:""},{label:(0,c.__)("Fade","generateblocks-pro"),value:"fade"},{label:(0,c.__)("Slide left","generateblocks-pro"),value:"slide-left"},{label:(0,c.__)("Slide right","generateblocks-pro"),value:"slide-right"},{label:(0,c.__)("Slide up","generateblocks-pro"),value:"slide-up"},{label:(0,c.__)("Slide down","generateblocks-pro"),value:"slide-down"},{label:(0,c.__)("Slide & fade left","generateblocks-pro"),value:"fade-slide-left"},{label:(0,c.__)("Slide & fade right","generateblocks-pro"),value:"fade-slide-right"},{label:(0,c.__)("Slide & fade up","generateblocks-pro"),value:"fade-slide-up"},{label:(0,c.__)("Slide & fade down","generateblocks-pro"),value:"fade-slide-down"}],onChange:e=>{const t={...w};e?t["data-gb-mobile-menu-transition"]=e:(delete t["data-gb-mobile-menu-transition"],v("--mobile-transition-speed","")),y({htmlAttributes:t})}}),""!==W&&(0,e.createElement)(m.UnitControl,{units:["ms"],defaultUnitValue:"ms",label:(0,c.__)("Mobile Menu Transition Speed","generateblocks-pro"),placeholder:"200ms",value:h("--mobile-transition-speed",""),onChange:e=>{v("--mobile-transition-speed",e)}}))),stylesTab:(0,e.createElement)(I,{attributes:p,setAttributes:y,shortcuts:{},onStyleChange:v})})),(0,e.createElement)(H,{name:k,clientId:S},(0,e.createElement)(G,{...Z})))}));const J=(0,i.compose)(x,b.withUniqueId)((function(t){var n;const{attributes:o,setAttributes:a,getStyleValue:i,onStyleChange:d,clientId:m,name:p,context:y,isSelected:f}=t,{menu:v,uniqueId:h}=o,S=(0,r.useRef)(),k=(0,u.useSelect)((e=>e(F).menuToggleState())),{getBlockParentsByBlockName:E,getBlock:A}=(0,u.useSelect)((e=>e(l.store)),[]),{selectBlock:w}=(0,u.useDispatch)(l.store),C=(0,r.useMemo)((()=>{var e;return null!==(e=E(m,"generateblocks-pro/navigation",!0)?.[0])&&void 0!==e?e:""}),[m]),_=(0,r.useMemo)((()=>A(C)),[C,f]),L=(0,r.useMemo)((()=>A(m)),[m,f]),R=(0,r.useMemo)((()=>{var e;return null!==(e=L?.innerBlocks?.find((e=>"generateblocks-pro/classic-menu-item"===e.name))?.clientId)&&void 0!==e?e:""}),[L]),M=(0,r.useMemo)((()=>{var e;return null!==(e=L?.innerBlocks?.find((e=>"generateblocks-pro/classic-sub-menu"===e.name))?.clientId)&&void 0!==e?e:""}),[L]),x=S?.current?.querySelector(".gb-menu"),N=(0,r.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus?.map((e=>({label:e.name,value:e.term_id.toString()}))))&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]);(0,r.useEffect)((()=>{var e;v&&N.find((e=>e.value===v))||a({menu:null!==(e=N[0]?.value)&&void 0!==e?e:""})}),[N.length]);const D=q("gb-menu",o,!0),O=(0,l.useBlockProps)({className:D.filter(Boolean).join(" ").trim(),ref:S}),U=(0,l.useInnerBlocksProps)({},{allowedBlocks:["generateblocks-pro/classic-menu-item","generateblocks-pro/classic-sub-menu"],renderAppender:!1}),Z={name:p,attributes:o,setAttributes:a,clientId:m,getStyleValue:i,onStyleChange:d},G={};return G.subMenuType=null!==(n=y?.["generateblocks-pro/subMenuType"])&&void 0!==n?n:"hover",G.disableLinks=!0,(0,r.useEffect)((()=>{if(!S?.current)return;const e=S.current.closest(".gb-menu-container");if(!e)return;const t=e.querySelectorAll(".gb-menu--click .menu-item-has-children > a"),n=[];return t.length>0&&t.forEach((e=>{const t=t=>{(0,j.Qg)(e,t)};e.addEventListener("click",t),n.push({item:e,handler:t})})),()=>{n.forEach((({item:e,handler:t})=>{e.removeEventListener("click",t)}))}}),[S,x]),(0,r.useEffect)((()=>{if(!S?.current)return;const e=S?.current?.closest(".gb-navigation");if(!e)return;const t=e.querySelectorAll(".gb-submenu-toggle"),n=[];return t.length>0&&t.forEach((e=>{const t=t=>{const n=e.closest(".gb-menu-container--toggled"),l=e.closest(".gb-menu--hover");"click"===t?.type&&l&&!n||(0,j.Qg)(e,t)};e.addEventListener("click",t),n.push({item:e,handler:t})})),()=>{n.forEach((({item:e,handler:t})=>{e.removeEventListener("click",t)}))}}),[S,k,x]),(0,r.useEffect)((()=>{const e=S?.current;if(e)return e.addEventListener("click",t),()=>{e.removeEventListener("click",t)};function t(e){e.target.closest(".gb-sub-menu")?w(M):e.target.closest(".menu-item")&&w(R)}}),[S,R,x,M]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(b.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(g.OpenPanel,{title:(0,c.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...Z},(0,e.createElement)(P,{blocks:[_],clientId:m,allowedBlocks:V,showAllLabel:(0,c.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(Q,null),(0,e.createElement)(g.OpenPanel,{...Z,panelId:"settings"},(0,e.createElement)($,{value:v,onChange:e=>a({menu:e})}))),stylesTab:(0,e.createElement)(I,{attributes:o,setAttributes:a,shortcuts:{selectorShortcuts:T,visibleShortcuts:B},onStyleChange:d})})),(0,e.createElement)("div",{...O},N.length&&v?(0,e.createElement)(s(),{key:v+h,block:"generateblocks-pro/classic-menu",attributes:o,urlQueryArgs:G}):(0,e.createElement)(e.Fragment,null,(0,c.__)("No menu found.","generateblocks-pro")),(0,e.createElement)("div",{...U})))})),Y=JSON.parse('{"UU":"generateblocks-pro/classic-menu"}');(0,t.registerBlockType)(Y.UU,{edit:J,save:()=>(0,e.createElement)(l.InnerBlocks.Content,null),icon:(0,e.createElement)((function(){return(0,e.createElement)("svg",{viewBox:"0 0 24 24",className:"gblocks-block-icon",style:{fillRule:"evenodd",clipRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:2}},(0,e.createElement)("path",{d:"M8.55,5.705C8.55,6.037 8.281,6.305 7.95,6.305L5.15,6.305C4.819,6.305 4.55,6.037 4.55,5.705C4.55,5.375 4.819,5.106 5.15,5.106L7.95,5.106C8.281,5.106 8.55,5.375 8.55,5.705Z"}),(0,e.createElement)("path",{d:"M14.03,5.705C14.03,6.037 13.761,6.305 13.43,6.305L10.63,6.305C10.299,6.305 10.03,6.037 10.03,5.705C10.03,5.375 10.299,5.106 10.63,5.106L13.43,5.106C13.761,5.106 14.03,5.375 14.03,5.705Z"}),(0,e.createElement)("path",{d:"M19.509,5.705C19.509,6.037 19.24,6.305 18.909,6.305L16.109,6.305C15.778,6.305 15.509,6.037 15.509,5.705C15.509,5.375 15.778,5.106 16.109,5.106L18.909,5.106C19.24,5.106 19.509,5.375 19.509,5.705Z"}),(0,e.createElement)("g",{opacity:"0.3"},(0,e.createElement)("path",{d:"M21.405,22.172L17.655,22.172L17.655,20.922L20.78,20.922L20.78,17.797L22.03,17.797L22.03,21.547C22.03,21.892 21.75,22.172 21.405,22.172ZM6.405,22.172L2.655,22.172C2.312,22.172 2.03,21.89 2.03,21.547L2.03,17.797L3.28,17.797L3.28,20.922L6.405,20.922L6.405,22.172Z",style:{fillRule:"nonzero"}}),(0,e.createElement)("path",{d:"M3.28,12.35L2.03,12.35L2.03,8.6C2.03,8.255 2.31,7.975 2.655,7.975L6.405,7.975L6.405,9.225L3.28,9.225L3.28,12.35ZM22.03,12.35L20.78,12.35L20.78,9.225L17.655,9.225L17.655,7.975L21.405,7.975C21.75,7.975 22.03,8.255 22.03,8.6L22.03,12.35Z",style:{fillRule:"nonzero"}})),(0,e.createElement)("path",{d:"M18.235,14.454L5.775,14.454C5.435,14.454 5.155,14.734 5.155,15.074C5.155,15.414 5.435,15.694 5.775,15.694L18.235,15.694C18.575,15.694 18.855,15.414 18.855,15.074C18.855,14.734 18.585,14.454 18.235,14.454ZM18.245,17.484L5.775,17.484C5.435,17.484 5.155,17.764 5.155,18.104C5.155,18.444 5.435,18.724 5.775,18.724L18.235,18.724C18.575,18.724 18.855,18.444 18.855,18.104L18.855,18.094C18.855,17.759 18.58,17.484 18.245,17.484ZM5.145,12.044C5.145,12.384 5.425,12.664 5.765,12.664L18.225,12.664C18.565,12.664 18.845,12.384 18.845,12.044C18.845,11.704 18.565,11.424 18.225,11.424L5.758,11.424C5.422,11.424 5.145,11.7 5.145,12.037L5.145,12.044Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,e.createElement)("path",{d:"M21.405,9.239L2.655,9.239C2.312,9.239 2.03,8.957 2.03,8.614L2.03,2.797C2.03,2.452 2.31,2.172 2.655,2.172L21.406,2.172C21.75,2.172 22.03,2.452 22.03,2.797L22.03,8.613C22.03,8.959 21.75,9.239 21.405,9.239ZM3.261,7.975L20.778,7.975L20.78,3.422L3.279,3.422L3.261,7.975Z",style:{fillRule:"nonzero"}}))}),null)})})()})();