(()=>{var e={46942:(e,t)=>{var l;!function(){"use strict";var s={}.hasOwnProperty;function r(){for(var e="",t=0;t<arguments.length;t++){var l=arguments[t];l&&(e=o(e,n(l)))}return e}function n(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return r.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var l in e)s.call(e,l)&&e[l]&&(t=o(t,l));return t}function o(e,t){return t?e?e+" "+t:e+t:e}e.exports?(r.default=r,e.exports=r):void 0===(l=function(){return r}.apply(t,[]))||(e.exports=l)}()}},t={};function l(s){var r=t[s];if(void 0!==r)return r.exports;var n=t[s]={exports:{}};return e[s](n,n.exports,l),n.exports}l.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return l.d(t,{a:t}),t},l.d=(e,t)=>{for(var s in t)l.o(t,s)&&!l.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React,t=window.wp.blocks,s=window.wp.blockEditor,r=window.wp.element,n=window.wp.i18n,o=window.wp.compose,a=window.wp.components,c=window.gbp.components,i=window.gbp.blockStyles,u=window.gbp.stylesBuilder;function d(e,t,l=!1){const{styles:s={},uniqueId:r="",globalClasses:n=[]}=t,o=[];return l&&o.push(e),n.length>0&&o.push(...n),Object.keys(s).length>0&&o.push(`${e}-${r}`),o}const p=window.wp.data,b=(0,p.createReduxStore)("gbp-current-style",{reducer:u.currentStyleReducer,actions:u.currentStyleActions,selectors:u.currentStyleSelectors}),y=(0,p.createReduxStore)("gbp-styles",{reducer:u.styleReducer,actions:u.styleActions,selectors:u.styleSelectors}),g=(0,p.createReduxStore)("gbp-styles-at-rule",{reducer:u.atRuleReducer,actions:u.atRuleActions,selectors:u.atRuleSelectors}),S=(0,p.createReduxStore)("gbp-styles-nested-rule",{reducer:u.nestedRuleReducer,actions:u.nestedRuleActions,selectors:u.nestedRuleSelectors}),h=window.wp.apiFetch;var m=l.n(h);const f=window.wp.notices,k=window.wp.url,v=window.wp.coreData;var w;window.lodash;const R="undefined"!=typeof gbGlobalStylePermissions&&null!==(w=gbGlobalStylePermissions?.canManageStyles)&&void 0!==w&&w,A=window.wp.editPost;const C=(0,p.createReduxStore)("gbp-block-styles-current-style",{reducer:u.currentStyleReducer,actions:u.currentStyleActions,selectors:u.currentStyleSelectors}),E=(0,p.createReduxStore)("gbp-block-styles-at-rule",{reducer:u.atRuleReducer,actions:u.atRuleActions,selectors:u.atRuleSelectors}),_=(0,p.createReduxStore)("gbp-block-styles-nested-rule",{reducer:u.nestedRuleReducer,actions:u.nestedRuleActions,selectors:u.nestedRuleSelectors});function x(){const e=(0,p.useSelect)((e=>e(E).getAtRule())),{setAtRule:t}=(0,p.useDispatch)(E),l=(0,p.useSelect)((e=>e(_).getNestedRule())),{setNestedRule:r}=(0,p.useDispatch)(_),o=(0,i.useCurrentAtRule)(u.defaultAtRules),{setCurrentStyle:a}=(0,p.useDispatch)(C),c=(0,p.useSelect)((e=>e(C).currentStyle())),{deviceType:d,setDeviceType:h}=(0,i.useDeviceType)(),w=function(){const{setCurrentStyle:e}=(0,p.useDispatch)(b),{setStyles:t}=(0,p.useDispatch)(y),{createNotice:l,removeAllNotices:r}=(0,p.useDispatch)(f.store),{getEntityRecordEdits:o}=(0,p.useSelect)(v.store),{getSelectedBlock:a}=(0,p.useSelect)((e=>e(s.store)),[]),{setAtRule:c}=(0,p.useDispatch)(g),{setNestedRule:i}=(0,p.useDispatch)(S),{openGeneralSidebar:u}=(0,p.useDispatch)(A.store);return async(s,d={})=>{if(!R)return;const{classStyles:p,classPostId:b}=await async function(e){var t;const l=await m()({path:(0,k.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let s=null!==(t=l?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(s)&&0===s.length&&(s={}),{classStyles:s,classPostId:l?.response?.data?.postId}}(s);if(!b)return r("snackbar"),void l("error",(0,n.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,n.__)("%s does not exist.","generateblocks-pro"),s),{type:"snackbar"});c(""),i(""),u("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:b,name:s,classStyles:p,clientId:a()?.clientId,options:d}),d.nestedRule&&i(d.nestedRule),d.atRule&&c(d.atRule);const y=o("postType","gblocks_styles",b);t(y?.gb_style_data||p),r("snackbar"),l("info",(0,n.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,n.__)("Editing %s.","generateblocks-pro"),s),{type:"snackbar"})}}(),x=function(){const{setCurrentStyle:e}=(0,p.useDispatch)(b),{setStyles:t}=(0,p.useDispatch)(y),{setAtRule:l}=(0,p.useDispatch)(g),{setNestedRule:s}=(0,p.useDispatch)(S);return()=>{e({}),t({}),l(""),s("")}}();return{atRule:e,nestedRule:l,setAtRule:t,currentAtRule:o,setNestedRule:r,setDeviceType:h,deviceType:d,setCurrentStyle:a,currentStyle:c,getPreviewDevice:i.getPreviewDevice,setGlobalStyle:w,cancelEditGlobalStyle:x}}function N({attributes:t,setAttributes:l,shortcuts:s,onStyleChange:r}){const{atRule:n,setAtRule:o,nestedRule:a,setNestedRule:c,setDeviceType:d,getPreviewDevice:p,currentStyle:b,setGlobalStyle:y,cancelEditGlobalStyle:g}=x(),{styles:S,globalClasses:h=[]}=t,m=(0,u.getStylesObject)(S,n,a);return(0,e.createElement)(u.StylesBuilder,{currentSelector:b?.selector,styles:m,allStyles:S,onDeleteStyle:(e,t)=>{const s=(0,u.deleteStylesObjectKey)(S,e,t);l({styles:s})},nestedRule:a,atRule:n,onStyleChange:(e,t=null)=>r(e,t,n,a),onNestedRuleChange:e=>c(e),onAtRuleChange:e=>{o(e);const t=(0,u.getPreviewWidth)(e),l=p(t);l&&d(l)},onUpdateKey:(e,t,s)=>{const r=(0,u.updateStylesObjectKey)(S,e,t,s);l({styles:r})},selectorShortcuts:s.selectorShortcuts,visibleSelectors:s.visibleShortcuts,onEditStyle:y,cancelEditStyle:g,setLocalTab:e=>{sessionStorage.setItem(i.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:h})}const L=window.wp.hooks,I=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function D(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function O({value:l,options:s=[],onChange:r,blockName:o}){var c;const i=null!==(c=(0,t.getBlockType)(o)?.attributes?.tagName?.enum)&&void 0!==c?c:[],u=s.length?s:i.map((e=>({label:e,value:e})));return u.length?(0,e.createElement)(a.SelectControl,{label:(0,n.__)("Tag Name","generateblocks-pro"),value:l,options:u,onChange:r}):null}var T=l(46942),j=l.n(T);function P({name:e,clientId:t,align:l,children:n}){const{getBlockRootClientId:o}=(0,p.useSelect)((e=>e("core/block-editor")),[]),a=(0,p.useSelect)((e=>{const{getSettings:t}=e(s.store);return t().supportsLayout||!1}),[]),c=e.toString().replace("/","-"),i={className:j()({"wp-block":!0,"gb-is-root-block":!0,[`gb-root-block-${c}`]:!0,[`align${l}`]:a}),"data-align":l&&!a?l:null,"data-block":t},u=o(t);return(0,L.applyFilters)("generateblocks.rootElement.disable",u,{name:e})?n:(0,r.createElement)("div",i,n)}const B={default:{items:[{label:(0,n.__)("Sticky","generateblocks-pro"),value:"&.gb-is-sticky"},{label:(0,n.__)("Not Sticky","generateblocks-pro"),value:"&:not(.gb-is-sticky)"}]}},M=[],G=(0,o.compose)((function(t){return l=>{var n,o,c,i;const{attributes:u,setAttributes:d,context:b}=l,{htmlAttributes:y={},uniqueId:g,className:S,align:h}=u,m=(0,p.useSelect)((e=>e("core/editor").isSavingPost())),{style:f="",href:k,...v}=y,w=Object.keys(v).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(v[t]),e)),{}),[R,A]=(0,r.useState)(f);var C,E,_,x;(0,r.useEffect)((()=>{!async function(){const e=await(0,L.applyFilters)("generateblocks.editor.htmlAttributes.style",f,{...l});A(e)}()}),[f,b,m]),C=()=>{const e=["alignwide","alignfull"],t=(S?.split(" ")||[]).filter((t=>!e.includes(t)));h&&t.push("align"+h),d({className:t.join(" ")})},E=[h],x=(_=(0,e.useRef)(!0)).current?(_.current=!1,!0):_.current,(0,e.useEffect)((function(){if(!x)return C()}),E);const N="string"==typeof R?R.split(";").reduce(((e,t)=>{const l=t.indexOf(":");if(-1===l)return e;let s=t.slice(0,l).trim();const r=t.slice(l+1).trim();return s&&r&&(s.startsWith("--")||(s=s.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[s]=r),e}),{}):"",O={...w,style:N,"data-gb-id":g,"data-context-post-id":null!==(n=null!==(o=b?.postId)&&void 0!==o?o:b?.["generateblocks/loopIndex"])&&void 0!==n?n:0,"data-align":h||void 0},T=(0,r.useMemo)((()=>Array.isArray(y)?{}:y),[JSON.stringify(y)]);return(0,r.useEffect)((()=>{const e={...y};Object.keys(e).forEach((t=>{const l=t.startsWith("data-"),s=e[t];I.includes(t)||""!==s||l||"alt"===t||delete e[t],"string"!=typeof s&&"boolean"!=typeof s&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const l=Object.keys(e),s=Object.keys(t);if(l.length!==s.length)return!1;for(const s of l)if(e[s]!==t[s])return!1;return!0}(e,y)||d({htmlAttributes:e})}),[JSON.stringify(y)]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(t,{...l,editorHtmlAttributes:O,htmlAttributes:T}),(0,e.createElement)(s.InspectorAdvancedControls,null,(0,e.createElement)(a.TextControl,{label:"HTML ID",value:null!==(c=y.id)&&void 0!==c?c:"",onChange:e=>{d({htmlAttributes:{...y,id:e}})},onBlur:()=>{y.id&&d({htmlAttributes:{...y,id:D(y.id)}})}}),(0,e.createElement)(a.TextControl,{label:"ARIA Label",value:null!==(i=y["aria-label"])&&void 0!==i?i:"",onChange:e=>{d({htmlAttributes:{...y,"aria-label":e}})}})))}}),(function(t){return l=>{const{attributes:s,name:n,setAttributes:o,isSelected:a,clientId:c}=l,{uniqueId:d,styles:p,css:b}=s,{atRule:y,deviceType:g,setAtRule:S,currentStyle:h,setCurrentStyle:m,setNestedRule:f}=x(),k=(0,i.useSetStyles)(l,{cleanStylesObject:u.cleanStylesObject}),v=(0,r.useMemo)((()=>d?(0,i.getSelector)(n,d):""),[n,d]),w=Array.isArray(p)?{}:p;return(0,i.useAtRuleEffect)({deviceType:g,atRule:y,setAtRule:S,defaultAtRules:u.defaultAtRules,isSelected:a,getPreviewWidth:u.getPreviewWidth}),(0,i.useGenerateCSSEffect)({selector:v,styles:w,setAttributes:o,getCss:u.getCss,getSelector:i.getSelector,isSelected:a,blockCss:b,clientId:c}),(0,i.useStyleSelectorEffect)({isSelected:a,currentStyle:h,selector:v,setCurrentStyle:m,setNestedRule:f}),(0,i.useDecodeStyleKeys)({styles:p,setAttributes:o}),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(i.Style,{selector:v,getCss:u.getCss,styles:w,clientId:c,name:n}),(0,e.createElement)(t,{...l,selector:v,onStyleChange:function(e,t="",l="",s=""){const r="object"==typeof e?e:{[e]:t},n=(0,i.buildChangedStylesObject)(r,l,s);k(n)},getStyleValue:function(e,t="",l=""){var s,r,n,o;return l?t?null!==(n=p?.[l]?.[t]?.[e])&&void 0!==n?n:"":null!==(r=p?.[l]?.[e])&&void 0!==r?r:"":t?null!==(o=p?.[t]?.[e])&&void 0!==o?o:"":null!==(s=p?.[e])&&void 0!==s?s:""},styles:w}))}}),i.withUniqueId)((function(t){var l,o;const{attributes:p,setAttributes:b,editorHtmlAttributes:y,onStyleChange:g,getStyleValue:S,clientId:h,name:m}=t,{tagName:f,htmlAttributes:k}=p,v=(0,r.useRef)(),w=d("gb-site-header",p,!0),R=(0,s.useBlockProps)({className:w.filter((e=>e)).join(" ").trim(),...y,ref:v}),A=(0,s.useInnerBlocksProps)(R),C=f||"header";(0,r.useEffect)((()=>{f||b({tagName:"header"})}),[f]),(0,r.useEffect)((()=>{var e,t;const l=null!==(e=k?.["data-gb-sticky-header-type"])&&void 0!==e?e:"",s=null!==(t=k?.["data-gb-is-sticky"])&&void 0!==t&&t;if(!l&&!1!==s){const e={...k};delete e["data-gb-is-sticky"],b({htmlAttributes:e})}l&&!1===s&&b({htmlAttributes:{...k,"data-gb-is-sticky":""}})}),[k?.["data-gb-sticky-header-type"],k?.["data-gb-is-sticky"]]);const E={name:m,attributes:p,setAttributes:b,clientId:h,getStyleValue:S,onStyleChange:g},_=[{label:(0,n.__)("Always sticky","generateblocks-pro"),value:""},{label:(0,n.__)("Mobile","generateblocks-pro"),value:"max-width: 767px"},{label:(0,n.__)("Desktop","generateblocks-pro"),value:"min-width: 768px"},{label:(0,n.__)("Custom","generateblocks-pro"),value:"custom"}],x=null!==(l=k?.["data-gb-sticky-breakpoint"])&&void 0!==l?l:"",L=x&&!_.some((e=>e.value===x)),[I,D]=(0,r.useState)(!!L);return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(s.InspectorControls,null,(0,e.createElement)(i.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.OpenPanel,{panelId:"settings",...E},(0,e.createElement)(a.SelectControl,{label:(0,n.__)("Sticky Header","generateblocks-pro"),value:k?.["data-gb-sticky-header-type"],onChange:e=>{if(e)b({htmlAttributes:{...k,"data-gb-sticky-header-type":e}}),g("boxShadow","1px 1px 5px rgba( 0, 0, 0, 0.1 )","","&.gb-is-sticky");else{const e={...k};delete e["data-gb-sticky-header-type"],b({htmlAttributes:e})}},options:[{label:(0,n.__)("None","generateblocks-pro"),value:""},{label:(0,n.__)("Always sticky","generateblocks-pro"),value:"always"},{label:(0,n.__)("When scrolling up","generateblocks-pro"),value:"scroll-up"},{label:(0,n.__)("Past threshold","generateblocks-pro"),value:"past-threshold"}]}),"past-threshold"===k?.["data-gb-sticky-header-type"]&&(0,e.createElement)(u.UnitControl,{label:(0,n.__)("Sticky Threshold","generateblocks-pro"),value:null!==(o=k?.["data-gb-sticky-threshold"])&&void 0!==o?o:"",units:["px"],onChange:e=>{if(e)b({htmlAttributes:{...k,"data-gb-sticky-threshold":e}});else{const e={...k};delete e["data-gb-sticky-threshold"],b({htmlAttributes:e})}},placeholder:"200px",allowedUnits:["px"]}),!!k?.["data-gb-sticky-header-type"]&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(a.SelectControl,{value:L?"custom":x,label:(0,n.__)("Sticky Breakpoint","generateblocks-pro"),options:_,onChange:e=>{if(e&&"custom"!==e&&(b({htmlAttributes:{...k,"data-gb-sticky-breakpoint":e}}),D(!1)),!e){const e={...k};delete e["data-gb-sticky-breakpoint"],b({htmlAttributes:e}),D(!1)}"custom"===e&&D(!0)}}),!!I&&(0,e.createElement)(a.TextControl,{label:(0,n.__)("Custom Sticky Breakpoint","generateblocks-pro"),value:x,onChange:e=>{if(e)b({htmlAttributes:{...k,"data-gb-sticky-breakpoint":e}});else{const e={...k};delete e["data-gb-sticky-breakpoint"],b({htmlAttributes:e})}},placeholder:(0,n.__)("e.g. max-width: 767px","generateblocks-pro"),type:"text",help:(0,n.__)("Use CSS media query syntax. e.g. max-width: 767px","generateblocks-pro")})),(0,e.createElement)(O,{label:(0,n.__)("Tag Name","generateblocks-pro"),value:f,onChange:e=>b({tagName:e}),blockName:m}))),stylesTab:(0,e.createElement)(N,{attributes:p,setAttributes:b,shortcuts:{selectorShortcuts:B,visibleShortcuts:M},onStyleChange:g})})),(0,e.createElement)(P,{name:m,clientId:h},(0,e.createElement)(C,{...A})))})),U=JSON.parse('{"UU":"generateblocks-pro/site-header"}');(0,t.registerBlockType)(U.UU,{edit:G,save:function({attributes:t}){const{tagName:l,htmlAttributes:r={}}=t,n=d("gb-site-header",t,!0),o=s.useBlockProps.save({className:n.join(" ").trim(),...r});return(0,e.createElement)(l,{...s.useInnerBlocksProps.save(o)})},icon:function(){return(0,e.createElement)("svg",{viewBox:"0 0 24 24",className:"gblocks-block-icon",style:{fillRule:"evenodd",clipRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:2}},(0,e.createElement)("path",{d:"M21.375,22L16.361,22.006L16.361,9.193L22.006,9.187L22,21.376C22,21.72 21.72,22 21.375,22ZM17.625,10.424L17.625,20.754L20.75,20.75L20.75,10.436L17.625,10.424Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,e.createElement)("path",{d:"M2.631,22L14.813,22L14.813,9.187L2,9.187L2.006,21.376C2.006,21.72 2.286,22 2.631,22ZM13.549,10.418L13.549,20.748L3.256,20.75L3.256,10.436L13.549,10.418Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,e.createElement)("path",{d:"M22,2.625L22.006,7.639L2.006,7.639L2,2.625C2,2.28 2.28,2 2.625,2L21.376,2C21.72,2 22,2.28 22,2.625ZM3.237,6.375L20.754,6.375L20.75,3.25L3.249,3.25L3.237,6.375Z",style:{fillRule:"nonzero"}}))}})})()})();