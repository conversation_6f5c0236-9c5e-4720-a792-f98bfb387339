(()=>{"use strict";var e={n:t=>{var l=t&&t.__esModule?()=>t.default:()=>t;return e.d(l,{a:l}),l},d:(t,l)=>{for(var r in l)e.o(l,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:l[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.React,l=window.wp.blocks,r=window.wp.compose,n=window.wp.i18n,o=window.wp.element,s=window.wp.blockEditor,c=window.gbp.blockStyles,i=window.gbp.stylesBuilder,a=window.wp.data,u=(0,a.createReduxStore)("gbp-current-style",{reducer:i.currentStyleReducer,actions:i.currentStyleActions,selectors:i.currentStyleSelectors}),d=(0,a.createReduxStore)("gbp-styles",{reducer:i.styleReducer,actions:i.styleActions,selectors:i.styleSelectors}),p=(0,a.createReduxStore)("gbp-styles-at-rule",{reducer:i.atRuleReducer,actions:i.atRuleActions,selectors:i.atRuleSelectors}),y=(0,a.createReduxStore)("gbp-styles-nested-rule",{reducer:i.nestedRuleReducer,actions:i.nestedRuleActions,selectors:i.nestedRuleSelectors}),g=window.wp.apiFetch;var b=e.n(g);const h=window.wp.notices,m=window.wp.url,k=window.wp.coreData;var f;window.lodash;const S="undefined"!=typeof gbGlobalStylePermissions&&null!==(f=gbGlobalStylePermissions?.canManageStyles)&&void 0!==f&&f,v=window.wp.editPost;const w=(0,a.createReduxStore)("gbp-block-styles-current-style",{reducer:i.currentStyleReducer,actions:i.currentStyleActions,selectors:i.currentStyleSelectors}),C=(0,a.createReduxStore)("gbp-block-styles-at-rule",{reducer:i.atRuleReducer,actions:i.atRuleActions,selectors:i.atRuleSelectors}),E=(0,a.createReduxStore)("gbp-block-styles-nested-rule",{reducer:i.nestedRuleReducer,actions:i.nestedRuleActions,selectors:i.nestedRuleSelectors});function L(){const e=(0,a.useSelect)((e=>e(C).getAtRule())),{setAtRule:t}=(0,a.useDispatch)(C),l=(0,a.useSelect)((e=>e(E).getNestedRule())),{setNestedRule:r}=(0,a.useDispatch)(E),o=(0,c.useCurrentAtRule)(i.defaultAtRules),{setCurrentStyle:g}=(0,a.useDispatch)(w),f=(0,a.useSelect)((e=>e(w).currentStyle())),{deviceType:L,setDeviceType:R}=(0,c.useDeviceType)(),x=function(){const{setCurrentStyle:e}=(0,a.useDispatch)(u),{setStyles:t}=(0,a.useDispatch)(d),{createNotice:l,removeAllNotices:r}=(0,a.useDispatch)(h.store),{getEntityRecordEdits:o}=(0,a.useSelect)(k.store),{getSelectedBlock:c}=(0,a.useSelect)((e=>e(s.store)),[]),{setAtRule:i}=(0,a.useDispatch)(p),{setNestedRule:g}=(0,a.useDispatch)(y),{openGeneralSidebar:f}=(0,a.useDispatch)(v.store);return async(s,a={})=>{if(!S)return;const{classStyles:u,classPostId:d}=await async function(e){var t;const l=await b()({path:(0,m.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let r=null!==(t=l?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(r)&&0===r.length&&(r={}),{classStyles:r,classPostId:l?.response?.data?.postId}}(s);if(!d)return r("snackbar"),void l("error",(0,n.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,n.__)("%s does not exist.","generateblocks-pro"),s),{type:"snackbar"});i(""),g(""),f("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:d,name:s,classStyles:u,clientId:c()?.clientId,options:a}),a.nestedRule&&g(a.nestedRule),a.atRule&&i(a.atRule);const p=o("postType","gblocks_styles",d);t(p?.gb_style_data||u),r("snackbar"),l("info",(0,n.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,n.__)("Editing %s.","generateblocks-pro"),s),{type:"snackbar"})}}(),_=function(){const{setCurrentStyle:e}=(0,a.useDispatch)(u),{setStyles:t}=(0,a.useDispatch)(d),{setAtRule:l}=(0,a.useDispatch)(p),{setNestedRule:r}=(0,a.useDispatch)(y);return()=>{e({}),t({}),l(""),r("")}}();return{atRule:e,nestedRule:l,setAtRule:t,currentAtRule:o,setNestedRule:r,setDeviceType:R,deviceType:L,setCurrentStyle:g,currentStyle:f,getPreviewDevice:c.getPreviewDevice,setGlobalStyle:x,cancelEditGlobalStyle:_}}function R({attributes:e,setAttributes:l,shortcuts:r,onStyleChange:n}){const{atRule:o,setAtRule:s,nestedRule:a,setNestedRule:u,setDeviceType:d,getPreviewDevice:p,currentStyle:y,setGlobalStyle:g,cancelEditGlobalStyle:b}=L(),{styles:h,globalClasses:m=[]}=e,k=(0,i.getStylesObject)(h,o,a);return(0,t.createElement)(i.StylesBuilder,{currentSelector:y?.selector,styles:k,allStyles:h,onDeleteStyle:(e,t)=>{const r=(0,i.deleteStylesObjectKey)(h,e,t);l({styles:r})},nestedRule:a,atRule:o,onStyleChange:(e,t=null)=>n(e,t,o,a),onNestedRuleChange:e=>u(e),onAtRuleChange:e=>{s(e);const t=(0,i.getPreviewWidth)(e),l=p(t);l&&d(l)},onUpdateKey:(e,t,r)=>{const n=(0,i.updateStylesObjectKey)(h,e,t,r);l({styles:n})},selectorShortcuts:r.selectorShortcuts,visibleSelectors:r.visibleShortcuts,onEditStyle:g,cancelEditStyle:b,setLocalTab:e=>{sessionStorage.setItem(c.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:m})}const x=window.wp.components,_=window.wp.hooks,A=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function I(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function j(e,t,l=!1){const{styles:r={},uniqueId:n="",globalClasses:o=[]}=t,s=[];return l&&s.push(e),o.length>0&&s.push(...o),Object.keys(r).length>0&&s.push(`${e}-${n}`),s}const D=window.gbp.components,N={caretDown:{label:(0,n._x)("Caret down","label","generateblocks-pro"),icon:(0,t.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 256 256"},(0,t.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,t.createElement)("polyline",{points:"208 96 128 176 48 96",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}))},caretUp:{label:(0,n._x)("Caret Up","label","generateblocks-pro"),icon:(0,t.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 256 256"},(0,t.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,t.createElement)("polyline",{points:"48 160 128 80 208 160",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}))},caretDownCircle:{label:(0,n._x)("Caret Down Circle","label","generateblocks-pro"),icon:(0,t.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 256 256"},(0,t.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,t.createElement)("circle",{cx:"128",cy:"128",r:"96",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),(0,t.createElement)("polyline",{points:"88 112 128 152 168 112",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}))},caretUpCircle:{label:(0,n._x)("Caret Up Circle","label","generateblocks-pro"),icon:(0,t.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 256 256"},(0,t.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,t.createElement)("circle",{cx:"128",cy:"128",r:"96",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),(0,t.createElement)("polyline",{points:"88 144 128 104 168 144",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}))},plus:{label:(0,n._x)("Plus","label","generateblocks-pro"),icon:(0,t.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 256 256"},(0,t.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,t.createElement)("line",{x1:"40",y1:"128",x2:"216",y2:"128",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),(0,t.createElement)("line",{x1:"128",y1:"40",x2:"128",y2:"216",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}))},minus:{label:(0,n._x)("Minus","label","generateblocks-pro"),icon:(0,t.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 256 256"},(0,t.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,t.createElement)("line",{x1:"40",y1:"128",x2:"216",y2:"128",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}))},plusCircle:{label:(0,n._x)("Plus Circle","label","generateblocks-pro"),icon:(0,t.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 256 256"},(0,t.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,t.createElement)("circle",{cx:"128",cy:"128",r:"96",fill:"none",stroke:"currentColor",strokeMiterlimit:"10",strokeWidth:"16"}),(0,t.createElement)("line",{x1:"88",y1:"128",x2:"168",y2:"128",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),(0,t.createElement)("line",{x1:"128",y1:"88",x2:"128",y2:"168",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}))},minusCircle:{label:(0,n._x)("Minus Circle","label","generateblocks-pro"),icon:(0,t.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 256 256"},(0,t.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,t.createElement)("line",{x1:"88",y1:"128",x2:"168",y2:"128",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),(0,t.createElement)("circle",{cx:"128",cy:"128",r:"96",fill:"none",stroke:"currentColor",strokeMiterlimit:"10",strokeWidth:"16"}))},plusSquare:{label:(0,n._x)("Plus Square","label","generateblocks-pro"),icon:(0,t.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 256 256"},(0,t.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,t.createElement)("rect",{x:"40",y:"40",width:"176",height:"176",rx:"8",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),(0,t.createElement)("line",{x1:"88",y1:"128",x2:"168",y2:"128",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),(0,t.createElement)("line",{x1:"128",y1:"88",x2:"128",y2:"168",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}))},minusSquare:{label:(0,n._x)("Minus Square","label","generateblocks-pro"),icon:(0,t.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 256 256"},(0,t.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,t.createElement)("rect",{x:"40",y:"40",width:"176",height:"176",rx:"8",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),(0,t.createElement)("line",{x1:"88",y1:"128",x2:"168",y2:"128",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}))}},O={default:{items:[{label:"Hover",value:"&:hover"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"},{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"}]},interactions:{label:(0,n.__)("Interactions","generateblocks-pro"),items:[{label:"Hover",value:"&:hover"},{label:"Focus",value:"&:focus"},{label:"Hover & Focus",value:"&:is(:hover, :focus)"}]},links:{label:(0,n.__)("Links","generateblocks-pro"),items:[{label:"Links",value:"a"},{label:"Hovered links",value:"a:hover"},{label:"Hovered & focused links",value:"a:is(:hover, :focus)"},{label:"Visited links",value:"a:visited"}]},pseudoElements:{label:(0,n.__)("Pseudo Elements","generateblocks-pro"),items:[{label:"Before",value:"&:before"},{label:"After",value:"&:after"}]}},T=(0,r.compose)((function(e){return l=>{var r,n,c,i;const{attributes:u,setAttributes:d,context:p}=l,{htmlAttributes:y={},uniqueId:g,className:b,align:h}=u,m=(0,a.useSelect)((e=>e("core/editor").isSavingPost())),{style:k="",href:f,...S}=y,v=Object.keys(S).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(S[t]),e)),{}),[w,C]=(0,o.useState)(k);var E,L,R,j;(0,o.useEffect)((()=>{!async function(){const e=await(0,_.applyFilters)("generateblocks.editor.htmlAttributes.style",k,{...l});C(e)}()}),[k,p,m]),E=()=>{const e=["alignwide","alignfull"],t=(b?.split(" ")||[]).filter((t=>!e.includes(t)));h&&t.push("align"+h),d({className:t.join(" ")})},L=[h],j=(R=(0,t.useRef)(!0)).current?(R.current=!1,!0):R.current,(0,t.useEffect)((function(){if(!j)return E()}),L);const D="string"==typeof w?w.split(";").reduce(((e,t)=>{const l=t.indexOf(":");if(-1===l)return e;let r=t.slice(0,l).trim();const n=t.slice(l+1).trim();return r&&n&&(r.startsWith("--")||(r=r.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[r]=n),e}),{}):"",N={...v,style:D,"data-gb-id":g,"data-context-post-id":null!==(r=null!==(n=p?.postId)&&void 0!==n?n:p?.["generateblocks/loopIndex"])&&void 0!==r?r:0,"data-align":h||void 0},O=(0,o.useMemo)((()=>Array.isArray(y)?{}:y),[JSON.stringify(y)]);return(0,o.useEffect)((()=>{const e={...y};Object.keys(e).forEach((t=>{const l=t.startsWith("data-"),r=e[t];A.includes(t)||""!==r||l||"alt"===t||delete e[t],"string"!=typeof r&&"boolean"!=typeof r&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const l=Object.keys(e),r=Object.keys(t);if(l.length!==r.length)return!1;for(const r of l)if(e[r]!==t[r])return!1;return!0}(e,y)||d({htmlAttributes:e})}),[JSON.stringify(y)]),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(e,{...l,editorHtmlAttributes:N,htmlAttributes:O}),(0,t.createElement)(s.InspectorAdvancedControls,null,(0,t.createElement)(x.TextControl,{label:"HTML ID",value:null!==(c=y.id)&&void 0!==c?c:"",onChange:e=>{d({htmlAttributes:{...y,id:e}})},onBlur:()=>{y.id&&d({htmlAttributes:{...y,id:I(y.id)}})}}),(0,t.createElement)(x.TextControl,{label:"ARIA Label",value:null!==(i=y["aria-label"])&&void 0!==i?i:"",onChange:e=>{d({htmlAttributes:{...y,"aria-label":e}})}})))}}),(function(e){return l=>{const{attributes:r,name:n,setAttributes:s,isSelected:a,clientId:u}=l,{uniqueId:d,styles:p,css:y}=r,{atRule:g,deviceType:b,setAtRule:h,currentStyle:m,setCurrentStyle:k,setNestedRule:f}=L(),S=(0,c.useSetStyles)(l,{cleanStylesObject:i.cleanStylesObject}),v=(0,o.useMemo)((()=>d?(0,c.getSelector)(n,d):""),[n,d]),w=Array.isArray(p)?{}:p;return(0,c.useAtRuleEffect)({deviceType:b,atRule:g,setAtRule:h,defaultAtRules:i.defaultAtRules,isSelected:a,getPreviewWidth:i.getPreviewWidth}),(0,c.useGenerateCSSEffect)({selector:v,styles:w,setAttributes:s,getCss:i.getCss,getSelector:c.getSelector,isSelected:a,blockCss:y,clientId:u}),(0,c.useStyleSelectorEffect)({isSelected:a,currentStyle:m,selector:v,setCurrentStyle:k,setNestedRule:f}),(0,c.useDecodeStyleKeys)({styles:p,setAttributes:s}),(0,t.createElement)(t.Fragment,null,(0,t.createElement)(c.Style,{selector:v,getCss:i.getCss,styles:w,clientId:u,name:n}),(0,t.createElement)(e,{...l,selector:v,onStyleChange:function(e,t="",l="",r=""){const n="object"==typeof e?e:{[e]:t},o=(0,c.buildChangedStylesObject)(n,l,r);S(o)},getStyleValue:function(e,t="",l=""){var r,n,o,s;return l?t?null!==(o=p?.[l]?.[t]?.[e])&&void 0!==o?o:"":null!==(n=p?.[l]?.[e])&&void 0!==n?n:"":t?null!==(s=p?.[t]?.[e])&&void 0!==s?s:"":null!==(r=p?.[e])&&void 0!==r?r:""},styles:w}))}}),c.withUniqueId)((function(e){const{attributes:l,setAttributes:r,onStyleChange:i,getStyleValue:a,editorHtmlAttributes:u={},styles:d,name:p,clientId:y}=e,{tagName:g,openIcon:b,closeIcon:h}=l,m=j("gb-accordion__toggle-icon",{...l,styles:d},!0),k=(0,o.useRef)(),f=(0,s.useBlockProps)({className:m.join(" ").trim(),...u,ref:k}),S=g||"span",v=(0,o.useMemo)((()=>{const e=[];return e.push({label:"SVG Element",value:"svg"}),{selectorShortcuts:O,visibleShortcuts:e}}),[]);(0,o.useEffect)((()=>{g||r({tagName:"span"})}),[]);const w={clearLabel:(0,n.__)("Clear","generateblocks-pro"),openLabel:(0,n.__)("Open Library","generateblocks-pro"),modalTitle:(0,n.__)("Select Icon","generateblocks-pro"),icons:{accordion:{group:(0,n.__)("Accordion","generateblocks-pro"),svgs:N}}},C={name:p,attributes:l,setAttributes:r,clientId:y,getStyleValue:a,onStyleChange:i};return(0,t.createElement)(t.Fragment,null,(0,t.createElement)(s.InspectorControls,null,(0,t.createElement)(c.BlockStyles,{settingsTab:(0,t.createElement)(D.OpenPanel,{...C,panelId:"settings"},(0,t.createElement)(D.IconControl,{label:(0,n.__)("Open Icon","generateblocks-pro"),value:b,onChange:e=>r({openIcon:e}),onClear:()=>r({openIcon:""}),...w}),(0,t.createElement)(D.IconControl,{label:(0,n.__)("Close Icon","generateblocks-pro"),value:h,onChange:e=>r({closeIcon:e}),onClear:()=>r({closeIcon:""}),...w})),stylesTab:(0,t.createElement)(R,{attributes:l,setAttributes:r,shortcuts:v,onStyleChange:i})})),(0,t.createElement)(S,{...f},(0,t.createElement)("span",{className:"gb-accordion__toggle-icon-open",dangerouslySetInnerHTML:{__html:b}}),(0,t.createElement)("span",{className:"gb-accordion__toggle-icon-close",dangerouslySetInnerHTML:{__html:h}})))})),W=JSON.parse('{"UU":"generateblocks-pro/accordion-toggle-icon"}');(0,l.registerBlockType)(W.UU,{edit:T,save:function({attributes:e}){const{tagName:l,htmlAttributes:r={},openIcon:n,closeIcon:o}=e,c=j("gb-accordion__toggle-icon",e,!0),i=s.useBlockProps.save({className:c.join(" ").trim(),...r});return(0,t.createElement)(l,{...i},(0,t.createElement)("span",{className:"gb-accordion__toggle-icon-open",dangerouslySetInnerHTML:{__html:n}}),(0,t.createElement)("span",{className:"gb-accordion__toggle-icon-close",dangerouslySetInnerHTML:{__html:o}}))},icon:function(){return(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",className:"gblocks-block-icon","aria-hidden":"true",focusable:"false"},(0,t.createElement)("path",{d:"M21.376,21.918L2.626,21.918C2.282,21.918 2,21.636 2,21.292C2,20.949 2.282,20.667 2.626,20.667L21.377,20.667C21.719,20.667 22.002,20.95 22.002,21.293C22.002,21.636 21.719,21.918 21.376,21.918C21.376,21.918 21.376,21.918 21.376,21.918ZM21.376,18.765L2.626,18.765C2.283,18.765 2.001,18.483 2.001,18.14C2.001,17.797 2.283,17.515 2.626,17.515L21.377,17.515C21.719,17.515 22.001,17.797 22.001,18.14C22.001,18.483 21.719,18.765 21.376,18.765C21.376,18.765 21.376,18.765 21.376,18.765ZM21.376,15.613L2.626,15.613C2.283,15.613 2.001,15.331 2.001,14.988C2.001,14.645 2.283,14.363 2.626,14.363L21.377,14.363C21.719,14.363 22.001,14.645 22.001,14.988C22.001,15.331 21.719,15.613 21.376,15.613C21.376,15.613 21.376,15.613 21.376,15.613Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M17.831,9.944C17.693,9.944 17.561,9.889 17.463,9.791L15.901,8.229L16.638,7.492L17.831,8.686L19.026,7.492L19.762,8.229L18.2,9.792C18.102,9.889 17.969,9.944 17.831,9.944Z",style:{fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M19.352,2L19.764,2.412L18.201,3.975C18.103,4.072 17.97,4.127 17.833,4.127C17.694,4.127 17.562,4.072 17.465,3.974L15.902,2.412L16.314,2L16.964,2L17.833,2.869L18.702,2L19.352,2Z",style:{fillOpacity:.3,fillRule:"nonzero"}}),(0,t.createElement)("path",{d:"M2.002,5.66L2.002,2L3.251,2L3.251,5.069L20.752,5.069L20.752,2L22.002,2L22.002,5.694C22.002,5.706 22.001,5.718 22.001,5.73L22.001,11.511C22.001,11.857 21.721,12.137 21.376,12.137L2.626,12.137C2.283,12.137 2,11.855 2,11.512C2,11.512 2,11.511 2.001,11.511L2.001,5.695C2.001,5.683 2.001,5.671 2.002,5.66ZM20.751,6.32L3.25,6.32L3.25,10.886L20.751,10.886L20.751,6.32Z",style:{fillOpacity:.3,fillRule:"nonzero"}}))}})})();