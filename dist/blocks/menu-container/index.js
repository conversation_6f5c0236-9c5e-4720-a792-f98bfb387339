(()=>{var e={20493:(e,t,n)=>{"use strict";function l(e){if("Tab"!==e.key&&9!==e.keyCode)return;e.preventDefault();const t=e.currentTarget,n=t.querySelectorAll('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]'),l=Array.from(n).filter((e=>null!==e.offsetParent&&"hidden"!==getComputedStyle(e).visibility&&"none"!==getComputedStyle(e).display));if(0===l.length)return;const o=l[0],r=l[l.length-1],s=document.activeElement;if(t.contains(s))if(e.shiftKey)if(s===o)r.focus();else{const e=l.indexOf(s);e>0&&l[e-1].focus()}else if(s===r)o.focus();else{const e=l.indexOf(s);e<l.length-1&&l[e+1].focus()}else o.focus()}function o(e){const t=e.querySelectorAll(".gb-menu-toggle"),n=e.querySelector(".gb-menu-container"),o=n.querySelector(".gb-menu"),r=o.querySelectorAll(".menu-item"),s=o.querySelectorAll(".menu-item > a"),a=o.querySelectorAll(".gb-submenu-toggle"),i=e.closest("body");requestAnimationFrame((()=>{i.removeAttribute("data-gb-menu-open"),e.classList.remove("gb-navigation--open"),n&&(n.classList.remove("gb-menu-container--toggled"),n.removeEventListener("keydown",l)),t.forEach((e=>{e&&(e.classList.remove("gb-menu-toggle--toggled"),e.ariaExpanded="false",(e.offsetHeight>0||e.offsetWidth>0)&&e.focus())})),r?.length>0&&r.forEach((e=>{e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open")})),s?.length>0&&s.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")})),a?.length>0&&a.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")}))}))}function r(e){const t=e.querySelectorAll(".gb-menu-toggle"),n=e.querySelector(".gb-menu-container"),o=e.getAttribute("data-gb-mobile-menu-type"),r=n.querySelector(".gb-menu-toggle:not(.gb-menu-toggle--clone)"),s=r||"full-overlay"!==o?null:n.querySelector("*"),a=e.closest("body");let i=!1;requestAnimationFrame((()=>{if(e.classList.add("gb-navigation--open"),a.setAttribute("data-gb-menu-open",o),t.forEach((e=>{if(e&&(e.classList.add("gb-menu-toggle--toggled"),e.ariaExpanded="true",!r&&n&&"full-overlay"===o)){s&&(s.style.opacity="0");const t=e.closest(".editor-styles-wrapper"),l=n.querySelector(".gb-menu-toggle--clone");if(t&&l){const t=e.attributes;for(const e of t)l.setAttribute(e.name,e.value);l.innerHTML=e.innerHTML,l.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),i=!0}else if(!l){const t=e.cloneNode(!0);t.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),n.insertAdjacentElement("afterbegin",t),i=!0}}})),i&&s?requestAnimationFrame((()=>{!function(e,t=()=>{}){const n=e.querySelector(".gb-menu-container .gb-menu-toggle");if(n){var l,o;const r=window.getComputedStyle(n),s=null!==(l=parseInt(r?.top,10))&&void 0!==l?l:0,a=null!==(o=parseInt(r?.height,10))&&void 0!==o?o:0;requestAnimationFrame((()=>{e.style.setProperty("--gb-menu-toggle-offset",a+2*s+"px"),t()}))}}(e,(()=>{s.style.opacity=""}))})):s&&"0"===s.style.opacity&&(s.style.opacity=""),n){n.classList.add("gb-menu-container--toggled");const e=n.querySelector('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]');e?e.focus():n.focus(),n.addEventListener("keydown",l)}})),"partial-overlay"===o&&function(e){const t=(n=function(e){var t;const n=null!==(t=e.getAttribute("data-gb-menu-toggle-anchor"))&&void 0!==t?t:"";let l=".gb-navigation";return n?l=n:e.closest(".gb-site-header")&&(l=".gb-site-header"),e.closest(l)}(e))?n.getBoundingClientRect().bottom:0;var n;requestAnimationFrame((()=>e.style.setProperty("--gb-menu-offset",t+"px")))}(e)}function s(e,t=null){if(!e)return;const n=e.querySelectorAll(".menu-item.gb-sub-menu--open");n&&Array.from(n).filter((e=>!e.contains(t))).forEach((e=>{const t=e.querySelector("a"),n=e.querySelector(".gb-submenu-toggle");e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open"),e.setAttribute("aria-current","false"),t&&t.hasAttribute("aria-expanded")&&t.setAttribute("aria-expanded","false"),n&&n.hasAttribute("aria-expanded")&&n.setAttribute("aria-expanded","false")}))}function a(e,t=!1){if(e){t&&t.preventDefault();const n=e.closest(".gb-navigation"),l=e.closest(".menu-item"),o="true"===e.getAttribute("aria-expanded");s(n,l),e.setAttribute("aria-expanded",o?"false":"true"),l.classList.toggle("current-menu-item"),l.classList.toggle("gb-sub-menu--open")}}function i(e,t=!1){if(e){t&&t.preventDefault();const n=t.type,l=e.closest(".gb-menu-container--toggled"),o=e.closest(".gb-menu--hover");if("click"===n&&o&&!l)return;const r=e.closest(".menu-item"),s="true"===e.getAttribute("aria-expanded");e.setAttribute("aria-expanded",s?"false":"true"),r.classList.toggle("current-menu-item"),r.classList.toggle("gb-sub-menu--open")}}function c(e){e&&e.forEach((e=>{var t;const n=e.querySelector(".gb-menu-toggle"),l=e.querySelector(".gb-menu-container"),r=null!==(t=e.getAttribute("data-gb-mobile-breakpoint"))&&void 0!==t?t:"",s=window.matchMedia(`(max-width: ${r})`);n&&l&&n.setAttribute("aria-controls",l.id),e.classList.toggle("gb-navigation--mobile",s.matches),l.classList.toggle("gb-menu-container--mobile",s.matches),s.addEventListener("change",(t=>{e.classList.toggle("gb-navigation--mobile",t.matches),l.classList.toggle("gb-menu-container--mobile",t.matches),o(e)})),setTimeout((()=>{const t=e.querySelector(".gb-menu");if(t){const e=t.querySelectorAll(".menu-item-has-children");e.length>0&&requestAnimationFrame((()=>{e.forEach((e=>{const n=e.querySelector("a"),l=t.classList.contains("gb-menu--click")?n:e.querySelector(".gb-submenu-toggle");if(l){l.setAttribute("aria-controls",`sub-menu-${e.id}`),l.setAttribute("aria-label",`Toggle submenu for ${n.textContent}`);const t=e.querySelector(".gb-sub-menu");t&&(t.id=`sub-menu-${e.id}`)}}))}))}}),0)}))}function u(){let e=document.querySelectorAll(".gb-navigation");if(!e.length){const t=window.frameElement;if(t&&t.id&&t.id.startsWith("pattern-"))return void new MutationObserver(((t,n)=>{e=document.querySelectorAll(".gb-navigation"),e.length&&(n.disconnect(),c(e))})).observe(document.body,{childList:!0,subtree:!0})}c(e),function(){const e=document.querySelectorAll(".gb-navigation--mobile");e&&e.length&&e.forEach((e=>{e.addEventListener("click",(t=>{const n=t.target.closest('a[href*="#"]');if(!n)return;const l=n.getAttribute("href").match(/#(.+)$/);if(l){const t=l[1];document.getElementById(t)&&setTimeout((()=>{o(e)}),50)}}))}))}()}var g;n.d(t,{IJ:()=>o,Qg:()=>a,SL:()=>r}),window.myNavigationScriptInitialized||(window.myNavigationScriptInitialized=!0,document.addEventListener("click",(e=>{const t=e.target;!function(e){if(e){var t;const n=e.closest(".gb-navigation");if(!n)return;n.classList.contains("gb-navigation--open")?o(n):r(n);const l=null!==(t=window.frameElement)&&void 0!==t&&t;if(l&&l.id&&l.id.startsWith("pattern-"))if(n.classList.contains("gb-navigation--open")){const e=l.getAttribute("data-gb-original-height");e&&(l.style.height=e)}else l.style.height&&parseInt(l.style.height,10)<800&&(l.setAttribute("data-gb-original-height",l.style.height),requestAnimationFrame((()=>l.style.height="800px")))}}(t.closest(".gb-menu-toggle")),a(t.closest(".gb-menu--click .menu-item-has-children > a"),e),i(t.closest(".gb-submenu-toggle"),e);const n=document.querySelector(".menu-item.gb-sub-menu--open");n&&!n.contains(e.target)&&s(n.closest(".gb-navigation:not(.gb-navigation--open)"))})),document.addEventListener("keydown",(e=>{const t="Escape"===e.key,n="Enter"===e.key,l=" "===e.key,r="Tab"===e.key;if((n||l)&&(i(e.target.closest(".gb-submenu-toggle"),e),a(e.target.closest(".gb-menu--click .menu-item-has-children > a"),e)),r){const e=document.querySelector(".gb-sub-menu--open");e&&setTimeout((()=>{const t=document.activeElement;t.closest(".gb-sub-menu--open")||s(e.closest(".gb-navigation"),t)}),0)}if(t){const t=e.target.closest(".gb-sub-menu--open");if(t){s(t.closest(".gb-navigation"));const e=t.querySelector(".gb-submenu-toggle");e&&e.focus()}else{const e=document.querySelector(".gb-navigation--open");e&&o(e)}}})),window.addEventListener("pagehide",(()=>{const e=document.querySelectorAll(".gb-navigation--open");e.length&&e.forEach((e=>o(e)))})),g=()=>{document.querySelector(".editor-styles-wrapper, .wp-admin")?window.addEventListener("load",u):u()},"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",g):g()))},46942:(e,t)=>{var n;!function(){"use strict";var l={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=s(e,r(n)))}return e}function r(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)l.call(e,n)&&e[n]&&(t=s(t,n));return t}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},t={};function n(l){var o=t[l];if(void 0!==o)return o.exports;var r=t[l]={exports:{}};return e[l](r,r.exports,n),r.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var l in t)n.o(t,l)&&!n.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React,t=window.wp.blocks,l=window.wp.blockEditor,o=window.wp.element,r=window.wp.i18n,s=window.wp.data,a=window.wp.components,i=window.gbp.blockStyles,c=window.wp.compose,u=window.gbp.stylesBuilder;function g(t){const{colors:n,label:l}=t;return(0,e.createElement)(a.BaseControl,{className:"gpp-color-group",label:l,id:""},(0,e.createElement)("div",{className:"gpp-color-group__row"},n.map(((t,n)=>(0,e.createElement)(u.ColorPicker,{key:n,tooltip:t?.tooltip,value:t.value,onChange:t.onChange})))))}const b=(0,s.createReduxStore)("gbp-menu-toggle-state",{reducer:function(e=!1,t){return"SET_DATA"===t.type?t.payload:e},actions:{setMenuToggleState:e=>({type:"SET_DATA",payload:e})},selectors:{menuToggleState:e=>e}}),m=(0,s.createReduxStore)("gbp-current-style",{reducer:u.currentStyleReducer,actions:u.currentStyleActions,selectors:u.currentStyleSelectors}),d=(0,s.createReduxStore)("gbp-styles",{reducer:u.styleReducer,actions:u.styleActions,selectors:u.styleSelectors}),p=(0,s.createReduxStore)("gbp-styles-at-rule",{reducer:u.atRuleReducer,actions:u.atRuleActions,selectors:u.atRuleSelectors}),y=(0,s.createReduxStore)("gbp-styles-nested-rule",{reducer:u.nestedRuleReducer,actions:u.nestedRuleActions,selectors:u.nestedRuleSelectors}),f=window.wp.apiFetch;var v=n.n(f);const h=window.wp.notices,k=window.wp.url,S=window.wp.coreData;var E;window.lodash;const _="undefined"!=typeof gbGlobalStylePermissions&&null!==(E=gbGlobalStylePermissions?.canManageStyles)&&void 0!==E&&E,C=window.wp.editPost;const w=(0,s.createReduxStore)("gbp-block-styles-current-style",{reducer:u.currentStyleReducer,actions:u.currentStyleActions,selectors:u.currentStyleSelectors}),A=(0,s.createReduxStore)("gbp-block-styles-at-rule",{reducer:u.atRuleReducer,actions:u.atRuleActions,selectors:u.atRuleSelectors}),M=(0,s.createReduxStore)("gbp-block-styles-nested-rule",{reducer:u.nestedRuleReducer,actions:u.nestedRuleActions,selectors:u.nestedRuleSelectors});function L(){const e=(0,s.useSelect)((e=>e(A).getAtRule())),{setAtRule:t}=(0,s.useDispatch)(A),n=(0,s.useSelect)((e=>e(M).getNestedRule())),{setNestedRule:o}=(0,s.useDispatch)(M),a=(0,i.useCurrentAtRule)(u.defaultAtRules),{setCurrentStyle:c}=(0,s.useDispatch)(w),g=(0,s.useSelect)((e=>e(w).currentStyle())),{deviceType:b,setDeviceType:f}=(0,i.useDeviceType)(),E=function(){const{setCurrentStyle:e}=(0,s.useDispatch)(m),{setStyles:t}=(0,s.useDispatch)(d),{createNotice:n,removeAllNotices:o}=(0,s.useDispatch)(h.store),{getEntityRecordEdits:a}=(0,s.useSelect)(S.store),{getSelectedBlock:i}=(0,s.useSelect)((e=>e(l.store)),[]),{setAtRule:c}=(0,s.useDispatch)(p),{setNestedRule:u}=(0,s.useDispatch)(y),{openGeneralSidebar:g}=(0,s.useDispatch)(C.store);return async(l,s={})=>{if(!_)return;const{classStyles:b,classPostId:m}=await async function(e){var t;const n=await v()({path:(0,k.addQueryArgs)("/generateblocks-pro/v1/global-classes/get_styles",{globalClass:e}),method:"GET"});let l=null!==(t=n?.response?.data?.styles)&&void 0!==t?t:{};return Array.isArray(l)&&0===l.length&&(l={}),{classStyles:l,classPostId:n?.response?.data?.postId}}(l);if(!m)return o("snackbar"),void n("error",(0,r.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,r.__)("%s does not exist.","generateblocks-pro"),l),{type:"snackbar"});c(""),u(""),g("gblocks-editor-sidebar/gblocks-editor-sidebar"),e({postId:m,name:l,classStyles:b,clientId:i()?.clientId,options:s}),s.nestedRule&&u(s.nestedRule),s.atRule&&c(s.atRule);const d=a("postType","gblocks_styles",m);t(d?.gb_style_data||b),o("snackbar"),n("info",(0,r.sprintf)(
// Translators: Global class name.
// Translators: Global class name.
(0,r.__)("Editing %s.","generateblocks-pro"),l),{type:"snackbar"})}}(),L=function(){const{setCurrentStyle:e}=(0,s.useDispatch)(m),{setStyles:t}=(0,s.useDispatch)(d),{setAtRule:n}=(0,s.useDispatch)(p),{setNestedRule:l}=(0,s.useDispatch)(y);return()=>{e({}),t({}),n(""),l("")}}();return{atRule:e,nestedRule:n,setAtRule:t,currentAtRule:a,setNestedRule:o,setDeviceType:f,deviceType:b,setCurrentStyle:c,currentStyle:g,getPreviewDevice:i.getPreviewDevice,setGlobalStyle:E,cancelEditGlobalStyle:L}}function R({attributes:t,setAttributes:n,shortcuts:l,onStyleChange:o}){const{atRule:r,setAtRule:s,nestedRule:a,setNestedRule:c,setDeviceType:g,getPreviewDevice:b,currentStyle:m,setGlobalStyle:d,cancelEditGlobalStyle:p}=L(),{styles:y,globalClasses:f=[]}=t,v=(0,u.getStylesObject)(y,r,a);return(0,e.createElement)(u.StylesBuilder,{currentSelector:m?.selector,styles:v,allStyles:y,onDeleteStyle:(e,t)=>{const l=(0,u.deleteStylesObjectKey)(y,e,t);n({styles:l})},nestedRule:a,atRule:r,onStyleChange:(e,t=null)=>o(e,t,r,a),onNestedRuleChange:e=>c(e),onAtRuleChange:e=>{s(e);const t=(0,u.getPreviewWidth)(e),n=b(t);n&&g(n)},onUpdateKey:(e,t,l)=>{const o=(0,u.updateStylesObjectKey)(y,e,t,l);n({styles:o})},selectorShortcuts:l.selectorShortcuts,visibleSelectors:l.visibleShortcuts,onEditStyle:d,cancelEditStyle:p,setLocalTab:e=>{sessionStorage.setItem(i.TABS_STORAGE_KEY,e)},scope:"local",appliedGlobalStyles:f})}function I(t){return n=>{const{attributes:l,name:r,setAttributes:s,isSelected:a,clientId:c}=n,{uniqueId:g,styles:b,css:m}=l,{atRule:d,deviceType:p,setAtRule:y,currentStyle:f,setCurrentStyle:v,setNestedRule:h}=L(),k=(0,i.useSetStyles)(n,{cleanStylesObject:u.cleanStylesObject}),S=(0,o.useMemo)((()=>g?(0,i.getSelector)(r,g):""),[r,g]),E=Array.isArray(b)?{}:b;return(0,i.useAtRuleEffect)({deviceType:p,atRule:d,setAtRule:y,defaultAtRules:u.defaultAtRules,isSelected:a,getPreviewWidth:u.getPreviewWidth}),(0,i.useGenerateCSSEffect)({selector:S,styles:E,setAttributes:s,getCss:u.getCss,getSelector:i.getSelector,isSelected:a,blockCss:m,clientId:c}),(0,i.useStyleSelectorEffect)({isSelected:a,currentStyle:f,selector:S,setCurrentStyle:v,setNestedRule:h}),(0,i.useDecodeStyleKeys)({styles:b,setAttributes:s}),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(i.Style,{selector:S,getCss:u.getCss,styles:E,clientId:c,name:r}),(0,e.createElement)(t,{...n,selector:S,onStyleChange:function(e,t="",n="",l=""){const o="object"==typeof e?e:{[e]:t},r=(0,i.buildChangedStylesObject)(o,n,l);k(r)},getStyleValue:function(e,t="",n=""){var l,o,r,s;return n?t?null!==(r=b?.[n]?.[t]?.[e])&&void 0!==r?r:"":null!==(o=b?.[n]?.[e])&&void 0!==o?o:"":t?null!==(s=b?.[t]?.[e])&&void 0!==s?s:"":null!==(l=b?.[e])&&void 0!==l?l:""},styles:E}))}}const B=window.wp.hooks,T=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","readonly","required","reversed","selected"];function x(e){const t=e.trim().replace(/[^A-Za-z0-9-_:.]+/g,"-").replace(/-+/g,"-").replace(/^-|-$/g,"");return t?/^[A-Za-z]/.test(t)?t:`id-${t}`:""}function q(t){return n=>{var r,i,c,u;const{attributes:g,setAttributes:b,context:m}=n,{htmlAttributes:d={},uniqueId:p,className:y,align:f}=g,v=(0,s.useSelect)((e=>e("core/editor").isSavingPost())),{style:h="",href:k,...S}=d,E=Object.keys(S).reduce(((e,t)=>(e[t]=(e=>{if(null==e)return"";let t="";if("object"==typeof e)try{t=JSON.stringify(e)}catch(e){return""}else t=String(e);return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})(S[t]),e)),{}),[_,C]=(0,o.useState)(h);var w,A,M,L;(0,o.useEffect)((()=>{!async function(){const e=await(0,B.applyFilters)("generateblocks.editor.htmlAttributes.style",h,{...n});C(e)}()}),[h,m,v]),w=()=>{const e=["alignwide","alignfull"],t=(y?.split(" ")||[]).filter((t=>!e.includes(t)));f&&t.push("align"+f),b({className:t.join(" ")})},A=[f],L=(M=(0,e.useRef)(!0)).current?(M.current=!1,!0):M.current,(0,e.useEffect)((function(){if(!L)return w()}),A);const R="string"==typeof _?_.split(";").reduce(((e,t)=>{const n=t.indexOf(":");if(-1===n)return e;let l=t.slice(0,n).trim();const o=t.slice(n+1).trim();return l&&o&&(l.startsWith("--")||(l=l.replace(/-([a-z])/g,(e=>e[1].toUpperCase()))),e[l]=o),e}),{}):"",I={...E,style:R,"data-gb-id":p,"data-context-post-id":null!==(r=null!==(i=m?.postId)&&void 0!==i?i:m?.["generateblocks/loopIndex"])&&void 0!==r?r:0,"data-align":f||void 0},q=(0,o.useMemo)((()=>Array.isArray(d)?{}:d),[JSON.stringify(d)]);return(0,o.useEffect)((()=>{const e={...d};Object.keys(e).forEach((t=>{const n=t.startsWith("data-"),l=e[t];T.includes(t)||""!==l||n||"alt"===t||delete e[t],"string"!=typeof l&&"boolean"!=typeof l&&delete e[t],"class"===t&&delete e[t]})),function(e,t){if(e===t)return!0;if(!e||!t)return!1;const n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(const l of n)if(e[l]!==t[l])return!1;return!0}(e,d)||b({htmlAttributes:e})}),[JSON.stringify(d)]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(t,{...n,editorHtmlAttributes:I,htmlAttributes:q}),(0,e.createElement)(l.InspectorAdvancedControls,null,(0,e.createElement)(a.TextControl,{label:"HTML ID",value:null!==(c=d.id)&&void 0!==c?c:"",onChange:e=>{b({htmlAttributes:{...d,id:e}})},onBlur:()=>{d.id&&b({htmlAttributes:{...d,id:x(d.id)}})}}),(0,e.createElement)(a.TextControl,{label:"ARIA Label",value:null!==(u=d["aria-label"])&&void 0!==u?u:"",onChange:e=>{b({htmlAttributes:{...d,"aria-label":e}})}})))}}function N(e,t,n=!1){const{styles:l={},uniqueId:o="",globalClasses:r=[]}=t,s=[];return n&&s.push(e),r.length>0&&s.push(...r),Object.keys(l).length>0&&s.push(`${e}-${o}`),s}const P=window.gbp.components,D={default:{items:[{label:(0,r.__)("Mobile Menu Container","generateblocks-pro"),value:"&.gb-menu-container--mobile"},{label:(0,r.__)("Mobile Menu","generateblocks-pro"),value:"&.gb-menu-container--mobile .gb-menu"},{label:(0,r.__)("Mobile Menu Item","generateblocks-pro"),value:"&.gb-menu-container--mobile .menu-item"},{label:(0,r.__)("Mobile Menu Item Hover","generateblocks-pro"),value:"&.gb-menu-container--mobile .menu-item:is(:hover, :focus-within)"},{label:(0,r.__)("Mobile Menu Item Current","generateblocks-pro"),value:"&.gb-menu-container--mobile .menu-item:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus-within)"},{label:(0,r.__)("Mobile Sub-Menu","generateblocks-pro"),value:"&.gb-menu-container--mobile .gb-sub-menu"},{label:(0,r.__)("Mobile Sub-Menu Item","generateblocks-pro"),value:"&.gb-menu-container--mobile .gb-sub-menu .menu-item"},{label:(0,r.__)("Mobile Sub-Menu Item Hover","generateblocks-pro"),value:"&.gb-menu-container--mobile .gb-sub-menu .menu-item:is(:hover, :focus-within)"},{label:(0,r.__)("Mobile Sub-Menu Item Current","generateblocks-pro"),value:"&.gb-menu-container--mobile .gb-sub-menu .menu-item:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus-within)"}]}},O=[],j=(e,t)=>e&&Array.isArray(e)?e.reduce(((e,n)=>{const l=!(t&&t.length>0)||t.includes(n.name),o=j(n.innerBlocks,t);return{total:e.total+1+o.total,allowed:e.allowed+(l?1:0)+o.allowed}}),{total:0,allowed:0}):{total:0,allowed:0},F=(e,t)=>e&&Array.isArray(e)?e.filter((e=>{const{name:n}=e;return!(t&&t.length>0)||t.includes(n)})).map((e=>({...e,innerBlocks:F(e.innerBlocks,t)}))):[],U=(0,o.memo)((({block:n,level:l=0,currentClientId:o,selectBlock:r})=>{var i;const{name:c,innerBlocks:u,clientId:g}=n,{getBlockType:b}=(0,s.useSelect)((e=>e(t.store)),[]),m=b(c);return(0,e.createElement)("div",{className:"gb-block-node","data-level":l},(0,e.createElement)(a.Button,{variant:"tertiary",size:"compact",className:"gb-block-node-button",onClick:()=>r(g),isPressed:o===g,icon:null!==(i=m?.icon?.src)&&void 0!==i?i:null},function(e,t,n="list-view"){const{__experimentalLabel:l,title:o}=e||{};if(!e)return"Unknown Block";const r=l&&l(t,{context:n});return!r||"string"!=typeof r&&"number"!=typeof r?o||"Unnamed Block":r}(m,n.attributes)),u&&u.length>0&&(0,e.createElement)("div",{className:"gb-block-tree-inner-blocks"},u.map((t=>(0,e.createElement)(U,{key:t.clientId,block:t,level:l+1,currentClientId:o,blockType:m,selectBlock:r})))))}));function H({blocks:t,clientId:n,allowedBlocks:i,showAllLabel:c=(0,r.__)("Show all blocks","generateblocks-pro")}){const[u,g]=(0,o.useState)(!1),{selectBlock:b}=(0,s.useDispatch)(l.store),m=(0,o.useMemo)((()=>j(t,i)),[t,i]),d=(0,o.useMemo)((()=>F(t,i)),[t,i]),p=(0,o.useMemo)((()=>F(t,[])),[t]),y=(0,o.useMemo)((()=>u?p:d),[u,p,d]),f=m.total!==m.allowed;return(0,e.createElement)("div",{className:"gb-block-tree"},!!f&&(0,e.createElement)("div",{className:"gb-block-tree__show-all"},(0,e.createElement)(a.ToggleControl,{label:c,checked:u,onChange:e=>g(e)})),y.map((t=>(0,e.createElement)(U,{key:t.clientId,block:t,currentClientId:n,selectBlock:b}))))}function G(){const t=document.querySelector('.gb-block-styles-tab-panel button[id*="styles"]');return t?(0,e.createElement)("div",{className:"gb-more-style-controls"},(0,o.createInterpolateElement)(
// Translators: the at-rule for deletion.
// Translators: the at-rule for deletion.
(0,r.__)("Open the <Styles /> tab for more controls.","generateblocks-pro"),{Styles:(0,e.createElement)(a.Button,{variant:"link",className:"gb-more-style-controls__styles-tab",onClick:()=>t.click()},(0,r.__)("Styles","generateblocks-pro"))})):null}function Z({value:t,onChange:n}){const l=(0,o.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus?.map((e=>({label:e.name,value:e.term_id.toString()}))))&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]);return l.length?(0,e.createElement)(a.SelectControl,{label:(0,r.__)("Menu","generateblocks-pro"),value:t,options:l,onChange:n}):null}var $=n(46942),z=n.n($);function V({name:e,clientId:t,align:n,children:r}){const{getBlockRootClientId:a}=(0,s.useSelect)((e=>e("core/block-editor")),[]),i=(0,s.useSelect)((e=>{const{getSettings:t}=e(l.store);return t().supportsLayout||!1}),[]),c=e.toString().replace("/","-"),u={className:z()({"wp-block":!0,"gb-is-root-block":!0,[`gb-root-block-${c}`]:!0,[`align${n}`]:i}),"data-align":n&&!i?n:null,"data-block":t},g=a(t);return(0,B.applyFilters)("generateblocks.rootElement.disable",g,{name:e})?r:(0,o.createElement)("div",u,r)}const W=window.wp.serverSideRender;var J=n.n(W);const K={},Q=[];var Y=n(20493);function X(){var t,n;const l=null!==(t=generateblocksBlockClassicMenu?.menuAdminUrl)&&void 0!==t?t:"",[i,c]=(0,o.useState)(null!==(n=generateblocksBlockClassicMenu?.hasMenuSupport)&&void 0!==n&&n),u=(0,o.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus)&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]),g=(0,s.useSelect)((e=>e(S.store).getEntityRecord("root","site")?.generateblocks_pro_classic_menu_support||!1),[]),{editEntityRecord:b,saveEditedEntityRecord:m}=(0,s.useDispatch)(S.store),[d,p]=(0,o.useState)(g);return i?!l||u.length>0?null:(0,e.createElement)(a.Notice,{isDismissible:!1,status:"warning"},(0,o.createInterpolateElement)((0,r.__)("No menus found. Please <CreateMenuLink />.","generateblocks-pro"),{CreateMenuLink:(0,e.createElement)("a",{href:l,target:"_blank",rel:"noopener noreferrer"},(0,r.__)("create a menu","generateblocks-pro"))})):(0,e.createElement)(a.Notice,{isDismissible:!1,status:"warning"},(0,e.createElement)(a.ToggleControl,{label:(0,r.__)("Enable Menu Support","generateblocks-pro"),checked:d,onChange:async e=>{p(e),c(e);try{await b("root","site",void 0,{generateblocks_pro_classic_menu_support:e}),await m("root","site",void 0)}catch(e){var t;console.error("Save failed:",e),p(g),c(null!==(t=generateblocksBlockClassicMenu?.hasMenuSupport)&&void 0!==t&&t)}},help:(0,r.__)("Your theme does not support the menu system. Enable it here.","generateblocks-pro")}))}(0,c.compose)(I,i.withUniqueId)((function(t){var n;const{attributes:a,setAttributes:c,getStyleValue:u,onStyleChange:g,clientId:m,name:d,context:p,isSelected:y}=t,{menu:f,uniqueId:v}=a,h=(0,o.useRef)(),k=(0,s.useSelect)((e=>e(b).menuToggleState())),{getBlockParentsByBlockName:S,getBlock:E}=(0,s.useSelect)((e=>e(l.store)),[]),{selectBlock:_}=(0,s.useDispatch)(l.store),C=(0,o.useMemo)((()=>{var e;return null!==(e=S(m,"generateblocks-pro/navigation",!0)?.[0])&&void 0!==e?e:""}),[m]),w=(0,o.useMemo)((()=>E(C)),[C,y]),A=(0,o.useMemo)((()=>E(m)),[m,y]),M=(0,o.useMemo)((()=>{var e;return null!==(e=A?.innerBlocks?.find((e=>"generateblocks-pro/classic-menu-item"===e.name))?.clientId)&&void 0!==e?e:""}),[A]),L=(0,o.useMemo)((()=>{var e;return null!==(e=A?.innerBlocks?.find((e=>"generateblocks-pro/classic-sub-menu"===e.name))?.clientId)&&void 0!==e?e:""}),[A]),I=h?.current?.querySelector(".gb-menu"),B=(0,o.useMemo)((()=>{var e;return null!==(e=generateblocksBlockClassicMenu?.navMenus?.map((e=>({label:e.name,value:e.term_id.toString()}))))&&void 0!==e?e:[]}),[generateblocksBlockClassicMenu?.navMenus?.length]);(0,o.useEffect)((()=>{var e;f&&B.find((e=>e.value===f))||c({menu:null!==(e=B[0]?.value)&&void 0!==e?e:""})}),[B.length]);const T=N("gb-menu",a,!0),x=(0,l.useBlockProps)({className:T.filter(Boolean).join(" ").trim(),ref:h}),q=(0,l.useInnerBlocksProps)({},{allowedBlocks:["generateblocks-pro/classic-menu-item","generateblocks-pro/classic-sub-menu"],renderAppender:!1}),D={name:d,attributes:a,setAttributes:c,clientId:m,getStyleValue:u,onStyleChange:g},O={};return O.subMenuType=null!==(n=p?.["generateblocks-pro/subMenuType"])&&void 0!==n?n:"hover",O.disableLinks=!0,(0,o.useEffect)((()=>{if(!h?.current)return;const e=h.current.closest(".gb-menu-container");if(!e)return;const t=e.querySelectorAll(".gb-menu--click .menu-item-has-children > a"),n=[];return t.length>0&&t.forEach((e=>{const t=t=>{(0,Y.Qg)(e,t)};e.addEventListener("click",t),n.push({item:e,handler:t})})),()=>{n.forEach((({item:e,handler:t})=>{e.removeEventListener("click",t)}))}}),[h,I]),(0,o.useEffect)((()=>{if(!h?.current)return;const e=h?.current?.closest(".gb-navigation");if(!e)return;const t=e.querySelectorAll(".gb-submenu-toggle"),n=[];return t.length>0&&t.forEach((e=>{const t=t=>{const n=e.closest(".gb-menu-container--toggled"),l=e.closest(".gb-menu--hover");"click"===t?.type&&l&&!n||(0,Y.Qg)(e,t)};e.addEventListener("click",t),n.push({item:e,handler:t})})),()=>{n.forEach((({item:e,handler:t})=>{e.removeEventListener("click",t)}))}}),[h,k,I]),(0,o.useEffect)((()=>{const e=h?.current;if(e)return e.addEventListener("click",t),()=>{e.removeEventListener("click",t)};function t(e){e.target.closest(".gb-sub-menu")?_(L):e.target.closest(".menu-item")&&_(M)}}),[h,M,I,L]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(i.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(P.OpenPanel,{title:(0,r.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...D},(0,e.createElement)(H,{blocks:[w],clientId:m,allowedBlocks:ee,showAllLabel:(0,r.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(X,null),(0,e.createElement)(P.OpenPanel,{...D,panelId:"settings"},(0,e.createElement)(Z,{value:f,onChange:e=>c({menu:e})}))),stylesTab:(0,e.createElement)(R,{attributes:a,setAttributes:c,shortcuts:{selectorShortcuts:K,visibleShortcuts:Q},onStyleChange:g})})),(0,e.createElement)("div",{...x},B.length&&f?(0,e.createElement)(J(),{key:f+v,block:"generateblocks-pro/classic-menu",attributes:a,urlQueryArgs:O}):(0,e.createElement)(e.Fragment,null,(0,r.__)("No menu found.","generateblocks-pro")),(0,e.createElement)("div",{...q})))}));const ee=["generateblocks-pro/navigation","generateblocks-pro/menu-container","generateblocks-pro/classic-menu","generateblocks-pro/classic-menu-item","generateblocks-pro/classic-sub-menu","generateblocks-pro/menu-toggle"];function te(e){for(const t of e){if("generateblocks-pro/classic-menu"===t.name)return t;if(t.innerBlocks&&t.innerBlocks.length>0){const e=te(t.innerBlocks);if(e)return e}}return null}function ne(e){for(const t of e){if("generateblocks-pro/menu-toggle"===t.name)return t;if(t.innerBlocks&&t.innerBlocks.length>0){const e=ne(t.innerBlocks);if(e)return e}}return null}(0,c.compose)(q,I,i.withUniqueId)((function(t){var n,c,g,m,d;const{attributes:p,setAttributes:y,editorHtmlAttributes:f,onStyleChange:v,getStyleValue:h,clientId:k,name:S,isSelected:E}=t,{styles:_,htmlAttributes:C,tagName:w,uniqueId:A,subMenuType:M}=p,L=(0,s.useSelect)((e=>e(b).menuToggleState())),I=(0,o.useRef)(),{getBlock:B,getBlocks:T}=(0,s.useSelect)((e=>e(l.store)),[]),{updateBlockAttributes:x}=(0,s.useDispatch)(l.store),q=(0,o.useMemo)((()=>B(k)),[k,E]),[D,O]=(0,o.useState)(null),j=N("gb-navigation",{...p,styles:_},!0);L&&j.push("gb-navigation--open"),j.includes("gb-navigation-"+A)||j.push("gb-navigation-"+A);const F=(0,l.useBlockProps)({className:j.filter((e=>e)).join(" ").trim(),...f,ref:I}),U=(0,l.useInnerBlocksProps)(F),G=w||"nav";(0,o.useEffect)((()=>{w||y({tagName:"nav"})}),[w]),(0,o.useEffect)((()=>{C?.["data-gb-mobile-menu-type"]||y({htmlAttributes:{...C,"data-gb-mobile-menu-type":"full-overlay"}})}),[C?.["data-gb-mobile-menu-type"]]),(0,o.useEffect)((()=>{if(!E)return;const e=te(T(k));e&&O(e)}),[E,k]);const $={name:S,attributes:p,setAttributes:y,clientId:k,getStyleValue:h,onStyleChange:v},z=null!==(n=C?.["data-gb-mobile-menu-transition"])&&void 0!==n?n:"",W=null!==(c=C?.["data-gb-sub-menu-transition"])&&void 0!==c?c:"";return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(i.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(P.OpenPanel,{title:(0,r.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...$},(0,e.createElement)(H,{blocks:[q],clientId:k,allowedBlocks:ee,showAllLabel:(0,r.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(X,null),(0,e.createElement)(P.OpenPanel,{panelId:"settings",...$},!!D&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Z,{value:D?.attributes?.menu,onChange:e=>{x(D.clientId,{menu:e});const t=te(T(k));t&&O(t)}})),(0,e.createElement)(a.SelectControl,{label:(0,r.__)("Sub-menu type","generateblocks-pro"),value:M,options:[{label:(0,r.__)("Hover","generateblocks-pro"),value:"hover"},{label:(0,r.__)("Click Menu Item","generateblocks-pro"),value:"click"},{label:(0,r.__)("Click Toggle","generateblocks-pro"),value:"click-toggle"}],onChange:e=>y({subMenuType:e})}),(0,e.createElement)(a.SelectControl,{label:(0,r.__)("Sub-menu Transition","generateblocks-pro"),value:W,options:[{label:(0,r.__)("None","generateblocks-pro"),value:""},{label:(0,r.__)("Fade","generateblocks-pro"),value:"fade"},{label:(0,r.__)("Slide & fade up","generateblocks-pro"),value:"fade-slide-up"},{label:(0,r.__)("Slide & fade down","generateblocks-pro"),value:"fade-slide-down"},{label:(0,r.__)("Slide & fade left","generateblocks-pro"),value:"fade-slide-left"},{label:(0,r.__)("Slide & fade right","generateblocks-pro"),value:"fade-slide-right"}],onChange:e=>{const t={...C};e?t["data-gb-sub-menu-transition"]=e:(delete t["data-gb-sub-menu-transition"],v("--sub-menu-transition-speed","")),y({htmlAttributes:t})}}),""!==W&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(u.UnitControl,{units:["ms"],defaultUnitValue:"ms",label:(0,r.__)("Sub-menu Transition Speed","generateblocks-pro"),placeholder:"200ms",value:h("--sub-menu-transition-speed",""),onChange:e=>{v("--sub-menu-transition-speed",e)}}),"fade"!==W&&(0,e.createElement)(u.UnitControl,{label:(0,r.__)("Sub-menu Transition Distance","generateblocks-pro"),placeholder:"5px",value:h("--sub-menu-transition-distance",""),onChange:e=>{v("--sub-menu-transition-distance",e)}})),(0,e.createElement)(u.UnitControl,{label:(0,r.__)("Mobile breakpoint","generateblocks-pro"),value:null!==(g=C?.["data-gb-mobile-breakpoint"])&&void 0!==g?g:"",onChange:e=>{const t={...C};e?t["data-gb-mobile-breakpoint"]=e:delete t["data-gb-mobile-breakpoint"],y({htmlAttributes:t})}}),(0,e.createElement)(a.SelectControl,{label:(0,r.__)("Mobile Menu Type","generateblocks-pro"),value:null!==(m=C?.["data-gb-mobile-menu-type"])&&void 0!==m?m:"",options:[{label:(0,r.__)("Full overlay","generateblocks-pro"),value:"full-overlay"},{label:(0,r.__)("Partial overlay","generateblocks-pro"),value:"partial-overlay"}],onChange:e=>{const t={...C};e?t["data-gb-mobile-menu-type"]=e:delete t["data-gb-mobile-menu-type"],y({htmlAttributes:t})}}),"partial-overlay"===C?.["data-gb-mobile-menu-type"]&&(0,e.createElement)(a.TextControl,{label:(0,r.__)("Mobile Menu Anchor","generateblocks-pro"),help:(0,r.__)("The selector for the element the mobile menu will attach to the bottom of.","generateblocks-pro"),value:null!==(d=C?.["data-gb-menu-toggle-anchor"])&&void 0!==d?d:"",placeholder:"Calculate automatically",onChange:e=>{const t={...C};e?t["data-gb-menu-toggle-anchor"]=e:delete t["data-gb-menu-toggle-anchor"],y({htmlAttributes:t})}}),(0,e.createElement)(a.SelectControl,{label:(0,r.__)("Mobile Menu Transition","generateblocks-pro"),value:z,options:[{label:(0,r.__)("None","generateblocks-pro"),value:""},{label:(0,r.__)("Fade","generateblocks-pro"),value:"fade"},{label:(0,r.__)("Slide left","generateblocks-pro"),value:"slide-left"},{label:(0,r.__)("Slide right","generateblocks-pro"),value:"slide-right"},{label:(0,r.__)("Slide up","generateblocks-pro"),value:"slide-up"},{label:(0,r.__)("Slide down","generateblocks-pro"),value:"slide-down"},{label:(0,r.__)("Slide & fade left","generateblocks-pro"),value:"fade-slide-left"},{label:(0,r.__)("Slide & fade right","generateblocks-pro"),value:"fade-slide-right"},{label:(0,r.__)("Slide & fade up","generateblocks-pro"),value:"fade-slide-up"},{label:(0,r.__)("Slide & fade down","generateblocks-pro"),value:"fade-slide-down"}],onChange:e=>{const t={...C};e?t["data-gb-mobile-menu-transition"]=e:(delete t["data-gb-mobile-menu-transition"],v("--mobile-transition-speed","")),y({htmlAttributes:t})}}),""!==z&&(0,e.createElement)(u.UnitControl,{units:["ms"],defaultUnitValue:"ms",label:(0,r.__)("Mobile Menu Transition Speed","generateblocks-pro"),placeholder:"200ms",value:h("--mobile-transition-speed",""),onChange:e=>{v("--mobile-transition-speed",e)}}))),stylesTab:(0,e.createElement)(R,{attributes:p,setAttributes:y,shortcuts:{},onStyleChange:v})})),(0,e.createElement)(V,{name:S,clientId:k},(0,e.createElement)(G,{...U})))}));const le=(0,c.compose)(q,I,i.withUniqueId)((function(t){const{attributes:n,setAttributes:c,editorHtmlAttributes:u,getStyleValue:m,onStyleChange:d,name:p,clientId:y,isSelected:f,context:v}=t,{tagName:h}=n,k=(0,s.useSelect)((e=>e(b).menuToggleState())),{getBlockParentsByBlockName:S,getBlock:E,getBlocks:_}=(0,s.useSelect)((e=>e(l.store)),[]),C=(0,o.useMemo)((()=>{var e;return null!==(e=S(y,"generateblocks-pro/navigation",!0)?.[0])&&void 0!==e?e:""}),[y]),w=(0,o.useMemo)((()=>E(C)),[C,f]),{setMenuToggleState:A}=(0,s.useDispatch)(b),M=(0,s.useSelect)((e=>{const{getDeviceType:t}=e("core/editor")||{};if("function"==typeof t)return t();const{__experimentalGetPreviewDeviceType:n=()=>""}=e("core/edit-post");return n()}),[]),[L,I]=(0,o.useState)(!1),B=document.querySelector(".editor-resizable-editor"),[T,x]=(0,o.useState)(null),[q,j]=(0,o.useState)([]),[F,U]=(0,o.useState)(null);(0,o.useEffect)((()=>{const e=N("gb-menu-container",n,!0);k&&e.push("gb-menu-container--toggled"),L?e.push("gb-menu-container--mobile"):e.filter((e=>"gb-menu-container--mobile"!==e)),j(e)}),[n,L,k,N]);const Z=(0,o.useRef)(),$=(0,l.useBlockProps)({className:q.filter((e=>e)).join(" ").trim(),...u,ref:Z}),z=(0,l.useInnerBlocksProps)($),V=h||"div";(0,o.useEffect)((()=>{h||c({tagName:"div"})}),[h]);const W={name:p,attributes:n,setAttributes:c,clientId:y,getStyleValue:m,onStyleChange:d},J=v?.["generateblocks-pro/navigation/htmlAttributes"]?.["data-gb-mobile-menu-type"]||"";return(0,o.useEffect)((()=>{var e,t;if(!Z?.current)return;const n=null!==(e=Z.current.closest(".gb-navigation"))&&void 0!==e?e:"",l=null!==(t=n?.querySelector(".gb-menu-toggle"))&&void 0!==t?t:"",o=document.querySelector('iframe[name="editor-canvas"]'),r=()=>{if(l){const e=window.getComputedStyle(l);I("none"!==e.display)}},s=()=>{r()};r();const a=new ResizeObserver((()=>{r(),!k&&B.style.height&&x(B.style.height)}));return B&&a.observe(B),o&&o.addEventListener("transitionend",s),()=>{B&&a.unobserve(B),o&&o.removeEventListener("transitionend",s)}}),[Z?.current,M,B,k]),(0,o.useEffect)((()=>{B&&(k?B.style.height="100%":T&&(B.style.height=T))}),[k,T]),(0,o.useEffect)((()=>{if(!f)return;const e=ne(_(y));e&&U(e)}),[f,y]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.InspectorControls,null,(0,e.createElement)(i.BlockStyles,{settingsTab:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(P.OpenPanel,{title:(0,r.__)("Navigation Tree","generateblocks-pro"),panelId:"block-list",...W},(0,e.createElement)(H,{blocks:[w],clientId:y,allowedBlocks:ee,showAllLabel:(0,r.__)("Show all blocks in navigation","generateblocks-pro")})),(0,e.createElement)(X,null),(0,e.createElement)(P.OpenPanel,{panelId:"design",title:(0,r.__)("Style Shortcuts","generateblocks-pro"),...W},(0,e.createElement)(g,{label:(0,r.__)("Mobile Background","generateblocks-pro"),colors:[{value:m("backgroundColor","&.gb-menu-container--mobile"),onChange:e=>d("backgroundColor",e,"","&.gb-menu-container--mobile")}]}),(0,e.createElement)(g,{label:(0,r.__)("Mobile Text","generateblocks-pro"),colors:[{value:m("color","&.gb-menu-container--mobile"),onChange:e=>d("color",e,"","&.gb-menu-container--mobile")}]}),(0,e.createElement)(g,{label:(0,r.__)("Mobile Menu Item Background","generateblocks-pro"),colors:[{value:m("backgroundColor","&.gb-menu-container--mobile .menu-item"),onChange:e=>d("backgroundColor",e,"","&.gb-menu-container--mobile .menu-item"),tooltip:(0,r.__)("Background","generateblocks-pro")},{value:m("backgroundColor","&.gb-menu-container--mobile .menu-item:is(:hover, :focus-within)"),onChange:e=>d("backgroundColor",e,"","&.gb-menu-container--mobile .menu-item:is(:hover, :focus-within)"),tooltip:(0,r.__)("Hover","generateblocks-pro")},{value:m("backgroundColor","&.gb-menu-container--mobile .menu-item:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus-within)"),onChange:e=>d("backgroundColor",e,"","&.gb-menu-container--mobile .menu-item:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus-within)"),tooltip:(0,r.__)("Current","generateblocks-pro")}]}),(0,e.createElement)(g,{label:(0,r.__)("Mobile Menu Item Text","generateblocks-pro"),colors:[{value:m("color","&.gb-menu-container--mobile .menu-item"),onChange:e=>d("color",e,"","&.gb-menu-container--mobile .menu-item"),tooltip:(0,r.__)("Text","generateblocks-pro")},{value:m("color","&.gb-menu-container--mobile .menu-item:is(:hover, :focus-within)"),onChange:e=>d("color",e,"","&.gb-menu-container--mobile .menu-item:is(:hover, :focus-within)"),tooltip:(0,r.__)("Hover","generateblocks-pro")},{value:m("color","&.gb-menu-container--mobile .menu-item:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus-within)"),onChange:e=>d("color",e,"","&.gb-menu-container--mobile .menu-item:is(.current-menu-item, .current-menu-item:hover, .current-menu-item:focus-within)"),tooltip:(0,r.__)("Current","generateblocks-pro")}]}),(0,e.createElement)(G,null)),(0,e.createElement)(P.OpenPanel,{panelId:"helper-classes",title:(0,r.__)("Helper Classes","generateblocks-pro"),...W},(0,e.createElement)(a.BaseControl,{className:"gb-menu-container-helper-class"},(0,e.createElement)("code",null,"gb-menu-show-on-toggled"),(0,e.createElement)("br",null),(0,e.createElement)("span",null,(0,r.__)("Add to blocks you want to only show in the toggled mobile menu.","generateblocks-pro"))),(0,e.createElement)(a.BaseControl,{className:"gb-menu-container-helper-class"},(0,e.createElement)("code",null,"gb-menu-hide-on-toggled"),(0,e.createElement)("br",null),(0,e.createElement)("span",null,(0,r.__)("Add to blocks you want to only show when the mobile menu is not toggled.","generateblocks-pro")))),(0,e.createElement)(P.OpenPanel,{panelId:"settings",...W})),stylesTab:(0,e.createElement)(R,{attributes:n,setAttributes:c,shortcuts:{selectorShortcuts:D,visibleShortcuts:O},onStyleChange:d})})),(0,e.createElement)(V,{...$},"full-overlay"===J&&!F&&(0,e.createElement)("button",{className:"gb-menu-toggle gb-menu-toggle--clone",onClick:()=>{var e;const t=k,n=null!==(e=Z?.current.closest(".gb-navigation"))&&void 0!==e?e:"";A(!t),n&&(t?(0,Y.IJ)(n):(0,Y.SL)(n))}}),z?.children))})),oe=JSON.parse('{"UU":"generateblocks-pro/menu-container"}');(0,t.registerBlockType)(oe.UU,{edit:le,save:function({attributes:t}){const{tagName:n,htmlAttributes:o={}}=t,r=N("gb-menu-container",t,!0),s=l.useBlockProps.save({className:r.join(" ").trim(),...o});return(0,e.createElement)(n,{...l.useInnerBlocksProps.save(s)})},icon:(0,e.createElement)((function(){return(0,e.createElement)("svg",{viewBox:"0 0 24 24",className:"gblocks-block-icon",style:{fillRule:"evenodd",clipRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:2}},(0,e.createElement)("path",{d:"M8.55,5.705C8.55,6.037 8.281,6.305 7.95,6.305L5.15,6.305C4.819,6.305 4.55,6.037 4.55,5.705C4.55,5.375 4.819,5.106 5.15,5.106L7.95,5.106C8.281,5.106 8.55,5.375 8.55,5.705Z",style:{fillOpacity:.3}}),(0,e.createElement)("path",{d:"M14.03,5.705C14.03,6.037 13.761,6.305 13.43,6.305L10.63,6.305C10.299,6.305 10.03,6.037 10.03,5.705C10.03,5.375 10.299,5.106 10.63,5.106L13.43,5.106C13.761,5.106 14.03,5.375 14.03,5.705Z",style:{fillOpacity:.3}}),(0,e.createElement)("path",{d:"M19.509,5.705C19.509,6.037 19.24,6.305 18.909,6.305L16.109,6.305C15.778,6.305 15.509,6.037 15.509,5.705C15.509,5.375 15.778,5.106 16.109,5.106L18.909,5.106C19.24,5.106 19.509,5.375 19.509,5.705Z",style:{fillOpacity:.3}}),(0,e.createElement)("path",{d:"M3.279,3.422L3.28,12.35L2.03,12.35L2.03,2.797C2.03,2.452 2.31,2.172 2.655,2.172L21.406,2.172C21.75,2.172 22.03,2.452 22.03,2.797L22.03,12.35L20.78,12.35L20.78,3.422L3.279,3.422ZM21.405,22.172L17.655,22.172L17.655,20.922L20.78,20.922L20.78,17.797L22.03,17.797L22.03,21.547C22.03,21.892 21.75,22.172 21.405,22.172ZM6.405,22.172L2.655,22.172C2.312,22.172 2.03,21.89 2.03,21.547L2.03,17.797L3.28,17.797L3.28,20.922L6.405,20.922L6.405,22.172Z"}),(0,e.createElement)("path",{d:"M18.235,14.454L5.775,14.454C5.435,14.454 5.155,14.734 5.155,15.074C5.155,15.414 5.435,15.694 5.775,15.694L18.235,15.694C18.575,15.694 18.855,15.414 18.855,15.074C18.855,14.734 18.585,14.454 18.235,14.454ZM18.245,17.484L5.775,17.484C5.435,17.484 5.155,17.764 5.155,18.104C5.155,18.444 5.435,18.724 5.775,18.724L18.235,18.724C18.575,18.724 18.855,18.444 18.855,18.104L18.855,18.094C18.855,17.759 18.58,17.484 18.245,17.484ZM5.145,12.044C5.145,12.384 5.425,12.664 5.765,12.664L18.225,12.664C18.565,12.664 18.845,12.384 18.845,12.044C18.845,11.704 18.565,11.424 18.225,11.424L5.758,11.424C5.422,11.424 5.145,11.7 5.145,12.037L5.145,12.044Z",style:{fillOpacity:.3,fillRule:"nonzero"}}))}),null)})})()})();