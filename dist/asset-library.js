(()=>{var e={4213:function(e,t,n){var a,s;void 0===(s="function"==typeof(a=function(){"use strict";function t(e,t,n){var a=new XMLHttpRequest;a.open("GET",e),a.responseType="blob",a.onload=function(){i(a.response,t,n)},a.onerror=function(){console.error("could not download file")},a.send()}function a(e){var t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(e){}return 200<=t.status&&299>=t.status}function s(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(n){var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}}var o="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof n.g&&n.g.global===n.g?n.g:void 0,r=o.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),i=o.saveAs||("object"!=typeof window||window!==o?function(){}:"download"in HTMLAnchorElement.prototype&&!r?function(e,n,r){var i=o.URL||o.webkitURL,l=document.createElement("a");n=n||e.name||"download",l.download=n,l.rel="noopener","string"==typeof e?(l.href=e,l.origin===location.origin?s(l):a(l.href)?t(e,n,r):s(l,l.target="_blank")):(l.href=i.createObjectURL(e),setTimeout((function(){i.revokeObjectURL(l.href)}),4e4),setTimeout((function(){s(l)}),0))}:"msSaveOrOpenBlob"in navigator?function(e,n,o){if(n=n||e.name||"download","string"!=typeof e)navigator.msSaveOrOpenBlob(function(e,t){return void 0===t?t={autoBom:!1}:"object"!=typeof t&&(console.warn("Deprecated: Expected third argument to be a object"),t={autoBom:!t}),t.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob(["\ufeff",e],{type:e.type}):e}(e,o),n);else if(a(e))t(e,n,o);else{var r=document.createElement("a");r.href=e,r.target="_blank",setTimeout((function(){s(r)}))}}:function(e,n,a,s){if((s=s||open("","_blank"))&&(s.document.title=s.document.body.innerText="downloading..."),"string"==typeof e)return t(e,n,a);var i="application/octet-stream"===e.type,l=/constructor/i.test(o.HTMLElement)||o.safari,c=/CriOS\/[\d]+/.test(navigator.userAgent);if((c||i&&l||r)&&"undefined"!=typeof FileReader){var m=new FileReader;m.onloadend=function(){var e=m.result;e=c?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),s?s.location.href=e:location=e,s=null},m.readAsDataURL(e)}else{var p=o.URL||o.webkitURL,u=p.createObjectURL(e);s?s.location=u:location.href=u,s=null,setTimeout((function(){p.revokeObjectURL(u)}),4e4)}});o.saveAs=i.saveAs=i,e.exports=i})?a.apply(t,[]):a)||(e.exports=s)},42838:function(e){e.exports=function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,n){return t=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},t(e,n)}function n(e,a,s){return n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct:function(e,n,a){var s=[null];s.push.apply(s,n);var o=new(Function.bind.apply(e,s));return a&&t(o,a.prototype),o},n.apply(null,arguments)}function a(e){return function(e){if(Array.isArray(e))return s(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var o=Object.hasOwnProperty,r=Object.setPrototypeOf,i=Object.isFrozen,l=Object.getPrototypeOf,c=Object.getOwnPropertyDescriptor,m=Object.freeze,p=Object.seal,u=Object.create,h="undefined"!=typeof Reflect&&Reflect,d=h.apply,g=h.construct;d||(d=function(e,t,n){return e.apply(t,n)}),m||(m=function(e){return e}),p||(p=function(e){return e}),g||(g=function(e,t){return n(e,a(t))});var f,v=L(Array.prototype.forEach),b=L(Array.prototype.pop),w=L(Array.prototype.push),y=L(String.prototype.toLowerCase),E=L(String.prototype.toString),k=L(String.prototype.match),S=L(String.prototype.replace),_=L(String.prototype.indexOf),N=L(String.prototype.trim),x=L(RegExp.prototype.test),T=(f=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return g(f,t)});function L(e){return function(t){for(var n=arguments.length,a=new Array(n>1?n-1:0),s=1;s<n;s++)a[s-1]=arguments[s];return d(e,t,a)}}function A(e,t,n){var a;n=null!==(a=n)&&void 0!==a?a:y,r&&r(e,null);for(var s=t.length;s--;){var o=t[s];if("string"==typeof o){var l=n(o);l!==o&&(i(t)||(t[s]=l),o=l)}e[o]=!0}return e}function C(e){var t,n=u(null);for(t in e)!0===d(o,e,[t])&&(n[t]=e[t]);return n}function M(e,t){for(;null!==e;){var n=c(e,t);if(n){if(n.get)return L(n.get);if("function"==typeof n.value)return L(n.value)}e=l(e)}return function(e){return console.warn("fallback value for",e),null}}var z=m(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),O=m(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),H=m(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),R=m(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),I=m(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),D=m(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),B=m(["#text"]),F=m(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),P=m(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),V=m(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),U=m(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),q=p(/\{\{[\w\W]*|[\w\W]*\}\}/gm),j=p(/<%[\w\W]*|[\w\W]*%>/gm),G=p(/\${[\w\W]*}/gm),W=p(/^data-[\-\w.\u00B7-\uFFFF]+$/),Z=p(/^aria-[\-\w]+$/),$=p(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),J=p(/^(?:\w+script|data):/i),Y=p(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),K=p(/^html$/i),X=p(/^[a-z][.\w]*(-[.\w]+)+$/i),Q=function(){return"undefined"==typeof window?null:window};return function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Q(),s=function(e){return t(e)};if(s.version="2.5.8",s.removed=[],!n||!n.document||9!==n.document.nodeType)return s.isSupported=!1,s;var o=n.document,r=n.document,i=n.DocumentFragment,l=n.HTMLTemplateElement,c=n.Node,p=n.Element,u=n.NodeFilter,h=n.NamedNodeMap,d=void 0===h?n.NamedNodeMap||n.MozNamedAttrMap:h,g=n.HTMLFormElement,f=n.DOMParser,L=n.trustedTypes,ee=p.prototype,te=M(ee,"cloneNode"),ne=M(ee,"nextSibling"),ae=M(ee,"childNodes"),se=M(ee,"parentNode");if("function"==typeof l){var oe=r.createElement("template");oe.content&&oe.content.ownerDocument&&(r=oe.content.ownerDocument)}var re=function(t,n){if("object"!==e(t)||"function"!=typeof t.createPolicy)return null;var a=null,s="data-tt-policy-suffix";n.currentScript&&n.currentScript.hasAttribute(s)&&(a=n.currentScript.getAttribute(s));var o="dompurify"+(a?"#"+a:"");try{return t.createPolicy(o,{createHTML:function(e){return e},createScriptURL:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+o+" could not be created."),null}}(L,o),ie=re?re.createHTML(""):"",le=r,ce=le.implementation,me=le.createNodeIterator,pe=le.createDocumentFragment,ue=le.getElementsByTagName,he=o.importNode,de={};try{de=C(r).documentMode?r.documentMode:{}}catch(e){}var ge={};s.isSupported="function"==typeof se&&ce&&void 0!==ce.createHTMLDocument&&9!==de;var fe,ve,be=q,we=j,ye=G,Ee=W,ke=Z,Se=J,_e=Y,Ne=X,xe=$,Te=null,Le=A({},[].concat(a(z),a(O),a(H),a(I),a(B))),Ae=null,Ce=A({},[].concat(a(F),a(P),a(V),a(U))),Me=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),ze=null,Oe=null,He=!0,Re=!0,Ie=!1,De=!0,Be=!1,Fe=!0,Pe=!1,Ve=!1,Ue=!1,qe=!1,je=!1,Ge=!1,We=!0,Ze=!1,$e=!0,Je=!1,Ye={},Ke=null,Xe=A({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Qe=null,et=A({},["audio","video","img","source","image","track"]),tt=null,nt=A({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),at="http://www.w3.org/1998/Math/MathML",st="http://www.w3.org/2000/svg",ot="http://www.w3.org/1999/xhtml",rt=ot,it=!1,lt=null,ct=A({},[at,st,ot],E),mt=["application/xhtml+xml","text/html"],pt=null,ut=r.createElement("form"),ht=function(e){return e instanceof RegExp||e instanceof Function},dt=function(t){pt&&pt===t||(t&&"object"===e(t)||(t={}),t=C(t),fe=fe=-1===mt.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE,ve="application/xhtml+xml"===fe?E:y,Te="ALLOWED_TAGS"in t?A({},t.ALLOWED_TAGS,ve):Le,Ae="ALLOWED_ATTR"in t?A({},t.ALLOWED_ATTR,ve):Ce,lt="ALLOWED_NAMESPACES"in t?A({},t.ALLOWED_NAMESPACES,E):ct,tt="ADD_URI_SAFE_ATTR"in t?A(C(nt),t.ADD_URI_SAFE_ATTR,ve):nt,Qe="ADD_DATA_URI_TAGS"in t?A(C(et),t.ADD_DATA_URI_TAGS,ve):et,Ke="FORBID_CONTENTS"in t?A({},t.FORBID_CONTENTS,ve):Xe,ze="FORBID_TAGS"in t?A({},t.FORBID_TAGS,ve):{},Oe="FORBID_ATTR"in t?A({},t.FORBID_ATTR,ve):{},Ye="USE_PROFILES"in t&&t.USE_PROFILES,He=!1!==t.ALLOW_ARIA_ATTR,Re=!1!==t.ALLOW_DATA_ATTR,Ie=t.ALLOW_UNKNOWN_PROTOCOLS||!1,De=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,Be=t.SAFE_FOR_TEMPLATES||!1,Fe=!1!==t.SAFE_FOR_XML,Pe=t.WHOLE_DOCUMENT||!1,qe=t.RETURN_DOM||!1,je=t.RETURN_DOM_FRAGMENT||!1,Ge=t.RETURN_TRUSTED_TYPE||!1,Ue=t.FORCE_BODY||!1,We=!1!==t.SANITIZE_DOM,Ze=t.SANITIZE_NAMED_PROPS||!1,$e=!1!==t.KEEP_CONTENT,Je=t.IN_PLACE||!1,xe=t.ALLOWED_URI_REGEXP||xe,rt=t.NAMESPACE||ot,Me=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&ht(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Me.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&ht(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Me.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Me.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Be&&(Re=!1),je&&(qe=!0),Ye&&(Te=A({},a(B)),Ae=[],!0===Ye.html&&(A(Te,z),A(Ae,F)),!0===Ye.svg&&(A(Te,O),A(Ae,P),A(Ae,U)),!0===Ye.svgFilters&&(A(Te,H),A(Ae,P),A(Ae,U)),!0===Ye.mathMl&&(A(Te,I),A(Ae,V),A(Ae,U))),t.ADD_TAGS&&(Te===Le&&(Te=C(Te)),A(Te,t.ADD_TAGS,ve)),t.ADD_ATTR&&(Ae===Ce&&(Ae=C(Ae)),A(Ae,t.ADD_ATTR,ve)),t.ADD_URI_SAFE_ATTR&&A(tt,t.ADD_URI_SAFE_ATTR,ve),t.FORBID_CONTENTS&&(Ke===Xe&&(Ke=C(Ke)),A(Ke,t.FORBID_CONTENTS,ve)),$e&&(Te["#text"]=!0),Pe&&A(Te,["html","head","body"]),Te.table&&(A(Te,["tbody"]),delete ze.tbody),m&&m(t),pt=t)},gt=A({},["mi","mo","mn","ms","mtext"]),ft=A({},["annotation-xml"]),vt=A({},["title","style","font","a","script"]),bt=A({},O);A(bt,H),A(bt,R);var wt=A({},I);A(wt,D);var yt=function(e){w(s.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=ie}catch(t){e.remove()}}},Et=function(e,t){try{w(s.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){w(s.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!Ae[e])if(qe||je)try{yt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},kt=function(e){var t,n;if(Ue)e="<remove></remove>"+e;else{var a=k(e,/^[\r\n\t ]+/);n=a&&a[0]}"application/xhtml+xml"===fe&&rt===ot&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var s=re?re.createHTML(e):e;if(rt===ot)try{t=(new f).parseFromString(s,fe)}catch(e){}if(!t||!t.documentElement){t=ce.createDocument(rt,"template",null);try{t.documentElement.innerHTML=it?ie:s}catch(e){}}var o=t.body||t.documentElement;return e&&n&&o.insertBefore(r.createTextNode(n),o.childNodes[0]||null),rt===ot?ue.call(t,Pe?"html":"body")[0]:Pe?t.documentElement:o},St=function(e){return me.call(e.ownerDocument||e,e,u.SHOW_ELEMENT|u.SHOW_COMMENT|u.SHOW_TEXT|u.SHOW_PROCESSING_INSTRUCTION|u.SHOW_CDATA_SECTION,null,!1)},_t=function(e){return e instanceof g&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof d)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Nt=function(t){return"object"===e(c)?t instanceof c:t&&"object"===e(t)&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName},xt=function(e,t,n){ge[e]&&v(ge[e],(function(e){e.call(s,t,n,pt)}))},Tt=function(e){var t;if(xt("beforeSanitizeElements",e,null),_t(e))return yt(e),!0;if(x(/[\u0080-\uFFFF]/,e.nodeName))return yt(e),!0;var n=ve(e.nodeName);if(xt("uponSanitizeElement",e,{tagName:n,allowedTags:Te}),e.hasChildNodes()&&!Nt(e.firstElementChild)&&(!Nt(e.content)||!Nt(e.content.firstElementChild))&&x(/<[/\w]/g,e.innerHTML)&&x(/<[/\w]/g,e.textContent))return yt(e),!0;if("select"===n&&x(/<template/i,e.innerHTML))return yt(e),!0;if(7===e.nodeType)return yt(e),!0;if(Fe&&8===e.nodeType&&x(/<[/\w]/g,e.data))return yt(e),!0;if(!Te[n]||ze[n]){if(!ze[n]&&At(n)){if(Me.tagNameCheck instanceof RegExp&&x(Me.tagNameCheck,n))return!1;if(Me.tagNameCheck instanceof Function&&Me.tagNameCheck(n))return!1}if($e&&!Ke[n]){var a=se(e)||e.parentNode,o=ae(e)||e.childNodes;if(o&&a)for(var r=o.length-1;r>=0;--r){var i=te(o[r],!0);i.__removalCount=(e.__removalCount||0)+1,a.insertBefore(i,ne(e))}}return yt(e),!0}return e instanceof p&&!function(e){var t=se(e);t&&t.tagName||(t={namespaceURI:rt,tagName:"template"});var n=y(e.tagName),a=y(t.tagName);return!!lt[e.namespaceURI]&&(e.namespaceURI===st?t.namespaceURI===ot?"svg"===n:t.namespaceURI===at?"svg"===n&&("annotation-xml"===a||gt[a]):Boolean(bt[n]):e.namespaceURI===at?t.namespaceURI===ot?"math"===n:t.namespaceURI===st?"math"===n&&ft[a]:Boolean(wt[n]):e.namespaceURI===ot?!(t.namespaceURI===st&&!ft[a])&&!(t.namespaceURI===at&&!gt[a])&&!wt[n]&&(vt[n]||!bt[n]):!("application/xhtml+xml"!==fe||!lt[e.namespaceURI]))}(e)?(yt(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!x(/<\/no(script|embed|frames)/i,e.innerHTML)?(Be&&3===e.nodeType&&(t=e.textContent,t=S(t,be," "),t=S(t,we," "),t=S(t,ye," "),e.textContent!==t&&(w(s.removed,{element:e.cloneNode()}),e.textContent=t)),xt("afterSanitizeElements",e,null),!1):(yt(e),!0)},Lt=function(e,t,n){if(We&&("id"===t||"name"===t)&&(n in r||n in ut))return!1;if(Re&&!Oe[t]&&x(Ee,t));else if(He&&x(ke,t));else if(!Ae[t]||Oe[t]){if(!(At(e)&&(Me.tagNameCheck instanceof RegExp&&x(Me.tagNameCheck,e)||Me.tagNameCheck instanceof Function&&Me.tagNameCheck(e))&&(Me.attributeNameCheck instanceof RegExp&&x(Me.attributeNameCheck,t)||Me.attributeNameCheck instanceof Function&&Me.attributeNameCheck(t))||"is"===t&&Me.allowCustomizedBuiltInElements&&(Me.tagNameCheck instanceof RegExp&&x(Me.tagNameCheck,n)||Me.tagNameCheck instanceof Function&&Me.tagNameCheck(n))))return!1}else if(tt[t]);else if(x(xe,S(n,_e,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==_(n,"data:")||!Qe[e])if(Ie&&!x(Se,S(n,_e,"")));else if(n)return!1;return!0},At=function(e){return"annotation-xml"!==e&&k(e,Ne)},Ct=function(t){var n,a,o,r;xt("beforeSanitizeAttributes",t,null);var i=t.attributes;if(i&&!_t(t)){var l={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Ae};for(r=i.length;r--;){var c=n=i[r],m=c.name,p=c.namespaceURI;if(a="value"===m?n.value:N(n.value),o=ve(m),l.attrName=o,l.attrValue=a,l.keepAttr=!0,l.forceKeepAttr=void 0,xt("uponSanitizeAttribute",t,l),a=l.attrValue,!l.forceKeepAttr&&(Et(m,t),l.keepAttr))if(De||!x(/\/>/i,a)){Be&&(a=S(a,be," "),a=S(a,we," "),a=S(a,ye," "));var u=ve(t.nodeName);if(Lt(u,o,a))if(!Ze||"id"!==o&&"name"!==o||(Et(m,t),a="user-content-"+a),Fe&&x(/((--!?|])>)|<\/(style|title)/i,a))Et(m,t);else{if(re&&"object"===e(L)&&"function"==typeof L.getAttributeType)if(p);else switch(L.getAttributeType(u,o)){case"TrustedHTML":a=re.createHTML(a);break;case"TrustedScriptURL":a=re.createScriptURL(a)}try{p?t.setAttributeNS(p,m,a):t.setAttribute(m,a),_t(t)?yt(t):b(s.removed)}catch(e){}}}else Et(m,t)}xt("afterSanitizeAttributes",t,null)}},Mt=function e(t){var n,a=St(t);for(xt("beforeSanitizeShadowDOM",t,null);n=a.nextNode();)xt("uponSanitizeShadowNode",n,null),Tt(n),Ct(n),n.content instanceof i&&e(n.content);xt("afterSanitizeShadowDOM",t,null)};return s.sanitize=function(t){var a,r,l,m,p,u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if((it=!t)&&(t="\x3c!--\x3e"),"string"!=typeof t&&!Nt(t)){if("function"!=typeof t.toString)throw T("toString is not a function");if("string"!=typeof(t=t.toString()))throw T("dirty is not a string, aborting")}if(!s.isSupported){if("object"===e(n.toStaticHTML)||"function"==typeof n.toStaticHTML){if("string"==typeof t)return n.toStaticHTML(t);if(Nt(t))return n.toStaticHTML(t.outerHTML)}return t}if(Ve||dt(u),s.removed=[],"string"==typeof t&&(Je=!1),Je){if(t.nodeName){var h=ve(t.nodeName);if(!Te[h]||ze[h])throw T("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof c)1===(r=(a=kt("\x3c!----\x3e")).ownerDocument.importNode(t,!0)).nodeType&&"BODY"===r.nodeName||"HTML"===r.nodeName?a=r:a.appendChild(r);else{if(!qe&&!Be&&!Pe&&-1===t.indexOf("<"))return re&&Ge?re.createHTML(t):t;if(!(a=kt(t)))return qe?null:Ge?ie:""}a&&Ue&&yt(a.firstChild);for(var d=St(Je?t:a);l=d.nextNode();)3===l.nodeType&&l===m||(Tt(l),Ct(l),l.content instanceof i&&Mt(l.content),m=l);if(m=null,Je)return t;if(qe){if(je)for(p=pe.call(a.ownerDocument);a.firstChild;)p.appendChild(a.firstChild);else p=a;return(Ae.shadowroot||Ae.shadowrootmod)&&(p=he.call(o,p,!0)),p}var g=Pe?a.outerHTML:a.innerHTML;return Pe&&Te["!doctype"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&x(K,a.ownerDocument.doctype.name)&&(g="<!DOCTYPE "+a.ownerDocument.doctype.name+">\n"+g),Be&&(g=S(g,be," "),g=S(g,we," "),g=S(g,ye," ")),re&&Ge?re.createHTML(g):g},s.setConfig=function(e){dt(e),Ve=!0},s.clearConfig=function(){pt=null,Ve=!1},s.isValidAttribute=function(e,t,n){pt||dt({});var a=ve(e),s=ve(t);return Lt(a,s,n)},s.addHook=function(e,t){"function"==typeof t&&(ge[e]=ge[e]||[],w(ge[e],t))},s.removeHook=function(e){if(ge[e])return b(ge[e])},s.removeHooks=function(e){ge[e]&&(ge[e]=[])},s.removeAllHooks=function(){ge={}},s}()}()}},t={};function n(a){var s=t[a];if(void 0!==s)return s.exports;var o=t[a]={exports:{}};return e[a].call(o.exports,o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.React;var t=n(42838),a=n.n(t);function s(e){return a().sanitize(e,{USE_PROFILES:{svg:!0,svgFilters:!0}})}const o=wp.element.createElement;function r(t){return"effects"===t?o("svg",{width:20,height:20,viewBox:"0 0 113 113",fillRule:"evenodd"},o("path",{d:"M106.283 6.217c8.289 8.29 8.289 91.776 0 100.066-8.29 8.289-91.776 8.289-100.066 0-8.289-8.29-8.289-91.776 0-100.066 8.29-8.289 91.776-8.289 100.066 0zM96.276 16.224c6.632 6.632 6.632 73.42 0 80.052-6.632 6.632-73.42 6.632-80.052 0-6.632-6.632-6.632-73.42 0-80.052 6.632-6.632 73.42-6.632 80.052 0z"}),o("path",{d:"M48.418 58.413H33.304c-.71 0-1.019-.577-.687-1.288l16.637-35.68c.332-.71 1.178-1.287 1.889-1.287h22.962c.711 0 1.02.577.687 1.288l-10.934 23.45h15.74c.796 0 1.14.647.77 1.443L37.959 91.72c-2.42 2.448-5.343.578-3.83-2.666l14.289-30.642z"})):"colors"===t?o("svg",{width:20,height:20,viewBox:"0 0 113 113",fillRule:"evenodd"},o("path",{d:"M106.283,6.217c8.289,8.29 8.289,91.776 0,100.066c-8.29,8.289 -91.776,8.289 -100.066,0c-8.289,-8.29 -8.289,-91.776 0,-100.066c8.29,-8.289 91.776,-8.289 100.066,0Zm-50.033,12.818c-20.551,0 -37.215,16.664 -37.215,37.215c0,20.551 16.664,37.215 37.215,37.215c3.432,0 6.202,-2.77 6.202,-6.203c0,-1.612 -0.62,-3.059 -1.612,-4.176c-0.951,-1.075 -1.571,-2.522 -1.571,-4.094c0,-3.432 2.77,-6.202 6.202,-6.202l7.319,0c11.413,0 20.675,-9.262 20.675,-20.675c0,-18.277 -16.664,-33.08 -37.215,-33.08Zm-22.742,37.215c-3.433,0 -6.203,-2.77 -6.203,-6.202c0,-3.433 2.77,-6.203 6.203,-6.203c3.432,0 6.202,2.77 6.202,6.203c0,3.432 -2.77,6.202 -6.202,6.202Zm45.484,0c-3.432,0 -6.202,-2.77 -6.202,-6.202c0,-3.433 2.77,-6.203 6.202,-6.203c3.433,0 6.203,2.77 6.203,6.203c0,3.432 -2.77,6.202 -6.203,6.202Zm-33.079,-16.54c-3.433,0 -6.203,-2.77 -6.203,-6.202c0,-3.433 2.77,-6.203 6.203,-6.203c3.432,0 6.202,2.77 6.202,6.203c0,3.432 -2.77,6.202 -6.202,6.202Zm20.674,0c-3.432,0 -6.202,-2.77 -6.202,-6.202c0,-3.433 2.77,-6.203 6.202,-6.203c3.433,0 6.203,2.77 6.203,6.203c0,3.432 -2.77,6.202 -6.203,6.202Z"})):"gradient"===t?o("svg",{width:24,height:24,viewBox:"0 0 24 24",fillRule:"evenodd"},o("path",{d:"M17.66 8L12 2.35L6.34 8A8.02 8.02 0 0 0 4 13.64c0 2 .78 4.11 2.34 5.67a7.99 7.99 0 0 0 11.32 0c1.56-1.56 2.34-3.67 2.34-5.67S19.22 9.56 17.66 8zM6 14c.01-2 .62-3.27 1.76-4.4L12 5.27l4.24 4.38C17.38 10.77 17.99 12 18 14H6z"})):"copy"===t?(0,e.createElement)("svg",{strokeWidth:"1.2",fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)("path",{d:"M19.4 20H9.6a.6.6 0 0 1-.6-.6V9.6a.6.6 0 0 1 .6-.6h9.8a.6.6 0 0 1 .6.6v9.8a.6.6 0 0 1-.6.6Z",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),(0,e.createElement)("path",{d:"M15 9V4.6a.6.6 0 0 0-.6-.6H4.6a.6.6 0 0 0-.6.6v9.8a.6.6 0 0 0 .6.6H9",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"})):"link"===t?(0,e.createElement)("svg",{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",role:"img","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M15.6 7.2H14v1.5h1.6c2 0 3.7 1.7 3.7 3.7s-1.7 3.7-3.7 3.7H14v1.5h1.6c2.8 0 5.2-2.3 5.2-5.2 0-2.9-2.3-5.2-5.2-5.2zM4.7 12.4c0-2 1.7-3.7 3.7-3.7H10V7.2H8.4c-2.9 0-5.2 2.3-5.2 5.2 0 2.9 2.3 5.2 5.2 5.2H10v-1.5H8.4c-2 0-3.7-1.7-3.7-3.7zm4.6.9h5.3v-1.5H9.3v1.5z"})):"globe"===t?(0,e.createElement)("svg",{width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024",role:"img","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M512 1024q-104 0-199-40.5t-163.5-109T40.5 711 0 512t40.5-199 109-163.5T313 40.5 512 0t199 40.5 163.5 109 109 163.5 40.5 199-40.5 199-109 163.5-163.5 109-199 40.5zM293 293l-48-42q-11 0-26 2.5t-27 2.5q-1-1-18-37Q64 346 64 512q0 3 .5 8t.5 7q6 6 29.5 22.5T128 576h64q3-2 5.5-3t5.5-2q-10-11-29.5-32.5T144 507q4-23 11-69.5t10-69.5q86-36 128-53v-22zm201-163q-14-6-26-11-3-8-4-12-6 19-19 57 4 1 11.5 2t11.5 2h26v-38zm-4 471q-5 5-7 8-12 21-34 64t-33 64q14 21 42.5 64t42.5 64q130 8 197 12 2 25 16 34 91-46 154-127.5T951 601q-19-4-41.5-11t-32.5-9.5-39.5-5T776 579q-12 1-15.5-15.5t-3.5-34-4-18.5q-22-4-89 7.5t-89 7.5q-13 12-85 75zm59-501q-3 20-10.5 60T527 221q5-1 16.5-2.5T560 217q-3-2-7-4 15-5 22-8-17-70-26-105zm116-9q-2 11-2 31t-10 53q1 2 4 4 20-2 67-7 0-21 21-42-38-23-80-39zm125 70q-2 4-7 11 19 3 25 5-12-11-18-16zm27 24q-3 6-9.5 18t-9.5 18q-29 1-78 3l-4-34q-2 1-7 2.5t-8 1.5v49q-21 2-64.5 6t-64.5 6q-7 10-15 22 27 58 41 87-20 5-82 22v34q0 2 1.5 6t2.5 6q17 8 53 24t54 25l22-27q-1-10-5-31.5t-6-32.5q3-2 9.5-5.5t9.5-5.5q27-8 41-11 13 21 36.5 60t29.5 49q9-8 25-24.5t24-24.5q-54-38-71-49 1-8 4-23h37q56 48 115 98 1 0 2-1.5t2-1.5q-4-8-26-49 0-1 3-4l4-4h41q1-1 17-9-34-116-124-200z"})):"info"===t?(0,e.createElement)("svg",{width:"1em",height:"1em",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024","aria-hidden":"true"},(0,e.createElement)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}),(0,e.createElement)("path",{d:"M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"})):"wrench"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},(0,e.createElement)("rect",{x:"0",fill:"none",width:"20",height:"20"}),(0,e.createElement)("g",null,(0,e.createElement)("path",{d:"M16.68 9.77c-1.34 1.34-3.3 1.67-4.95.99l-5.41 6.52c-.99.99-2.59.99-3.58 0s-.99-2.59 0-3.57l6.52-5.42c-.68-1.65-.35-3.61.99-4.95 1.28-1.28 3.12-1.62 4.72-1.06l-2.89 2.89 2.82 2.82 2.86-2.87c.53 1.58.18 3.39-1.08 4.65zM3.81 16.21c.4.39 1.04.39 1.43 0 .4-.4.4-1.04 0-1.43-.39-.4-1.03-.4-1.43 0-.39.39-.39 1.03 0 1.43z"}))):"x"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},(0,e.createElement)("rect",{x:"0",fill:"none",width:"20",height:"20"}),(0,e.createElement)("g",null,(0,e.createElement)("path",{d:"M14.95 6.46L11.41 10l3.54 3.54-1.41 1.41L10 11.42l-3.53 3.53-1.42-1.42L8.58 10 5.05 6.47l1.42-1.42L10 8.58l3.54-3.53z"}))):"trash"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",style:{fill:"none"},stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,e.createElement)("path",{d:"M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2M10 11v6M14 11v6"})):"question"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",style:{fill:"none"},stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,e.createElement)("circle",{cx:"12",cy:"12",r:"10"}),(0,e.createElement)("path",{d:"M9.09 9a3 3 0 015.83 1c0 2-3 3-3 3M12 17h.01"})):"lock"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",style:{fill:"none"},stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24"},(0,e.createElement)("rect",{x:"3",y:"11",width:"18",height:"11",rx:"2",ry:"2"}),(0,e.createElement)("path",{d:"M7 11V7a5 5 0 0110 0v4"})):"template-library"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"gblocks-block-icon"},(0,e.createElement)("path",{style:{fill:"none"},d:"M0 0h24v24H0z"}),(0,e.createElement)("path",{d:"M21.375 12.625H2.625A.625.625 0 0 1 2 12V2.625C2 2.28 2.28 2 2.625 2h18.75c.345 0 .625.28.625.625V12c0 .345-.28.625-.625.625zM3.25 11.375h17.5V3.25H3.25v8.125zM10.283 22H2.625A.625.625 0 0 1 2 21.375V15.31c0-.345.28-.625.625-.625h7.658c.345 0 .625.28.625.625v6.065c0 .345-.28.625-.625.625zM3.25 20.75h6.407v-4.815H3.25v4.815zm18.125-1.783h-7.892a.625.625 0 0 1 0-1.25h7.892a.625.625 0 0 1 0 1.25zm0 3.033h-7.892a.625.625 0 0 1 0-1.25h7.892a.625.625 0 0 1 0 1.25zm0-6.065h-7.892a.625.625 0 0 1 0-1.25h7.892a.625.625 0 0 1 0 1.25z"}),(0,e.createElement)("path",{d:"M12 10.106a.625.625 0 0 1-.625-.625V5.144a.625.625 0 0 1 1.25 0v4.337c0 .346-.28.625-.625.625z"}),(0,e.createElement)("path",{d:"M14.169 7.938H9.831a.625.625 0 0 1 0-1.25h4.337a.625.625 0 0 1 .001 1.25z"})):"accordion"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"gblocks-block-icon"},(0,e.createElement)("path",{style:{fill:"none"},d:"M0 0h24v24H0z"}),(0,e.createElement)("path",{d:"M21.375 9.067H2.625A.625.625 0 0 1 2 8.441V2.625C2 2.28 2.28 2 2.625 2h18.751c.344 0 .624.28.624.625v5.816c0 .346-.28.626-.625.626zM3.249 7.816H20.75V3.25H3.249v4.566zm18.126 11.032H2.625a.625.625 0 0 1 0-1.251h18.751a.625.625 0 1 1-.001 1.251zm0 3.152H2.625a.625.625 0 0 1 0-1.25h18.751a.625.625 0 1 1-.001 1.25zm0-6.305H2.625a.625.625 0 0 1 0-1.25h18.751a.625.625 0 1 1-.001 1.25zm0-3.152H2.625a.625.625 0 0 1 0-1.25h18.751a.625.625 0 1 1-.001 1.25z"}),(0,e.createElement)("path",{d:"M17.831 6.874a.517.517 0 0 1-.368-.153L15.9 5.159l.737-.737 1.194 1.194 1.194-1.194.737.737-1.563 1.563a.522.522 0 0 1-.368.152z"})):"tabs"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"gblocks-block-icon"},(0,e.createElement)("path",{style:{fill:"none"},d:"M0 0h24v24H0V0z"}),(0,e.createElement)("path",{d:"M21.38 2H2.62c-.34 0-.62.28-.62.62v18.75c0 .35.28.63.62.63h18.75c.34 0 .62-.28.62-.62V2.62a.608.608 0 0 0-.61-.62zM9.5 3.25h5v2.51h-5V3.25zm11.25 17.5H3.25V3.25h5v3.14c0 .34.28.62.62.62h11.88v13.74zm0-14.99h-5V3.25h5v2.51z"}),(0,e.createElement)("path",{d:"M18.23 13.26H5.77c-.34 0-.62.28-.62.62s.28.62.62.62h12.46c.34 0 .62-.28.62-.62s-.27-.62-.62-.62zm0 3.03H5.77c-.34 0-.62.28-.62.62 0 .34.28.62.62.62h12.46c.34 0 .62-.28.62-.62a.61.61 0 0 0-.62-.62zM5.14 10.85c0 .34.28.62.62.62h12.46c.34 0 .62-.28.62-.62s-.28-.62-.62-.62H5.77a.613.613 0 0 0-.63.62z"})):"horizontal-tabs"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"50",height:"50",viewBox:"0 0 100 100"},(0,e.createElement)("path",{d:"M83.91 48.67H16.09a1.92 1.92 0 0 0 0 3.84h67.82a1.92 1.92 0 0 0 0-3.84zm0 15.17H16.09a1.92 1.92 0 0 0 0 3.84h67.82a1.92 1.92 0 0 0 0-3.84zm0 15.17H16.09a1.92 1.92 0 0 0 0 3.84h67.82a1.92 1.92 0 0 0 0-3.84zm0-45.51H16.09a1.92 1.92 0 0 0 0 3.84h67.82a1.92 1.92 0 0 0 0-3.84z"}),(0,e.createElement)("path",{d:"M98.08 0H1.92C.86 0 0 .86 0 1.92v96.15C0 99.14.86 100 1.92 100h96.15c1.06 0 1.92-.86 1.92-1.92V1.92C100 .86 99.14 0 98.08 0zm-1.93 15.59-28.2.09V3.85h28.2v11.74zM35.9 3.85h28.21v11.84l-28.21.08V3.85zM3.85 96.15V3.85h28.21V17.7c0 .51.2 1 .57 1.36.36.36.85.56 1.36.56H34l62.17-.19v76.72H3.85z"})):"vertical-tabs"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"50",height:"50",viewBox:"0 0 100 100"},(0,e.createElement)("path",{d:"M33.41 52.51h50.91a1.92 1.92 0 0 0 0-3.84H33.41a1.92 1.92 0 0 0 0 3.84zm0 15.17h50.91a1.92 1.92 0 0 0 0-3.84H33.41a1.92 1.92 0 0 0 0 3.84zm0 15.18h50.91a1.92 1.92 0 0 0 0-3.84H33.41a1.92 1.92 0 0 0 0 3.84zm0-45.52h50.91a1.92 1.92 0 0 0 0-3.84H33.41a1.92 1.92 0 0 0 0 3.84zm0-15.17h50.91a1.92 1.92 0 0 0 0-3.84H33.41a1.92 1.92 0 0 0 0 3.84z"}),(0,e.createElement)("path",{d:"M98.08 0H1.92C.86 0 0 .86 0 1.92v96.15C0 99.14.86 100 1.92 100h96.15c1.06 0 1.92-.86 1.92-1.92V1.92C100 .86 99.14 0 98.08 0zM3.85 52.12h12.52l-.05 19.97H3.85V52.12zm12.53-3.84H3.85V27.99h12.58l-.05 20.29zM3.85 75.94H16.3l-.05 20.21H3.85V75.94zm92.3 20.21H20.09l.19-70.08a1.932 1.932 0 0 0-1.92-1.93H3.85V3.85h92.31v92.3z"})):"button-tabs"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"50",height:"50",viewBox:"0 0 100 100"},(0,e.createElement)("path",{d:"M16.09 55.55h67.82a1.92 1.92 0 0 0 0-3.84H16.09a1.92 1.92 0 0 0 0 3.84zm0 14.22h67.82a1.92 1.92 0 0 0 0-3.84H16.09a1.92 1.92 0 0 0 0 3.84zm0 14.23h67.82a1.92 1.92 0 0 0 0-3.84H16.09a1.92 1.92 0 0 0 0 3.84zm0-42.67h67.82a1.92 1.92 0 0 0 0-3.84H16.09a1.92 1.92 0 0 0 0 3.84z"}),(0,e.createElement)("path",{d:"M98.08 21.47H1.92C.86 21.47 0 22.33 0 23.4v74.68C0 99.14.86 100 1.92 100h96.15c1.06 0 1.92-.86 1.92-1.92V23.4c.01-1.07-.85-1.93-1.91-1.93zm-1.93 74.68H3.85V25.32h92.31v70.83zM1.92 14.9l25 .05c.48 0 .95-.19 1.29-.53.34-.34.54-.81.54-1.29V1.83C28.75.82 27.93 0 26.92 0h-25C.91 0 .1.82.1 1.83v11.24c0 1.01.81 1.82 1.82 1.83zM3.75 3.66H25.1v7.63l-21.35-.05V3.66zM37.5 14.9l25 .05c.48 0 .95-.19 1.29-.53.34-.34.54-.81.54-1.29V1.83C64.33.82 63.51 0 62.5 0h-25c-1.01 0-1.83.82-1.83 1.83v11.24c0 1.01.82 1.82 1.83 1.83zm1.83-11.24h21.35v7.63l-21.35-.05V3.66zM73.07 14.9l25 .05c.48 0 .95-.19 1.29-.53.34-.34.54-.81.54-1.29V1.83C99.9.82 99.09 0 98.08 0h-25c-1.01 0-1.83.82-1.83 1.83v11.24c0 1.01.82 1.82 1.82 1.83zM74.9 3.66h21.35v7.63l-21.35-.05V3.66z"})):"vertical-dots"===t?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256"},(0,e.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,e.createElement)("circle",{cx:"128",cy:"60",r:"16"}),(0,e.createElement)("circle",{cx:"128",cy:"128",r:"16"}),(0,e.createElement)("circle",{cx:"128",cy:"196",r:"16"})):"generateblocks"===t?(0,e.createElement)("svg",{viewBox:"0 0 50 60.12",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M6.686 31.622V18.918a.077.077 0 0 1 .05-.072l6.5-2.313 6.5-2.313 9.682-3.445L39.1 7.33a.067.067 0 0 0 .036-.028.074.074 0 0 0 .014-.044V.076a.077.077 0 0 0-.032-.062.076.076 0 0 0-.069-.009l-13 4.625-13 4.625-6.5 2.313-6.5 2.313a.067.067 0 0 0-.036.028.097.097 0 0 0-.013.046V52.067c0 .026.013.048.032.062s.044.018.069.009l3.267-1.163 3.267-1.163c.015-.005.028-.015.036-.028s.014-.028.014-.044V37.999l.001-6.377c-.001 0 0 0 0 0z"}),(0,e.createElement)("path",{d:"m23.949 29.976 13-4.625 13-4.625c.015-.005.028-.015.036-.028s.015-.028.015-.044V8.056a.077.077 0 0 0-.032-.062.076.076 0 0 0-.069-.009l-13 4.625-13 4.625-6.5 2.313-6.5 2.313a.067.067 0 0 0-.036.028.074.074 0 0 0-.014.044V60.045c0 .026.013.048.032.062a.076.076 0 0 0 .069.009l6.475-2.304 6.475-2.304 6.525-2.322 6.525-2.322 6.5-2.313 6.5-2.313c.015-.005.028-.015.036-.028s.014-.025.014-.041V27.193a.077.077 0 0 0-.032-.062.076.076 0 0 0-.069-.009l-6.45 2.295L37 31.711a.067.067 0 0 0-.036.028.074.074 0 0 0-.014.044v6.272a.077.077 0 0 1-.05.072l-6.45 2.295L24 42.715a.075.075 0 0 1-.101-.071V30.046c0-.016.005-.031.014-.044a.08.08 0 0 1 .036-.026z"})):void 0}var i=n(4213),l=n.n(i);const c=window.wp.i18n,m=window.wp.components,p=window.wp.element,u=window.wp.apiFetch;var h=n.n(u);async function d(e){try{const t=await fetch(e);return s(await t.text())}catch(e){return console.error("Error fetching SVG:",e),null}}class g extends p.Component{constructor(){super(...arguments),this.state={isAPILoaded:!1,isAPISaving:!1,icons:generateBlocksProSettings.icons,showImportField:!1}}componentDidMount(){this.setState({isAPILoaded:!0})}updateSettings(e){this.setState({isAPISaving:!0});const t=e.target.nextElementSibling;h()({path:"/generateblocks-pro/v1/icon-settings",method:"POST",data:{settings:this.state.icons}}).then((e=>{this.setState({isAPISaving:!1}),t.classList.add("gblocks-action-message--show"),t.textContent=e.response,e.success&&e.response?setTimeout((function(){t.classList.remove("gblocks-action-message--show")}),3e3):t.classList.add("gblocks-action-message--error")}))}render(){if(!this.state.isAPILoaded)return(0,e.createElement)(m.Placeholder,{className:"gblocks-settings-placeholder"},(0,e.createElement)(m.Spinner,null));let t="";return(0,e.createElement)(p.Fragment,null,(0,e.createElement)("div",{className:"generateblocks-settings-main"},(0,e.createElement)(m.PanelBody,{title:(0,c.__)("Icons","generateblocks-pro")},(0,e.createElement)("div",{className:"gblocks-dashboard-panel-row-wrapper"},(0,e.createElement)(m.PanelRow,null,(0,e.createElement)(m.BaseControl,{id:"gblocks-asset-library-control",className:"gblocks-asset-library-control"},(0,e.createElement)(m.Notice,{className:"gblocks-assets-safe-svg-notice",isDismissible:!1,status:"info"},(0,c.__)("Icons are static elements that are added to your content. Changes here will not affect your existing icons.","generateblocks-pro")),(0,e.createElement)(m.BaseControl,{className:"gb-icon-chooser gb-shape-chooser"},this.state.icons.map(((n,a)=>(0,e.createElement)(p.Fragment,{key:a},(0,e.createElement)(m.PanelBody,{title:this.state.icons[a].group?this.state.icons[a].group:(0,c.__)("Group","generateblocks-pro"),initialOpen:!1},(0,e.createElement)(m.TextControl,{className:"gblocks-group-name",type:"text",label:(0,c.__)("Group Name","generateblocks-pro"),value:this.state.icons[a].group,onChange:e=>{const t=[...this.state.icons];t[a]={...t[a],group:e},this.setState({icons:t})}}),(0,e.createElement)(m.Button,{className:"gblocks-delete-asset-group is-secondary is-small",onClick:()=>{if(window.confirm((0,c.__)("This will permanently delete all icons in this group.","generateblocks-pro"))){const e=[...this.state.icons];e.splice(a,1),this.setState({icons:e})}}},(0,c.__)("Delete Group","generateblocks-pro")),void 0!==this.state.icons[a].icons&&this.state.icons[a].icons.length>0&&(0,e.createElement)(m.Button,{className:"gblocks-export-asset-group is-secondary is-small",onClick:()=>{h()({path:"/generateblocks-pro/v1/export-asset-group",method:"POST",data:{assetType:"icons",groupName:this.state.icons[a].group,assets:this.state.icons[a].icons}}).then((e=>{const t="group-"+this.state.icons[a].group+".json",n=new Blob([JSON.stringify(e.response)],{type:"application/json",name:t});l()(n,t)}))}},(0,c.__)("Export Group","generateblocks-pro")),!!this.state.icons[a].group&&(0,e.createElement)(m.BaseControl,{className:"gblocks-asset-items"},this.state.icons[a].icons&&this.state.icons[a].icons.map(((n,o)=>(0,e.createElement)(p.Fragment,{key:o},(0,e.createElement)("div",{className:"gblocks-asset-item"},(0,e.createElement)("div",{className:"gblocks-asset-name-area"},(0,e.createElement)("div",{className:"gblocks-asset-name-upload"},(0,e.createElement)(m.TextControl,{type:"text",placeholder:(0,c.__)("Name","generateblocks-pro"),value:this.state.icons[a].icons[o].name||"",onChange:e=>{const t=[...this.state.icons];t[a].icons[o].name=e,this.setState({icons:t})}}),generateBlocksProSettings.hasSVGSupport&&(0,e.createElement)(p.Fragment,null,(0,e.createElement)(m.Tooltip,{text:(0,c.__)("Upload an SVG file","generateblocks-pro")},(0,e.createElement)(m.Button,{className:"gblocks-upload-svg is-secondary",onClick:e=>{const n=e.target.closest(".gblocks-asset-item"),s=[...this.state.icons],r=this.state.icons[a].icons[o].name;t=wp.media({title:(0,c.__)("Upload SVG","generateblocks-pro"),multiple:!1,library:{type:"image/svg+xml"},button:{text:(0,c.__)("Insert SVG","generateblocks-pro")}}),t.on("select",(async()=>{const e=t.state().get("selection").first().toJSON();!r&&e.name&&(s[a].icons[o].name=e.name),n.classList.add("gblocks-asset-loading"),n.querySelector(".gblocks-asset-spinner").classList.add("gblocks-asset-show-spinner");const i=await d(e.url);s[a].icons[o].icon=i,this.setState({icons:s}),n.classList.remove("gblocks-asset-loading"),n.querySelector(".gblocks-asset-spinner").classList.remove("gblocks-asset-show-spinner")})),t.open()}},(0,c.__)("Browse","generateblocks-pro"))),(0,e.createElement)("span",{className:"gblocks-asset-spinner"},(0,e.createElement)(m.Spinner,null)))),""!==this.state.icons[a].icons[o].icon&&(0,e.createElement)("span",{className:"gblocks-asset-preview",dangerouslySetInnerHTML:{__html:s(this.state.icons[a].icons[o].icon)}})),(0,e.createElement)(m.TextareaControl,{className:"gblocks-asset-textarea-control",label:(0,c.__)("SVG HTML","generateblocks-pro"),value:this.state.icons[a].icons[o].icon||"",onChange:e=>{const t=[...this.state.icons];t[a].icons[o].icon=s(e),this.setState({icons:t})}}),(0,e.createElement)(m.Tooltip,{text:(0,c.__)("Delete Icon","generateblocks-pro")},(0,e.createElement)(m.Button,{className:"gblocks-delete-asset",onClick:()=>{if(window.confirm((0,c.__)("This will permanently delete this icon.","generateblocks-pro"))){const e=[...this.state.icons];e[a].icons.splice(o,1),this.setState({icons:e})}},icon:r("x")})))))),(0,e.createElement)("div",{className:"gblocks-add-new-asset"},(0,e.createElement)(m.Button,{isSecondary:!0,onClick:()=>{const e=[...this.state.icons];e[a].icons.push({name:"",icon:""}),this.setState({icons:e})}},(0,c.__)("Add Icon","generateblocks-pro"))))))))))),(0,e.createElement)(m.PanelRow,{className:"gblocks-asset-library-group-actions"},(0,e.createElement)("div",{className:"gblocks-add-new-asset-group"},(0,e.createElement)(m.Button,{isSecondary:!0,onClick:()=>{const e=[...this.state.icons];e.push({group:"",icons:[]}),this.setState({icons:e})}},(0,c.__)("Add Group","generateblocks-pro"))),(0,e.createElement)("div",{className:"gblocks-import-asset-group"},!this.state.showImportField&&(0,e.createElement)(m.Button,{isSecondary:!0,onClick:()=>this.setState({showImportField:!0})},(0,c.__)("Import Group","generateblocks-pro")),this.state.showImportField&&(0,e.createElement)("input",{type:"file",accept:".json",onChange:e=>{const t=e.target.nextElementSibling,n=new FileReader;n.onloadend=()=>{let e=n.result;if(e=JSON.parse(e),e&&"icons"===e.type){const n=[...this.state.icons];n.push({group:e.group,icons:e.assets}),this.setState({icons:n}),t.classList.add("gblocks-action-message--show"),t.textContent=(0,c.__)("Group imported.","generateblocks-pro"),setTimeout((function(){t.classList.remove("gblocks-action-message--show")}),3e3)}e||(t.classList.add("gblocks-action-message--show"),t.classList.add("gblocks-action-message--error"),t.textContent=(0,c.__)("File not valid.","generateblocks-pro"),setTimeout((function(){t.classList.remove("gblocks-action-message--show"),t.classList.remove("gblocks-action-message--error")}),3e3)),"icons"!==e.type&&(t.classList.add("gblocks-action-message--show"),t.classList.add("gblocks-action-message--error"),t.textContent=(0,c.__)("Wrong asset type.","generateblocks-pro"),setTimeout((function(){t.classList.remove("gblocks-action-message--show"),t.classList.remove("gblocks-action-message--error")}),3e3))},n.readAsText(e.target.files[0]),e.target.value="",this.setState({showImportField:!1})}}),(0,e.createElement)("span",{className:"gblocks-action-message"}))),(0,e.createElement)(m.PanelRow,{className:"gblocks-asset-library-actions"},(0,e.createElement)("div",{className:"gblocks-action-button"},(0,e.createElement)(m.Button,{isPrimary:!0,disabled:this.state.isAPISaving,onClick:e=>this.updateSettings(e)},this.state.isAPISaving&&(0,e.createElement)(m.Spinner,null),!this.state.isAPISaving&&(0,c.__)("Save Icons","generateblocks-pro")),(0,e.createElement)("span",{className:"gblocks-action-message"})))))))}}window.addEventListener("DOMContentLoaded",(()=>{(0,p.render)((0,e.createElement)(g,null),document.getElementById("gblocks-icon-library"))}));class f extends p.Component{constructor(){super(...arguments),this.state={isAPILoaded:!1,isAPISaving:!1,shapes:generateBlocksProSettings.shapes,showImportField:!1}}componentDidMount(){this.setState({isAPILoaded:!0})}updateSettings(e){this.setState({isAPISaving:!0});const t=e.target.nextElementSibling;h()({path:"/generateblocks-pro/v1/shape-settings",method:"POST",data:{settings:this.state.shapes}}).then((e=>{this.setState({isAPISaving:!1}),t.classList.add("gblocks-action-message--show"),t.textContent=e.response,e.success&&e.response?setTimeout((function(){t.classList.remove("gblocks-action-message--show")}),3e3):t.classList.add("gblocks-action-message--error")}))}render(){if(!this.state.isAPILoaded)return(0,e.createElement)(m.Placeholder,{className:"gblocks-settings-placeholder"},(0,e.createElement)(m.Spinner,null));let t="";return(0,e.createElement)(p.Fragment,null,(0,e.createElement)("div",{className:"generateblocks-settings-main"},(0,e.createElement)(m.PanelBody,{title:(0,c.__)("Shapes")},(0,e.createElement)("div",{className:"gblocks-dashboard-panel-row-wrapper"},(0,e.createElement)(m.PanelRow,null,(0,e.createElement)(m.BaseControl,{id:"gblocks-asset-library-control",className:"gblocks-asset-library-control"},(0,e.createElement)(m.Notice,{className:"gblocks-assets-safe-svg-notice",isDismissible:!1,status:"info"},(0,c.__)("Shapes are dynamic elements that will update automatically on your website if altered/removed here.","generateblocks-pro")),(0,e.createElement)(m.BaseControl,{className:"gb-icon-chooser gb-shape-chooser"},this.state.shapes.map(((n,a)=>(0,e.createElement)(p.Fragment,{key:a},(0,e.createElement)(m.PanelBody,{title:this.state.shapes[a].group?this.state.shapes[a].group:(0,c.__)("Group","generateblocks-pro"),initialOpen:!1},(0,e.createElement)(m.TextControl,{className:"gblocks-group-name",type:"text",label:(0,c.__)("Group Name","generateblocks-pro"),value:this.state.shapes[a].group,onChange:e=>{const t=[...this.state.shapes];t[a]={...t[a],group:e},this.setState({shapes:t})}}),(0,e.createElement)(m.Button,{className:"gblocks-delete-asset-group is-secondary is-small",onClick:()=>{if(window.confirm((0,c.__)("This will permanently delete all shapes in this group and remove them from the front-end of your website.","generateblocks-pro"))){const e=[...this.state.shapes];e.splice(a,1),this.setState({shapes:e})}}},(0,c.__)("Delete Group","generateblocks-pro")),void 0!==this.state.shapes[a].shapes&&this.state.shapes[a].shapes.length>0&&(0,e.createElement)(m.Button,{className:"gblocks-export-asset-group is-secondary is-small",onClick:()=>{h()({path:"/generateblocks-pro/v1/export-asset-group",method:"POST",data:{assetType:"shapes",groupName:this.state.shapes[a].group,assets:this.state.shapes[a].shapes}}).then((e=>{const t="group-"+this.state.shapes[a].group+".json",n=new Blob([JSON.stringify(e.response)],{type:"application/json",name:t});l()(n,t)}))}},(0,c.__)("Export Group","generateblocks-pro")),!!this.state.shapes[a].group&&(0,e.createElement)(m.BaseControl,{className:"gblocks-asset-items"},this.state.shapes[a].shapes&&this.state.shapes[a].shapes.map(((n,o)=>(0,e.createElement)(p.Fragment,{key:o},(0,e.createElement)("div",{className:"gblocks-asset-item"},(0,e.createElement)("div",{className:"gblocks-asset-name-area"},(0,e.createElement)("div",{className:"gblocks-asset-name-upload"},(0,e.createElement)(m.TextControl,{type:"text",placeholder:(0,c.__)("Name","generateblocks-pro"),value:this.state.shapes[a].shapes[o].name||"",onChange:e=>{const t=[...this.state.shapes];t[a].shapes[o].name=e,this.setState({shapes:t})}}),generateBlocksProSettings.hasSVGSupport&&(0,e.createElement)(p.Fragment,null,(0,e.createElement)(m.Tooltip,{text:(0,c.__)("Upload an SVG file","generateblocks-pro")},(0,e.createElement)(m.Button,{className:"gblocks-upload-svg is-secondary",onClick:e=>{const n=e.target.closest(".gblocks-asset-item"),s=[...this.state.shapes],r=this.state.shapes[a].shapes[o].name;t=wp.media({title:(0,c.__)("Upload SVG","generateblocks-pro"),multiple:!1,library:{type:"image/svg+xml"},button:{text:(0,c.__)("Insert SVG","generateblocks-pro")}}),t.on("select",(async()=>{const e=t.state().get("selection").first().toJSON();!r&&e.name&&(s[a].shapes[o].name=e.name),n.classList.add("gblocks-asset-loading"),n.querySelector(".gblocks-asset-spinner").classList.add("gblocks-asset-show-spinner");let i=await d(e.url);i.toLowerCase().includes('preserveaspectratio="none"')||(i=i.replace("<svg",'<svg preserveAspectRatio="none"')),s[a].shapes[o].shape=i,this.setState({shapes:s}),n.classList.remove("gblocks-asset-loading"),n.querySelector(".gblocks-asset-spinner").classList.remove("gblocks-asset-show-spinner")})),t.open()}},(0,c.__)("Browse","generateblocks-pro"))),(0,e.createElement)("span",{className:"gblocks-asset-spinner"},(0,e.createElement)(m.Spinner,null)))),""!==this.state.shapes[a].shapes[o].shape&&(0,e.createElement)("span",{className:"gblocks-asset-preview",dangerouslySetInnerHTML:{__html:s(this.state.shapes[a].shapes[o].shape)}})),(0,e.createElement)(m.TextareaControl,{className:"gblocks-asset-textarea-control",label:(0,c.__)("SVG HTML","generateblocks-pro"),value:this.state.shapes[a].shapes[o].shape||"",onChange:e=>{const t=[...this.state.shapes];e.toLowerCase().includes('preserveaspectratio="none"')||(e=e.replace("<svg",'<svg preserveAspectRatio="none"')),t[a].shapes[o].shape=s(e),this.setState({shapes:t})}}),(0,e.createElement)(m.Tooltip,{text:(0,c.__)("Delete Shape","generateblocks-pro")},(0,e.createElement)(m.Button,{className:"gblocks-delete-asset",onClick:()=>{if(window.confirm((0,c.__)("This will permanently delete this shape and remove it from the front-end of your website.","generateblocks-pro"))){const e=[...this.state.shapes];e[a].shapes.splice(o,1),this.setState({shapes:e})}},icon:r("x")})))))),(0,e.createElement)("div",{className:"gblocks-add-new-asset"},(0,e.createElement)(m.Button,{isSecondary:!0,onClick:()=>{const e=[...this.state.shapes];e[a].shapes.push({name:"",shape:""}),this.setState({shapes:e})}},(0,c.__)("Add Shape","generateblocks-pro"))))))))))),(0,e.createElement)(m.PanelRow,{className:"gblocks-asset-library-group-actions"},(0,e.createElement)("div",{className:"gblocks-add-new-asset-group"},(0,e.createElement)(m.Button,{isSecondary:!0,onClick:()=>{const e=[...this.state.shapes];e.push({group:"",shapes:[]}),this.setState({shapes:e})}},(0,c.__)("Add Group","generateblocks-pro"))),(0,e.createElement)("div",{className:"gblocks-import-asset-group"},!this.state.showImportField&&(0,e.createElement)(m.Button,{isSecondary:!0,onClick:()=>this.setState({showImportField:!0})},(0,c.__)("Import Group","generateblocks-pro")),this.state.showImportField&&(0,e.createElement)("input",{type:"file",accept:".json",onChange:e=>{const t=e.target.nextElementSibling,n=new FileReader;n.onloadend=()=>{let e=n.result;if(e=JSON.parse(e),e&&"shapes"===e.type){const n=[...this.state.shapes];n.push({group:e.group,shapes:e.assets}),this.setState({shapes:n}),t.classList.add("gblocks-action-message--show"),t.textContent=(0,c.__)("Group imported.","generateblocks-pro"),setTimeout((function(){t.classList.remove("gblocks-action-message--show")}),3e3)}e||(t.classList.add("gblocks-action-message--show"),t.classList.add("gblocks-action-message--error"),t.textContent=(0,c.__)("File not valid.","generateblocks-pro"),setTimeout((function(){t.classList.remove("gblocks-action-message--show"),t.classList.remove("gblocks-action-message--error")}),3e3)),"shapes"!==e.type&&(t.classList.add("gblocks-action-message--show"),t.classList.add("gblocks-action-message--error"),t.textContent=(0,c.__)("Wrong asset type.","generateblocks-pro"),setTimeout((function(){t.classList.remove("gblocks-action-message--show"),t.classList.remove("gblocks-action-message--error")}),3e3))},n.readAsText(e.target.files[0]),e.target.value="",this.setState({showImportField:!1})}}),(0,e.createElement)("span",{className:"gblocks-action-message"}))),(0,e.createElement)(m.PanelRow,{className:"gblocks-asset-library-actions"},(0,e.createElement)("div",{className:"gblocks-action-button"},(0,e.createElement)(m.Button,{isPrimary:!0,disabled:this.state.isAPISaving,onClick:e=>this.updateSettings(e)},this.state.isAPISaving&&(0,e.createElement)(m.Spinner,null),!this.state.isAPISaving&&(0,c.__)("Save Shapes","generateblocks-pro")),(0,e.createElement)("span",{className:"gblocks-action-message"})))))))}}window.addEventListener("DOMContentLoaded",(()=>{(0,p.render)((0,e.createElement)(f,null),document.getElementById("gblocks-shape-library"))}))})()})();