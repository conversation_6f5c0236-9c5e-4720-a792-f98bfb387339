(()=>{var e={7189:e=>{var t;self,t=()=>(()=>{var e={8907:(e,t)=>{"use strict";t.match=function(e,t){return l(e).some((function(e){var r=e.inverse,n="all"===e.type||t.type===e.type;if(n&&r||!n&&!r)return!1;var o=e.expressions.every((function(e){var r=e.feature,n=e.modifier,o=e.value,a=t[r];if(!a)return!1;switch(r){case"orientation":case"scan":return a.toLowerCase()===o.toLowerCase();case"width":case"height":case"device-width":case"device-height":o=u(o),a=u(a);break;case"resolution":o=c(o),a=c(a);break;case"aspect-ratio":case"device-aspect-ratio":case"device-pixel-ratio":o=i(o),a=i(a);break;case"grid":case"color":case"color-index":case"monochrome":o=parseInt(o,10)||1,a=parseInt(a,10)||0}switch(n){case"min":return a>=o;case"max":return a<=o;default:return a===o}}));return o&&!r||!o&&r}))},t.parse=l;var r=/(?:(only|not)?\s*([^\s\(\)]+)(?:\s*and)?\s*)?(.+)?/i,n=/\(\s*([^\s\:\)]+)\s*(?:\:\s*([^\s\)]+))?\s*\)/,o=/^(?:(min|max)-)?(.+)/,a=/(em|rem|px|cm|mm|in|pt|pc)?$/,s=/(dpi|dpcm|dppx)?$/;function l(e){return e.split(",").map((function(e){var t=(e=e.trim()).match(r),a=t[1],s=t[2],l=t[3]||"",i={};return i.inverse=!!a&&"not"===a.toLowerCase(),i.type=s?s.toLowerCase():"all",l=l.match(/\([^\)]+\)/g)||[],i.expressions=l.map((function(e){var t=e.match(n),r=t[1].toLowerCase().match(o);return{modifier:r[1],feature:r[2],value:t[2]}})),i}))}function i(e){var t,r=Number(e);return r||(r=(t=e.match(/^(\d+)\s*\/\s*(\d+)$/))[1]/t[2]),r}function c(e){var t=parseFloat(e);switch(String(e).match(s)[1]){case"dpcm":return t/2.54;case"dppx":return 96*t;default:return t}}function u(e){var t=parseFloat(e);switch(String(e).match(a)[1]){case"em":case"rem":return 16*t;case"cm":return 96*t/2.54;case"mm":return 96*t/2.54/10;case"in":return 96*t;case"pt":return 72*t;case"pc":return 72*t/12;default:return t}}},8937:e=>{"use strict";var t={}.hasOwnProperty,r=/[ -,\.\/:-@\[-\^`\{-~]/,n=/[ -,\.\/:-@\[\]\^`\{-~]/,o=/(^|\\+)?(\\[A-F0-9]{1,6})\x20(?![a-fA-F0-9\x20])/g,a=function e(a,s){"single"!=(s=function(e,r){if(!e)return r;var n={};for(var o in r)n[o]=t.call(e,o)?e[o]:r[o];return n}(s,e.options)).quotes&&"double"!=s.quotes&&(s.quotes="single");for(var l="double"==s.quotes?'"':"'",i=s.isIdentifier,c=a.charAt(0),u="",d=0,p=a.length;d<p;){var h=a.charAt(d++),m=h.charCodeAt(),f=void 0;if(m<32||m>126){if(m>=55296&&m<=56319&&d<p){var g=a.charCodeAt(d++);56320==(64512&g)?m=((1023&m)<<10)+(1023&g)+65536:d--}f="\\"+m.toString(16).toUpperCase()+" "}else f=s.escapeEverything?r.test(h)?"\\"+h:"\\"+m.toString(16).toUpperCase()+" ":/[\t\n\f\r\x0B]/.test(h)?"\\"+m.toString(16).toUpperCase()+" ":"\\"==h||!i&&('"'==h&&l==h||"'"==h&&l==h)||i&&n.test(h)?"\\"+h:h;u+=f}return i&&(/^-[-\d]/.test(u)?u="\\-"+u.slice(1):/\d/.test(c)&&(u="\\3"+c+" "+u.slice(1))),u=u.replace(o,(function(e,t,r){return t&&t.length%2?e:(t||"")+r})),!i&&s.wrap?l+u+l:u};a.options={escapeEverything:!1,isIdentifier:!1,quotes:"single",wrap:!1},a.version="3.0.0",e.exports=a},9456:(e,t,r)=>{e.exports.all=r(9817).properties},8633:e=>{var t=String,r=function(){return{isColorSupported:!1,reset:t,bold:t,dim:t,italic:t,underline:t,inverse:t,hidden:t,strikethrough:t,black:t,red:t,green:t,yellow:t,blue:t,magenta:t,cyan:t,white:t,gray:t,bgBlack:t,bgRed:t,bgGreen:t,bgYellow:t,bgBlue:t,bgMagenta:t,bgCyan:t,bgWhite:t}};e.exports=r(),e.exports.createColors=r},3268:e=>{"use strict";const t="postcss-discard-empty";function r(){return{postcssPlugin:t,OnceExit(e,{result:r}){!function(e,r){e.each((function e(n){const{type:o}=n,a=n.nodes;a&&n.each(e),("decl"===o&&!n.value&&!n.prop.startsWith("--")||"rule"===o&&!n.selector||a&&!a.length&&("atrule"!==o||"layer"!==n.name)||"atrule"===o&&(!a&&!n.params||!n.params&&!a.length))&&(n.remove(),r.messages.push({type:"removal",plugin:t,node:n}))}))}(e,r)}}}r.postcss=!0,e.exports=r},691:(e,t,r)=>{const n=r(7149),o=r(5482),a=n((e=>{e.walk((e=>{e.spaces={before:"",after:""},e.raws&&e.raws.spaces&&(e.raws.spaces={})}))}));function s(e){const t=o(e.trim());return t.walk((e=>{e.before&&(e.before=""),e.after&&(e.after=""),"space"===e.type&&(e.value=" ")})),t.toString()}e.exports=()=>({postcssPlugin:"postcss-minify",AtRule:e=>{e.raws={before:"",after:"",afterName:" "},e.params=s(e.params)},Comment:e=>{"!"===e.text[0]?(e.raws.before="",e.raws.after=""):e.remove()},Declaration:e=>{e.raws={before:"",between:":"},e.value=s(e.value)},Rule:e=>{var t;e.raws={before:"",between:"",after:"",semicolon:!1},e.selector=(t=e.selector,a.processSync(t))}}),e.exports.postcss=!0},7149:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(8349))&&n.__esModule?n:{default:n},a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var l=o?Object.getOwnPropertyDescriptor(e,a):null;l&&(l.get||l.set)?Object.defineProperty(n,a,l):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(680));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}var l=function(e){return new o.default(e)};Object.assign(l,a),delete l.__esModule;var i=l;t.default=i,e.exports=t.default},5170:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o,a=x(r(518)),s=x(r(339)),l=x(r(4195)),i=x(r(425)),c=x(r(7071)),u=x(r(9720)),d=x(r(5799)),p=x(r(7324)),h=S(r(5588)),m=x(r(1669)),f=x(r(1704)),g=x(r(2918)),b=x(r(263)),v=S(r(2648)),y=S(r(71)),w=S(r(1581)),k=r(9606);function E(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(E=function(e){return e?r:t})(e)}function S(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=E(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}function x(e){return e&&e.__esModule?e:{default:e}}function C(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var _=((n={})[y.space]=!0,n[y.cr]=!0,n[y.feed]=!0,n[y.newline]=!0,n[y.tab]=!0,n),O=Object.assign({},_,((o={})[y.comment]=!0,o));function T(e){return{line:e[v.FIELDS.START_LINE],column:e[v.FIELDS.START_COL]}}function I(e){return{line:e[v.FIELDS.END_LINE],column:e[v.FIELDS.END_COL]}}function R(e,t,r,n){return{start:{line:e,column:t},end:{line:r,column:n}}}function M(e){return R(e[v.FIELDS.START_LINE],e[v.FIELDS.START_COL],e[v.FIELDS.END_LINE],e[v.FIELDS.END_COL])}function P(e,t){if(e)return R(e[v.FIELDS.START_LINE],e[v.FIELDS.START_COL],t[v.FIELDS.END_LINE],t[v.FIELDS.END_COL])}function A(e,t){var r=e[t];if("string"==typeof r)return-1!==r.indexOf("\\")&&((0,k.ensureObject)(e,"raws"),e[t]=(0,k.unesc)(r),void 0===e.raws[t]&&(e.raws[t]=r)),e}function L(e,t){for(var r=-1,n=[];-1!==(r=e.indexOf(t,r+1));)n.push(r);return n}var N=function(){function e(e,t){void 0===t&&(t={}),this.rule=e,this.options=Object.assign({lossy:!1,safe:!1},t),this.position=0,this.css="string"==typeof this.rule?this.rule:this.rule.selector,this.tokens=(0,v.default)({css:this.css,error:this._errorGenerator(),safe:this.options.safe});var r=P(this.tokens[0],this.tokens[this.tokens.length-1]);this.root=new a.default({source:r}),this.root.errorGenerator=this._errorGenerator();var n=new s.default({source:{start:{line:1,column:1}},sourceIndex:0});this.root.append(n),this.current=n,this.loop()}var t,r,n=e.prototype;return n._errorGenerator=function(){var e=this;return function(t,r){return"string"==typeof e.rule?new Error(t):e.rule.error(t,r)}},n.attribute=function(){var e=[],t=this.currToken;for(this.position++;this.position<this.tokens.length&&this.currToken[v.FIELDS.TYPE]!==y.closeSquare;)e.push(this.currToken),this.position++;if(this.currToken[v.FIELDS.TYPE]!==y.closeSquare)return this.expected("closing square bracket",this.currToken[v.FIELDS.START_POS]);var r=e.length,n={source:R(t[1],t[2],this.currToken[3],this.currToken[4]),sourceIndex:t[v.FIELDS.START_POS]};if(1===r&&!~[y.word].indexOf(e[0][v.FIELDS.TYPE]))return this.expected("attribute",e[0][v.FIELDS.START_POS]);for(var o=0,a="",s="",l=null,i=!1;o<r;){var c=e[o],u=this.content(c),d=e[o+1];switch(c[v.FIELDS.TYPE]){case y.space:if(i=!0,this.options.lossy)break;if(l){(0,k.ensureObject)(n,"spaces",l);var p=n.spaces[l].after||"";n.spaces[l].after=p+u;var m=(0,k.getProp)(n,"raws","spaces",l,"after")||null;m&&(n.raws.spaces[l].after=m+u)}else a+=u,s+=u;break;case y.asterisk:d[v.FIELDS.TYPE]===y.equals?(n.operator=u,l="operator"):n.namespace&&("namespace"!==l||i)||!d||(a&&((0,k.ensureObject)(n,"spaces","attribute"),n.spaces.attribute.before=a,a=""),s&&((0,k.ensureObject)(n,"raws","spaces","attribute"),n.raws.spaces.attribute.before=a,s=""),n.namespace=(n.namespace||"")+u,(0,k.getProp)(n,"raws","namespace")&&(n.raws.namespace+=u),l="namespace"),i=!1;break;case y.dollar:if("value"===l){var f=(0,k.getProp)(n,"raws","value");n.value+="$",f&&(n.raws.value=f+"$");break}case y.caret:d[v.FIELDS.TYPE]===y.equals&&(n.operator=u,l="operator"),i=!1;break;case y.combinator:if("~"===u&&d[v.FIELDS.TYPE]===y.equals&&(n.operator=u,l="operator"),"|"!==u){i=!1;break}d[v.FIELDS.TYPE]===y.equals?(n.operator=u,l="operator"):n.namespace||n.attribute||(n.namespace=!0),i=!1;break;case y.word:if(d&&"|"===this.content(d)&&e[o+2]&&e[o+2][v.FIELDS.TYPE]!==y.equals&&!n.operator&&!n.namespace)n.namespace=u,l="namespace";else if(!n.attribute||"attribute"===l&&!i)a&&((0,k.ensureObject)(n,"spaces","attribute"),n.spaces.attribute.before=a,a=""),s&&((0,k.ensureObject)(n,"raws","spaces","attribute"),n.raws.spaces.attribute.before=s,s=""),n.attribute=(n.attribute||"")+u,(0,k.getProp)(n,"raws","attribute")&&(n.raws.attribute+=u),l="attribute";else if(!n.value&&""!==n.value||"value"===l&&!i&&!n.quoteMark){var g=(0,k.unesc)(u),b=(0,k.getProp)(n,"raws","value")||"",w=n.value||"";n.value=w+g,n.quoteMark=null,(g!==u||b)&&((0,k.ensureObject)(n,"raws"),n.raws.value=(b||w)+u),l="value"}else{var E="i"===u||"I"===u;!n.value&&""!==n.value||!n.quoteMark&&!i?(n.value||""===n.value)&&(l="value",n.value+=u,n.raws.value&&(n.raws.value+=u)):(n.insensitive=E,E&&"I"!==u||((0,k.ensureObject)(n,"raws"),n.raws.insensitiveFlag=u),l="insensitive",a&&((0,k.ensureObject)(n,"spaces","insensitive"),n.spaces.insensitive.before=a,a=""),s&&((0,k.ensureObject)(n,"raws","spaces","insensitive"),n.raws.spaces.insensitive.before=s,s=""))}i=!1;break;case y.str:if(!n.attribute||!n.operator)return this.error("Expected an attribute followed by an operator preceding the string.",{index:c[v.FIELDS.START_POS]});var S=(0,h.unescapeValue)(u),x=S.unescaped,C=S.quoteMark;n.value=x,n.quoteMark=C,l="value",(0,k.ensureObject)(n,"raws"),n.raws.value=u,i=!1;break;case y.equals:if(!n.attribute)return this.expected("attribute",c[v.FIELDS.START_POS],u);if(n.value)return this.error('Unexpected "=" found; an operator was already defined.',{index:c[v.FIELDS.START_POS]});n.operator=n.operator?n.operator+u:u,l="operator",i=!1;break;case y.comment:if(l)if(i||d&&d[v.FIELDS.TYPE]===y.space||"insensitive"===l){var _=(0,k.getProp)(n,"spaces",l,"after")||"",O=(0,k.getProp)(n,"raws","spaces",l,"after")||_;(0,k.ensureObject)(n,"raws","spaces",l),n.raws.spaces[l].after=O+u}else{var T=n[l]||"",I=(0,k.getProp)(n,"raws",l)||T;(0,k.ensureObject)(n,"raws"),n.raws[l]=I+u}else s+=u;break;default:return this.error('Unexpected "'+u+'" found.',{index:c[v.FIELDS.START_POS]})}o++}A(n,"attribute"),A(n,"namespace"),this.newNode(new h.default(n)),this.position++},n.parseWhitespaceEquivalentTokens=function(e){e<0&&(e=this.tokens.length);var t=this.position,r=[],n="",o=void 0;do{if(_[this.currToken[v.FIELDS.TYPE]])this.options.lossy||(n+=this.content());else if(this.currToken[v.FIELDS.TYPE]===y.comment){var a={};n&&(a.before=n,n=""),o=new i.default({value:this.content(),source:M(this.currToken),sourceIndex:this.currToken[v.FIELDS.START_POS],spaces:a}),r.push(o)}}while(++this.position<e);if(n)if(o)o.spaces.after=n;else if(!this.options.lossy){var s=this.tokens[t],l=this.tokens[this.position-1];r.push(new d.default({value:"",source:R(s[v.FIELDS.START_LINE],s[v.FIELDS.START_COL],l[v.FIELDS.END_LINE],l[v.FIELDS.END_COL]),sourceIndex:s[v.FIELDS.START_POS],spaces:{before:n,after:""}}))}return r},n.convertWhitespaceNodesToSpace=function(e,t){var r=this;void 0===t&&(t=!1);var n="",o="";return e.forEach((function(e){var a=r.lossySpace(e.spaces.before,t),s=r.lossySpace(e.rawSpaceBefore,t);n+=a+r.lossySpace(e.spaces.after,t&&0===a.length),o+=a+e.value+r.lossySpace(e.rawSpaceAfter,t&&0===s.length)})),o===n&&(o=void 0),{space:n,rawSpace:o}},n.isNamedCombinator=function(e){return void 0===e&&(e=this.position),this.tokens[e+0]&&this.tokens[e+0][v.FIELDS.TYPE]===y.slash&&this.tokens[e+1]&&this.tokens[e+1][v.FIELDS.TYPE]===y.word&&this.tokens[e+2]&&this.tokens[e+2][v.FIELDS.TYPE]===y.slash},n.namedCombinator=function(){if(this.isNamedCombinator()){var e=this.content(this.tokens[this.position+1]),t=(0,k.unesc)(e).toLowerCase(),r={};t!==e&&(r.value="/"+e+"/");var n=new f.default({value:"/"+t+"/",source:R(this.currToken[v.FIELDS.START_LINE],this.currToken[v.FIELDS.START_COL],this.tokens[this.position+2][v.FIELDS.END_LINE],this.tokens[this.position+2][v.FIELDS.END_COL]),sourceIndex:this.currToken[v.FIELDS.START_POS],raws:r});return this.position=this.position+3,n}this.unexpected()},n.combinator=function(){var e=this;if("|"===this.content())return this.namespace();var t=this.locateNextMeaningfulToken(this.position);if(!(t<0||this.tokens[t][v.FIELDS.TYPE]===y.comma||this.tokens[t][v.FIELDS.TYPE]===y.closeParenthesis)){var r,n=this.currToken,o=void 0;if(t>this.position&&(o=this.parseWhitespaceEquivalentTokens(t)),this.isNamedCombinator()?r=this.namedCombinator():this.currToken[v.FIELDS.TYPE]===y.combinator?(r=new f.default({value:this.content(),source:M(this.currToken),sourceIndex:this.currToken[v.FIELDS.START_POS]}),this.position++):_[this.currToken[v.FIELDS.TYPE]]||o||this.unexpected(),r){if(o){var a=this.convertWhitespaceNodesToSpace(o),s=a.space,l=a.rawSpace;r.spaces.before=s,r.rawSpaceBefore=l}}else{var i=this.convertWhitespaceNodesToSpace(o,!0),c=i.space,u=i.rawSpace;u||(u=c);var d={},p={spaces:{}};c.endsWith(" ")&&u.endsWith(" ")?(d.before=c.slice(0,c.length-1),p.spaces.before=u.slice(0,u.length-1)):c.startsWith(" ")&&u.startsWith(" ")?(d.after=c.slice(1),p.spaces.after=u.slice(1)):p.value=u,r=new f.default({value:" ",source:P(n,this.tokens[this.position-1]),sourceIndex:n[v.FIELDS.START_POS],spaces:d,raws:p})}return this.currToken&&this.currToken[v.FIELDS.TYPE]===y.space&&(r.spaces.after=this.optionalSpace(this.content()),this.position++),this.newNode(r)}var h=this.parseWhitespaceEquivalentTokens(t);if(h.length>0){var m=this.current.last;if(m){var g=this.convertWhitespaceNodesToSpace(h),b=g.space,w=g.rawSpace;void 0!==w&&(m.rawSpaceAfter+=w),m.spaces.after+=b}else h.forEach((function(t){return e.newNode(t)}))}},n.comma=function(){if(this.position===this.tokens.length-1)return this.root.trailingComma=!0,void this.position++;this.current._inferEndPosition();var e=new s.default({source:{start:T(this.tokens[this.position+1])},sourceIndex:this.tokens[this.position+1][v.FIELDS.START_POS]});this.current.parent.append(e),this.current=e,this.position++},n.comment=function(){var e=this.currToken;this.newNode(new i.default({value:this.content(),source:M(e),sourceIndex:e[v.FIELDS.START_POS]})),this.position++},n.error=function(e,t){throw this.root.error(e,t)},n.missingBackslash=function(){return this.error("Expected a backslash preceding the semicolon.",{index:this.currToken[v.FIELDS.START_POS]})},n.missingParenthesis=function(){return this.expected("opening parenthesis",this.currToken[v.FIELDS.START_POS])},n.missingSquareBracket=function(){return this.expected("opening square bracket",this.currToken[v.FIELDS.START_POS])},n.unexpected=function(){return this.error("Unexpected '"+this.content()+"'. Escaping special characters with \\ may help.",this.currToken[v.FIELDS.START_POS])},n.unexpectedPipe=function(){return this.error("Unexpected '|'.",this.currToken[v.FIELDS.START_POS])},n.namespace=function(){var e=this.prevToken&&this.content(this.prevToken)||!0;return this.nextToken[v.FIELDS.TYPE]===y.word?(this.position++,this.word(e)):this.nextToken[v.FIELDS.TYPE]===y.asterisk?(this.position++,this.universal(e)):void this.unexpectedPipe()},n.nesting=function(){if(this.nextToken&&"|"===this.content(this.nextToken))this.position++;else{var e=this.currToken;this.newNode(new g.default({value:this.content(),source:M(e),sourceIndex:e[v.FIELDS.START_POS]})),this.position++}},n.parentheses=function(){var e=this.current.last,t=1;if(this.position++,e&&e.type===w.PSEUDO){var r=new s.default({source:{start:T(this.tokens[this.position])},sourceIndex:this.tokens[this.position][v.FIELDS.START_POS]}),n=this.current;for(e.append(r),this.current=r;this.position<this.tokens.length&&t;)this.currToken[v.FIELDS.TYPE]===y.openParenthesis&&t++,this.currToken[v.FIELDS.TYPE]===y.closeParenthesis&&t--,t?this.parse():(this.current.source.end=I(this.currToken),this.current.parent.source.end=I(this.currToken),this.position++);this.current=n}else{for(var o,a=this.currToken,l="(";this.position<this.tokens.length&&t;)this.currToken[v.FIELDS.TYPE]===y.openParenthesis&&t++,this.currToken[v.FIELDS.TYPE]===y.closeParenthesis&&t--,o=this.currToken,l+=this.parseParenthesisToken(this.currToken),this.position++;e?e.appendToPropertyAndEscape("value",l,l):this.newNode(new d.default({value:l,source:R(a[v.FIELDS.START_LINE],a[v.FIELDS.START_COL],o[v.FIELDS.END_LINE],o[v.FIELDS.END_COL]),sourceIndex:a[v.FIELDS.START_POS]}))}if(t)return this.expected("closing parenthesis",this.currToken[v.FIELDS.START_POS])},n.pseudo=function(){for(var e=this,t="",r=this.currToken;this.currToken&&this.currToken[v.FIELDS.TYPE]===y.colon;)t+=this.content(),this.position++;return this.currToken?this.currToken[v.FIELDS.TYPE]!==y.word?this.expected(["pseudo-class","pseudo-element"],this.currToken[v.FIELDS.START_POS]):void this.splitWord(!1,(function(n,o){t+=n,e.newNode(new p.default({value:t,source:P(r,e.currToken),sourceIndex:r[v.FIELDS.START_POS]})),o>1&&e.nextToken&&e.nextToken[v.FIELDS.TYPE]===y.openParenthesis&&e.error("Misplaced parenthesis.",{index:e.nextToken[v.FIELDS.START_POS]})})):this.expected(["pseudo-class","pseudo-element"],this.position-1)},n.space=function(){var e=this.content();0===this.position||this.prevToken[v.FIELDS.TYPE]===y.comma||this.prevToken[v.FIELDS.TYPE]===y.openParenthesis||this.current.nodes.every((function(e){return"comment"===e.type}))?(this.spaces=this.optionalSpace(e),this.position++):this.position===this.tokens.length-1||this.nextToken[v.FIELDS.TYPE]===y.comma||this.nextToken[v.FIELDS.TYPE]===y.closeParenthesis?(this.current.last.spaces.after=this.optionalSpace(e),this.position++):this.combinator()},n.string=function(){var e=this.currToken;this.newNode(new d.default({value:this.content(),source:M(e),sourceIndex:e[v.FIELDS.START_POS]})),this.position++},n.universal=function(e){var t=this.nextToken;if(t&&"|"===this.content(t))return this.position++,this.namespace();var r=this.currToken;this.newNode(new m.default({value:this.content(),source:M(r),sourceIndex:r[v.FIELDS.START_POS]}),e),this.position++},n.splitWord=function(e,t){for(var r=this,n=this.nextToken,o=this.content();n&&~[y.dollar,y.caret,y.equals,y.word].indexOf(n[v.FIELDS.TYPE]);){this.position++;var a=this.content();if(o+=a,a.lastIndexOf("\\")===a.length-1){var s=this.nextToken;s&&s[v.FIELDS.TYPE]===y.space&&(o+=this.requiredSpace(this.content(s)),this.position++)}n=this.nextToken}var i=L(o,".").filter((function(e){var t="\\"===o[e-1],r=/^\d+\.\d+%$/.test(o);return!t&&!r})),d=L(o,"#").filter((function(e){return"\\"!==o[e-1]})),p=L(o,"#{");p.length&&(d=d.filter((function(e){return!~p.indexOf(e)})));var h=(0,b.default)(function(){var e=Array.prototype.concat.apply([],arguments);return e.filter((function(t,r){return r===e.indexOf(t)}))}([0].concat(i,d)));h.forEach((function(n,a){var s,p=h[a+1]||o.length,m=o.slice(n,p);if(0===a&&t)return t.call(r,m,h.length);var f=r.currToken,g=f[v.FIELDS.START_POS]+h[a],b=R(f[1],f[2]+n,f[3],f[2]+(p-1));if(~i.indexOf(n)){var y={value:m.slice(1),source:b,sourceIndex:g};s=new l.default(A(y,"value"))}else if(~d.indexOf(n)){var w={value:m.slice(1),source:b,sourceIndex:g};s=new c.default(A(w,"value"))}else{var k={value:m,source:b,sourceIndex:g};A(k,"value"),s=new u.default(k)}r.newNode(s,e),e=null})),this.position++},n.word=function(e){var t=this.nextToken;return t&&"|"===this.content(t)?(this.position++,this.namespace()):this.splitWord(e)},n.loop=function(){for(;this.position<this.tokens.length;)this.parse(!0);return this.current._inferEndPosition(),this.root},n.parse=function(e){switch(this.currToken[v.FIELDS.TYPE]){case y.space:this.space();break;case y.comment:this.comment();break;case y.openParenthesis:this.parentheses();break;case y.closeParenthesis:e&&this.missingParenthesis();break;case y.openSquare:this.attribute();break;case y.dollar:case y.caret:case y.equals:case y.word:this.word();break;case y.colon:this.pseudo();break;case y.comma:this.comma();break;case y.asterisk:this.universal();break;case y.ampersand:this.nesting();break;case y.slash:case y.combinator:this.combinator();break;case y.str:this.string();break;case y.closeSquare:this.missingSquareBracket();case y.semicolon:this.missingBackslash();default:this.unexpected()}},n.expected=function(e,t,r){if(Array.isArray(e)){var n=e.pop();e=e.join(", ")+" or "+n}var o=/^[aeiou]/.test(e[0])?"an":"a";return r?this.error("Expected "+o+" "+e+', found "'+r+'" instead.',{index:t}):this.error("Expected "+o+" "+e+".",{index:t})},n.requiredSpace=function(e){return this.options.lossy?" ":e},n.optionalSpace=function(e){return this.options.lossy?"":e},n.lossySpace=function(e,t){return this.options.lossy?t?" ":"":e},n.parseParenthesisToken=function(e){var t=this.content(e);return e[v.FIELDS.TYPE]===y.space?this.requiredSpace(t):t},n.newNode=function(e,t){return t&&(/^ +$/.test(t)&&(this.options.lossy||(this.spaces=(this.spaces||"")+t),t=!0),e.namespace=t,A(e,"namespace")),this.spaces&&(e.spaces.before=this.spaces,this.spaces=""),this.current.append(e)},n.content=function(e){return void 0===e&&(e=this.currToken),this.css.slice(e[v.FIELDS.START_POS],e[v.FIELDS.END_POS])},n.locateNextMeaningfulToken=function(e){void 0===e&&(e=this.position+1);for(var t=e;t<this.tokens.length;){if(!O[this.tokens[t][v.FIELDS.TYPE]])return t;t++}return-1},t=e,(r=[{key:"currToken",get:function(){return this.tokens[this.position]}},{key:"nextToken",get:function(){return this.tokens[this.position+1]}},{key:"prevToken",get:function(){return this.tokens[this.position-1]}}])&&C(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();t.default=N,e.exports=t.default},8349:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(5170))&&n.__esModule?n:{default:n},a=function(){function e(e,t){this.func=e||function(){},this.funcRes=null,this.options=t}var t=e.prototype;return t._shouldUpdateSelector=function(e,t){return void 0===t&&(t={}),!1!==Object.assign({},this.options,t).updateSelector&&"string"!=typeof e},t._isLossy=function(e){return void 0===e&&(e={}),!1===Object.assign({},this.options,e).lossless},t._root=function(e,t){return void 0===t&&(t={}),new o.default(e,this._parseOptions(t)).root},t._parseOptions=function(e){return{lossy:this._isLossy(e)}},t._run=function(e,t){var r=this;return void 0===t&&(t={}),new Promise((function(n,o){try{var a=r._root(e,t);Promise.resolve(r.func(a)).then((function(n){var o=void 0;return r._shouldUpdateSelector(e,t)&&(o=a.toString(),e.selector=o),{transform:n,root:a,string:o}})).then(n,o)}catch(e){return void o(e)}}))},t._runSync=function(e,t){void 0===t&&(t={});var r=this._root(e,t),n=this.func(r);if(n&&"function"==typeof n.then)throw new Error("Selector processor returned a promise to a synchronous call.");var o=void 0;return t.updateSelector&&"string"!=typeof e&&(o=r.toString(),e.selector=o),{transform:n,root:r,string:o}},t.ast=function(e,t){return this._run(e,t).then((function(e){return e.root}))},t.astSync=function(e,t){return this._runSync(e,t).root},t.transform=function(e,t){return this._run(e,t).then((function(e){return e.transform}))},t.transformSync=function(e,t){return this._runSync(e,t).transform},t.process=function(e,t){return this._run(e,t).then((function(e){return e.string||e.root.toString()}))},t.processSync=function(e,t){var r=this._runSync(e,t);return r.string||r.root.toString()},e}();t.default=a,e.exports=t.default},5588:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0,t.unescapeValue=g;var n,o=i(r(8937)),a=i(r(5286)),s=i(r(3295)),l=r(1581);function i(e){return e&&e.__esModule?e:{default:e}}function c(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function u(e,t){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},u(e,t)}var d=r(4643),p=/^('|")([^]*)\1$/,h=d((function(){}),"Assigning an attribute a value containing characters that might need to be escaped is deprecated. Call attribute.setValue() instead."),m=d((function(){}),"Assigning attr.quoted is deprecated and has no effect. Assign to attr.quoteMark instead."),f=d((function(){}),"Constructing an Attribute selector with a value without specifying quoteMark is deprecated. Note: The value should be unescaped now.");function g(e){var t=!1,r=null,n=e,o=n.match(p);return o&&(r=o[1],n=o[2]),(n=(0,a.default)(n))!==e&&(t=!0),{deprecatedUsage:t,unescaped:n,quoteMark:r}}var b=function(e){var t,r;function n(t){var r;return void 0===t&&(t={}),r=e.call(this,function(e){if(void 0!==e.quoteMark)return e;if(void 0===e.value)return e;f();var t=g(e.value),r=t.quoteMark,n=t.unescaped;return e.raws||(e.raws={}),void 0===e.raws.value&&(e.raws.value=e.value),e.value=n,e.quoteMark=r,e}(t))||this,r.type=l.ATTRIBUTE,r.raws=r.raws||{},Object.defineProperty(r.raws,"unquoted",{get:d((function(){return r.value}),"attr.raws.unquoted is deprecated. Call attr.value instead."),set:d((function(){return r.value}),"Setting attr.raws.unquoted is deprecated and has no effect. attr.value is unescaped by default now.")}),r._constructed=!0,r}r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,u(t,r);var a,s,i=n.prototype;return i.getQuotedValue=function(e){void 0===e&&(e={});var t=this._determineQuoteMark(e),r=v[t];return(0,o.default)(this._value,r)},i._determineQuoteMark=function(e){return e.smart?this.smartQuoteMark(e):this.preferredQuoteMark(e)},i.setValue=function(e,t){void 0===t&&(t={}),this._value=e,this._quoteMark=this._determineQuoteMark(t),this._syncRawValue()},i.smartQuoteMark=function(e){var t=this.value,r=t.replace(/[^']/g,"").length,a=t.replace(/[^"]/g,"").length;if(r+a===0){var s=(0,o.default)(t,{isIdentifier:!0});if(s===t)return n.NO_QUOTE;var l=this.preferredQuoteMark(e);if(l===n.NO_QUOTE){var i=this.quoteMark||e.quoteMark||n.DOUBLE_QUOTE,c=v[i];if((0,o.default)(t,c).length<s.length)return i}return l}return a===r?this.preferredQuoteMark(e):a<r?n.DOUBLE_QUOTE:n.SINGLE_QUOTE},i.preferredQuoteMark=function(e){var t=e.preferCurrentQuoteMark?this.quoteMark:e.quoteMark;return void 0===t&&(t=e.preferCurrentQuoteMark?e.quoteMark:this.quoteMark),void 0===t&&(t=n.DOUBLE_QUOTE),t},i._syncRawValue=function(){var e=(0,o.default)(this._value,v[this.quoteMark]);e===this._value?this.raws&&delete this.raws.value:this.raws.value=e},i._handleEscapes=function(e,t){if(this._constructed){var r=(0,o.default)(t,{isIdentifier:!0});r!==t?this.raws[e]=r:delete this.raws[e]}},i._spacesFor=function(e){var t=this.spaces[e]||{},r=this.raws.spaces&&this.raws.spaces[e]||{};return Object.assign({before:"",after:""},t,r)},i._stringFor=function(e,t,r){void 0===t&&(t=e),void 0===r&&(r=y);var n=this._spacesFor(t);return r(this.stringifyProperty(e),n)},i.offsetOf=function(e){var t=1,r=this._spacesFor("attribute");if(t+=r.before.length,"namespace"===e||"ns"===e)return this.namespace?t:-1;if("attributeNS"===e)return t;if(t+=this.namespaceString.length,this.namespace&&(t+=1),"attribute"===e)return t;t+=this.stringifyProperty("attribute").length,t+=r.after.length;var n=this._spacesFor("operator");t+=n.before.length;var o=this.stringifyProperty("operator");if("operator"===e)return o?t:-1;t+=o.length,t+=n.after.length;var a=this._spacesFor("value");t+=a.before.length;var s=this.stringifyProperty("value");return"value"===e?s?t:-1:(t+=s.length,t+=a.after.length,t+=this._spacesFor("insensitive").before.length,"insensitive"===e&&this.insensitive?t:-1)},i.toString=function(){var e=this,t=[this.rawSpaceBefore,"["];return t.push(this._stringFor("qualifiedAttribute","attribute")),this.operator&&(this.value||""===this.value)&&(t.push(this._stringFor("operator")),t.push(this._stringFor("value")),t.push(this._stringFor("insensitiveFlag","insensitive",(function(t,r){return!(t.length>0)||e.quoted||0!==r.before.length||e.spaces.value&&e.spaces.value.after||(r.before=" "),y(t,r)})))),t.push("]"),t.push(this.rawSpaceAfter),t.join("")},a=n,(s=[{key:"quoted",get:function(){var e=this.quoteMark;return"'"===e||'"'===e},set:function(e){m()}},{key:"quoteMark",get:function(){return this._quoteMark},set:function(e){this._constructed?this._quoteMark!==e&&(this._quoteMark=e,this._syncRawValue()):this._quoteMark=e}},{key:"qualifiedAttribute",get:function(){return this.qualifiedName(this.raws.attribute||this.attribute)}},{key:"insensitiveFlag",get:function(){return this.insensitive?"i":""}},{key:"value",get:function(){return this._value},set:function(e){if(this._constructed){var t=g(e),r=t.deprecatedUsage,n=t.unescaped,o=t.quoteMark;if(r&&h(),n===this._value&&o===this._quoteMark)return;this._value=n,this._quoteMark=o,this._syncRawValue()}else this._value=e}},{key:"insensitive",get:function(){return this._insensitive},set:function(e){e||(this._insensitive=!1,!this.raws||"I"!==this.raws.insensitiveFlag&&"i"!==this.raws.insensitiveFlag||(this.raws.insensitiveFlag=void 0)),this._insensitive=e}},{key:"attribute",get:function(){return this._attribute},set:function(e){this._handleEscapes("attribute",e),this._attribute=e}}])&&c(a.prototype,s),Object.defineProperty(a,"prototype",{writable:!1}),n}(s.default);t.default=b,b.NO_QUOTE=null,b.SINGLE_QUOTE="'",b.DOUBLE_QUOTE='"';var v=((n={"'":{quotes:"single",wrap:!0},'"':{quotes:"double",wrap:!0}}).null={isIdentifier:!0},n);function y(e,t){return""+t.before+e+t.after}},4195:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n=l(r(8937)),o=r(9606),a=l(r(4646)),s=r(1581);function l(e){return e&&e.__esModule?e:{default:e}}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function c(e,t){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},c(e,t)}var u=function(e){var t,r,a,l;function u(t){var r;return(r=e.call(this,t)||this).type=s.CLASS,r._constructed=!0,r}return r=e,(t=u).prototype=Object.create(r.prototype),t.prototype.constructor=t,c(t,r),u.prototype.valueToString=function(){return"."+e.prototype.valueToString.call(this)},a=u,(l=[{key:"value",get:function(){return this._value},set:function(e){if(this._constructed){var t=(0,n.default)(e,{isIdentifier:!0});t!==e?((0,o.ensureObject)(this,"raws"),this.raws.value=t):this.raws&&delete this.raws.value}this._value=e}}])&&i(a.prototype,l),Object.defineProperty(a,"prototype",{writable:!1}),u}(a.default);t.default=u,e.exports=t.default},1704:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(4646))&&n.__esModule?n:{default:n},a=r(1581);function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}var l=function(e){var t,r;function n(t){var r;return(r=e.call(this,t)||this).type=a.COMBINATOR,r}return r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,s(t,r),n}(o.default);t.default=l,e.exports=t.default},425:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(4646))&&n.__esModule?n:{default:n},a=r(1581);function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}var l=function(e){var t,r;function n(t){var r;return(r=e.call(this,t)||this).type=a.COMMENT,r}return r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,s(t,r),n}(o.default);t.default=l,e.exports=t.default},4451:(e,t,r)=>{"use strict";t.__esModule=!0,t.universal=t.tag=t.string=t.selector=t.root=t.pseudo=t.nesting=t.id=t.comment=t.combinator=t.className=t.attribute=void 0;var n=f(r(5588)),o=f(r(4195)),a=f(r(1704)),s=f(r(425)),l=f(r(7071)),i=f(r(2918)),c=f(r(7324)),u=f(r(518)),d=f(r(339)),p=f(r(5799)),h=f(r(9720)),m=f(r(1669));function f(e){return e&&e.__esModule?e:{default:e}}t.attribute=function(e){return new n.default(e)},t.className=function(e){return new o.default(e)},t.combinator=function(e){return new a.default(e)},t.comment=function(e){return new s.default(e)},t.id=function(e){return new l.default(e)},t.nesting=function(e){return new i.default(e)},t.pseudo=function(e){return new c.default(e)},t.root=function(e){return new u.default(e)},t.selector=function(e){return new d.default(e)},t.string=function(e){return new p.default(e)},t.tag=function(e){return new h.default(e)},t.universal=function(e){return new m.default(e)}},3631:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(4646))&&n.__esModule?n:{default:n},a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var l=o?Object.getOwnPropertyDescriptor(e,a):null;l&&(l.get||l.set)?Object.defineProperty(n,a,l):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(1581));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function c(e,t){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},c(e,t)}var u=function(e){var t,r;function n(t){var r;return(r=e.call(this,t)||this).nodes||(r.nodes=[]),r}r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,c(t,r);var o,s,u=n.prototype;return u.append=function(e){return e.parent=this,this.nodes.push(e),this},u.prepend=function(e){return e.parent=this,this.nodes.unshift(e),this},u.at=function(e){return this.nodes[e]},u.index=function(e){return"number"==typeof e?e:this.nodes.indexOf(e)},u.removeChild=function(e){var t;for(var r in e=this.index(e),this.at(e).parent=void 0,this.nodes.splice(e,1),this.indexes)(t=this.indexes[r])>=e&&(this.indexes[r]=t-1);return this},u.removeAll=function(){for(var e,t=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(r)return(r=r.call(e)).next.bind(r);if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return l(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(this.nodes);!(e=t()).done;)e.value.parent=void 0;return this.nodes=[],this},u.empty=function(){return this.removeAll()},u.insertAfter=function(e,t){t.parent=this;var r,n=this.index(e);for(var o in this.nodes.splice(n+1,0,t),t.parent=this,this.indexes)n<=(r=this.indexes[o])&&(this.indexes[o]=r+1);return this},u.insertBefore=function(e,t){t.parent=this;var r,n=this.index(e);for(var o in this.nodes.splice(n,0,t),t.parent=this,this.indexes)(r=this.indexes[o])<=n&&(this.indexes[o]=r+1);return this},u._findChildAtPosition=function(e,t){var r=void 0;return this.each((function(n){if(n.atPosition){var o=n.atPosition(e,t);if(o)return r=o,!1}else if(n.isAtPosition(e,t))return r=n,!1})),r},u.atPosition=function(e,t){return this.isAtPosition(e,t)?this._findChildAtPosition(e,t)||this:void 0},u._inferEndPosition=function(){this.last&&this.last.source&&this.last.source.end&&(this.source=this.source||{},this.source.end=this.source.end||{},Object.assign(this.source.end,this.last.source.end))},u.each=function(e){this.lastEach||(this.lastEach=0),this.indexes||(this.indexes={}),this.lastEach++;var t=this.lastEach;if(this.indexes[t]=0,this.length){for(var r,n;this.indexes[t]<this.length&&(r=this.indexes[t],!1!==(n=e(this.at(r),r)));)this.indexes[t]+=1;return delete this.indexes[t],!1!==n&&void 0}},u.walk=function(e){return this.each((function(t,r){var n=e(t,r);if(!1!==n&&t.length&&(n=t.walk(e)),!1===n)return!1}))},u.walkAttributes=function(e){var t=this;return this.walk((function(r){if(r.type===a.ATTRIBUTE)return e.call(t,r)}))},u.walkClasses=function(e){var t=this;return this.walk((function(r){if(r.type===a.CLASS)return e.call(t,r)}))},u.walkCombinators=function(e){var t=this;return this.walk((function(r){if(r.type===a.COMBINATOR)return e.call(t,r)}))},u.walkComments=function(e){var t=this;return this.walk((function(r){if(r.type===a.COMMENT)return e.call(t,r)}))},u.walkIds=function(e){var t=this;return this.walk((function(r){if(r.type===a.ID)return e.call(t,r)}))},u.walkNesting=function(e){var t=this;return this.walk((function(r){if(r.type===a.NESTING)return e.call(t,r)}))},u.walkPseudos=function(e){var t=this;return this.walk((function(r){if(r.type===a.PSEUDO)return e.call(t,r)}))},u.walkTags=function(e){var t=this;return this.walk((function(r){if(r.type===a.TAG)return e.call(t,r)}))},u.walkUniversals=function(e){var t=this;return this.walk((function(r){if(r.type===a.UNIVERSAL)return e.call(t,r)}))},u.split=function(e){var t=this,r=[];return this.reduce((function(n,o,a){var s=e.call(t,o);return r.push(o),s?(n.push(r),r=[]):a===t.length-1&&n.push(r),n}),[])},u.map=function(e){return this.nodes.map(e)},u.reduce=function(e,t){return this.nodes.reduce(e,t)},u.every=function(e){return this.nodes.every(e)},u.some=function(e){return this.nodes.some(e)},u.filter=function(e){return this.nodes.filter(e)},u.sort=function(e){return this.nodes.sort(e)},u.toString=function(){return this.map(String).join("")},o=n,(s=[{key:"first",get:function(){return this.at(0)}},{key:"last",get:function(){return this.at(this.length-1)}},{key:"length",get:function(){return this.nodes.length}}])&&i(o.prototype,s),Object.defineProperty(o,"prototype",{writable:!1}),n}(o.default);t.default=u,e.exports=t.default},8500:(e,t,r)=>{"use strict";t.__esModule=!0,t.isComment=t.isCombinator=t.isClassName=t.isAttribute=void 0,t.isContainer=function(e){return!(!s(e)||!e.walk)},t.isIdentifier=void 0,t.isNamespace=function(e){return i(e)||v(e)},t.isNesting=void 0,t.isNode=s,t.isPseudo=void 0,t.isPseudoClass=function(e){return m(e)&&!w(e)},t.isPseudoElement=w,t.isUniversal=t.isTag=t.isString=t.isSelector=t.isRoot=void 0;var n,o=r(1581),a=((n={})[o.ATTRIBUTE]=!0,n[o.CLASS]=!0,n[o.COMBINATOR]=!0,n[o.COMMENT]=!0,n[o.ID]=!0,n[o.NESTING]=!0,n[o.PSEUDO]=!0,n[o.ROOT]=!0,n[o.SELECTOR]=!0,n[o.STRING]=!0,n[o.TAG]=!0,n[o.UNIVERSAL]=!0,n);function s(e){return"object"==typeof e&&a[e.type]}function l(e,t){return s(t)&&t.type===e}var i=l.bind(null,o.ATTRIBUTE);t.isAttribute=i;var c=l.bind(null,o.CLASS);t.isClassName=c;var u=l.bind(null,o.COMBINATOR);t.isCombinator=u;var d=l.bind(null,o.COMMENT);t.isComment=d;var p=l.bind(null,o.ID);t.isIdentifier=p;var h=l.bind(null,o.NESTING);t.isNesting=h;var m=l.bind(null,o.PSEUDO);t.isPseudo=m;var f=l.bind(null,o.ROOT);t.isRoot=f;var g=l.bind(null,o.SELECTOR);t.isSelector=g;var b=l.bind(null,o.STRING);t.isString=b;var v=l.bind(null,o.TAG);t.isTag=v;var y=l.bind(null,o.UNIVERSAL);function w(e){return m(e)&&e.value&&(e.value.startsWith("::")||":before"===e.value.toLowerCase()||":after"===e.value.toLowerCase()||":first-letter"===e.value.toLowerCase()||":first-line"===e.value.toLowerCase())}t.isUniversal=y},7071:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(4646))&&n.__esModule?n:{default:n},a=r(1581);function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}var l=function(e){var t,r;function n(t){var r;return(r=e.call(this,t)||this).type=a.ID,r}return r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,s(t,r),n.prototype.valueToString=function(){return"#"+e.prototype.valueToString.call(this)},n}(o.default);t.default=l,e.exports=t.default},680:(e,t,r)=>{"use strict";t.__esModule=!0;var n=r(1581);Object.keys(n).forEach((function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===n[e]||(t[e]=n[e]))}));var o=r(4451);Object.keys(o).forEach((function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===o[e]||(t[e]=o[e]))}));var a=r(8500);Object.keys(a).forEach((function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===a[e]||(t[e]=a[e]))}))},3295:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n=a(r(8937)),o=r(9606);function a(e){return e&&e.__esModule?e:{default:e}}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function l(e,t){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},l(e,t)}var i=function(e){var t,r;function a(){return e.apply(this,arguments)||this}r=e,(t=a).prototype=Object.create(r.prototype),t.prototype.constructor=t,l(t,r);var i,c,u=a.prototype;return u.qualifiedName=function(e){return this.namespace?this.namespaceString+"|"+e:e},u.valueToString=function(){return this.qualifiedName(e.prototype.valueToString.call(this))},i=a,(c=[{key:"namespace",get:function(){return this._namespace},set:function(e){if(!0===e||"*"===e||"&"===e)return this._namespace=e,void(this.raws&&delete this.raws.namespace);var t=(0,n.default)(e,{isIdentifier:!0});this._namespace=e,t!==e?((0,o.ensureObject)(this,"raws"),this.raws.namespace=t):this.raws&&delete this.raws.namespace}},{key:"ns",get:function(){return this._namespace},set:function(e){this.namespace=e}},{key:"namespaceString",get:function(){if(this.namespace){var e=this.stringifyProperty("namespace");return!0===e?"":e}return""}}])&&s(i.prototype,c),Object.defineProperty(i,"prototype",{writable:!1}),a}(a(r(4646)).default);t.default=i,e.exports=t.default},2918:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(4646))&&n.__esModule?n:{default:n},a=r(1581);function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}var l=function(e){var t,r;function n(t){var r;return(r=e.call(this,t)||this).type=a.NESTING,r.value="&",r}return r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,s(t,r),n}(o.default);t.default=l,e.exports=t.default},4646:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n=r(9606);function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var a=function e(t,r){if("object"!=typeof t||null===t)return t;var n=new t.constructor;for(var o in t)if(t.hasOwnProperty(o)){var a=t[o];"parent"===o&&"object"==typeof a?r&&(n[o]=r):n[o]=a instanceof Array?a.map((function(t){return e(t,n)})):e(a,n)}return n},s=function(){function e(e){void 0===e&&(e={}),Object.assign(this,e),this.spaces=this.spaces||{},this.spaces.before=this.spaces.before||"",this.spaces.after=this.spaces.after||""}var t,r,s=e.prototype;return s.remove=function(){return this.parent&&this.parent.removeChild(this),this.parent=void 0,this},s.replaceWith=function(){if(this.parent){for(var e in arguments)this.parent.insertBefore(this,arguments[e]);this.remove()}return this},s.next=function(){return this.parent.at(this.parent.index(this)+1)},s.prev=function(){return this.parent.at(this.parent.index(this)-1)},s.clone=function(e){void 0===e&&(e={});var t=a(this);for(var r in e)t[r]=e[r];return t},s.appendToPropertyAndEscape=function(e,t,r){this.raws||(this.raws={});var n=this[e],o=this.raws[e];this[e]=n+t,o||r!==t?this.raws[e]=(o||n)+r:delete this.raws[e]},s.setPropertyAndEscape=function(e,t,r){this.raws||(this.raws={}),this[e]=t,this.raws[e]=r},s.setPropertyWithoutEscape=function(e,t){this[e]=t,this.raws&&delete this.raws[e]},s.isAtPosition=function(e,t){if(this.source&&this.source.start&&this.source.end)return!(this.source.start.line>e||this.source.end.line<e||this.source.start.line===e&&this.source.start.column>t||this.source.end.line===e&&this.source.end.column<t)},s.stringifyProperty=function(e){return this.raws&&this.raws[e]||this[e]},s.valueToString=function(){return String(this.stringifyProperty("value"))},s.toString=function(){return[this.rawSpaceBefore,this.valueToString(),this.rawSpaceAfter].join("")},t=e,(r=[{key:"rawSpaceBefore",get:function(){var e=this.raws&&this.raws.spaces&&this.raws.spaces.before;return void 0===e&&(e=this.spaces&&this.spaces.before),e||""},set:function(e){(0,n.ensureObject)(this,"raws","spaces"),this.raws.spaces.before=e}},{key:"rawSpaceAfter",get:function(){var e=this.raws&&this.raws.spaces&&this.raws.spaces.after;return void 0===e&&(e=this.spaces.after),e||""},set:function(e){(0,n.ensureObject)(this,"raws","spaces"),this.raws.spaces.after=e}}])&&o(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();t.default=s,e.exports=t.default},7324:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(3631))&&n.__esModule?n:{default:n},a=r(1581);function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}var l=function(e){var t,r;function n(t){var r;return(r=e.call(this,t)||this).type=a.PSEUDO,r}return r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,s(t,r),n.prototype.toString=function(){var e=this.length?"("+this.map(String).join(",")+")":"";return[this.rawSpaceBefore,this.stringifyProperty("value"),e,this.rawSpaceAfter].join("")},n}(o.default);t.default=l,e.exports=t.default},518:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(3631))&&n.__esModule?n:{default:n},a=r(1581);function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function l(e,t){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},l(e,t)}var i=function(e){var t,r;function n(t){var r;return(r=e.call(this,t)||this).type=a.ROOT,r}r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,l(t,r);var o,i,c=n.prototype;return c.toString=function(){var e=this.reduce((function(e,t){return e.push(String(t)),e}),[]).join(",");return this.trailingComma?e+",":e},c.error=function(e,t){return this._error?this._error(e,t):new Error(e)},o=n,(i=[{key:"errorGenerator",set:function(e){this._error=e}}])&&s(o.prototype,i),Object.defineProperty(o,"prototype",{writable:!1}),n}(o.default);t.default=i,e.exports=t.default},339:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(3631))&&n.__esModule?n:{default:n},a=r(1581);function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}var l=function(e){var t,r;function n(t){var r;return(r=e.call(this,t)||this).type=a.SELECTOR,r}return r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,s(t,r),n}(o.default);t.default=l,e.exports=t.default},5799:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(4646))&&n.__esModule?n:{default:n},a=r(1581);function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}var l=function(e){var t,r;function n(t){var r;return(r=e.call(this,t)||this).type=a.STRING,r}return r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,s(t,r),n}(o.default);t.default=l,e.exports=t.default},9720:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(3295))&&n.__esModule?n:{default:n},a=r(1581);function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}var l=function(e){var t,r;function n(t){var r;return(r=e.call(this,t)||this).type=a.TAG,r}return r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,s(t,r),n}(o.default);t.default=l,e.exports=t.default},1581:(e,t)=>{"use strict";t.__esModule=!0,t.UNIVERSAL=t.TAG=t.STRING=t.SELECTOR=t.ROOT=t.PSEUDO=t.NESTING=t.ID=t.COMMENT=t.COMBINATOR=t.CLASS=t.ATTRIBUTE=void 0,t.TAG="tag",t.STRING="string",t.SELECTOR="selector",t.ROOT="root",t.PSEUDO="pseudo",t.NESTING="nesting",t.ID="id",t.COMMENT="comment",t.COMBINATOR="combinator",t.CLASS="class",t.ATTRIBUTE="attribute",t.UNIVERSAL="universal"},1669:(e,t,r)=>{"use strict";t.__esModule=!0,t.default=void 0;var n,o=(n=r(3295))&&n.__esModule?n:{default:n},a=r(1581);function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}var l=function(e){var t,r;function n(t){var r;return(r=e.call(this,t)||this).type=a.UNIVERSAL,r.value="*",r}return r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,s(t,r),n}(o.default);t.default=l,e.exports=t.default},263:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e){return e.sort((function(e,t){return e-t}))},e.exports=t.default},71:(e,t)=>{"use strict";t.__esModule=!0,t.word=t.tilde=t.tab=t.str=t.space=t.slash=t.singleQuote=t.semicolon=t.plus=t.pipe=t.openSquare=t.openParenthesis=t.newline=t.greaterThan=t.feed=t.equals=t.doubleQuote=t.dollar=t.cr=t.comment=t.comma=t.combinator=t.colon=t.closeSquare=t.closeParenthesis=t.caret=t.bang=t.backslash=t.at=t.asterisk=t.ampersand=void 0,t.ampersand=38,t.asterisk=42,t.at=64,t.comma=44,t.colon=58,t.semicolon=59,t.openParenthesis=40,t.closeParenthesis=41,t.openSquare=91,t.closeSquare=93,t.dollar=36,t.tilde=126,t.caret=94,t.plus=43,t.equals=61,t.pipe=124,t.greaterThan=62,t.space=32,t.singleQuote=39,t.doubleQuote=34,t.slash=47,t.bang=33,t.backslash=92,t.cr=13,t.feed=12,t.newline=10,t.tab=9,t.str=39,t.comment=-1,t.word=-2,t.combinator=-3},2648:(e,t,r)=>{"use strict";t.__esModule=!0,t.FIELDS=void 0,t.default=function(e){var t,r,n,o,s,l,i,c,u,p,h,m,f=[],g=e.css.valueOf(),b=g.length,v=-1,y=1,w=0,k=0;function E(t,r){if(!e.safe)throw e.error("Unclosed "+t,y,w-v,w);c=(g+=r).length-1}for(;w<b;){switch((t=g.charCodeAt(w))===a.newline&&(v=w,y+=1),t){case a.space:case a.tab:case a.newline:case a.cr:case a.feed:c=w;do{c+=1,(t=g.charCodeAt(c))===a.newline&&(v=c,y+=1)}while(t===a.space||t===a.newline||t===a.tab||t===a.cr||t===a.feed);m=a.space,n=y,r=c-v-1,k=c;break;case a.plus:case a.greaterThan:case a.tilde:case a.pipe:c=w;do{c+=1,t=g.charCodeAt(c)}while(t===a.plus||t===a.greaterThan||t===a.tilde||t===a.pipe);m=a.combinator,n=y,r=w-v,k=c;break;case a.asterisk:case a.ampersand:case a.bang:case a.comma:case a.equals:case a.dollar:case a.caret:case a.openSquare:case a.closeSquare:case a.colon:case a.semicolon:case a.openParenthesis:case a.closeParenthesis:m=t,n=y,r=w-v,k=(c=w)+1;break;case a.singleQuote:case a.doubleQuote:h=t===a.singleQuote?"'":'"',c=w;do{for(o=!1,-1===(c=g.indexOf(h,c+1))&&E("quote",h),s=c;g.charCodeAt(s-1)===a.backslash;)s-=1,o=!o}while(o);m=a.str,n=y,r=w-v,k=c+1;break;default:t===a.slash&&g.charCodeAt(w+1)===a.asterisk?(0===(c=g.indexOf("*/",w+2)+1)&&E("comment","*/"),(l=(i=g.slice(w,c+1).split("\n")).length-1)>0?(u=y+l,p=c-i[l].length):(u=y,p=v),m=a.comment,y=u,n=u,r=c-p):t===a.slash?(m=t,n=y,r=w-v,k=(c=w)+1):(c=d(g,w),m=a.word,n=y,r=c-v),k=c+1}f.push([m,y,w-v,n,r,w,k]),p&&(v=p,p=null),w=k}return f};var n,o,a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var l=o?Object.getOwnPropertyDescriptor(e,a):null;l&&(l.get||l.set)?Object.defineProperty(n,a,l):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(71));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}for(var l=((n={})[a.tab]=!0,n[a.newline]=!0,n[a.cr]=!0,n[a.feed]=!0,n),i=((o={})[a.space]=!0,o[a.tab]=!0,o[a.newline]=!0,o[a.cr]=!0,o[a.feed]=!0,o[a.ampersand]=!0,o[a.asterisk]=!0,o[a.bang]=!0,o[a.comma]=!0,o[a.colon]=!0,o[a.semicolon]=!0,o[a.openParenthesis]=!0,o[a.closeParenthesis]=!0,o[a.openSquare]=!0,o[a.closeSquare]=!0,o[a.singleQuote]=!0,o[a.doubleQuote]=!0,o[a.plus]=!0,o[a.pipe]=!0,o[a.tilde]=!0,o[a.greaterThan]=!0,o[a.equals]=!0,o[a.dollar]=!0,o[a.caret]=!0,o[a.slash]=!0,o),c={},u=0;u<22;u++)c["0123456789abcdefABCDEF".charCodeAt(u)]=!0;function d(e,t){var r,n=t;do{if(r=e.charCodeAt(n),i[r])return n-1;r===a.backslash?n=p(e,n)+1:n++}while(n<e.length);return n-1}function p(e,t){var r=t,n=e.charCodeAt(r+1);if(l[n]);else if(c[n]){var o=0;do{r++,o++,n=e.charCodeAt(r+1)}while(c[n]&&o<6);o<6&&n===a.space&&r++}else r++;return r}t.FIELDS={TYPE:0,START_LINE:1,START_COL:2,END_LINE:3,END_COL:4,START_POS:5,END_POS:6}},1557:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(;r.length>0;){var o=r.shift();e[o]||(e[o]={}),e=e[o]}},e.exports=t.default},6291:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(;r.length>0;){var o=r.shift();if(!e[o])return;e=e[o]}return e},e.exports=t.default},9606:(e,t,r)=>{"use strict";t.__esModule=!0,t.unesc=t.stripComments=t.getProp=t.ensureObject=void 0;var n=l(r(5286));t.unesc=n.default;var o=l(r(6291));t.getProp=o.default;var a=l(r(1557));t.ensureObject=a.default;var s=l(r(8354));function l(e){return e&&e.__esModule?e:{default:e}}t.stripComments=s.default},8354:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e){for(var t="",r=e.indexOf("/*"),n=0;r>=0;){t+=e.slice(n,r);var o=e.indexOf("*/",r+2);if(o<0)return t;n=o+2,r=e.indexOf("/*",n)}return t+e.slice(n)},e.exports=t.default},5286:(e,t)=>{"use strict";function r(e){for(var t=e.toLowerCase(),r="",n=!1,o=0;o<6&&void 0!==t[o];o++){var a=t.charCodeAt(o);if(n=32===a,!(a>=97&&a<=102||a>=48&&a<=57))break;r+=t[o]}if(0!==r.length){var s=parseInt(r,16);return s>=55296&&s<=57343||0===s||s>1114111?["�",r.length+(n?1:0)]:[String.fromCodePoint(s),r.length+(n?1:0)]}}t.__esModule=!0,t.default=function(e){if(!n.test(e))return e;for(var t="",o=0;o<e.length;o++)if("\\"!==e[o])t+=e[o];else{var a=r(e.slice(o+1,o+7));if(void 0!==a){t+=a[0],o+=a[1];continue}if("\\"===e[o+1]){t+="\\",o++;continue}e.length===o+1&&(t+=e[o])}return t};var n=/\\/;e.exports=t.default},5482:(e,t,r)=>{var n=r(8809),o=r(8449),a=r(9063);function s(e){return this instanceof s?(this.nodes=n(e),this):new s(e)}s.prototype.toString=function(){return Array.isArray(this.nodes)?a(this.nodes):""},s.prototype.walk=function(e,t){return o(this.nodes,e,t),this},s.unit=r(2882),s.walk=o,s.stringify=a,e.exports=s},8809:e=>{var t="(".charCodeAt(0),r=")".charCodeAt(0),n="'".charCodeAt(0),o='"'.charCodeAt(0),a="\\".charCodeAt(0),s="/".charCodeAt(0),l=",".charCodeAt(0),i=":".charCodeAt(0),c="*".charCodeAt(0),u="u".charCodeAt(0),d="U".charCodeAt(0),p="+".charCodeAt(0),h=/^[a-f0-9?-]+$/i;e.exports=function(e){for(var m,f,g,b,v,y,w,k,E,S=[],x=e,C=0,_=x.charCodeAt(C),O=x.length,T=[{nodes:S}],I=0,R="",M="",P="";C<O;)if(_<=32){m=C;do{m+=1,_=x.charCodeAt(m)}while(_<=32);b=x.slice(C,m),g=S[S.length-1],_===r&&I?P=b:g&&"div"===g.type?(g.after=b,g.sourceEndIndex+=b.length):_===l||_===i||_===s&&x.charCodeAt(m+1)!==c&&(!E||E&&"function"===E.type&&"calc"!==E.value)?M=b:S.push({type:"space",sourceIndex:C,sourceEndIndex:m,value:b}),C=m}else if(_===n||_===o){m=C,b={type:"string",sourceIndex:C,quote:f=_===n?"'":'"'};do{if(v=!1,~(m=x.indexOf(f,m+1)))for(y=m;x.charCodeAt(y-1)===a;)y-=1,v=!v;else m=(x+=f).length-1,b.unclosed=!0}while(v);b.value=x.slice(C+1,m),b.sourceEndIndex=b.unclosed?m:m+1,S.push(b),C=m+1,_=x.charCodeAt(C)}else if(_===s&&x.charCodeAt(C+1)===c)b={type:"comment",sourceIndex:C,sourceEndIndex:(m=x.indexOf("*/",C))+2},-1===m&&(b.unclosed=!0,m=x.length,b.sourceEndIndex=m),b.value=x.slice(C+2,m),S.push(b),C=m+2,_=x.charCodeAt(C);else if(_!==s&&_!==c||!E||"function"!==E.type||"calc"!==E.value)if(_===s||_===l||_===i)b=x[C],S.push({type:"div",sourceIndex:C-M.length,sourceEndIndex:C+b.length,value:b,before:M,after:""}),M="",C+=1,_=x.charCodeAt(C);else if(t===_){m=C;do{m+=1,_=x.charCodeAt(m)}while(_<=32);if(k=C,b={type:"function",sourceIndex:C-R.length,value:R,before:x.slice(k+1,m)},C=m,"url"===R&&_!==n&&_!==o){m-=1;do{if(v=!1,~(m=x.indexOf(")",m+1)))for(y=m;x.charCodeAt(y-1)===a;)y-=1,v=!v;else m=(x+=")").length-1,b.unclosed=!0}while(v);w=m;do{w-=1,_=x.charCodeAt(w)}while(_<=32);k<w?(b.nodes=C!==w+1?[{type:"word",sourceIndex:C,sourceEndIndex:w+1,value:x.slice(C,w+1)}]:[],b.unclosed&&w+1!==m?(b.after="",b.nodes.push({type:"space",sourceIndex:w+1,sourceEndIndex:m,value:x.slice(w+1,m)})):(b.after=x.slice(w+1,m),b.sourceEndIndex=m)):(b.after="",b.nodes=[]),C=m+1,b.sourceEndIndex=b.unclosed?m:C,_=x.charCodeAt(C),S.push(b)}else I+=1,b.after="",b.sourceEndIndex=C+1,S.push(b),T.push(b),S=b.nodes=[],E=b;R=""}else if(r===_&&I)C+=1,_=x.charCodeAt(C),E.after=P,E.sourceEndIndex+=P.length,P="",I-=1,T[T.length-1].sourceEndIndex=C,T.pop(),S=(E=T[I]).nodes;else{m=C;do{_===a&&(m+=1),m+=1,_=x.charCodeAt(m)}while(m<O&&!(_<=32||_===n||_===o||_===l||_===i||_===s||_===t||_===c&&E&&"function"===E.type&&"calc"===E.value||_===s&&"function"===E.type&&"calc"===E.value||_===r&&I));b=x.slice(C,m),t===_?R=b:u!==b.charCodeAt(0)&&d!==b.charCodeAt(0)||p!==b.charCodeAt(1)||!h.test(b.slice(2))?S.push({type:"word",sourceIndex:C,sourceEndIndex:m,value:b}):S.push({type:"unicode-range",sourceIndex:C,sourceEndIndex:m,value:b}),C=m}else b=x[C],S.push({type:"word",sourceIndex:C-M.length,sourceEndIndex:C+b.length,value:b}),C+=1,_=x.charCodeAt(C);for(C=T.length-1;C;C-=1)T[C].unclosed=!0,T[C].sourceEndIndex=x.length;return T[0].nodes}},9063:e=>{function t(e,t){var n,o,a=e.type,s=e.value;return t&&void 0!==(o=t(e))?o:"word"===a||"space"===a?s:"string"===a?(n=e.quote||"")+s+(e.unclosed?"":n):"comment"===a?"/*"+s+(e.unclosed?"":"*/"):"div"===a?(e.before||"")+s+(e.after||""):Array.isArray(e.nodes)?(n=r(e.nodes,t),"function"!==a?n:s+"("+(e.before||"")+n+(e.after||"")+(e.unclosed?"":")")):s}function r(e,r){var n,o;if(Array.isArray(e)){for(n="",o=e.length-1;~o;o-=1)n=t(e[o],r)+n;return n}return t(e,r)}e.exports=r},2882:e=>{var t="-".charCodeAt(0),r="+".charCodeAt(0),n=".".charCodeAt(0),o="e".charCodeAt(0),a="E".charCodeAt(0);e.exports=function(e){var s,l,i,c=0,u=e.length;if(0===u||!function(e){var o,a=e.charCodeAt(0);if(a===r||a===t){if((o=e.charCodeAt(1))>=48&&o<=57)return!0;var s=e.charCodeAt(2);return o===n&&s>=48&&s<=57}return a===n?(o=e.charCodeAt(1))>=48&&o<=57:a>=48&&a<=57}(e))return!1;for((s=e.charCodeAt(c))!==r&&s!==t||c++;c<u&&!((s=e.charCodeAt(c))<48||s>57);)c+=1;if(s=e.charCodeAt(c),l=e.charCodeAt(c+1),s===n&&l>=48&&l<=57)for(c+=2;c<u&&!((s=e.charCodeAt(c))<48||s>57);)c+=1;if(s=e.charCodeAt(c),l=e.charCodeAt(c+1),i=e.charCodeAt(c+2),(s===o||s===a)&&(l>=48&&l<=57||(l===r||l===t)&&i>=48&&i<=57))for(c+=l===r||l===t?3:2;c<u&&!((s=e.charCodeAt(c))<48||s>57);)c+=1;return{number:e.slice(0,c),unit:e.slice(c)}}},8449:e=>{e.exports=function e(t,r,n){var o,a,s,l;for(o=0,a=t.length;o<a;o+=1)s=t[o],n||(l=r(s,o,t)),!1!==l&&"function"===s.type&&Array.isArray(s.nodes)&&e(s.nodes,r,n),n&&r(s,o,t)}},396:(e,t,r)=>{"use strict";let n=r(7793);class o extends n{constructor(e){super(e),this.type="atrule"}append(...e){return this.proxyOf.nodes||(this.nodes=[]),super.append(...e)}prepend(...e){return this.proxyOf.nodes||(this.nodes=[]),super.prepend(...e)}}e.exports=o,o.default=o,n.registerAtRule(o)},9371:(e,t,r)=>{"use strict";let n=r(3152);class o extends n{constructor(e){super(e),this.type="comment"}}e.exports=o,o.default=o},7793:(e,t,r)=>{"use strict";let n,o,a,s,{isClean:l,my:i}=r(4151),c=r(5238),u=r(9371),d=r(3152);function p(e){return e.map((e=>(e.nodes&&(e.nodes=p(e.nodes)),delete e.source,e)))}function h(e){if(e[l]=!1,e.proxyOf.nodes)for(let t of e.proxyOf.nodes)h(t)}class m extends d{append(...e){for(let t of e){let e=this.normalize(t,this.last);for(let t of e)this.proxyOf.nodes.push(t)}return this.markDirty(),this}cleanRaws(e){if(super.cleanRaws(e),this.nodes)for(let t of this.nodes)t.cleanRaws(e)}each(e){if(!this.proxyOf.nodes)return;let t,r,n=this.getIterator();for(;this.indexes[n]<this.proxyOf.nodes.length&&(t=this.indexes[n],r=e(this.proxyOf.nodes[t],t),!1!==r);)this.indexes[n]+=1;return delete this.indexes[n],r}every(e){return this.nodes.every(e)}getIterator(){this.lastEach||(this.lastEach=0),this.indexes||(this.indexes={}),this.lastEach+=1;let e=this.lastEach;return this.indexes[e]=0,e}getProxyProcessor(){return{get:(e,t)=>"proxyOf"===t?e:e[t]?"each"===t||"string"==typeof t&&t.startsWith("walk")?(...r)=>e[t](...r.map((e=>"function"==typeof e?(t,r)=>e(t.toProxy(),r):e))):"every"===t||"some"===t?r=>e[t](((e,...t)=>r(e.toProxy(),...t))):"root"===t?()=>e.root().toProxy():"nodes"===t?e.nodes.map((e=>e.toProxy())):"first"===t||"last"===t?e[t].toProxy():e[t]:e[t],set:(e,t,r)=>(e[t]===r||(e[t]=r,"name"!==t&&"params"!==t&&"selector"!==t||e.markDirty()),!0)}}index(e){return"number"==typeof e?e:(e.proxyOf&&(e=e.proxyOf),this.proxyOf.nodes.indexOf(e))}insertAfter(e,t){let r,n=this.index(e),o=this.normalize(t,this.proxyOf.nodes[n]).reverse();n=this.index(e);for(let e of o)this.proxyOf.nodes.splice(n+1,0,e);for(let e in this.indexes)r=this.indexes[e],n<r&&(this.indexes[e]=r+o.length);return this.markDirty(),this}insertBefore(e,t){let r,n=this.index(e),o=0===n&&"prepend",a=this.normalize(t,this.proxyOf.nodes[n],o).reverse();n=this.index(e);for(let e of a)this.proxyOf.nodes.splice(n,0,e);for(let e in this.indexes)r=this.indexes[e],n<=r&&(this.indexes[e]=r+a.length);return this.markDirty(),this}normalize(e,t){if("string"==typeof e)e=p(n(e).nodes);else if(void 0===e)e=[];else if(Array.isArray(e)){e=e.slice(0);for(let t of e)t.parent&&t.parent.removeChild(t,"ignore")}else if("root"===e.type&&"document"!==this.type){e=e.nodes.slice(0);for(let t of e)t.parent&&t.parent.removeChild(t,"ignore")}else if(e.type)e=[e];else if(e.prop){if(void 0===e.value)throw new Error("Value field is missed in node creation");"string"!=typeof e.value&&(e.value=String(e.value)),e=[new c(e)]}else if(e.selector||e.selectors)e=[new o(e)];else if(e.name)e=[new a(e)];else{if(!e.text)throw new Error("Unknown node type in node creation");e=[new u(e)]}return e.map((e=>(e[i]||m.rebuild(e),(e=e.proxyOf).parent&&e.parent.removeChild(e),e[l]&&h(e),void 0===e.raws.before&&t&&void 0!==t.raws.before&&(e.raws.before=t.raws.before.replace(/\S/g,"")),e.parent=this.proxyOf,e)))}prepend(...e){e=e.reverse();for(let t of e){let e=this.normalize(t,this.first,"prepend").reverse();for(let t of e)this.proxyOf.nodes.unshift(t);for(let t in this.indexes)this.indexes[t]=this.indexes[t]+e.length}return this.markDirty(),this}push(e){return e.parent=this,this.proxyOf.nodes.push(e),this}removeAll(){for(let e of this.proxyOf.nodes)e.parent=void 0;return this.proxyOf.nodes=[],this.markDirty(),this}removeChild(e){let t;e=this.index(e),this.proxyOf.nodes[e].parent=void 0,this.proxyOf.nodes.splice(e,1);for(let r in this.indexes)t=this.indexes[r],t>=e&&(this.indexes[r]=t-1);return this.markDirty(),this}replaceValues(e,t,r){return r||(r=t,t={}),this.walkDecls((n=>{t.props&&!t.props.includes(n.prop)||t.fast&&!n.value.includes(t.fast)||(n.value=n.value.replace(e,r))})),this.markDirty(),this}some(e){return this.nodes.some(e)}walk(e){return this.each(((t,r)=>{let n;try{n=e(t,r)}catch(e){throw t.addToError(e)}return!1!==n&&t.walk&&(n=t.walk(e)),n}))}walkAtRules(e,t){return t?e instanceof RegExp?this.walk(((r,n)=>{if("atrule"===r.type&&e.test(r.name))return t(r,n)})):this.walk(((r,n)=>{if("atrule"===r.type&&r.name===e)return t(r,n)})):(t=e,this.walk(((e,r)=>{if("atrule"===e.type)return t(e,r)})))}walkComments(e){return this.walk(((t,r)=>{if("comment"===t.type)return e(t,r)}))}walkDecls(e,t){return t?e instanceof RegExp?this.walk(((r,n)=>{if("decl"===r.type&&e.test(r.prop))return t(r,n)})):this.walk(((r,n)=>{if("decl"===r.type&&r.prop===e)return t(r,n)})):(t=e,this.walk(((e,r)=>{if("decl"===e.type)return t(e,r)})))}walkRules(e,t){return t?e instanceof RegExp?this.walk(((r,n)=>{if("rule"===r.type&&e.test(r.selector))return t(r,n)})):this.walk(((r,n)=>{if("rule"===r.type&&r.selector===e)return t(r,n)})):(t=e,this.walk(((e,r)=>{if("rule"===e.type)return t(e,r)})))}get first(){if(this.proxyOf.nodes)return this.proxyOf.nodes[0]}get last(){if(this.proxyOf.nodes)return this.proxyOf.nodes[this.proxyOf.nodes.length-1]}}m.registerParse=e=>{n=e},m.registerRule=e=>{o=e},m.registerAtRule=e=>{a=e},m.registerRoot=e=>{s=e},e.exports=m,m.default=m,m.rebuild=e=>{"atrule"===e.type?Object.setPrototypeOf(e,a.prototype):"rule"===e.type?Object.setPrototypeOf(e,o.prototype):"decl"===e.type?Object.setPrototypeOf(e,c.prototype):"comment"===e.type?Object.setPrototypeOf(e,u.prototype):"root"===e.type&&Object.setPrototypeOf(e,s.prototype),e[i]=!0,e.nodes&&e.nodes.forEach((e=>{m.rebuild(e)}))}},3614:(e,t,r)=>{"use strict";let n=r(8633),o=r(9746);class a extends Error{constructor(e,t,r,n,o,s){super(e),this.name="CssSyntaxError",this.reason=e,o&&(this.file=o),n&&(this.source=n),s&&(this.plugin=s),void 0!==t&&void 0!==r&&("number"==typeof t?(this.line=t,this.column=r):(this.line=t.line,this.column=t.column,this.endLine=r.line,this.endColumn=r.column)),this.setMessage(),Error.captureStackTrace&&Error.captureStackTrace(this,a)}setMessage(){this.message=this.plugin?this.plugin+": ":"",this.message+=this.file?this.file:"<css input>",void 0!==this.line&&(this.message+=":"+this.line+":"+this.column),this.message+=": "+this.reason}showSourceCode(e){if(!this.source)return"";let t=this.source;null==e&&(e=n.isColorSupported),o&&e&&(t=o(t));let r,a,s=t.split(/\r?\n/),l=Math.max(this.line-3,0),i=Math.min(this.line+2,s.length),c=String(i).length;if(e){let{bold:e,gray:t,red:o}=n.createColors(!0);r=t=>e(o(t)),a=e=>t(e)}else r=a=e=>e;return s.slice(l,i).map(((e,t)=>{let n=l+1+t,o=" "+(" "+n).slice(-c)+" | ";if(n===this.line){let t=a(o.replace(/\d/g," "))+e.slice(0,this.column-1).replace(/[^\t]/g," ");return r(">")+a(o)+e+"\n "+t+r("^")}return" "+a(o)+e})).join("\n")}toString(){let e=this.showSourceCode();return e&&(e="\n\n"+e+"\n"),this.name+": "+this.message+e}}e.exports=a,a.default=a},5238:(e,t,r)=>{"use strict";let n=r(3152);class o extends n{constructor(e){e&&void 0!==e.value&&"string"!=typeof e.value&&(e={...e,value:String(e.value)}),super(e),this.type="decl"}get variable(){return this.prop.startsWith("--")||"$"===this.prop[0]}}e.exports=o,o.default=o},145:(e,t,r)=>{"use strict";let n,o,a=r(7793);class s extends a{constructor(e){super({type:"document",...e}),this.nodes||(this.nodes=[])}toResult(e={}){return new n(new o,this,e).stringify()}}s.registerLazyResult=e=>{n=e},s.registerProcessor=e=>{o=e},e.exports=s,s.default=s},3438:(e,t,r)=>{"use strict";let n=r(5238),o=r(3878),a=r(9371),s=r(396),l=r(1106),i=r(5644),c=r(1534);function u(e,t){if(Array.isArray(e))return e.map((e=>u(e)));let{inputs:r,...d}=e;if(r){t=[];for(let e of r){let r={...e,__proto__:l.prototype};r.map&&(r.map={...r.map,__proto__:o.prototype}),t.push(r)}}if(d.nodes&&(d.nodes=e.nodes.map((e=>u(e,t)))),d.source){let{inputId:e,...r}=d.source;d.source=r,null!=e&&(d.source.input=t[e])}if("root"===d.type)return new i(d);if("decl"===d.type)return new n(d);if("rule"===d.type)return new c(d);if("comment"===d.type)return new a(d);if("atrule"===d.type)return new s(d);throw new Error("Unknown node type: "+e.type)}e.exports=u,u.default=u},1106:(e,t,r)=>{"use strict";let{SourceMapConsumer:n,SourceMapGenerator:o}=r(1866),{fileURLToPath:a,pathToFileURL:s}=r(2739),{isAbsolute:l,resolve:i}=r(197),{nanoid:c}=r(5042),u=r(9746),d=r(3614),p=r(3878),h=Symbol("fromOffsetCache"),m=Boolean(n&&o),f=Boolean(i&&l);class g{constructor(e,t={}){if(null==e||"object"==typeof e&&!e.toString)throw new Error(`PostCSS received ${e} instead of CSS string`);if(this.css=e.toString(),"\ufeff"===this.css[0]||"￾"===this.css[0]?(this.hasBOM=!0,this.css=this.css.slice(1)):this.hasBOM=!1,t.from&&(!f||/^\w+:\/\//.test(t.from)||l(t.from)?this.file=t.from:this.file=i(t.from)),f&&m){let e=new p(this.css,t);if(e.text){this.map=e;let t=e.consumer().file;!this.file&&t&&(this.file=this.mapResolve(t))}}this.file||(this.id="<input css "+c(6)+">"),this.map&&(this.map.file=this.from)}error(e,t,r,n={}){let o,a,l;if(t&&"object"==typeof t){let e=t,n=r;if("number"==typeof e.offset){let n=this.fromOffset(e.offset);t=n.line,r=n.col}else t=e.line,r=e.column;if("number"==typeof n.offset){let e=this.fromOffset(n.offset);a=e.line,l=e.col}else a=n.line,l=n.column}else if(!r){let e=this.fromOffset(t);t=e.line,r=e.col}let i=this.origin(t,r,a,l);return o=i?new d(e,void 0===i.endLine?i.line:{column:i.column,line:i.line},void 0===i.endLine?i.column:{column:i.endColumn,line:i.endLine},i.source,i.file,n.plugin):new d(e,void 0===a?t:{column:r,line:t},void 0===a?r:{column:l,line:a},this.css,this.file,n.plugin),o.input={column:r,endColumn:l,endLine:a,line:t,source:this.css},this.file&&(s&&(o.input.url=s(this.file).toString()),o.input.file=this.file),o}fromOffset(e){let t,r;if(this[h])r=this[h];else{let e=this.css.split("\n");r=new Array(e.length);let t=0;for(let n=0,o=e.length;n<o;n++)r[n]=t,t+=e[n].length+1;this[h]=r}t=r[r.length-1];let n=0;if(e>=t)n=r.length-1;else{let t,o=r.length-2;for(;n<o;)if(t=n+(o-n>>1),e<r[t])o=t-1;else{if(!(e>=r[t+1])){n=t;break}n=t+1}}return{col:e-r[n]+1,line:n+1}}mapResolve(e){return/^\w+:\/\//.test(e)?e:i(this.map.consumer().sourceRoot||this.map.root||".",e)}origin(e,t,r,n){if(!this.map)return!1;let o,i,c=this.map.consumer(),u=c.originalPositionFor({column:t,line:e});if(!u.source)return!1;"number"==typeof r&&(o=c.originalPositionFor({column:n,line:r})),i=l(u.source)?s(u.source):new URL(u.source,this.map.consumer().sourceRoot||s(this.map.mapFile));let d={column:u.column,endColumn:o&&o.column,endLine:o&&o.line,line:u.line,url:i.toString()};if("file:"===i.protocol){if(!a)throw new Error("file: protocol is not available in this PostCSS build");d.file=a(i)}let p=c.sourceContentFor(u.source);return p&&(d.source=p),d}toJSON(){let e={};for(let t of["hasBOM","css","file","id"])null!=this[t]&&(e[t]=this[t]);return this.map&&(e.map={...this.map},e.map.consumerCache&&(e.map.consumerCache=void 0)),e}get from(){return this.file||this.id}}e.exports=g,g.default=g,u&&u.registerInput&&u.registerInput(g)},6966:(e,t,r)=>{"use strict";let{isClean:n,my:o}=r(4151),a=r(3604),s=r(3303),l=r(7793),i=r(145),c=(r(6156),r(3717)),u=r(9577),d=r(5644);const p={atrule:"AtRule",comment:"Comment",decl:"Declaration",document:"Document",root:"Root",rule:"Rule"},h={AtRule:!0,AtRuleExit:!0,Comment:!0,CommentExit:!0,Declaration:!0,DeclarationExit:!0,Document:!0,DocumentExit:!0,Once:!0,OnceExit:!0,postcssPlugin:!0,prepare:!0,Root:!0,RootExit:!0,Rule:!0,RuleExit:!0},m={Once:!0,postcssPlugin:!0,prepare:!0};function f(e){return"object"==typeof e&&"function"==typeof e.then}function g(e){let t=!1,r=p[e.type];return"decl"===e.type?t=e.prop.toLowerCase():"atrule"===e.type&&(t=e.name.toLowerCase()),t&&e.append?[r,r+"-"+t,0,r+"Exit",r+"Exit-"+t]:t?[r,r+"-"+t,r+"Exit",r+"Exit-"+t]:e.append?[r,0,r+"Exit"]:[r,r+"Exit"]}function b(e){let t;return t="document"===e.type?["Document",0,"DocumentExit"]:"root"===e.type?["Root",0,"RootExit"]:g(e),{eventIndex:0,events:t,iterator:0,node:e,visitorIndex:0,visitors:[]}}function v(e){return e[n]=!1,e.nodes&&e.nodes.forEach((e=>v(e))),e}let y={};class w{constructor(e,t,r){let n;if(this.stringified=!1,this.processed=!1,"object"!=typeof t||null===t||"root"!==t.type&&"document"!==t.type)if(t instanceof w||t instanceof c)n=v(t.root),t.map&&(void 0===r.map&&(r.map={}),r.map.inline||(r.map.inline=!1),r.map.prev=t.map);else{let e=u;r.syntax&&(e=r.syntax.parse),r.parser&&(e=r.parser),e.parse&&(e=e.parse);try{n=e(t,r)}catch(e){this.processed=!0,this.error=e}n&&!n[o]&&l.rebuild(n)}else n=v(t);this.result=new c(e,n,r),this.helpers={...y,postcss:y,result:this.result},this.plugins=this.processor.plugins.map((e=>"object"==typeof e&&e.prepare?{...e,...e.prepare(this.result)}:e))}async(){return this.error?Promise.reject(this.error):this.processed?Promise.resolve(this.result):(this.processing||(this.processing=this.runAsync()),this.processing)}catch(e){return this.async().catch(e)}finally(e){return this.async().then(e,e)}getAsyncError(){throw new Error("Use process(css).then(cb) to work with async plugins")}handleError(e,t){let r=this.result.lastPlugin;try{t&&t.addToError(e),this.error=e,"CssSyntaxError"!==e.name||e.plugin?r.postcssVersion:(e.plugin=r.postcssPlugin,e.setMessage())}catch(e){console&&console.error&&console.error(e)}return e}prepareVisitors(){this.listeners={};let e=(e,t,r)=>{this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push([e,r])};for(let t of this.plugins)if("object"==typeof t)for(let r in t){if(!h[r]&&/^[A-Z]/.test(r))throw new Error(`Unknown event ${r} in ${t.postcssPlugin}. Try to update PostCSS (${this.processor.version} now).`);if(!m[r])if("object"==typeof t[r])for(let n in t[r])e(t,"*"===n?r:r+"-"+n.toLowerCase(),t[r][n]);else"function"==typeof t[r]&&e(t,r,t[r])}this.hasListener=Object.keys(this.listeners).length>0}async runAsync(){this.plugin=0;for(let e=0;e<this.plugins.length;e++){let t=this.plugins[e],r=this.runOnRoot(t);if(f(r))try{await r}catch(e){throw this.handleError(e)}}if(this.prepareVisitors(),this.hasListener){let e=this.result.root;for(;!e[n];){e[n]=!0;let t=[b(e)];for(;t.length>0;){let e=this.visitTick(t);if(f(e))try{await e}catch(e){let r=t[t.length-1].node;throw this.handleError(e,r)}}}if(this.listeners.OnceExit)for(let[t,r]of this.listeners.OnceExit){this.result.lastPlugin=t;try{if("document"===e.type){let t=e.nodes.map((e=>r(e,this.helpers)));await Promise.all(t)}else await r(e,this.helpers)}catch(e){throw this.handleError(e)}}}return this.processed=!0,this.stringify()}runOnRoot(e){this.result.lastPlugin=e;try{if("object"==typeof e&&e.Once){if("document"===this.result.root.type){let t=this.result.root.nodes.map((t=>e.Once(t,this.helpers)));return f(t[0])?Promise.all(t):t}return e.Once(this.result.root,this.helpers)}if("function"==typeof e)return e(this.result.root,this.result)}catch(e){throw this.handleError(e)}}stringify(){if(this.error)throw this.error;if(this.stringified)return this.result;this.stringified=!0,this.sync();let e=this.result.opts,t=s;e.syntax&&(t=e.syntax.stringify),e.stringifier&&(t=e.stringifier),t.stringify&&(t=t.stringify);let r=new a(t,this.result.root,this.result.opts).generate();return this.result.css=r[0],this.result.map=r[1],this.result}sync(){if(this.error)throw this.error;if(this.processed)return this.result;if(this.processed=!0,this.processing)throw this.getAsyncError();for(let e of this.plugins)if(f(this.runOnRoot(e)))throw this.getAsyncError();if(this.prepareVisitors(),this.hasListener){let e=this.result.root;for(;!e[n];)e[n]=!0,this.walkSync(e);if(this.listeners.OnceExit)if("document"===e.type)for(let t of e.nodes)this.visitSync(this.listeners.OnceExit,t);else this.visitSync(this.listeners.OnceExit,e)}return this.result}then(e,t){return this.async().then(e,t)}toString(){return this.css}visitSync(e,t){for(let[r,n]of e){let e;this.result.lastPlugin=r;try{e=n(t,this.helpers)}catch(e){throw this.handleError(e,t.proxyOf)}if("root"!==t.type&&"document"!==t.type&&!t.parent)return!0;if(f(e))throw this.getAsyncError()}}visitTick(e){let t=e[e.length-1],{node:r,visitors:o}=t;if("root"!==r.type&&"document"!==r.type&&!r.parent)return void e.pop();if(o.length>0&&t.visitorIndex<o.length){let[e,n]=o[t.visitorIndex];t.visitorIndex+=1,t.visitorIndex===o.length&&(t.visitors=[],t.visitorIndex=0),this.result.lastPlugin=e;try{return n(r.toProxy(),this.helpers)}catch(e){throw this.handleError(e,r)}}if(0!==t.iterator){let o,a=t.iterator;for(;o=r.nodes[r.indexes[a]];)if(r.indexes[a]+=1,!o[n])return o[n]=!0,void e.push(b(o));t.iterator=0,delete r.indexes[a]}let a=t.events;for(;t.eventIndex<a.length;){let e=a[t.eventIndex];if(t.eventIndex+=1,0===e)return void(r.nodes&&r.nodes.length&&(r[n]=!0,t.iterator=r.getIterator()));if(this.listeners[e])return void(t.visitors=this.listeners[e])}e.pop()}walkSync(e){e[n]=!0;let t=g(e);for(let r of t)if(0===r)e.nodes&&e.each((e=>{e[n]||this.walkSync(e)}));else{let t=this.listeners[r];if(t&&this.visitSync(t,e.toProxy()))return}}warnings(){return this.sync().warnings()}get content(){return this.stringify().content}get css(){return this.stringify().css}get map(){return this.stringify().map}get messages(){return this.sync().messages}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){return this.sync().root}get[Symbol.toStringTag](){return"LazyResult"}}w.registerPostcss=e=>{y=e},e.exports=w,w.default=w,d.registerLazyResult(w),i.registerLazyResult(w)},1752:e=>{"use strict";let t={comma:e=>t.split(e,[","],!0),space:e=>t.split(e,[" ","\n","\t"]),split(e,t,r){let n=[],o="",a=!1,s=0,l=!1,i="",c=!1;for(let r of e)c?c=!1:"\\"===r?c=!0:l?r===i&&(l=!1):'"'===r||"'"===r?(l=!0,i=r):"("===r?s+=1:")"===r?s>0&&(s-=1):0===s&&t.includes(r)&&(a=!0),a?(""!==o&&n.push(o.trim()),o="",a=!1):o+=r;return(r||""!==o)&&n.push(o.trim()),n}};e.exports=t,t.default=t},3604:(e,t,r)=>{"use strict";let{SourceMapConsumer:n,SourceMapGenerator:o}=r(1866),{dirname:a,relative:s,resolve:l,sep:i}=r(197),{pathToFileURL:c}=r(2739),u=r(1106),d=Boolean(n&&o),p=Boolean(a&&l&&s&&i);e.exports=class{constructor(e,t,r,n){this.stringify=e,this.mapOpts=r.map||{},this.root=t,this.opts=r,this.css=n,this.originalCSS=n,this.usesFileUrls=!this.mapOpts.from&&this.mapOpts.absolute,this.memoizedFileURLs=new Map,this.memoizedPaths=new Map,this.memoizedURLs=new Map}addAnnotation(){let e;e=this.isInline()?"data:application/json;base64,"+this.toBase64(this.map.toString()):"string"==typeof this.mapOpts.annotation?this.mapOpts.annotation:"function"==typeof this.mapOpts.annotation?this.mapOpts.annotation(this.opts.to,this.root):this.outputFile()+".map";let t="\n";this.css.includes("\r\n")&&(t="\r\n"),this.css+=t+"/*# sourceMappingURL="+e+" */"}applyPrevMaps(){for(let e of this.previous()){let t,r=this.toUrl(this.path(e.file)),o=e.root||a(e.file);!1===this.mapOpts.sourcesContent?(t=new n(e.text),t.sourcesContent&&(t.sourcesContent=null)):t=e.consumer(),this.map.applySourceMap(t,r,this.toUrl(this.path(o)))}}clearAnnotation(){if(!1!==this.mapOpts.annotation)if(this.root){let e;for(let t=this.root.nodes.length-1;t>=0;t--)e=this.root.nodes[t],"comment"===e.type&&0===e.text.indexOf("# sourceMappingURL=")&&this.root.removeChild(t)}else this.css&&(this.css=this.css.replace(/\n*\/\*#[\S\s]*?\*\/$/gm,""))}generate(){if(this.clearAnnotation(),p&&d&&this.isMap())return this.generateMap();{let e="";return this.stringify(this.root,(t=>{e+=t})),[e]}}generateMap(){if(this.root)this.generateString();else if(1===this.previous().length){let e=this.previous()[0].consumer();e.file=this.outputFile(),this.map=o.fromSourceMap(e,{ignoreInvalidMapping:!0})}else this.map=new o({file:this.outputFile(),ignoreInvalidMapping:!0}),this.map.addMapping({generated:{column:0,line:1},original:{column:0,line:1},source:this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>"});return this.isSourcesContent()&&this.setSourcesContent(),this.root&&this.previous().length>0&&this.applyPrevMaps(),this.isAnnotation()&&this.addAnnotation(),this.isInline()?[this.css]:[this.css,this.map]}generateString(){this.css="",this.map=new o({file:this.outputFile(),ignoreInvalidMapping:!0});let e,t,r=1,n=1,a="<no source>",s={generated:{column:0,line:0},original:{column:0,line:0},source:""};this.stringify(this.root,((o,l,i)=>{if(this.css+=o,l&&"end"!==i&&(s.generated.line=r,s.generated.column=n-1,l.source&&l.source.start?(s.source=this.sourcePath(l),s.original.line=l.source.start.line,s.original.column=l.source.start.column-1,this.map.addMapping(s)):(s.source=a,s.original.line=1,s.original.column=0,this.map.addMapping(s))),e=o.match(/\n/g),e?(r+=e.length,t=o.lastIndexOf("\n"),n=o.length-t):n+=o.length,l&&"start"!==i){let e=l.parent||{raws:{}};("decl"===l.type||"atrule"===l.type&&!l.nodes)&&l===e.last&&!e.raws.semicolon||(l.source&&l.source.end?(s.source=this.sourcePath(l),s.original.line=l.source.end.line,s.original.column=l.source.end.column-1,s.generated.line=r,s.generated.column=n-2,this.map.addMapping(s)):(s.source=a,s.original.line=1,s.original.column=0,s.generated.line=r,s.generated.column=n-1,this.map.addMapping(s)))}}))}isAnnotation(){return!!this.isInline()||(void 0!==this.mapOpts.annotation?this.mapOpts.annotation:!this.previous().length||this.previous().some((e=>e.annotation)))}isInline(){if(void 0!==this.mapOpts.inline)return this.mapOpts.inline;let e=this.mapOpts.annotation;return(void 0===e||!0===e)&&(!this.previous().length||this.previous().some((e=>e.inline)))}isMap(){return void 0!==this.opts.map?!!this.opts.map:this.previous().length>0}isSourcesContent(){return void 0!==this.mapOpts.sourcesContent?this.mapOpts.sourcesContent:!this.previous().length||this.previous().some((e=>e.withContent()))}outputFile(){return this.opts.to?this.path(this.opts.to):this.opts.from?this.path(this.opts.from):"to.css"}path(e){if(this.mapOpts.absolute)return e;if(60===e.charCodeAt(0))return e;if(/^\w+:\/\//.test(e))return e;let t=this.memoizedPaths.get(e);if(t)return t;let r=this.opts.to?a(this.opts.to):".";"string"==typeof this.mapOpts.annotation&&(r=a(l(r,this.mapOpts.annotation)));let n=s(r,e);return this.memoizedPaths.set(e,n),n}previous(){if(!this.previousMaps)if(this.previousMaps=[],this.root)this.root.walk((e=>{if(e.source&&e.source.input.map){let t=e.source.input.map;this.previousMaps.includes(t)||this.previousMaps.push(t)}}));else{let e=new u(this.originalCSS,this.opts);e.map&&this.previousMaps.push(e.map)}return this.previousMaps}setSourcesContent(){let e={};if(this.root)this.root.walk((t=>{if(t.source){let r=t.source.input.from;if(r&&!e[r]){e[r]=!0;let n=this.usesFileUrls?this.toFileUrl(r):this.toUrl(this.path(r));this.map.setSourceContent(n,t.source.input.css)}}}));else if(this.css){let e=this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>";this.map.setSourceContent(e,this.css)}}sourcePath(e){return this.mapOpts.from?this.toUrl(this.mapOpts.from):this.usesFileUrls?this.toFileUrl(e.source.input.from):this.toUrl(this.path(e.source.input.from))}toBase64(e){return Buffer?Buffer.from(e).toString("base64"):window.btoa(unescape(encodeURIComponent(e)))}toFileUrl(e){let t=this.memoizedFileURLs.get(e);if(t)return t;if(c){let t=c(e).toString();return this.memoizedFileURLs.set(e,t),t}throw new Error("`map.absolute` option is not available in this PostCSS build")}toUrl(e){let t=this.memoizedURLs.get(e);if(t)return t;"\\"===i&&(e=e.replace(/\\/g,"/"));let r=encodeURI(e).replace(/[#?]/g,encodeURIComponent);return this.memoizedURLs.set(e,r),r}}},4211:(e,t,r)=>{"use strict";let n=r(3604),o=r(3303),a=(r(6156),r(9577));const s=r(3717);class l{constructor(e,t,r){let a;t=t.toString(),this.stringified=!1,this._processor=e,this._css=t,this._opts=r,this._map=void 0;let l=o;this.result=new s(this._processor,a,this._opts),this.result.css=t;let i=this;Object.defineProperty(this.result,"root",{get:()=>i.root});let c=new n(l,a,this._opts,t);if(c.isMap()){let[e,t]=c.generate();e&&(this.result.css=e),t&&(this.result.map=t)}else c.clearAnnotation(),this.result.css=c.css}async(){return this.error?Promise.reject(this.error):Promise.resolve(this.result)}catch(e){return this.async().catch(e)}finally(e){return this.async().then(e,e)}sync(){if(this.error)throw this.error;return this.result}then(e,t){return this.async().then(e,t)}toString(){return this._css}warnings(){return[]}get content(){return this.result.css}get css(){return this.result.css}get map(){return this.result.map}get messages(){return[]}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){if(this._root)return this._root;let e,t=a;try{e=t(this._css,this._opts)}catch(e){this.error=e}if(this.error)throw this.error;return this._root=e,e}get[Symbol.toStringTag](){return"NoWorkResult"}}e.exports=l,l.default=l},3152:(e,t,r)=>{"use strict";let{isClean:n,my:o}=r(4151),a=r(3614),s=r(7668),l=r(3303);function i(e,t){let r=new e.constructor;for(let n in e){if(!Object.prototype.hasOwnProperty.call(e,n))continue;if("proxyCache"===n)continue;let o=e[n],a=typeof o;"parent"===n&&"object"===a?t&&(r[n]=t):"source"===n?r[n]=o:Array.isArray(o)?r[n]=o.map((e=>i(e,r))):("object"===a&&null!==o&&(o=i(o)),r[n]=o)}return r}class c{constructor(e={}){this.raws={},this[n]=!1,this[o]=!0;for(let t in e)if("nodes"===t){this.nodes=[];for(let r of e[t])"function"==typeof r.clone?this.append(r.clone()):this.append(r)}else this[t]=e[t]}addToError(e){if(e.postcssNode=this,e.stack&&this.source&&/\n\s{4}at /.test(e.stack)){let t=this.source;e.stack=e.stack.replace(/\n\s{4}at /,`$&${t.input.from}:${t.start.line}:${t.start.column}$&`)}return e}after(e){return this.parent.insertAfter(this,e),this}assign(e={}){for(let t in e)this[t]=e[t];return this}before(e){return this.parent.insertBefore(this,e),this}cleanRaws(e){delete this.raws.before,delete this.raws.after,e||delete this.raws.between}clone(e={}){let t=i(this);for(let r in e)t[r]=e[r];return t}cloneAfter(e={}){let t=this.clone(e);return this.parent.insertAfter(this,t),t}cloneBefore(e={}){let t=this.clone(e);return this.parent.insertBefore(this,t),t}error(e,t={}){if(this.source){let{end:r,start:n}=this.rangeBy(t);return this.source.input.error(e,{column:n.column,line:n.line},{column:r.column,line:r.line},t)}return new a(e)}getProxyProcessor(){return{get:(e,t)=>"proxyOf"===t?e:"root"===t?()=>e.root().toProxy():e[t],set:(e,t,r)=>(e[t]===r||(e[t]=r,"prop"!==t&&"value"!==t&&"name"!==t&&"params"!==t&&"important"!==t&&"text"!==t||e.markDirty()),!0)}}markDirty(){if(this[n]){this[n]=!1;let e=this;for(;e=e.parent;)e[n]=!1}}next(){if(!this.parent)return;let e=this.parent.index(this);return this.parent.nodes[e+1]}positionBy(e,t){let r=this.source.start;if(e.index)r=this.positionInside(e.index,t);else if(e.word){let n=(t=this.toString()).indexOf(e.word);-1!==n&&(r=this.positionInside(n,t))}return r}positionInside(e,t){let r=t||this.toString(),n=this.source.start.column,o=this.source.start.line;for(let t=0;t<e;t++)"\n"===r[t]?(n=1,o+=1):n+=1;return{column:n,line:o}}prev(){if(!this.parent)return;let e=this.parent.index(this);return this.parent.nodes[e-1]}rangeBy(e){let t={column:this.source.start.column,line:this.source.start.line},r=this.source.end?{column:this.source.end.column+1,line:this.source.end.line}:{column:t.column+1,line:t.line};if(e.word){let n=this.toString(),o=n.indexOf(e.word);-1!==o&&(t=this.positionInside(o,n),r=this.positionInside(o+e.word.length,n))}else e.start?t={column:e.start.column,line:e.start.line}:e.index&&(t=this.positionInside(e.index)),e.end?r={column:e.end.column,line:e.end.line}:"number"==typeof e.endIndex?r=this.positionInside(e.endIndex):e.index&&(r=this.positionInside(e.index+1));return(r.line<t.line||r.line===t.line&&r.column<=t.column)&&(r={column:t.column+1,line:t.line}),{end:r,start:t}}raw(e,t){return(new s).raw(this,e,t)}remove(){return this.parent&&this.parent.removeChild(this),this.parent=void 0,this}replaceWith(...e){if(this.parent){let t=this,r=!1;for(let n of e)n===this?r=!0:r?(this.parent.insertAfter(t,n),t=n):this.parent.insertBefore(t,n);r||this.remove()}return this}root(){let e=this;for(;e.parent&&"document"!==e.parent.type;)e=e.parent;return e}toJSON(e,t){let r={},n=null==t;t=t||new Map;let o=0;for(let e in this){if(!Object.prototype.hasOwnProperty.call(this,e))continue;if("parent"===e||"proxyCache"===e)continue;let n=this[e];if(Array.isArray(n))r[e]=n.map((e=>"object"==typeof e&&e.toJSON?e.toJSON(null,t):e));else if("object"==typeof n&&n.toJSON)r[e]=n.toJSON(null,t);else if("source"===e){let a=t.get(n.input);null==a&&(a=o,t.set(n.input,o),o++),r[e]={end:n.end,inputId:a,start:n.start}}else r[e]=n}return n&&(r.inputs=[...t.keys()].map((e=>e.toJSON()))),r}toProxy(){return this.proxyCache||(this.proxyCache=new Proxy(this,this.getProxyProcessor())),this.proxyCache}toString(e=l){e.stringify&&(e=e.stringify);let t="";return e(this,(e=>{t+=e})),t}warn(e,t,r){let n={node:this};for(let e in r)n[e]=r[e];return e.warn(t,n)}get proxyOf(){return this}}e.exports=c,c.default=c},9577:(e,t,r)=>{"use strict";let n=r(7793),o=r(8339),a=r(1106);function s(e,t){let r=new a(e,t),n=new o(r);try{n.parse()}catch(e){throw e}return n.root}e.exports=s,s.default=s,n.registerParse(s)},8339:(e,t,r)=>{"use strict";let n=r(5238),o=r(5781),a=r(9371),s=r(396),l=r(5644),i=r(1534);const c={empty:!0,space:!0};e.exports=class{constructor(e){this.input=e,this.root=new l,this.current=this.root,this.spaces="",this.semicolon=!1,this.createTokenizer(),this.root.source={input:e,start:{column:1,line:1,offset:0}}}atrule(e){let t,r,n,o=new s;o.name=e[1].slice(1),""===o.name&&this.unnamedAtrule(o,e),this.init(o,e[2]);let a=!1,l=!1,i=[],c=[];for(;!this.tokenizer.endOfFile();){if(t=(e=this.tokenizer.nextToken())[0],"("===t||"["===t?c.push("("===t?")":"]"):"{"===t&&c.length>0?c.push("}"):t===c[c.length-1]&&c.pop(),0===c.length){if(";"===t){o.source.end=this.getPosition(e[2]),o.source.end.offset++,this.semicolon=!0;break}if("{"===t){l=!0;break}if("}"===t){if(i.length>0){for(n=i.length-1,r=i[n];r&&"space"===r[0];)r=i[--n];r&&(o.source.end=this.getPosition(r[3]||r[2]),o.source.end.offset++)}this.end(e);break}i.push(e)}else i.push(e);if(this.tokenizer.endOfFile()){a=!0;break}}o.raws.between=this.spacesAndCommentsFromEnd(i),i.length?(o.raws.afterName=this.spacesAndCommentsFromStart(i),this.raw(o,"params",i),a&&(e=i[i.length-1],o.source.end=this.getPosition(e[3]||e[2]),o.source.end.offset++,this.spaces=o.raws.between,o.raws.between="")):(o.raws.afterName="",o.params=""),l&&(o.nodes=[],this.current=o)}checkMissedSemicolon(e){let t=this.colon(e);if(!1===t)return;let r,n=0;for(let o=t-1;o>=0&&(r=e[o],"space"===r[0]||(n+=1,2!==n));o--);throw this.input.error("Missed semicolon","word"===r[0]?r[3]+1:r[2])}colon(e){let t,r,n,o=0;for(let[a,s]of e.entries()){if(t=s,r=t[0],"("===r&&(o+=1),")"===r&&(o-=1),0===o&&":"===r){if(n){if("word"===n[0]&&"progid"===n[1])continue;return a}this.doubleColon(t)}n=t}return!1}comment(e){let t=new a;this.init(t,e[2]),t.source.end=this.getPosition(e[3]||e[2]),t.source.end.offset++;let r=e[1].slice(2,-2);if(/^\s*$/.test(r))t.text="",t.raws.left=r,t.raws.right="";else{let e=r.match(/^(\s*)([^]*\S)(\s*)$/);t.text=e[2],t.raws.left=e[1],t.raws.right=e[3]}}createTokenizer(){this.tokenizer=o(this.input)}decl(e,t){let r=new n;this.init(r,e[0][2]);let o,a=e[e.length-1];for(";"===a[0]&&(this.semicolon=!0,e.pop()),r.source.end=this.getPosition(a[3]||a[2]||function(e){for(let t=e.length-1;t>=0;t--){let r=e[t],n=r[3]||r[2];if(n)return n}}(e)),r.source.end.offset++;"word"!==e[0][0];)1===e.length&&this.unknownWord(e),r.raws.before+=e.shift()[1];for(r.source.start=this.getPosition(e[0][2]),r.prop="";e.length;){let t=e[0][0];if(":"===t||"space"===t||"comment"===t)break;r.prop+=e.shift()[1]}for(r.raws.between="";e.length;){if(o=e.shift(),":"===o[0]){r.raws.between+=o[1];break}"word"===o[0]&&/\w/.test(o[1])&&this.unknownWord([o]),r.raws.between+=o[1]}"_"!==r.prop[0]&&"*"!==r.prop[0]||(r.raws.before+=r.prop[0],r.prop=r.prop.slice(1));let s,l=[];for(;e.length&&(s=e[0][0],"space"===s||"comment"===s);)l.push(e.shift());this.precheckMissedSemicolon(e);for(let t=e.length-1;t>=0;t--){if(o=e[t],"!important"===o[1].toLowerCase()){r.important=!0;let n=this.stringFrom(e,t);n=this.spacesFromEnd(e)+n," !important"!==n&&(r.raws.important=n);break}if("important"===o[1].toLowerCase()){let n=e.slice(0),o="";for(let e=t;e>0;e--){let t=n[e][0];if(0===o.trim().indexOf("!")&&"space"!==t)break;o=n.pop()[1]+o}0===o.trim().indexOf("!")&&(r.important=!0,r.raws.important=o,e=n)}if("space"!==o[0]&&"comment"!==o[0])break}e.some((e=>"space"!==e[0]&&"comment"!==e[0]))&&(r.raws.between+=l.map((e=>e[1])).join(""),l=[]),this.raw(r,"value",l.concat(e),t),r.value.includes(":")&&!t&&this.checkMissedSemicolon(e)}doubleColon(e){throw this.input.error("Double colon",{offset:e[2]},{offset:e[2]+e[1].length})}emptyRule(e){let t=new i;this.init(t,e[2]),t.selector="",t.raws.between="",this.current=t}end(e){this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.semicolon=!1,this.current.raws.after=(this.current.raws.after||"")+this.spaces,this.spaces="",this.current.parent?(this.current.source.end=this.getPosition(e[2]),this.current.source.end.offset++,this.current=this.current.parent):this.unexpectedClose(e)}endFile(){this.current.parent&&this.unclosedBlock(),this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.current.raws.after=(this.current.raws.after||"")+this.spaces,this.root.source.end=this.getPosition(this.tokenizer.position())}freeSemicolon(e){if(this.spaces+=e[1],this.current.nodes){let e=this.current.nodes[this.current.nodes.length-1];e&&"rule"===e.type&&!e.raws.ownSemicolon&&(e.raws.ownSemicolon=this.spaces,this.spaces="")}}getPosition(e){let t=this.input.fromOffset(e);return{column:t.col,line:t.line,offset:e}}init(e,t){this.current.push(e),e.source={input:this.input,start:this.getPosition(t)},e.raws.before=this.spaces,this.spaces="","comment"!==e.type&&(this.semicolon=!1)}other(e){let t=!1,r=null,n=!1,o=null,a=[],s=e[1].startsWith("--"),l=[],i=e;for(;i;){if(r=i[0],l.push(i),"("===r||"["===r)o||(o=i),a.push("("===r?")":"]");else if(s&&n&&"{"===r)o||(o=i),a.push("}");else if(0===a.length){if(";"===r){if(n)return void this.decl(l,s);break}if("{"===r)return void this.rule(l);if("}"===r){this.tokenizer.back(l.pop()),t=!0;break}":"===r&&(n=!0)}else r===a[a.length-1]&&(a.pop(),0===a.length&&(o=null));i=this.tokenizer.nextToken()}if(this.tokenizer.endOfFile()&&(t=!0),a.length>0&&this.unclosedBracket(o),t&&n){if(!s)for(;l.length&&(i=l[l.length-1][0],"space"===i||"comment"===i);)this.tokenizer.back(l.pop());this.decl(l,s)}else this.unknownWord(l)}parse(){let e;for(;!this.tokenizer.endOfFile();)switch(e=this.tokenizer.nextToken(),e[0]){case"space":this.spaces+=e[1];break;case";":this.freeSemicolon(e);break;case"}":this.end(e);break;case"comment":this.comment(e);break;case"at-word":this.atrule(e);break;case"{":this.emptyRule(e);break;default:this.other(e)}this.endFile()}precheckMissedSemicolon(){}raw(e,t,r,n){let o,a,s,l,i=r.length,u="",d=!0;for(let e=0;e<i;e+=1)o=r[e],a=o[0],"space"!==a||e!==i-1||n?"comment"===a?(l=r[e-1]?r[e-1][0]:"empty",s=r[e+1]?r[e+1][0]:"empty",c[l]||c[s]||","===u.slice(-1)?d=!1:u+=o[1]):u+=o[1]:d=!1;if(!d){let n=r.reduce(((e,t)=>e+t[1]),"");e.raws[t]={raw:n,value:u}}e[t]=u}rule(e){e.pop();let t=new i;this.init(t,e[0][2]),t.raws.between=this.spacesAndCommentsFromEnd(e),this.raw(t,"selector",e),this.current=t}spacesAndCommentsFromEnd(e){let t,r="";for(;e.length&&(t=e[e.length-1][0],"space"===t||"comment"===t);)r=e.pop()[1]+r;return r}spacesAndCommentsFromStart(e){let t,r="";for(;e.length&&(t=e[0][0],"space"===t||"comment"===t);)r+=e.shift()[1];return r}spacesFromEnd(e){let t,r="";for(;e.length&&(t=e[e.length-1][0],"space"===t);)r=e.pop()[1]+r;return r}stringFrom(e,t){let r="";for(let n=t;n<e.length;n++)r+=e[n][1];return e.splice(t,e.length-t),r}unclosedBlock(){let e=this.current.source.start;throw this.input.error("Unclosed block",e.line,e.column)}unclosedBracket(e){throw this.input.error("Unclosed bracket",{offset:e[2]},{offset:e[2]+1})}unexpectedClose(e){throw this.input.error("Unexpected }",{offset:e[2]},{offset:e[2]+1})}unknownWord(e){throw this.input.error("Unknown word",{offset:e[0][2]},{offset:e[0][2]+e[0][1].length})}unnamedAtrule(e,t){throw this.input.error("At-rule without name",{offset:t[2]},{offset:t[2]+t[1].length})}}},2895:(e,t,r)=>{"use strict";let n=r(3614),o=r(5238),a=r(6966),s=r(7793),l=r(6846),i=r(3303),c=r(3438),u=r(145),d=r(38),p=r(9371),h=r(396),m=r(3717),f=r(1106),g=r(9577),b=r(1752),v=r(1534),y=r(5644),w=r(3152);function k(...e){return 1===e.length&&Array.isArray(e[0])&&(e=e[0]),new l(e)}k.plugin=function(e,t){let r,n=!1;function o(...r){console&&console.warn&&!n&&(n=!0,console.warn(e+": postcss.plugin was deprecated. Migration guide:\nhttps://evilmartians.com/chronicles/postcss-8-plugin-migration"),process.env.LANG&&process.env.LANG.startsWith("cn")&&console.warn(e+": 里面 postcss.plugin 被弃用. 迁移指南:\nhttps://www.w3ctech.com/topic/2226"));let o=t(...r);return o.postcssPlugin=e,o.postcssVersion=(new l).version,o}return Object.defineProperty(o,"postcss",{get:()=>(r||(r=o()),r)}),o.process=function(e,t,r){return k([o(r)]).process(e,t)},o},k.stringify=i,k.parse=g,k.fromJSON=c,k.list=b,k.comment=e=>new p(e),k.atRule=e=>new h(e),k.decl=e=>new o(e),k.rule=e=>new v(e),k.root=e=>new y(e),k.document=e=>new u(e),k.CssSyntaxError=n,k.Declaration=o,k.Container=s,k.Processor=l,k.Document=u,k.Comment=p,k.Warning=d,k.AtRule=h,k.Result=m,k.Input=f,k.Rule=v,k.Root=y,k.Node=w,a.registerPostcss(k),e.exports=k,k.default=k},3878:(e,t,r)=>{"use strict";let{SourceMapConsumer:n,SourceMapGenerator:o}=r(1866),{existsSync:a,readFileSync:s}=r(9977),{dirname:l,join:i}=r(197);class c{constructor(e,t){if(!1===t.map)return;this.loadAnnotation(e),this.inline=this.startWith(this.annotation,"data:");let r=t.map?t.map.prev:void 0,n=this.loadMap(t.from,r);!this.mapFile&&t.from&&(this.mapFile=t.from),this.mapFile&&(this.root=l(this.mapFile)),n&&(this.text=n)}consumer(){return this.consumerCache||(this.consumerCache=new n(this.text)),this.consumerCache}decodeInline(e){let t=e.match(/^data:application\/json;charset=utf-?8,/)||e.match(/^data:application\/json,/);if(t)return decodeURIComponent(e.substr(t[0].length));let r=e.match(/^data:application\/json;charset=utf-?8;base64,/)||e.match(/^data:application\/json;base64,/);if(r)return n=e.substr(r[0].length),Buffer?Buffer.from(n,"base64").toString():window.atob(n);var n;let o=e.match(/data:application\/json;([^,]+),/)[1];throw new Error("Unsupported source map encoding "+o)}getAnnotationURL(e){return e.replace(/^\/\*\s*# sourceMappingURL=/,"").trim()}isMap(e){return"object"==typeof e&&("string"==typeof e.mappings||"string"==typeof e._mappings||Array.isArray(e.sections))}loadAnnotation(e){let t=e.match(/\/\*\s*# sourceMappingURL=/g);if(!t)return;let r=e.lastIndexOf(t.pop()),n=e.indexOf("*/",r);r>-1&&n>-1&&(this.annotation=this.getAnnotationURL(e.substring(r,n)))}loadFile(e){if(this.root=l(e),a(e))return this.mapFile=e,s(e,"utf-8").toString().trim()}loadMap(e,t){if(!1===t)return!1;if(t){if("string"==typeof t)return t;if("function"!=typeof t){if(t instanceof n)return o.fromSourceMap(t).toString();if(t instanceof o)return t.toString();if(this.isMap(t))return JSON.stringify(t);throw new Error("Unsupported previous source map format: "+t.toString())}{let r=t(e);if(r){let e=this.loadFile(r);if(!e)throw new Error("Unable to load previous source map: "+r.toString());return e}}}else{if(this.inline)return this.decodeInline(this.annotation);if(this.annotation){let t=this.annotation;return e&&(t=i(l(e),t)),this.loadFile(t)}}}startWith(e,t){return!!e&&e.substr(0,t.length)===t}withContent(){return!!(this.consumer().sourcesContent&&this.consumer().sourcesContent.length>0)}}e.exports=c,c.default=c},6846:(e,t,r)=>{"use strict";let n=r(4211),o=r(6966),a=r(145),s=r(5644);class l{constructor(e=[]){this.version="8.4.41",this.plugins=this.normalize(e)}normalize(e){let t=[];for(let r of e)if(!0===r.postcss?r=r():r.postcss&&(r=r.postcss),"object"==typeof r&&Array.isArray(r.plugins))t=t.concat(r.plugins);else if("object"==typeof r&&r.postcssPlugin)t.push(r);else if("function"==typeof r)t.push(r);else if("object"!=typeof r||!r.parse&&!r.stringify)throw new Error(r+" is not a PostCSS plugin");return t}process(e,t={}){return this.plugins.length||t.parser||t.stringifier||t.syntax?new o(this,e,t):new n(this,e,t)}use(e){return this.plugins=this.plugins.concat(this.normalize([e])),this}}e.exports=l,l.default=l,s.registerProcessor(l),a.registerProcessor(l)},3717:(e,t,r)=>{"use strict";let n=r(38);class o{constructor(e,t,r){this.processor=e,this.messages=[],this.root=t,this.opts=r,this.css=void 0,this.map=void 0}toString(){return this.css}warn(e,t={}){t.plugin||this.lastPlugin&&this.lastPlugin.postcssPlugin&&(t.plugin=this.lastPlugin.postcssPlugin);let r=new n(e,t);return this.messages.push(r),r}warnings(){return this.messages.filter((e=>"warning"===e.type))}get content(){return this.css}}e.exports=o,o.default=o},5644:(e,t,r)=>{"use strict";let n,o,a=r(7793);class s extends a{constructor(e){super(e),this.type="root",this.nodes||(this.nodes=[])}normalize(e,t,r){let n=super.normalize(e);if(t)if("prepend"===r)this.nodes.length>1?t.raws.before=this.nodes[1].raws.before:delete t.raws.before;else if(this.first!==t)for(let e of n)e.raws.before=t.raws.before;return n}removeChild(e,t){let r=this.index(e);return!t&&0===r&&this.nodes.length>1&&(this.nodes[1].raws.before=this.nodes[r].raws.before),super.removeChild(e)}toResult(e={}){return new n(new o,this,e).stringify()}}s.registerLazyResult=e=>{n=e},s.registerProcessor=e=>{o=e},e.exports=s,s.default=s,a.registerRoot(s)},1534:(e,t,r)=>{"use strict";let n=r(7793),o=r(1752);class a extends n{constructor(e){super(e),this.type="rule",this.nodes||(this.nodes=[])}get selectors(){return o.comma(this.selector)}set selectors(e){let t=this.selector?this.selector.match(/,\s*/):null,r=t?t[0]:","+this.raw("between","beforeOpen");this.selector=e.join(r)}}e.exports=a,a.default=a,n.registerRule(a)},7668:e=>{"use strict";const t={after:"\n",beforeClose:"\n",beforeComment:"\n",beforeDecl:"\n",beforeOpen:" ",beforeRule:"\n",colon:": ",commentLeft:" ",commentRight:" ",emptyBody:"",indent:"    ",semicolon:!1};class r{constructor(e){this.builder=e}atrule(e,t){let r="@"+e.name,n=e.params?this.rawValue(e,"params"):"";if(void 0!==e.raws.afterName?r+=e.raws.afterName:n&&(r+=" "),e.nodes)this.block(e,r+n);else{let o=(e.raws.between||"")+(t?";":"");this.builder(r+n+o,e)}}beforeAfter(e,t){let r;r="decl"===e.type?this.raw(e,null,"beforeDecl"):"comment"===e.type?this.raw(e,null,"beforeComment"):"before"===t?this.raw(e,null,"beforeRule"):this.raw(e,null,"beforeClose");let n=e.parent,o=0;for(;n&&"root"!==n.type;)o+=1,n=n.parent;if(r.includes("\n")){let t=this.raw(e,null,"indent");if(t.length)for(let e=0;e<o;e++)r+=t}return r}block(e,t){let r,n=this.raw(e,"between","beforeOpen");this.builder(t+n+"{",e,"start"),e.nodes&&e.nodes.length?(this.body(e),r=this.raw(e,"after")):r=this.raw(e,"after","emptyBody"),r&&this.builder(r),this.builder("}",e,"end")}body(e){let t=e.nodes.length-1;for(;t>0&&"comment"===e.nodes[t].type;)t-=1;let r=this.raw(e,"semicolon");for(let n=0;n<e.nodes.length;n++){let o=e.nodes[n],a=this.raw(o,"before");a&&this.builder(a),this.stringify(o,t!==n||r)}}comment(e){let t=this.raw(e,"left","commentLeft"),r=this.raw(e,"right","commentRight");this.builder("/*"+t+e.text+r+"*/",e)}decl(e,t){let r=this.raw(e,"between","colon"),n=e.prop+r+this.rawValue(e,"value");e.important&&(n+=e.raws.important||" !important"),t&&(n+=";"),this.builder(n,e)}document(e){this.body(e)}raw(e,r,n){let o;if(n||(n=r),r&&(o=e.raws[r],void 0!==o))return o;let a=e.parent;if("before"===n){if(!a||"root"===a.type&&a.first===e)return"";if(a&&"document"===a.type)return""}if(!a)return t[n];let s=e.root();if(s.rawCache||(s.rawCache={}),void 0!==s.rawCache[n])return s.rawCache[n];if("before"===n||"after"===n)return this.beforeAfter(e,n);{let t="raw"+((l=n)[0].toUpperCase()+l.slice(1));this[t]?o=this[t](s,e):s.walk((e=>{if(o=e.raws[r],void 0!==o)return!1}))}var l;return void 0===o&&(o=t[n]),s.rawCache[n]=o,o}rawBeforeClose(e){let t;return e.walk((e=>{if(e.nodes&&e.nodes.length>0&&void 0!==e.raws.after)return t=e.raws.after,t.includes("\n")&&(t=t.replace(/[^\n]+$/,"")),!1})),t&&(t=t.replace(/\S/g,"")),t}rawBeforeComment(e,t){let r;return e.walkComments((e=>{if(void 0!==e.raws.before)return r=e.raws.before,r.includes("\n")&&(r=r.replace(/[^\n]+$/,"")),!1})),void 0===r?r=this.raw(t,null,"beforeDecl"):r&&(r=r.replace(/\S/g,"")),r}rawBeforeDecl(e,t){let r;return e.walkDecls((e=>{if(void 0!==e.raws.before)return r=e.raws.before,r.includes("\n")&&(r=r.replace(/[^\n]+$/,"")),!1})),void 0===r?r=this.raw(t,null,"beforeRule"):r&&(r=r.replace(/\S/g,"")),r}rawBeforeOpen(e){let t;return e.walk((e=>{if("decl"!==e.type&&(t=e.raws.between,void 0!==t))return!1})),t}rawBeforeRule(e){let t;return e.walk((r=>{if(r.nodes&&(r.parent!==e||e.first!==r)&&void 0!==r.raws.before)return t=r.raws.before,t.includes("\n")&&(t=t.replace(/[^\n]+$/,"")),!1})),t&&(t=t.replace(/\S/g,"")),t}rawColon(e){let t;return e.walkDecls((e=>{if(void 0!==e.raws.between)return t=e.raws.between.replace(/[^\s:]/g,""),!1})),t}rawEmptyBody(e){let t;return e.walk((e=>{if(e.nodes&&0===e.nodes.length&&(t=e.raws.after,void 0!==t))return!1})),t}rawIndent(e){if(e.raws.indent)return e.raws.indent;let t;return e.walk((r=>{let n=r.parent;if(n&&n!==e&&n.parent&&n.parent===e&&void 0!==r.raws.before){let e=r.raws.before.split("\n");return t=e[e.length-1],t=t.replace(/\S/g,""),!1}})),t}rawSemicolon(e){let t;return e.walk((e=>{if(e.nodes&&e.nodes.length&&"decl"===e.last.type&&(t=e.raws.semicolon,void 0!==t))return!1})),t}rawValue(e,t){let r=e[t],n=e.raws[t];return n&&n.value===r?n.raw:r}root(e){this.body(e),e.raws.after&&this.builder(e.raws.after)}rule(e){this.block(e,this.rawValue(e,"selector")),e.raws.ownSemicolon&&this.builder(e.raws.ownSemicolon,e,"end")}stringify(e,t){if(!this[e.type])throw new Error("Unknown AST node type "+e.type+". Maybe you need to change PostCSS stringifier.");this[e.type](e,t)}}e.exports=r,r.default=r},3303:(e,t,r)=>{"use strict";let n=r(7668);function o(e,t){new n(t).stringify(e)}e.exports=o,o.default=o},4151:e=>{"use strict";e.exports.isClean=Symbol("isClean"),e.exports.my=Symbol("my")},5781:e=>{"use strict";const t="'".charCodeAt(0),r='"'.charCodeAt(0),n="\\".charCodeAt(0),o="/".charCodeAt(0),a="\n".charCodeAt(0),s=" ".charCodeAt(0),l="\f".charCodeAt(0),i="\t".charCodeAt(0),c="\r".charCodeAt(0),u="[".charCodeAt(0),d="]".charCodeAt(0),p="(".charCodeAt(0),h=")".charCodeAt(0),m="{".charCodeAt(0),f="}".charCodeAt(0),g=";".charCodeAt(0),b="*".charCodeAt(0),v=":".charCodeAt(0),y="@".charCodeAt(0),w=/[\t\n\f\r "#'()/;[\\\]{}]/g,k=/[\t\n\f\r !"#'():;@[\\\]{}]|\/(?=\*)/g,E=/.[\r\n"'(/\\]/,S=/[\da-f]/i;e.exports=function(e,x={}){let C,_,O,T,I,R,M,P,A,L,N=e.css.valueOf(),z=x.ignoreErrors,D=N.length,F=0,B=[],j=[];function V(t){throw e.error("Unclosed "+t,F)}return{back:function(e){j.push(e)},endOfFile:function(){return 0===j.length&&F>=D},nextToken:function(e){if(j.length)return j.pop();if(F>=D)return;let x=!!e&&e.ignoreUnclosed;switch(C=N.charCodeAt(F),C){case a:case s:case i:case c:case l:_=F;do{_+=1,C=N.charCodeAt(_)}while(C===s||C===a||C===i||C===c||C===l);L=["space",N.slice(F,_)],F=_-1;break;case u:case d:case m:case f:case v:case g:case h:{let e=String.fromCharCode(C);L=[e,e,F];break}case p:if(P=B.length?B.pop()[1]:"",A=N.charCodeAt(F+1),"url"===P&&A!==t&&A!==r&&A!==s&&A!==a&&A!==i&&A!==l&&A!==c){_=F;do{if(R=!1,_=N.indexOf(")",_+1),-1===_){if(z||x){_=F;break}V("bracket")}for(M=_;N.charCodeAt(M-1)===n;)M-=1,R=!R}while(R);L=["brackets",N.slice(F,_+1),F,_],F=_}else _=N.indexOf(")",F+1),T=N.slice(F,_+1),-1===_||E.test(T)?L=["(","(",F]:(L=["brackets",T,F,_],F=_);break;case t:case r:O=C===t?"'":'"',_=F;do{if(R=!1,_=N.indexOf(O,_+1),-1===_){if(z||x){_=F+1;break}V("string")}for(M=_;N.charCodeAt(M-1)===n;)M-=1,R=!R}while(R);L=["string",N.slice(F,_+1),F,_],F=_;break;case y:w.lastIndex=F+1,w.test(N),_=0===w.lastIndex?N.length-1:w.lastIndex-2,L=["at-word",N.slice(F,_+1),F,_],F=_;break;case n:for(_=F,I=!0;N.charCodeAt(_+1)===n;)_+=1,I=!I;if(C=N.charCodeAt(_+1),I&&C!==o&&C!==s&&C!==a&&C!==i&&C!==c&&C!==l&&(_+=1,S.test(N.charAt(_)))){for(;S.test(N.charAt(_+1));)_+=1;N.charCodeAt(_+1)===s&&(_+=1)}L=["word",N.slice(F,_+1),F,_],F=_;break;default:C===o&&N.charCodeAt(F+1)===b?(_=N.indexOf("*/",F+2)+1,0===_&&(z||x?_=N.length:V("comment")),L=["comment",N.slice(F,_+1),F,_],F=_):(k.lastIndex=F+1,k.test(N),_=0===k.lastIndex?N.length-1:k.lastIndex-2,L=["word",N.slice(F,_+1),F,_],B.push(L),F=_)}return F++,L},position:function(){return F}}}},6156:e=>{"use strict";let t={};e.exports=function(e){t[e]||(t[e]=!0,"undefined"!=typeof console&&console.warn&&console.warn(e))}},38:e=>{"use strict";class t{constructor(e,t={}){if(this.type="warning",this.text=e,t.node&&t.node.source){let e=t.node.rangeBy(t);this.line=e.start.line,this.column=e.start.column,this.endLine=e.end.line,this.endColumn=e.end.column}for(let e in t)this[e]=t[e]}toString(){return this.node?this.node.error(this.text,{index:this.index,plugin:this.plugin,word:this.word}).message:this.plugin?this.plugin+": "+this.text:this.text}}e.exports=t,t.default=t},2694:(e,t,r)=>{"use strict";var n=r(6925);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,a,s){if(s!==n){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return r.PropTypes=r,r}},5556:(e,t,r)=>{e.exports=r(2694)()},6925:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},2799:(e,t)=>{"use strict";Symbol.for("react.element"),Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.server_context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.suspense_list"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.for("react.offscreen"),Symbol.for("react.module.reference")},4363:(e,t,r)=>{"use strict";r(2799)},4643:(e,t,r)=>{function n(e){try{if(!r.g.localStorage)return!1}catch(e){return!1}var t=r.g.localStorage[e];return null!=t&&"true"===String(t).toLowerCase()}e.exports=function(e,t){if(n("noDeprecation"))return e;var r=!1;return function(){if(!r){if(n("throwDeprecation"))throw new Error(t);n("traceDeprecation")?console.trace(t):console.warn(t),r=!0}return e.apply(this,arguments)}}},9746:()=>{},9977:()=>{},197:()=>{},1866:()=>{},2739:()=>{},5042:e=>{e.exports={nanoid:(e=21)=>{let t="",r=e;for(;r--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[64*Math.random()|0];return t},customAlphabet:(e,t=21)=>(r=t)=>{let n="",o=r;for(;o--;)n+=e[Math.random()*e.length|0];return n}}},9817:e=>{"use strict";e.exports=JSON.parse('{"properties":["-epub-caption-side","-epub-hyphens","-epub-text-combine","-epub-text-emphasis","-epub-text-emphasis-color","-epub-text-emphasis-style","-epub-text-orientation","-epub-text-transform","-epub-word-break","-epub-writing-mode","-internal-text-autosizing-status","accelerator","accent-color","-wap-accesskey","additive-symbols","align-content","-webkit-align-content","align-items","-webkit-align-items","align-self","-webkit-align-self","alignment-baseline","all","alt","-webkit-alt","anchor-default","anchor-name","anchor-scroll","animation","animation-composition","animation-delay","-moz-animation-delay","-ms-animation-delay","-webkit-animation-delay","animation-direction","-moz-animation-direction","-ms-animation-direction","-webkit-animation-direction","animation-duration","-moz-animation-duration","-ms-animation-duration","-webkit-animation-duration","animation-fill-mode","-moz-animation-fill-mode","-ms-animation-fill-mode","-webkit-animation-fill-mode","animation-iteration-count","-moz-animation-iteration-count","-ms-animation-iteration-count","-webkit-animation-iteration-count","-moz-animation","-ms-animation","animation-name","-moz-animation-name","-ms-animation-name","-webkit-animation-name","animation-play-state","-moz-animation-play-state","-ms-animation-play-state","-webkit-animation-play-state","animation-range","animation-range-end","animation-range-start","animation-timeline","animation-timing-function","-moz-animation-timing-function","-ms-animation-timing-function","-webkit-animation-timing-function","-webkit-animation-trigger","-webkit-animation","app-region","-webkit-app-region","appearance","-moz-appearance","-webkit-appearance","ascent-override","aspect-ratio","-webkit-aspect-ratio","audio-level","azimuth","backdrop-filter","-webkit-backdrop-filter","backface-visibility","-moz-backface-visibility","-ms-backface-visibility","-webkit-backface-visibility","background","background-attachment","-webkit-background-attachment","background-blend-mode","background-clip","-moz-background-clip","-webkit-background-clip","background-color","-webkit-background-color","-webkit-background-composite","background-image","-webkit-background-image","-moz-background-inline-policy","background-origin","-moz-background-origin","-webkit-background-origin","background-position","-webkit-background-position","background-position-x","-webkit-background-position-x","background-position-y","-webkit-background-position-y","background-repeat","-webkit-background-repeat","background-repeat-x","background-repeat-y","background-size","-moz-background-size","-webkit-background-size","-webkit-background","base-palette","baseline-shift","baseline-source","behavior","-moz-binding","block-ellipsis","-ms-block-progression","block-size","block-step","block-step-align","block-step-insert","block-step-round","block-step-size","bookmark-label","bookmark-level","bookmark-state","border","-webkit-border-after-color","-webkit-border-after-style","-webkit-border-after","-webkit-border-after-width","-webkit-border-before-color","-webkit-border-before-style","-webkit-border-before","-webkit-border-before-width","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-bottom","border-bottom-color","-moz-border-bottom-colors","border-bottom-left-radius","-webkit-border-bottom-left-radius","border-bottom-right-radius","-webkit-border-bottom-right-radius","border-bottom-style","border-bottom-width","border-boundary","border-collapse","border-color","-moz-border-end-color","-webkit-border-end-color","border-end-end-radius","-moz-border-end","border-end-start-radius","-moz-border-end-style","-webkit-border-end-style","-webkit-border-end","-moz-border-end-width","-webkit-border-end-width","-webkit-border-fit","-webkit-border-horizontal-spacing","border-image","-moz-border-image","-o-border-image","border-image-outset","-webkit-border-image-outset","border-image-repeat","-webkit-border-image-repeat","border-image-slice","-webkit-border-image-slice","border-image-source","-webkit-border-image-source","-webkit-border-image","border-image-width","-webkit-border-image-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","border-left","border-left-color","-moz-border-left-colors","border-left-style","border-left-width","border-radius","-moz-border-radius-bottomleft","-moz-border-radius-bottomright","-moz-border-radius","-moz-border-radius-topleft","-moz-border-radius-topright","-webkit-border-radius","border-right","border-right-color","-moz-border-right-colors","border-right-style","border-right-width","border-spacing","-moz-border-start-color","-webkit-border-start-color","border-start-end-radius","-moz-border-start","border-start-start-radius","-moz-border-start-style","-webkit-border-start-style","-webkit-border-start","-moz-border-start-width","-webkit-border-start-width","border-style","border-top","border-top-color","-moz-border-top-colors","border-top-left-radius","-webkit-border-top-left-radius","border-top-right-radius","-webkit-border-top-right-radius","border-top-style","border-top-width","-webkit-border-vertical-spacing","border-width","bottom","-moz-box-align","-webkit-box-align","box-decoration-break","-webkit-box-decoration-break","-moz-box-direction","-webkit-box-direction","-webkit-box-flex-group","-moz-box-flex","-webkit-box-flex","-webkit-box-lines","-moz-box-ordinal-group","-webkit-box-ordinal-group","-moz-box-orient","-webkit-box-orient","-moz-box-pack","-webkit-box-pack","-webkit-box-reflect","box-shadow","-moz-box-shadow","-webkit-box-shadow","box-sizing","-moz-box-sizing","-webkit-box-sizing","box-snap","break-after","break-before","break-inside","buffered-rendering","caption-side","caret","caret-animation","caret-color","caret-shape","chains","clear","clip","clip-path","-webkit-clip-path","clip-rule","color","color-adjust","-webkit-color-correction","-apple-color-filter","color-interpolation","color-interpolation-filters","color-profile","color-rendering","color-scheme","-webkit-column-axis","-webkit-column-break-after","-webkit-column-break-before","-webkit-column-break-inside","column-count","-moz-column-count","-webkit-column-count","column-fill","-moz-column-fill","-webkit-column-fill","column-gap","-moz-column-gap","-webkit-column-gap","column-progression","-webkit-column-progression","column-rule","column-rule-color","-moz-column-rule-color","-webkit-column-rule-color","-moz-column-rule","column-rule-style","-moz-column-rule-style","-webkit-column-rule-style","-webkit-column-rule","column-rule-width","-moz-column-rule-width","-webkit-column-rule-width","column-span","-moz-column-span","-webkit-column-span","column-width","-moz-column-width","-webkit-column-width","columns","-moz-columns","-webkit-columns","-webkit-composition-fill-color","-webkit-composition-frame-color","contain","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-size","contain-intrinsic-width","container","container-name","container-type","content","content-visibility","-ms-content-zoom-chaining","-ms-content-zoom-limit-max","-ms-content-zoom-limit-min","-ms-content-zoom-limit","-ms-content-zoom-snap","-ms-content-zoom-snap-points","-ms-content-zoom-snap-type","-ms-content-zooming","continue","counter-increment","counter-reset","counter-set","cue","cue-after","cue-before","cursor","-webkit-cursor-visibility","cx","cy","d","-apple-dashboard-region","-webkit-dashboard-region","descent-override","direction","display","display-align","dominant-baseline","elevation","empty-cells","enable-background","epub-caption-side","epub-hyphens","epub-text-combine","epub-text-emphasis","epub-text-emphasis-color","epub-text-emphasis-style","epub-text-orientation","epub-text-transform","epub-word-break","epub-writing-mode","fallback","field-sizing","fill","fill-break","fill-color","fill-image","fill-opacity","fill-origin","fill-position","fill-repeat","fill-rule","fill-size","filter","-ms-filter","-webkit-filter","flex","-ms-flex-align","-webkit-flex-align","flex-basis","-webkit-flex-basis","flex-direction","-ms-flex-direction","-webkit-flex-direction","flex-flow","-ms-flex-flow","-webkit-flex-flow","flex-grow","-webkit-flex-grow","-ms-flex-item-align","-webkit-flex-item-align","-ms-flex-line-pack","-webkit-flex-line-pack","-ms-flex","-ms-flex-negative","-ms-flex-order","-webkit-flex-order","-ms-flex-pack","-webkit-flex-pack","-ms-flex-positive","-ms-flex-preferred-size","flex-shrink","-webkit-flex-shrink","-webkit-flex","flex-wrap","-ms-flex-wrap","-webkit-flex-wrap","float","float-defer","-moz-float-edge","float-offset","float-reference","flood-color","flood-opacity","flow","flow-from","-ms-flow-from","-webkit-flow-from","flow-into","-ms-flow-into","-webkit-flow-into","font","font-display","font-family","font-feature-settings","-moz-font-feature-settings","-ms-font-feature-settings","-webkit-font-feature-settings","font-kerning","-webkit-font-kerning","font-language-override","-moz-font-language-override","font-optical-sizing","font-palette","font-size","font-size-adjust","-webkit-font-size-delta","-webkit-font-smoothing","font-stretch","font-style","font-synthesis","font-synthesis-position","font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-emoji","font-variant-ligatures","-webkit-font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","font-width","footnote-display","footnote-policy","-moz-force-broken-image-icon","forced-color-adjust","gap","glyph-orientation-horizontal","glyph-orientation-vertical","grid","-webkit-grid-after","grid-area","grid-auto-columns","-webkit-grid-auto-columns","grid-auto-flow","-webkit-grid-auto-flow","grid-auto-rows","-webkit-grid-auto-rows","-webkit-grid-before","grid-column","-ms-grid-column-align","grid-column-end","grid-column-gap","-ms-grid-column","-ms-grid-column-span","grid-column-start","-webkit-grid-column","-ms-grid-columns","-webkit-grid-columns","-webkit-grid-end","grid-gap","grid-row","-ms-grid-row-align","grid-row-end","grid-row-gap","-ms-grid-row","-ms-grid-row-span","grid-row-start","-webkit-grid-row","-ms-grid-rows","-webkit-grid-rows","-webkit-grid-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","-ms-high-contrast-adjust","-webkit-highlight","hyphenate-character","-webkit-hyphenate-character","-webkit-hyphenate-limit-after","-webkit-hyphenate-limit-before","hyphenate-limit-chars","-ms-hyphenate-limit-chars","hyphenate-limit-last","hyphenate-limit-lines","-ms-hyphenate-limit-lines","-webkit-hyphenate-limit-lines","hyphenate-limit-zone","-ms-hyphenate-limit-zone","hyphens","-moz-hyphens","-ms-hyphens","-webkit-hyphens","image-orientation","-moz-image-region","image-rendering","image-resolution","-ms-ime-align","ime-mode","inherits","initial-letter","initial-letter-align","-webkit-initial-letter","initial-letter-wrap","initial-value","inline-size","inline-sizing","input-format","-wap-input-format","-wap-input-required","input-security","inset","inset-area","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","-ms-interpolation-mode","isolation","justify-content","-webkit-justify-content","justify-items","-webkit-justify-items","justify-self","-webkit-justify-self","kerning","layout-flow","layout-grid","layout-grid-char","layout-grid-line","layout-grid-mode","layout-grid-type","left","letter-spacing","lighting-color","-webkit-line-align","-webkit-line-box-contain","line-break","-webkit-line-break","line-clamp","-webkit-line-clamp","line-gap-override","line-grid","-webkit-line-grid-snap","-webkit-line-grid","line-height","line-height-step","line-increment","line-padding","line-snap","-webkit-line-snap","-o-link","-o-link-source","list-style","list-style-image","list-style-position","list-style-type","-webkit-locale","-webkit-logical-height","-webkit-logical-width","margin","-webkit-margin-after-collapse","-webkit-margin-after","-webkit-margin-before-collapse","-webkit-margin-before","margin-block","margin-block-end","margin-block-start","margin-bottom","-webkit-margin-bottom-collapse","margin-break","-webkit-margin-collapse","-moz-margin-end","-webkit-margin-end","margin-inline","margin-inline-end","margin-inline-start","margin-left","margin-right","-moz-margin-start","-webkit-margin-start","margin-top","-webkit-margin-top-collapse","margin-trim","marker","marker-end","marker-knockout-left","marker-knockout-right","marker-mid","marker-offset","marker-pattern","marker-segment","marker-side","marker-start","marks","-wap-marquee-dir","-webkit-marquee-direction","-webkit-marquee-increment","-wap-marquee-loop","-webkit-marquee-repetition","-wap-marquee-speed","-webkit-marquee-speed","-wap-marquee-style","-webkit-marquee-style","-webkit-marquee","mask","-webkit-mask-attachment","mask-border","mask-border-mode","mask-border-outset","mask-border-repeat","mask-border-slice","mask-border-source","mask-border-width","-webkit-mask-box-image-outset","-webkit-mask-box-image-repeat","-webkit-mask-box-image-slice","-webkit-mask-box-image-source","-webkit-mask-box-image","-webkit-mask-box-image-width","mask-clip","-webkit-mask-clip","mask-composite","-webkit-mask-composite","mask-image","-webkit-mask-image","mask-mode","mask-origin","-webkit-mask-origin","mask-position","-webkit-mask-position","mask-position-x","-webkit-mask-position-x","mask-position-y","-webkit-mask-position-y","mask-repeat","-webkit-mask-repeat","-webkit-mask-repeat-x","-webkit-mask-repeat-y","mask-size","-webkit-mask-size","mask-source-type","-webkit-mask-source-type","mask-type","-webkit-mask","-webkit-match-nearest-mail-blockquote-color","math-depth","math-shift","math-style","max-block-size","max-height","max-inline-size","max-lines","-webkit-max-logical-height","-webkit-max-logical-width","max-width","max-zoom","min-block-size","min-height","min-inline-size","min-intrinsic-sizing","-webkit-min-logical-height","-webkit-min-logical-width","min-width","min-zoom","mix-blend-mode","motion","motion-offset","motion-path","motion-rotation","nav-down","nav-index","nav-left","nav-right","nav-up","-webkit-nbsp-mode","negative","object-fit","-o-object-fit","object-position","-o-object-position","object-view-box","offset","offset-anchor","offset-block-end","offset-block-start","offset-distance","offset-inline-end","offset-inline-start","offset-path","offset-position","offset-rotate","offset-rotation","opacity","-moz-opacity","-webkit-opacity","order","-webkit-order","-moz-orient","orientation","orphans","-moz-osx-font-smoothing","outline","outline-color","-moz-outline-color","-moz-outline","outline-offset","-moz-outline-offset","-moz-outline-radius-bottomleft","-moz-outline-radius-bottomright","-moz-outline-radius","-moz-outline-radius-topleft","-moz-outline-radius-topright","outline-style","-moz-outline-style","outline-width","-moz-outline-width","overflow","overflow-anchor","overflow-block","overflow-clip-margin","overflow-clip-margin-block","overflow-clip-margin-block-end","overflow-clip-margin-block-start","overflow-clip-margin-bottom","overflow-clip-margin-inline","overflow-clip-margin-inline-end","overflow-clip-margin-inline-start","overflow-clip-margin-left","overflow-clip-margin-right","overflow-clip-margin-top","overflow-inline","-webkit-overflow-scrolling","-ms-overflow-style","overflow-wrap","overflow-x","overflow-y","overlay","override-colors","overscroll-behavior","overscroll-behavior-block","overscroll-behavior-inline","overscroll-behavior-x","overscroll-behavior-y","pad","padding","-webkit-padding-after","-webkit-padding-before","padding-block","padding-block-end","padding-block-start","padding-bottom","-moz-padding-end","-webkit-padding-end","padding-inline","padding-inline-end","padding-inline-start","padding-left","padding-right","-moz-padding-start","-webkit-padding-start","padding-top","page","page-break-after","page-break-before","page-break-inside","page-orientation","paint-order","pause","pause-after","pause-before","-apple-pay-button-style","-apple-pay-button-type","pen-action","perspective","-moz-perspective","-ms-perspective","perspective-origin","-moz-perspective-origin","-ms-perspective-origin","-webkit-perspective-origin","perspective-origin-x","-webkit-perspective-origin-x","perspective-origin-y","-webkit-perspective-origin-y","-webkit-perspective","pitch","pitch-range","place-content","place-items","place-self","play-during","pointer-events","position","position-animation","position-fallback","position-fallback-bounds","position-try","position-try-options","position-try-order","prefix","print-color-adjust","-webkit-print-color-adjust","property-name","quotes","r","range","-webkit-region-break-after","-webkit-region-break-before","-webkit-region-break-inside","region-fragment","-webkit-region-fragment","-webkit-region-overflow","resize","rest","rest-after","rest-before","richness","right","rotate","row-gap","-webkit-rtl-ordering","ruby-align","ruby-merge","ruby-overhang","ruby-position","-webkit-ruby-position","running","rx","ry","scale","scroll-behavior","-ms-scroll-chaining","-ms-scroll-limit","-ms-scroll-limit-x-max","-ms-scroll-limit-x-min","-ms-scroll-limit-y-max","-ms-scroll-limit-y-min","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","-ms-scroll-rails","scroll-snap-align","scroll-snap-coordinate","-webkit-scroll-snap-coordinate","scroll-snap-destination","-webkit-scroll-snap-destination","scroll-snap-margin","scroll-snap-margin-bottom","scroll-snap-margin-left","scroll-snap-margin-right","scroll-snap-margin-top","scroll-snap-points-x","-ms-scroll-snap-points-x","-webkit-scroll-snap-points-x","scroll-snap-points-y","-ms-scroll-snap-points-y","-webkit-scroll-snap-points-y","scroll-snap-stop","scroll-snap-type","-ms-scroll-snap-type","-webkit-scroll-snap-type","scroll-snap-type-x","scroll-snap-type-y","-ms-scroll-snap-x","-ms-scroll-snap-y","scroll-timeline","scroll-timeline-axis","scroll-timeline-name","-ms-scroll-translation","scrollbar-arrow-color","scrollbar-base-color","scrollbar-color","scrollbar-dark-shadow-color","scrollbar-darkshadow-color","scrollbar-face-color","scrollbar-gutter","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","scrollbar-width","scrollbar3d-light-color","scrollbar3dlight-color","shape-image-threshold","-webkit-shape-image-threshold","shape-inside","-webkit-shape-inside","shape-margin","-webkit-shape-margin","shape-outside","-webkit-shape-outside","-webkit-shape-padding","shape-rendering","size","size-adjust","snap-height","solid-color","solid-opacity","spatial-navigation-action","spatial-navigation-contain","spatial-navigation-function","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","src","-moz-stack-sizing","stop-color","stop-opacity","stress","string-set","stroke","stroke-align","stroke-alignment","stroke-break","stroke-color","stroke-dash-corner","stroke-dash-justify","stroke-dashadjust","stroke-dasharray","stroke-dashcorner","stroke-dashoffset","stroke-image","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-origin","stroke-position","stroke-repeat","stroke-size","stroke-width","suffix","supported-color-schemes","-webkit-svg-shadow","symbols","syntax","system","tab-size","-moz-tab-size","-o-tab-size","-o-table-baseline","table-layout","-webkit-tap-highlight-color","text-align","text-align-all","text-align-last","-moz-text-align-last","text-anchor","text-autospace","-moz-text-blink","text-box-edge","text-box-trim","-ms-text-combine-horizontal","text-combine-upright","-webkit-text-combine","text-decoration","text-decoration-blink","text-decoration-color","-moz-text-decoration-color","-webkit-text-decoration-color","text-decoration-line","-moz-text-decoration-line","text-decoration-line-through","-webkit-text-decoration-line","text-decoration-none","text-decoration-overline","text-decoration-skip","text-decoration-skip-box","text-decoration-skip-ink","text-decoration-skip-inset","text-decoration-skip-self","text-decoration-skip-spaces","-webkit-text-decoration-skip","text-decoration-style","-moz-text-decoration-style","-webkit-text-decoration-style","text-decoration-thickness","text-decoration-trim","text-decoration-underline","-webkit-text-decoration","-webkit-text-decorations-in-effect","text-emphasis","text-emphasis-color","-webkit-text-emphasis-color","text-emphasis-position","-webkit-text-emphasis-position","text-emphasis-skip","text-emphasis-style","-webkit-text-emphasis-style","-webkit-text-emphasis","-webkit-text-fill-color","text-group-align","text-indent","text-justify","text-justify-trim","text-kashida","text-kashida-space","text-line-through","text-line-through-color","text-line-through-mode","text-line-through-style","text-line-through-width","text-orientation","-webkit-text-orientation","text-overflow","text-overline","text-overline-color","text-overline-mode","text-overline-style","text-overline-width","text-rendering","-webkit-text-security","text-shadow","text-size-adjust","-moz-text-size-adjust","-ms-text-size-adjust","-webkit-text-size-adjust","text-spacing","text-spacing-trim","-webkit-text-stroke-color","-webkit-text-stroke","-webkit-text-stroke-width","text-transform","text-underline","text-underline-color","text-underline-mode","text-underline-offset","text-underline-position","-webkit-text-underline-position","text-underline-style","text-underline-width","text-wrap","text-wrap-mode","text-wrap-style","-webkit-text-zoom","timeline-scope","top","touch-action","touch-action-delay","-ms-touch-action","-webkit-touch-callout","-ms-touch-select","-apple-trailing-word","transform","transform-box","-moz-transform","-ms-transform","-o-transform","transform-origin","-moz-transform-origin","-ms-transform-origin","-o-transform-origin","-webkit-transform-origin","transform-origin-x","-webkit-transform-origin-x","transform-origin-y","-webkit-transform-origin-y","transform-origin-z","-webkit-transform-origin-z","transform-style","-moz-transform-style","-ms-transform-style","-webkit-transform-style","-webkit-transform","transition","transition-behavior","transition-delay","-moz-transition-delay","-ms-transition-delay","-o-transition-delay","-webkit-transition-delay","transition-duration","-moz-transition-duration","-ms-transition-duration","-o-transition-duration","-webkit-transition-duration","-moz-transition","-ms-transition","-o-transition","transition-property","-moz-transition-property","-ms-transition-property","-o-transition-property","-webkit-transition-property","transition-timing-function","-moz-transition-timing-function","-ms-transition-timing-function","-o-transition-timing-function","-webkit-transition-timing-function","-webkit-transition","translate","uc-alt-skin","uc-skin","unicode-bidi","unicode-range","-webkit-user-drag","-moz-user-focus","-moz-user-input","-moz-user-modify","-webkit-user-modify","user-select","-moz-user-select","-ms-user-select","-webkit-user-select","user-zoom","vector-effect","vertical-align","view-timeline","view-timeline-axis","view-timeline-inset","view-timeline-name","view-transition-name","viewport-fill","viewport-fill-opacity","viewport-fit","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","white-space-collapse","white-space-trim","-webkit-widget-region","widows","width","will-change","-moz-window-dragging","-moz-window-shadow","word-break","word-space-transform","word-spacing","word-wrap","wrap-after","wrap-before","wrap-flow","-ms-wrap-flow","-webkit-wrap-flow","wrap-inside","-ms-wrap-margin","-webkit-wrap-margin","-webkit-wrap-padding","-webkit-wrap-shape-inside","-webkit-wrap-shape-outside","wrap-through","-ms-wrap-through","-webkit-wrap-through","-webkit-wrap","writing-mode","-webkit-writing-mode","x","y","z-index","zoom"]}')}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.nc=void 0;var n={};return(()=>{"use strict";r.r(n),r.d(n,{ActiveSelectors:()=>P,AtRules:()=>ct,BackgroundControl:()=>rs,BoxShadowControl:()=>bs,BuildAtRule:()=>st,BuildSelector:()=>v,ButtonIconControl:()=>fn,ColorPicker:()=>ba,Control:()=>bn,CustomSelect:()=>Kr,DimensionsControl:()=>mo,EffectControl:()=>yn,EffectEdit:()=>wn,EffectList:()=>xn,FilterControl:()=>Ps,ImageControl:()=>Ya,Selector:()=>M,StylesBuilder:()=>dl,TransformControl:()=>Ks,TransitionControl:()=>Cs,UnitControl:()=>Hn,atRuleActions:()=>_l,atRuleReducer:()=>Tl,atRuleSelectors:()=>Ol,cleanStylesObject:()=>Le,currentStyleActions:()=>El,currentStyleReducer:()=>xl,currentStyleSelectors:()=>Sl,defaultAtRules:()=>$e,deleteStylesObjectKey:()=>Fe,filterActions:()=>Rl,filterReducer:()=>Pl,filterSelectors:()=>Ml,getAtRuleValue:()=>Ge,getAtRules:()=>qe,getCss:()=>Ae,getElementStyles:()=>We,getPreviewWidth:()=>pl,getStylesObject:()=>Be,nestedRuleActions:()=>vl,nestedRuleReducer:()=>wl,nestedRuleSelectors:()=>yl,styleActions:()=>ml,styleReducer:()=>gl,styleSelectors:()=>fl,updateStylesObjectKey:()=>De,usePanelSections:()=>_,useSelectedBlockElement:()=>x,useSelectedBlockElements:()=>S});const e=window.React;var t=r.n(e);const o=window.wp.components,a=window.wp.element,s=window.wp.i18n,l=window.wp.primitives,i=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"})),c={component:"jkZSq3HgZ_wP8SvWVEf6",label:"AxmZs0v9UfB9jziMxLPb",header:"M6gSNIiFSG4BFpyqXsvb",list:"Qi0B_BJy2EY5EfQyjjki",options:"NEIH26sym81MPeULwS8S",buildSelector:"j5YK5M62cGBJmDyyXFZm",item:"yYYgOZy7mLja8ZESMZ0w",dot:"alc85lGXFwSqd2fu9Por",local:"jcXyOTiEvl8t00iW3CQE",current:"iFrTKFVQca9e5y2WeSgR",selector:"fT2RbYKNeQI2SgHHow_r",build:"O93oL9_azCyusvKP9wK5",errors:"I4e5HV6lLA7FopgHN4Xk",selectors:"eYf9NLyM4nNjaKj3wyqY",actions:"SyfTi0MqQlbuMKdZXDCQ",name:"g0qCfuVU2U4VfEQDZvvB",notice:"ANp7ICMsT7mDlEPdj_uz",button:"iammESrB5NoPltEQjth5",shortcuts:"gYv6oroi1cvPaRywq5we",more:"UjSXazmBGhfDldMxVWN2",icon:"RBbJ2bGN9kcyzpMSndVo",delete:"fsQyFb0av1fLNCb1lGhg",atRules:"ud4vtuACZACHT9d8Phr0",preview:"wypYc6BlbUkSNusOKe_C",filters:"V7LMleVx2d2F0gU5VuBI",filtersPopover:"VXdWGY73YHYyV0yFdpnp",filtersDropdown:"SONwKC5TO4OFEVH0n6Hg",search:"tC4fmXudvmZ8hRUtUJwx",atRuleButton:"GumwXJyPLPLNbj8AlQ5x",manageAtRules:"PL8xR0G5NPVX0SvlI726",indicatorDots:"mLh_YFAijKgsNcydbc6e",legend:"yaeBZp8Tzlen1atddONQ",atRulePreview:"z_TIQFKF4VS1oN3At0nQ"},u=window.wp.dom,d=(0,a.forwardRef)((function({icon:e,size:t=24,...r},n){return(0,a.cloneElement)(e,{width:t,height:t,...r,ref:n})})),p=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"m19 7.5h-7.628c-.3089-.87389-1.1423-1.5-2.122-1.5-.97966 0-1.81309.62611-2.12197 1.5h-2.12803v1.5h2.12803c.30888.87389 1.14231 1.5 2.12197 1.5.9797 0 1.8131-.62611 2.122-1.5h7.628z"}),(0,e.createElement)(l.Path,{d:"m19 15h-2.128c-.3089-.8739-1.1423-1.5-2.122-1.5s-1.8131.6261-2.122 1.5h-7.628v1.5h7.628c.3089.8739 1.1423 1.5 2.122 1.5s1.8131-.6261 2.122-1.5h2.128z"})),h=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"}));function m(e){var t,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(r=m(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function f(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=m(e))&&(n&&(n+=" "),n+=t);return n}const g=f,b=window.gbp.components;function v({editSelector:t="",setShowBuildSelector:r,allStyles:n,onNestedRuleChange:l,setTempSelector:i,removeTempSelector:u,onUpdateKey:d,selectorShortcuts:p,setShowSelectorOptions:h,setEditSelector:m}){const[f,g]=(0,a.useState)(!1),[v,y]=(0,a.useState)(""),[w,k]=(0,a.useState)("");(0,a.useEffect)((()=>{y(t||"")}),[]),(0,a.useEffect)((()=>{v.startsWith("&")&&!f?g(!0):!v.startsWith("&")&&f&&g(!1),E||g(!1)}),[v]);const E=(0,a.useMemo)((()=>{const e=v.replace("&","");return""===v||"&"===v||[":",".","#","["].some((t=>e.startsWith(t)))}),[v]);return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:c.options},(0,e.createElement)(o.BaseControl,{label:(0,s.__)("Selector","generateblocks-pro"),className:"gb-styles-builder__selector-options--build",id:"class-selector"},(0,e.createElement)(b.Stack,{className:c.buildSelector,direction:"vertical",gap:"12px"},(0,e.createElement)(o.TextControl,{id:"class-selector",value:v,onChange:e=>y(e)}),(0,e.createElement)(o.ToggleControl,{label:(0,s.__)("Compound selector","generateblocks-pro"),checked:!!f,disabled:!E,onChange:e=>{e&&!v.startsWith("&")&&y("&"+v),!e&&v.startsWith("&")&&y(v.replace("&",""))}}))),Object.entries(p).map((([t,r])=>"default"===t?null:(0,e.createElement)(o.BaseControl,{key:r?.label,label:r?.label,id:""},(0,e.createElement)("div",{className:c.list},r?.items.map((t=>(0,e.createElement)(o.Button,{key:t.value,size:"small",onClick:()=>{const e=t.value===v?"":t.value;y(e)},isPressed:t.value===v},t.label)))))))),(0,e.createElement)("div",{className:c.actions},(0,e.createElement)(o.Button,{variant:"tertiary",onClick:()=>{r(!1),m("")}},(0,s.__)("Cancel","generateblocks-pro")),!t&&(0,e.createElement)(o.Button,{variant:"primary",disabled:!v||"&"===v,onClick:()=>{n[v]?k((0,s.__)("Selector already exists.","generateblocks-pro")):(l(v),i(v),r(!1),h(!1))}},(0,s.__)("Create","generateblocks-pro")),!!t&&(0,e.createElement)(o.Button,{variant:"primary",disabled:!v||"&"===v||t===v,onClick:()=>{n[v]?k((0,s.__)("Selector already exists.","generateblocks-pro")):(d(t,v),l(v),i(v),u(t),r(!1),h(!1),m(""))}},(0,s.__)("Update","generateblocks-pro"))),!!w&&(0,e.createElement)(o.Notice,{status:"error",isDismissible:!1,className:c.notice},w))}const y=window.wp.data,w=window.wp.coreData,k=window.wp.compose;function E(e){return`.editor-styles-wrapper [data-block="${e}"]:not(.gb-is-root-block):not([data-block-wrapper])`}function S(){const{getSelectedBlockClientIds:e}=(0,y.useSelect)((e=>e("core/block-editor")),[]),t=e(),r=C(),[n,o]=(0,a.useState)([]);return(0,a.useEffect)((()=>{if(!t.length)return;const e=document.querySelector('iframe[name="editor-canvas"]')?.contentDocument||document;return o(t.map((t=>e.querySelector(E(t)))).filter((e=>null!==e))),()=>{o([])}}),[t,r]),n}function x(){const{getSelectedBlockClientId:e}=(0,y.useSelect)((e=>e("core/block-editor")),[]),t=e(),r=E(t),[n,o]=(0,a.useState)(null),s=C();return(0,a.useEffect)((()=>{let e=!1;const n=t=>{e||o(t)};if(!t)return n(null),()=>{e=!0,o(null)};const a=document.querySelector('iframe[name="editor-canvas"]');if("loading"===a?.contentDocument.readyState){const t=()=>n(a.contentDocument.querySelector(r));return a.addEventListener("load",t,{once:!0}),()=>{e=!0,a.removeEventListener("load",t),o(null)}}return n((a?.contentDocument||document).querySelector(r)),()=>{e=!0,o(null)}}),[t,s,r]),n}function C(){return(0,y.useSelect)((e=>{const{getDeviceType:t}=e("core/editor")||{};if("function"==typeof t)return t();const{__experimentalGetPreviewDeviceType:r=()=>""}=e("core/edit-post");return r()}),[])}function _({sectionState:e,setSectionState:t,storageKey:r,filtersActive:n=!1,search:o=""}){const a=Object.values(e).every((e=>e)),s=sessionStorage.getItem(r),l=JSON.stringify(e);return!n&&!o||a?n||o||!s||s===l||t(JSON.parse(s)):t((e=>{const t={...e};for(const e in t)t[e]=!0;return t})),{onSectionToggle:(a,s)=>{if(a in e){if(e[a]===s||n||o)return;t((e=>{const t={...e,[a]:s};return sessionStorage.setItem(r,JSON.stringify(t)),t}))}}}}const O=(0,a.createContext)(null);function T(){const e=(0,a.useContext)(O);if(null===e)throw new Error("useStylesBuilderContext must be used within a ControlFunctionsProvider");return e}function I({nestedRule:t,atRule:r,allStyles:n,showAll:o=!1}){var a;const l=T(),{getValueSources:i}=l,u=i(t,r,t?null!==(a=n?.[t])&&void 0!==a?a:{}:n),d=u.some((({source:e})=>"global"===e)),p=u.some((({source:e})=>"local"===e)),h=u.some((({source:e})=>"current"===e));return d||p||h?o?(0,e.createElement)("div",{className:c.indicatorDots},!!h&&(0,e.createElement)("div",{title:(0,s.__)("Has local styles","generateblocks-pro"),className:g(c.dot,c.current)}),!!p&&(0,e.createElement)("div",{title:(0,s.__)("Has inherited local styles","generateblocks-pro"),className:g(c.dot,c.local)}),!!d&&(0,e.createElement)("div",{title:(0,s.__)("Has inherited global styles","generateblocks-pro"),className:c.dot})):(0,e.createElement)("span",{title:(0,s.__)("Custom styles exist for this selector","generateblocks-pro"),className:g(c.dot,{[c.current]:h,[c.local]:p&&!h,[c.global]:d&&!p&&!h})}):null}function R({visibleSelectors:t,allStyles:r,atRule:n}){const o=T(),{getValueSources:a}=o,l=r&&Object.entries(r).filter((([e,r])=>"object"==typeof r&&!e.startsWith("@")&&!t.some((t=>e===t.value))));if(!l||!l.length)return null;let i=!1,u=!1,d=!1;return l.forEach((([e])=>{const t=r[e]||null,o=a(e,n,t);i=o.some((({source:e})=>"global"===e)),u=o.some((({source:e})=>"local"===e)),d=o.some((({source:e})=>"current"===e))})),i||u||d?(0,e.createElement)("span",{title:(0,s.__)("Custom styles exist for other selectors","generateblocks-pro"),className:g(c.dot,{[c.current]:d,[c.local]:u&&!d,[c.global]:i&&!u&&!d})}):null}function M({allStyles:t,onNestedRuleChange:r,onUpdateKey:n,currentSelector:l,nestedRule:i,showSelectorOptions:m,setShowSelectorOptions:f,onDeleteStyle:g,selectorShortcuts:b,visibleSelectors:y,allowCustomAdvancedSelector:w,atRule:k}){const[E,S]=(0,a.useState)([]),[x,C]=(0,a.useState)(!1),[_,O]=(0,a.useState)(""),M=T(),{getValueSources:A}=M,L=(0,a.useMemo)((()=>{const e=[];return b?.default?.items.forEach((t=>{e.push(t.value)})),Object.keys(t)?.forEach((r=>{"object"!=typeof t[r]||r.startsWith("@")||e.includes(r)||e.push(r)})),E.forEach((t=>{e.includes(t)||e.push(t)})),Array.from(new Set(e))}),[t,E]),N=(0,a.useMemo)((()=>{if(!i)return(0,e.createElement)("span",{className:c.name},l);const t=l.split(",").map((t=>(0,a.renderToString)((t=>(0,e.createElement)("span",{style:{opacity:i?.5:1}},t))(t)))),r=[];return t.forEach((e=>{r.push(i.startsWith("&")?e+i.replace("&",""):e+" "+i)})),(0,e.createElement)("span",{className:c.name,dangerouslySetInnerHTML:{__html:(0,u.safeHTML)(r.join(", "))}})}),[l,i]),z=(0,a.useMemo)((()=>{const e=[y.find((e=>""===e.value))||{label:(0,s.__)("Main","generateblocks-pro"),value:""}];if(!y.some((e=>"hover"===e?.id||"&:hover"===e.value||"&:is(:hover, :focus)"===e.value))){const r=()=>{var e,r;const n=null!==(e=t?.["&:is(:hover, :focus)"])&&void 0!==e?e:{};if(A("&:is(:hover, :focus)",k,n).some((({source:e,inheritedNestedRule:t})=>"&:is(:hover, :focus)"===t&&("global"===e||"current"===e))))return"&:is(:hover, :focus)";const o=null!==(r=t?.["&:hover"])&&void 0!==r?r:{};return A("&:hover",k,o).some((({source:e,inheritedNestedRule:t})=>"&:hover"===t&&("global"===e||"current"===e)))?"&:hover":"&:is(:hover, :focus)"};e.push({label:(0,s.__)("Hover","generateblocks-pro"),value:r()})}return[...e,...y.filter((e=>""!==e.value))]}),[y,t,A,k]);return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:c.selectors},N,(0,e.createElement)("div",{className:c.shortcuts},(0,e.createElement)(o.ButtonGroup,null,z&&z.map((n=>(0,e.createElement)(o.Button,{key:n.label,className:c.button,label:(0,s.sprintf)(/* translators: %s: selector name. */ /* translators: %s: selector name. */
(0,s.__)("%s selector","generateblocks-pro"),n.label),showTooltip:!0,isPressed:i===n.value,onClick:()=>{r(n.value),f(!1)},size:"small"},n.label,(0,e.createElement)(I,{nestedRule:n.value,atRule:k,allStyles:t})))),(0,e.createElement)(o.Button,{className:c.more,label:(0,s.__)("Manage selectors","generateblocks-pro"),showTooltip:!0,isPressed:!!m,onClick:()=>{f(!m)},size:"small"},(0,e.createElement)(d,{icon:p,size:"16"}),(0,e.createElement)(R,{allStyles:t,visibleSelectors:z,atRule:k}))))),!!m&&(0,e.createElement)(e.Fragment,null,!x&&(0,e.createElement)(e.Fragment,null,!!b&&Object.keys(b).length>0&&(0,e.createElement)(o.Notice,{className:c.notice,isDismissible:!1},(0,s.__)("Choose a selector from the list below, or create a new custom one.","generateblocks-pro")),(0,e.createElement)(P,{activeSelectors:L,nestedRule:i,onNestedRuleChange:r,setShowSelectorOptions:f,setShowBuildSelector:C,setEditSelector:O,onDeleteStyle:g,allStyles:t,selectorShortcuts:b,currentSelector:l,allowCustomAdvancedSelector:w,atRule:k}),(0,e.createElement)("div",{className:c.actions},(0,e.createElement)(o.Button,{variant:"tertiary",size:"compact",onClick:()=>f(!1)},(0,s.__)("Cancel","generateblocks-pro")),!!w&&(0,e.createElement)(o.Button,{variant:"primary",size:"compact",showTooltip:!0,label:(0,s.__)("Add a new custom selector","generateblocks-pro"),icon:h,onClick:()=>{C(!0)}},(0,s.__)("New","generateblocks-pro")))),!!x&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.Notice,{className:c.notice,isDismissible:!1},_?(0,s.sprintf)(
// translators: %s: selector name.
// translators: %s: selector name.
(0,s.__)("You are editing a selector: %s","generateblocks-pro"),_):(0,s.__)("You are creating a new custom selector.","generateblocks-pro")),(0,e.createElement)(v,{editSelector:_,setShowBuildSelector:C,allStyles:t,onNestedRuleChange:r,setTempSelector:function(e){S([...E,e])},removeTempSelector:function(e){S(E.filter((t=>t!==e)))},onUpdateKey:n,selectorShortcuts:b,setShowSelectorOptions:f,setEditSelector:O}))))}function P({activeSelectors:t,nestedRule:r,onNestedRuleChange:n,setShowSelectorOptions:l,setShowBuildSelector:u,setEditSelector:d,onDeleteStyle:p,allStyles:h,selectorShortcuts:m,currentSelector:f,allowCustomAdvancedSelector:g,atRule:b}){const[v,y]=(0,a.useState)(!1);function w({currentSelectorName:t}){return(0,e.createElement)("span",{style:{fontFamily:"monospace"}},t)}return(0,e.createElement)("div",{className:c.options},!!t.length&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.BaseControl,{label:(0,s.__)("Selectors","generateblocks-pro"),id:""},(0,e.createElement)("div",{className:c.list},(0,e.createElement)("span",{className:c.item},(0,e.createElement)(o.Button,{size:"small",onClick:()=>{n(""),l(!1)},isPressed:!r,title:f},(0,s.__)("Main Selector","generateblocks-pro"),(0,e.createElement)(I,{nestedRule:"",atRule:b,allStyles:h,showAll:!0}))),t.map((t=>{return(0,e.createElement)("span",{key:t,className:c.item},(0,e.createElement)(o.Button,{size:"small",onClick:()=>{n(t===r?"":t),l(!1)},isPressed:t===r,title:f+(k=t,k?k.startsWith("&")?k.replace("&",""):String.fromCharCode(160)+k:"")},function(e,t){for(const r in t){const n=t[r].items.find((t=>t.value===e));if(n)return n.label}return e.replace("&","")}(t,m),(0,e.createElement)(I,{nestedRule:t,atRule:b,allStyles:h,showAll:!0})),!!g&&(0,e.createElement)(o.DropdownMenu,{icon:i,label:(0,s.__)("Options","generateblocks-pro"),toggleProps:{isPressed:t===r}},(({onClose:l})=>(0,e.createElement)(o.MenuGroup,null,v?(0,e.createElement)("div",{className:c.delete},(0,e.createElement)("p",null,(0,a.createInterpolateElement)(
// Translators: the at-rule for deletion.
// Translators: the at-rule for deletion.
(0,s.__)("This will delete the <SelectorName /> selector and its styles. This operation cannot be undone.","generateblocks-pro"),{SelectorName:(0,e.createElement)(w,{currentSelectorName:t})})),(0,e.createElement)("div",{className:c.actions},(0,e.createElement)(o.Button,{variant:"secondary",size:"compact",onClick:()=>{y(!1),l()}},(0,s.__)("Cancel","generateblocks-pro")),(0,e.createElement)(o.Button,{isDestructive:!0,variant:"secondary",size:"compact",onClick:()=>{p(t),r===t&&n(""),y(!1),l()}},(0,s.__)("Confirm","generateblocks-pro")))):(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuItem,{onClick:()=>{u(!0),d(t),l()}},(0,s.__)("Edit selector","generateblocks-pro")),(0,e.createElement)(o.MenuItem,{onClick:()=>{y(!0)},disabled:!h[t],title:h[t]?"":(0,s.__)("No styles to be deleted.","generateblocks-pro")},(0,s.__)("Delete selector","generateblocks-pro")))))));var k}))))))}function A(e,t){var r;return null!==(r=t.find((t=>t.value===e))?.label)&&void 0!==r?r:e}function L({nestedRule:t,onAtRuleChange:r,onNestedRuleChange:n,atRule:l,setShowAtRuleOptions:u,setShowBuildAtRule:d,setEditAtRule:p,onDeleteStyle:h,allStyles:m,currentSelector:f,activeAtRules:g,defaultAtRules:b,allowCustomAtRule:v}){const[y,w]=(0,a.useState)(!1),k=(0,a.useCallback)((e=>t?m?.[t]?.[e]:m?.[e]),[m]);function E({currentAtRule:t}){return(0,e.createElement)("span",{style:{fontFamily:"monospace"}},t)}return(0,e.createElement)("div",{className:c.options},!!g.length&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.BaseControl,{label:(0,s.__)("At-Rules","generateblocks-pro"),id:""},(0,e.createElement)("div",{className:c.list},(0,e.createElement)("span",{className:c.item},(0,e.createElement)(o.Button,{size:"small",onClick:()=>{r(""),u(!1)},isPressed:!l,title:f},(0,s.__)("All screens","generateblocks-pro"),(0,e.createElement)(lt,{allStyles:m,atRule:"",nestedRule:t,showAll:!0}))),g.map((f=>(0,e.createElement)("span",{key:t+f,className:c.item},(0,e.createElement)(o.Button,{size:"small",onClick:()=>{const e=f===l?"":f;n(t),r(e),u(!1)},isPressed:f===l,title:f!==A(f,b)?f:""},A(f,b),(0,e.createElement)(lt,{allStyles:m,nestedRule:t,atRule:f,showAll:!0})),!!v&&(0,e.createElement)(o.DropdownMenu,{icon:i,label:(0,s.__)("Options","generateblocks-pro"),toggleProps:{isPressed:f===l}},(({onClose:n})=>(0,e.createElement)(o.MenuGroup,null,y?(0,e.createElement)("div",{className:c.delete},(0,e.createElement)("p",null,(0,a.createInterpolateElement)(
// Translators: the at-rule for deletion.
// Translators: the at-rule for deletion.
(0,s.__)("This will delete the <AtRuleName /> at-rule and its styles. This operation cannot be undone.","generateblocks-pro"),{AtRuleName:(0,e.createElement)(E,{currentAtRule:f})})),(0,e.createElement)("div",{className:c.actions},(0,e.createElement)(o.Button,{variant:"secondary",size:"compact",onClick:()=>{w(!1),n()}},(0,s.__)("Cancel","generateblocks-pro")),(0,e.createElement)(o.Button,{isDestructive:!0,variant:"secondary",size:"compact",onClick:()=>{h(f,t),l===f&&r(""),w(!1),n()}},(0,s.__)("Confirm","generateblocks-pro")))):(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuItem,{onClick:()=>{d(!0),p(f),n()}},(0,s.__)("Edit at-rule","generateblocks-pro")),(0,e.createElement)(o.MenuItem,{onClick:()=>{w(!0)},disabled:!k(f),title:k(f)?"":(0,s.__)("No styles to be deleted.","generateblocks-pro")},(0,s.__)("Delete at-rule","generateblocks-pro")))))))))))))}const N=window.wp.hooks,z=window.wp.blockEditor,D=window.wp.htmlEntities;var F=r(2895);const B=F;F.stringify,F.fromJSON,F.plugin,F.parse,F.list,F.document,F.comment,F.atRule,F.rule,F.decl,F.root,F.CssSyntaxError,F.Declaration,F.Container,F.Processor,F.Document,F.Comment,F.Warning,F.AtRule,F.Result,F.Input,F.Rule,F.Root,F.Node;var j=r(3268),V=r.n(j),W=r(691),U=r.n(W);const H={attribute:/\[\s*(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?(?<name>[-\w\P{ASCII}]+)\s*(?:(?<operator>\W?=)\s*(?<value>.+?)\s*(\s(?<caseSensitive>[iIsS]))?\s*)?\]/gu,id:/#(?<name>[-\w\P{ASCII}]+)/gu,class:/\.(?<name>[-\w\P{ASCII}]+)/gu,comma:/\s*,\s*/g,combinator:/\s*[\s>+~]\s*/g,"pseudo-element":/::(?<name>[-\w\P{ASCII}]+)(?:\((?<argument>¶*)\))?/gu,"pseudo-class":/:(?<name>[-\w\P{ASCII}]+)(?:\((?<argument>¶*)\))?/gu,universal:/(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?\*/gu,type:/(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?(?<name>[-\w\P{ASCII}]+)/gu},$=new Set(["combinator","comma"]),q=(new Set(["not","is","where","has","matches","-moz-any","-webkit-any","nth-child","nth-last-child"]),e=>{switch(e){case"pseudo-element":case"pseudo-class":return new RegExp(H[e].source.replace("(?<argument>¶*)","(?<argument>.*)"),"gu");default:return H[e]}});function G(e,t){let r=0,n="";for(;t<e.length;t++){const o=e[t];switch(o){case"(":++r;break;case")":--r}if(n+=o,0===r)return n}return n}const Z=/(['"])([^\\\n]+?)\1/g,K=/\\./g;function Y(e,t=H){if(""===(e=e.trim()))return[];const r=[];e=(e=e.replace(K,((e,t)=>(r.push({value:e,offset:t}),"".repeat(e.length))))).replace(Z,((e,t,n,o)=>(r.push({value:e,offset:o}),`${t}${"".repeat(n.length)}${t}`)));{let t,n=0;for(;(t=e.indexOf("(",n))>-1;){const o=G(e,t);r.push({value:o,offset:t}),e=`${e.substring(0,t)}(${"¶".repeat(o.length-2)})${e.substring(t+o.length)}`,n=t+o.length}}const n=function(e,t=H){if(!e)return[];const r=[e];for(const[e,n]of Object.entries(t))for(let t=0;t<r.length;t++){const o=r[t];if("string"!=typeof o)continue;n.lastIndex=0;const a=n.exec(o);if(!a)continue;const s=a.index-1,l=[],i=a[0],c=o.slice(0,s+1);c&&l.push(c),l.push({...a.groups,type:e,content:i});const u=o.slice(s+i.length+1);u&&l.push(u),r.splice(t,1,...l)}let n=0;for(const e of r)switch(typeof e){case"string":throw new Error(`Unexpected sequence ${e} found at index ${n}`);case"object":n+=e.content.length,e.pos=[n-e.content.length,n],$.has(e.type)&&(e.content=e.content.trim()||" ")}return r}(e,t),o=new Set;for(const e of r.reverse())for(const t of n){const{offset:r,value:n}=e;if(!(t.pos[0]<=r&&r+n.length<=t.pos[1]))continue;const{content:a}=t,s=r-t.pos[0];t.content=a.slice(0,s)+n+a.slice(s+n.length),t.content!==a&&o.add(t)}for(const e of o){const t=q(e.type);if(!t)throw new Error(`Unknown token type: ${e.type}`);t.lastIndex=0;const r=t.exec(e.content);if(!r)throw new Error(`Unable to parse content for ${e.type}: ${e.content}`);Object.assign(e,r.groups)}return n}const Q={a:"a:where(:not(.components-external-link))",button:"button:where(:not(.components-button))"};function X(e){let t="",r="",n="";return e.forEach((e=>{const{prop:o,value:a}=e;o.includes("width")?t=a:o.includes("style")?r=a:o.includes("color")&&(n=a)})),t&&r&&n?`${t} ${r} ${n}`:""}function J(e){let t="",r="",n="",o="";return e.forEach((e=>{const{prop:a,value:s}=e;a.includes("top")?t=s:a.includes("right")?r=s:a.includes("bottom")?n=s:a.includes("left")&&(o=s)})),[t,r,n,o].some((e=>""===e))?"":t===n&&r===o?t===r?t:`${t} ${r}`:`${t} ${r} ${n} ${o}`}const ee=e=>/^([-]?\d|[-]?\.)/.test(e);function te(e){var t;return e?(null!==(t=generateBlocksEditor?.wpContentUrl)&&void 0!==t?t:location.origin+"/wp-content")+e:""}function re(e=[]){return e.reduce(((e,t)=>{if(t.hidden)return e;const{type:r,backgroundAttachment:n="",backgroundImage:o="",backgroundSize:a="",backgroundRepeat:s="",backgroundPosition:l=""}=t;if(!o)return e;let i="";return o&&(i+=`${o.replaceAll("'","").replaceAll('"',"")}`),"image"===r&&(l&&(i+=` ${l}`),a&&(l||(i+="0% 0% "),i+=` / ${a}`),s&&(i+=` ${s}`),n&&(i+=` ${n}`)),i.length?e.length>0?`${e}, ${i}`:`${i}`:e}),"").replace(/,$/,"").trim()}function ne(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}const oe=/(?:#(?:[0-9a-fA-F]{3}){1,2}|(?:rgb|rgba|hsl|hsla)\(\s*\d+%?\s*(?:,\s*\d+%?\s*){2,3}(?:,\s*(?:0?\.\d+|1|100%))?\))/g,ae=/(linear-gradient|radial-gradient)\([^)]*\)(?:\s*\d+%?,\s*(?:rgba?\([^)]*\)|#[0-9a-fA-F]+)\s*\d*%?)+\s*\)|url\([^)]*\)[^,)]*/g;function se(e=""){const t=te("/plugins/generateblocks-pro/dist/assets/placeholder-lg.min.jpg");return""===e?{type:"image",media:{selectedSize:"full",sizes:{}},backgroundAttachment:"",backgroundImage:`url(${t})`,backgroundPosition:"center",backgroundSize:"cover",backgroundRepeat:"no-repeat",backgroundBlendMode:"normal"}:{type:"image",media:{id:0},backgroundAttachment:"",backgroundImage:"",backgroundPosition:"",backgroundSize:"",backgroundRepeat:"",backgroundBlendMode:""}}function le(){const e="rgba(0, 0, 0, .25)",t=`linear-gradient(to right, ${e} 0%, ${e} 100%)`;return{...se(),type:"overlay",backgroundBlendMode:"normal",backgroundImage:t,overlayColor:e}}function ie(e){const t=e.replace(/^(linear-gradient|radial-gradient)\(|\)$/g,"").trim(),r={type:e.includes("linear-gradient")?"linear-gradient":"radial-gradient",angle:null,colorStops:[]};let n=t;const o=n.match(/^(to\s+(?:top|bottom|left|right)(?:\s+(?:top|bottom|left|right))?|\d+deg|circle|ellipse(?:\s+at\s+center)?|closest-side|closest-corner|farthest-side|farthest-corner)/);for(o&&(r.angle=o[0],n=n.slice(o[0].length).trim());n.length>0;){let e="",t=0;for(let r=0;r<n.length;r++){const o=n[r];if("("===o&&t++,")"===o&&t--,","===o&&0===t)break;e+=o}if(e=e.trim(),n=n.slice(e.length+1).trim(),!e)continue;const o=e.match(/\s+(\d+(?:\.\d+)?%)$/),a=o?{type:"%",value:parseFloat(o[1])}:null,s=o?e.slice(0,-o[0].length).trim():e;r.colorStops.push({value:s,length:a})}return r}function ce(){const e=["background-attachment","background-image","background-size","background-repeat","background-position"];function t(t){var r;const n=t.nodes.filter((e=>e.prop&&e.prop.startsWith("background-")));if(0===n.length)return;const o=null!==(r=n.find((e=>"background-image"===e.prop)))&&void 0!==r?r:{value:""},a=Oe(o.value);if(a&&a.length>1)try{var s,l,i,c;const r=null!==(s=n.find((e=>"background-attachment"===e.prop)))&&void 0!==s?s:{value:""},a=null!==(l=n.find((e=>"background-size"===e.prop)))&&void 0!==l?l:{value:""},u=null!==(i=n.find((e=>"background-repeat"===e.prop)))&&void 0!==i?i:{value:""},d=null!==(c=n.find((e=>"background-position"===e.prop)))&&void 0!==c?c:{value:""},p=Te({backgroundAttachment:r.value,backgroundSize:a.value,backgroundImage:o.value,backgroundRepeat:u.value,backgroundPosition:d.value});n.forEach((t=>{const r=t.prop;e.includes(r)&&t.remove()})),t.append(B.decl({prop:"background",value:re(p)}))}catch(e){console.error(e.messsage)}}return{postcssPlugin:"combine-background-properties",Once(e){const r=e.nodes.filter((e=>"rule"===e.type)),n=e.nodes.filter((e=>"atrule"===e.type));r.forEach(t),n.forEach((e=>{e.nodes.forEach(t)}))}}}const ue=/(!?\(\s*min(-device)?-width)(.|\n)+\(\s*max(-device)?-width|\(\s*width\s*>(=)?(.|\n)+\(\s*width\s*<(=)?|(!?\(.*<(=)?\s*width\s*<(=)?)/i,de=/(!?\(\s*max(-device)?-width)(.|\n)+\(\s*min(-device)?-width|\(\s*width\s*<(=)?(.|\n)+\(\s*width\s*>(=)?|(!?\(.*>(=)?\s*width\s*>(=)?)/i,pe=Ee(ue,de,/\(\s*min(-device)?-width|\(\s*width\s*>(=)?/i),he=Ee(de,ue,/\(\s*max(-device)?-width|\(\s*width\s*<(=)?/i),me=/(!?\(\s*min(-device)?-height)(.|\n)+\(\s*max(-device)?-height|\(\s*height\s*>(=)?(.|\n)+\(\s*height\s*<(=)?|(!?\(.*<(=)?\s*height\s*<(=)?)/i,fe=/(!?\(\s*max(-device)?-height)(.|\n)+\(\s*min(-device)?-height|\(\s*height\s*<(=)?(.|\n)+\(\s*height\s*>(=)?|(!?\(.*>(=)?\s*height\s*>(=)?)/i,ge=Ee(me,fe,/\(\s*min(-device)?-height|\(\s*height\s*>(=)?/i),be=Ee(fe,me,/\(\s*max(-device)?-height|\(\s*height\s*<(=)?/i),ve=/print/i,ye=/^print$/i,we=Number.MAX_VALUE;function ke(e){let t=/(-?\d*\.?\d+)(ch|em|ex|px|rem)/.exec(e);if(null===t&&(pe(e)||ge(e))&&(t=/(\d)/.exec(e)),"0"===t)return 0;if(null===t)return we;let r=t[1];switch(t[2]){case"ch":r=8.8984375*parseFloat(r);break;case"em":case"rem":r=16*parseFloat(r);break;case"ex":r=8.296875*parseFloat(r);break;case"px":r=parseFloat(r)}return+r}function Ee(e,t,r){return function(n){return!!e.test(n)||!t.test(n)&&r.test(n)}}const Se=!0;function xe(e,t){const r=function(e,t){const r=ve.test(e),n=ye.test(e),o=ve.test(t),a=ye.test(t);return r&&o?!n&&a?1:n&&!a?-1:e.localeCompare(t):r?1:o?-1:null}(e,t);if(null!==r)return r;const n=pe(e)||ge(e),o=he(e)||be(e),a=pe(t)||ge(t),s=he(t)||be(t);if(Se&&(!n&&!o||!a&&!s))return n||o||a||s?a||s?-1:1:e.localeCompare(t);if(n&&s)return-1;if(o&&a)return 1;const l=ke(e),i=ke(t);return l===we&&i===we?e.localeCompare(t):l===we?1:i===we?-1:l>i?o?-1:1:l<i?o?1:-1:e.localeCompare(t)}function Ce(e={onlyTopLevel:!1}){return{postcssPlugin:"postcss-gb-sort-media-queries",Once(t,{AtRule:r}){const n=[];t.walkAtRules("media",(t=>{if(e.onlyTopLevel&&"root"===t.parent.type){const e=t.params;n[e]||(n[e]=new r({name:t.name,params:t.params,source:t.source})),t.nodes.forEach((t=>{n[e].append(t.clone())})),t.remove()}if(!e.onlyTopLevel){const e=t.params;n[e]||(n[e]=new r({name:t.name,params:t.params,source:t.source})),t.nodes.forEach((t=>{n[e].append(t.clone())})),t.remove()}})),n&&Object.keys(n).sort(xe).forEach((e=>{t.append(n[e])}))}}}function _e(e){let t=e.trim();t=t.replace(/url\s*\(\s*(['"]?)([^)]*)\)?/g,((e,t,r)=>`url('${r=r.replace(/^['"]|['"]$/g,"")}')`));const r=[];let n="";for(let e=0;e<t.length;e++){const o=t[e];if("("===o)r.push(e);else if(")"===o){if(!(r.length>0))continue;r.pop()}n+=o}return n+=")".repeat(r.length),(n.match(/"/g)||[]).length%2!=0&&(n+='"'),n}function Oe(e){const t=[];let r=0,n=0;for(let o=0;o<e.length;o++)"("===e[o]?r++:")"===e[o]?r--:","===e[o]&&0===r&&(t.push(e.slice(n,o).trim()),n=o+1);return t.push(e.slice(n).trim()),t}function Te({backgroundAttachment:e="",backgroundImage:t="",backgroundSize:r="",backgroundRepeat:n="",backgroundPosition:o="",backgroundBlendMode:a=""}){const s=/(?:#(?:[0-9a-fA-F]{3}){1,2}|(?:rgb|rgba|hsl|hsla)\(\s*\d+%?\s*(?:,\s*\d+%?\s*){2,3}(?:,\s*(?:0?\.\d+|1|100%))?\)|var\(--[^\s)]+\))/g,l=e.split(","),i=r.split(","),c=n.split(","),u=o.split(","),d=Oe(t),p=a.split(",");let h=0;const m=[];for(;h<d.length;){var f,g,b,v,y;const e=d[h];let t="image",r="";if(e.includes("gradient(")){t="gradient";const n=e.match(s);n&&n.every((e=>e===n[0]))&&(t="overlay",r=n[0])}m.push({backgroundAttachment:null!==(f=l[h])&&void 0!==f?f:"",backgroundSize:null!==(g=i[h])&&void 0!==g?g:"",backgroundRepeat:null!==(b=c[h])&&void 0!==b?b:"",backgroundPosition:null!==(v=u[h])&&void 0!==v?v:"",backgroundImage:d[h],backgroundBlendMode:null!==(y=p[h])&&void 0!==y?y:"",type:t,overlayColor:r,media:{id:0}}),h++}return m}function Ie(e){const t={atRules:{},nestedRules:{},propertyRules:{}};for(const[n,o]of Object.entries(e))if(n.startsWith("@")){var r;const e=null!==(r=t?.atRules?.[n])&&void 0!==r?r:{};t.atRules[n]={...e,...o}}else"object"==typeof o?Object.entries(o).forEach((([e,r])=>{if(e.startsWith("@")){var o;const a=null!==(o=t?.atRules?.[e])&&void 0!==o?o:{};t.atRules[e]={...a,[n]:r}}else{var a;const o=null!==(a=t?.nestedRules?.[n])&&void 0!==a?a:{};t.nestedRules[n]={...o,[e]:r}}})):t.propertyRules[n]=o;function n(e){return Object.keys(e).sort().reduce(((t,r)=>(t[r]=e[r],t)),{})}return{atRules:n(t.atRules),nestedRules:n(t.nestedRules),propertyRules:n(t.propertyRules)}}function Re(e){return e.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g,"$1-$2").toLowerCase()}function Me(e,t,r){e+=`${t} {`;for(let[t,n]of Object.entries(r))"settings"!==t&&(t=Re(t),e+=`${t}:${_e(n.toString())};`);return e+"}"}function Pe(e,t=""){const r=(0,D.decodeEntities)(t);if(!r)return e;const n=e.split(",").map((e=>e.trim())),o=[];return n.forEach((e=>{o.push(r.startsWith("&")?e+r.replace("&",""):e+" "+r)})),o.join(",")}async function Ae(e,t,r="frontend"){var n;const o=(0,N.applyFilters)("generateStylesBuilder.rawCss",function(e,t){if(!e)return"";let r="";if((t=Ie(t)).propertyRules&&Object.keys(t.propertyRules).length>0&&(r=Me(r,Pe(e),t.propertyRules)),t.nestedRules&&Object.keys(t.nestedRules).length>0)for(const n in t.nestedRules)r=Me(r,Pe(e,n),t.nestedRules[n]);if(t.atRules&&Object.keys(t.atRules).length>0)for(const n in t.atRules){const o=Ie(t.atRules[n]);if(r+=`${n} {`,o.propertyRules&&Object.keys(o.propertyRules).length>0&&(r=Me(r,e,o.propertyRules)),o.nestedRules&&Object.keys(o.nestedRules).length>0)for(const t in o.nestedRules)r=Me(r,Pe(e,t),o.nestedRules[t]);r+="}"}return r}(e,t)),a=[{css:o}],s="editor"===r?(0,z.transformStyles)(a,".editor-styles-wrapper")?.[0]:o;let l=[{postcssPlugin:"gb-editor-transforms",Rule(e){let t=e.selector;Object.keys(Q).forEach((e=>{const r=new RegExp(`(^|\\s|>|\\+|~)(${e})(\\s|$|>|\\+|~)(?!:where)`,"g");t=t.replace(r,`$1${Q[e]}$3`)})),e.selector=t}}];"frontend"===r&&(l=[V()(),{postcssPlugin:"gb-merge-longhand",OnceExit(e){e.walkRules((e=>{const t=e.nodes.filter((e=>e.prop&&e.prop.startsWith("border-")&&!e.prop.includes("radius"))),r=e.nodes.filter((e=>e.prop&&e.prop.startsWith("border-")&&e.prop.includes("radius"))),n=e.nodes.filter((e=>e.prop&&e.prop.startsWith("margin-"))),o=e.nodes.filter((e=>e.prop&&e.prop.startsWith("padding-")));if(t){const r=[],n=t.filter((e=>e.prop.startsWith("border-top"))),o=t.filter((e=>e.prop.startsWith("border-right"))),a=t.filter((e=>e.prop.startsWith("border-bottom"))),s=t.filter((e=>e.prop.startsWith("border-left"))),l={top:!1,left:!1,bottom:!1,right:!1},i=X(n);i&&(r.push(X(n)),l.top=!0);const c=X(o);c&&(r.push(X(o)),l.right=!0);const u=X(a);u&&(r.push(X(a)),l.bottom=!0);const d=X(s);d&&(r.push(X(s)),l.left=!0),4===r.length&&r.every((e=>e===r[0]))?(e.append({prop:"border",value:r[0]}),t.forEach((e=>e.remove()))):(l.top&&(e.append({prop:"border-top",value:i}),n.forEach((e=>e.remove()))),l.right&&(e.append({prop:"border-right",value:c}),o.forEach((e=>e.remove()))),l.bottom&&(e.append({prop:"border-bottom",value:u}),a.forEach((e=>e.remove()))),l.left&&(e.append({prop:"border-left",value:d}),s.forEach((e=>e.remove()))))}if(4===r.length){const t=function(e){let t="",r="",n="",o="";return e.forEach((e=>{const{prop:a,value:s}=e;a.includes("top-left")?t=s:a.includes("top-right")?r=s:a.includes("bottom-right")?n=s:a.includes("bottom-left")&&(o=s)})),t&&r&&n&&o?t===n&&r===o?t===r?t:`${t} ${r}`:`${t} ${r} ${n} ${o}`:""}(r);e.append({prop:"border-radius",value:t}),r.forEach((e=>e.remove()))}if(4===n.length){const t=J(n);e.append({prop:"margin",value:t}),n.forEach((e=>e.remove()))}if(4===o.length){const t=J(o);e.append({prop:"padding",value:t}),o.forEach((e=>e.remove()))}})),e.rawCache={beforeDecl:"",beforeRule:"",colon:":",indent:"",after:"",semicolon:!0}}},ce(),Ce(),U()()]);const i=await B(l).process(s,{from:void 0}).catch((e=>{console.error(e.message)}));return null!==(n=i?.css)&&void 0!==n?n:s}function Le(e){return"object"!=typeof e?{}:Object.entries(e).reduce(((e,[t,r])=>{if("object"==typeof r){const n=Le(r);Object.keys(n).length>0&&(e[t]=n)}else if(""!==r&&null!=r){const n=r.toString().replace(";","").replace("{","").replace("}","");e[t]=n}return e}),{})}class Ne{constructor({value:e,settings:t=[]}){this.value=e,this.settings=t}toString(){return this.value}}function ze(e,t,r,n,o){let a={};const s="object"==typeof r&&null!==r?new Ne(r):r;if(o){var l;const r=null!==(l=e?.[o])&&void 0!==l?l:{};if(n){var i;const l=null!==(i=e?.[o]?.[n])&&void 0!==i?i:{};a={...e,[o]:{...r,[n]:{...l,[t]:s}}}}else a={...e,[o]:{...r,[t]:s}}}else if(n){var c;const r=null!==(c=e?.[n])&&void 0!==c?c:{};a={...e,[n]:{...r,[t]:s}}}else a={...e,[t]:s};return Le(a)}function De(e,t,r,n=""){const o={},a=n&&t.startsWith("@"),s=a?e[n]:e;return Object.entries(s).forEach((([e,n])=>{e===t?o[r]=n:o[e]=n})),a?{...e,[n]:o}:o}function Fe(e,t,r=""){const n={},o=r&&t.startsWith("@"),a=o?e[r]:e;return Object.entries(a).forEach((([e,r])=>{e!==t&&(n[e]=r)})),o?{...e,[r]:n}:n}function Be(e,t="",r=""){return"object"!=typeof e||Array.isArray(e)?{}:r?t?null!==(o=e?.[r]?.[t])&&void 0!==o?o:{}:null!==(n=e?.[r])&&void 0!==n?n:{}:t?null!==(a=e?.[t])&&void 0!==a?a:{}:null!=e?e:{};var n,o,a}function je(e){return"CSSMediaRule"===e.constructor.name?{name:"media",type:CSSMediaRule}:"CSSSupportsRule"===e.constructor.name?{name:"supports",type:CSSSupportsRule}:"CSSContainerRule"===e.constructor.name?{name:"container",type:window.CSSContainerRule}:{name:"unknown",type:Error}}function Ve({element:e,properties:t,sources:r=["inline","tag"],computedStyles:n=null,deviceAttributes:o={},atRule:a="",nestedRule:s=""}){if(!n||!t||0===t.length)return null;const l=document.querySelector('iframe[name="editor-canvas"]'),i=l?l.contentDocument:document,c=l?l.contentWindow:window;if("loading"===i.readyState&&l)return l.addEventListener("load",(()=>Ve({element:e,properties:t,sources:r,computedStyles:n,deviceAttributes:o,atRule:a,nestedRule:s})),{once:!0}),{source:"other",selector:"",value:""};const u=null!=n?n:{},d={},p=r.includes("tag")?i.querySelectorAll("style"):[],h=r.includes("stylesheet")?i.styleSheets:[],m=["width","height","minWidth","minHeight","maxWidth","maxHeight"],f=["fontFamily","fontSize","fontWeight","letterSpacing","lineHeight","textAlign","textTransform"],g=e=>null!=e&&""!==e;for(const E of t){var b;const S=E.startsWith("padding")||E.startsWith("margin"),x=m.includes(E),C=E.startsWith("border");if(f.includes(E)){var v;const I=null!==(v=o?.typography?.[E])&&void 0!==v?v:"";if(g(I)){d[E]={source:"local",selector:"",value:I};continue}}else if(C){var y;const R=null!==(y=o?.borders?.[E])&&void 0!==y?y:"";if(g(R)){d[E]={source:"local",selector:"",value:R};continue}}else if(S){var w;const M=null!==(w=o?.spacing?.[E])&&void 0!==w?w:"";if(g(M)){d[E]={source:"local",selector:"",value:M};continue}}else if(x){var k;const P=null!==(k=o?.sizing?.[E])&&void 0!==k?k:"";if(g(P)){d[E]={source:"local",selector:"",value:P};continue}}else if(g(o?.[E])){d[E]={source:"local",selector:"",value:o[E]};continue}const _=null!==(b=u[E])&&void 0!==b?b:null;if(null===_){d[E]={source:null,selector:"",value:_};continue}const O=[".editor-styles-wrapper .gb-container, .editor-styles-wrapper .gb-headline, .editor-styles-wrapper .gb-button"];function T(t,r,o=""){for(const a of t){const t=!!a?.cssRules&&a.cssRules.length>0,i=!!a?.conditionText;if("CSSStyleRule"===a.constructor.name){let t=E in a.style?a.style[E]:null;if(t?.startsWith("var(")&&(e.style[E]=t,t=u[E]||t,e.style[E]=""),!g(t))continue;if(O.includes(a.selectorText))continue;if(o){const e=o.replace("@media ",""),t=c.matchMedia(e).matches;if(l&&l.contentWindow?.innerWidth+"px"!==l.style.width)continue;if(!t)continue}if(Y(a.selectorText).some((e=>"pseudo-class"===e.type))&&!s)continue;if(e.matches(a.selectorText)){let s=!1;if(S||x||C||["fontSize","lineHeight","letterSpacing","transform"].includes(E)){e.style[E]=t;const r=n?.[E];e.style[E]="",r===_&&(s=!0)}else t===_&&(s=!0);if(s){d[E]={source:r,selector:a.selectorText,value:_,valueRaw:t,atRule:o};break}}}else if(t&&i){const e=je(a);if("media"!==e.name)return;const t=Ue(`@${e.name} ${a.conditionText}`);T(a.cssRules,r,t)}}}if(r.includes("inline")&&!a){const A=e.style[E];if(A&&null!==_){d[E]={source:"inline",selector:"",value:_,valueRaw:A};continue}}if(r.includes("tag")){for(const L of p)try{T(L.sheet.cssRules,"tag")}catch(N){console.error(N.message)}if(E in d)continue}if(r.includes("stylesheet")){for(const z of h)try{T(z.rules||z.cssRules,"stylesheet")}catch(D){console.error("Error accessing stylesheet:",D.message)}if(E in d)continue}d[E]={source:"other",selector:"",value:_}}return d}function We({elements:e,properties:t,computedStyles:r=null,deviceAttributes:n={},atRule:o="",nestedRule:a="",sources:s=["inline","tag"]}){const l=!Array.isArray(t),i=l?[t]:t;if(Array.isArray(e))return e.map((e=>{const c=Ve({element:e,properties:i,computedStyles:r,deviceAttributes:n,atRule:o,nestedRule:a,sources:s});return l?c[t]:c}));const c=Ve({element:e,properties:i,computedStyles:r,deviceAttributes:n,atRule:o,nestedRule:a,sources:s});return l?c[t]:c}function Ue(e){let t=e;return t=t.replace("@media(","@media ("),t=t.replace("@supports(","@supports ("),t=t.replace("@container(","@container ("),t.includes(":")&&(t=t.replace(/\([^()]*\)/g,(e=>e.replace(/\s+/g,"")))),t}const He={all:"",largeWidth:"@media (min-width:1025px)",mediumWidth:"@media (max-width:1024px) and (min-width:768px)",mediumSmallWidth:"@media (max-width:1024px)",mediumLargeWidth:"@media (min-width:768px)",smallWidth:"@media (max-width:767px)"},$e=(0,N.applyFilters)("generateblocks.styles.defaultAtRules",[{label:(0,s.__)("All screens","generateblocks-pro"),value:"",icon:()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M57.128 31.707h5.556v5.556h-5.556zM75.489 50.836h3.703v3.704H75.49z"}),(0,e.createElement)("path",{d:"M93.396 15.181c1.926.074 3.598 1.704 3.673 3.676.263 20.76 0 41.528 0 62.287-.025 1.972-1.691 3.695-3.673 3.769-28.896.37-57.797.009-86.695.009-1.966-.028-3.694-1.695-3.769-3.676-.263-20.76-.001-41.528-.001-62.287.025-1.972 1.692-3.695 3.673-3.778 28.928-.361 57.864-.361 86.792 0Zm-4.772 65.111h3.816V19.811H7.56v60.481h31.664c-.21-17.342.006-34.676.006-52.009.027-2.13 1.792-4.037 4.014-4.12 11.109-.14 22.222-.14 33.331 0 2.113.083 3.93 1.86 4.012 4.009.07 5.565.096 11.12.1 16.685 1.907.01 3.817.028 5.725.046 1.147.047 2.139 1.028 2.183 2.186.14 11.064.15 22.13.03 33.203Zm-3.886 0V48.764H69.946v31.528h14.792Zm-7.962-35.444c-.023-5.51-.045-11.028-.045-16.537 0-.167-.107-.278-.245-.287-11.03-.417-22.075 0-33.112 0-.163 0-.28.11-.285.25-.225 17.333-.005 34.676-.001 52.018H66.06c-.12-11.074-.11-22.139.029-33.203.044-1.149 1.028-2.14 2.183-2.186 2.835-.037 5.67-.055 8.504-.055Z"})),show:!0,id:"all"},{label:(0,s.__)("Desktop","generateblocks-pro"),value:He.largeWidth,icon:()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 417 417",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M389.233 63.228c7.989.304 14.98 7.083 15.229 15.28 1.082 86.514 0 173.028 0 259.542-.083 8.196-6.99 15.38-15.23 15.684-120.42 1.518-240.84 0-361.26 0-8.155-.101-15.395-7.083-15.728-15.28-1.082-86.513 0-173.028 0-259.542.083-8.196 7.074-15.38 15.312-15.684a13466.862 13466.862 0 0 1 361.677 0ZM31.55 82.454v251.953h353.687V82.454H31.551Z"})),show:!1,id:"largeWidth"},{label:(0,s.__)("Desktop & tablet","generateblocks-pro"),value:He.mediumLargeWidth,icon:()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 417 417",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M389.145 63.263c8.025.309 15.008 7.099 15.317 15.316 1.08 86.497 0 173.033 0 259.53-.116 8.217-7.06 15.393-15.317 15.702-120.409 1.543-240.818.038-361.227.038-8.179-.115-15.393-7.06-15.702-15.316-1.08-86.497 0-173.032 0-259.53.077-8.217 7.06-15.393 15.317-15.74a14485.169 14485.169 0 0 1 361.612 0Zm-3.973 19.29H31.506v252.006h145.487c-.926-72.762 0-145.524 0-218.287.115-8.873 7.484-16.82 16.705-17.168 46.296-.579 92.592-.579 138.889 0 8.796.347 16.396 7.755 16.744 16.705.926 72.917 0 145.834 0 218.75h35.84V82.553Zm-51.89 252.006c2.7-72.685-.04-145.486-.04-218.21 0-.617-.424-1.119-1.002-1.157-45.988-1.736-91.976 0-137.963 0-.695 0-1.196.463-1.196 1.042-.926 72.762 0 145.524 0 218.325h140.2Z"}),(0,e.createElement)("path",{d:"M251.558 132.111h23.148v23.15h-23.148z"})),show:!1,id:"mediumLargeWidth"},{label:(0,s.__)("Tablet","generateblocks-pro"),value:He.mediumWidth,icon:()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M0 0h24v24H0z",style:{fill:"none",transform:"scale(4.16667)"}}),(0,e.createElement)("path",{d:"M69.096 15.124c2.445.045 4.626 2.189 4.659 4.673.132 20.139.396 40.278 0 60.417-.066 2.396-2.181 4.525-4.593 4.599a969.265 969.265 0 0 1-38.233 0c-2.412-.074-4.527-2.174-4.61-4.599-.38-20.154-.38-40.322 0-60.491.083-2.395 2.181-4.525 4.61-4.599 12.722-.236 25.444-.074 38.167 0ZM31.06 19.131c-.38 0-.727.311-.743.695-.397 20.08 0 40.175 0 60.255 0 .385.314.725.694.74 12.672.413 25.345 0 38.017 0 .397 0 .727-.34.727-.74.017-20.065.017-40.145 0-60.21 0-.4-.314-.725-.694-.74-12.656-.414-25.328 0-38 0Z"}),(0,e.createElement)("path",{d:"M46.842 22.597h6.4v6.4h-6.4z"})),show:!1,id:"mediumWidth"},{label:(0,s.__)("Tablet & mobile","generateblocks-pro"),value:He.mediumSmallWidth,icon:()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M42.284 22.603h6.4v6.4h-6.4zM63.082 45.178h4.8v4.8h-4.8z"}),(0,e.createElement)("path",{d:"M61.364 84.873c-11.663.184-23.333.168-34.996-.056-2.4-.072-4.528-2.183-4.6-4.6a1588.412 1588.412 0 0 1 0-60.489c.072-2.4 2.184-4.527 4.6-4.599 12.726-.24 25.453-.08 38.18 0 2.431.04 4.615 2.192 4.663 4.663.04 6.36.088 12.711.128 19.07 2.184.008 4.368.032 6.551.056 1.312.048 2.456 1.184 2.504 2.504.176 13.63.176 27.261 0 40.892-.048 1.32-1.176 2.455-2.504 2.503-4.839.064-9.678.08-14.526.056Zm3.104-65.744H26.504c-.4 0-.728.336-.736.711-.112 20.078 0 40.164 0 60.242 0 .368.32.72.688.736 8.703.28 17.398.176 26.101.088a1665.8 1665.8 0 0 1 .016-39.484c.056-1.32 1.184-2.456 2.504-2.504 3.423-.04 6.839-.064 10.255-.064a1880.03 1880.03 0 0 1-.12-18.99.751.751 0 0 0-.744-.735Zm9.927 23.789H56.573v37.9h17.822v-37.9Z"})),show:!0,id:"mediumSmallWidth"},{label:(0,s.__)("Mobile","generateblocks-pro"),value:He.smallWidth,icon:()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M62.043 23.435c1.52.056 2.841 1.364 2.893 2.897.208 15.781.208 31.563 0 47.333-.052 1.533-1.363 2.84-2.893 2.897-8.025.101-16.06.101-24.085 0-1.53-.056-2.84-1.364-2.903-2.897-.198-15.77-.198-31.552 0-47.333.062-1.522 1.363-2.84 2.903-2.897 8.025-.102 16.06-.102 24.085 0Zm-22.357 4.633V71.94h20.629V28.068H39.686Z"}),(0,e.createElement)("path",{d:"M47.22 30.36h5.556v5.557h-5.555z"})),show:!0,id:"smallWidth"}]);function qe(e="object"){return"object"===e?$e.reduce(((e,t)=>(e[t.id]=t,e)),{}):$e}function Ge(e){const t=$e.find((t=>t.id===e)),r=He[e];return t?.value||r}const Ze=["display","alignItems","justifyContent","justifyItems","justifySelf","alignSelf","gridTemplateColumns","gridTemplateRows","columnGap","rowGap","gridAutoFlow","gridColumn","gridRow","order","flexDirection","flexWrap","flexGrow","flexShrink","flexBasis","containerType","containerName","float","clear"],Ke=["width","height","minWidth","minHeight","maxWidth","maxHeight","aspectRatio"],Ye=["paddingTop","paddingRight","paddingBottom","paddingLeft","marginTop","marginRight","marginBottom","marginLeft"],Qe=["borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderTopColor","borderRightColor","borderBottomColor","borderLeftColor","borderTopStyle","borderRightStyle","borderBottomStyle","borderLeftStyle","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"],Xe=["color","fontSize","fontWeight","fontStyle","textAlign","textTransform","textDecoration","lineHeight","letterSpacing","fontFamily","whiteSpace"],Je=["background","backgroundColor","backgroundClip","backgroundOrigin","backgroundBlendMode","backgroundAttachment","backgroundImage","backgroundPosition","backgroundSize","backgroundRepeat"],et=["position","overflowX","overflowY","zIndex","top","right","bottom","left"],tt=["backdropFilter","boxShadow","filter","mixBlendMode","opacity","transform","transformOrigin","transition","visibility","textShadow"],rt=["objectFit","objectPosition"],nt=["listStyleType","listStyleImage","listStylePosition"],ot=["content","pointerEvents","fill","stroke","cursor"],at=[...Ze,...Ke,...Ye,...Qe,...Xe,...Je,...et,...tt,...rt,...nt,...ot];function st({editAtRule:t="",setShowBuildAtRule:r,allStyles:n,onAtRuleChange:l,setTempAtRule:i,removeTempAtRule:u,onUpdateKey:d,defaultAtRules:p,setShowAtRuleOptions:h,setEditAtRule:m,nestedRule:f}){const[g,b]=(0,a.useState)(""),[v,y]=(0,a.useState)("");return(0,a.useEffect)((()=>{b(t||"")}),[]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:c.options},(0,e.createElement)(o.BaseControl,{label:(0,s.__)("At-Rule","generateblocks-pro"),className:c.build,id:"class-selector"},(0,e.createElement)(o.TextControl,{id:"class-selector",value:g,onChange:e=>{y(""),b(e)},onBlur:()=>{g&&(["@media","@supports","@container"].some((e=>g.startsWith(e)))?b(Ue(g)):y((0,s.__)("Invalid at-rule.","generateblocks-pro")))}})),(0,e.createElement)("div",{className:c.list},p.filter((e=>e.value)).map((t=>(0,e.createElement)(o.Button,{key:t.value,size:"small",onClick:()=>{const e=t.value===g?"":t.value;b(e)},isPressed:t.value===g},t.label))))),(0,e.createElement)("div",{className:c.actions},(0,e.createElement)(o.Button,{variant:"tertiary",onClick:()=>{r(!1),m("")}},(0,s.__)("Cancel","generateblocks-pro")),!t&&(0,e.createElement)(o.Button,{variant:"primary",disabled:!g||v,onClick:()=>{(f?n?.[f]:n)[g]?y((0,s.__)("At-rule already exists.","generateblocks-pro")):(l(g),i(g),r(!1),h(!1))}},(0,s.__)("Create","generateblocks-pro")),!!t&&(0,e.createElement)(o.Button,{variant:"primary",disabled:!g||t===g,onClick:()=>{(f?n?.[f]:n)[g]?y((0,s.__)("At-rule already exists.","generateblocks-pro")):(d(t,g,f),l(g),i(g),u(t),r(!1),h(!1),m(""))}},(0,s.__)("Update","generateblocks-pro"))),!!v&&(0,e.createElement)(o.Notice,{status:"error",isDismissible:!1,style:c.notice},v))}function lt({allStyles:t,atRule:r="",nestedRule:n="",showAll:o=!1}){const a=T(),{getValueSources:l}=a;let i=!1,u=!1,d=!1;if(n)if(r){var p;const e=l(n,r,null!==(p=t?.[n]?.[r])&&void 0!==p?p:{});i=e.some((({source:e})=>"global"===e)),u=e.some((({source:e})=>"local"===e)),d=e.some((({source:e})=>"current"===e))}else{var h;const e=l(n,r,n?null!==(h=t?.[n])&&void 0!==h?h:{}:t);i=e.some((({source:e})=>"global"===e)),u=e.some((({source:e})=>"local"===e)),d=e.some((({source:e})=>"current"===e))}else if(r){var m;const e=l(n,r,null!==(m=t?.[r])&&void 0!==m?m:{});i=e.some((({source:e})=>"global"===e)),u=e.some((({source:e})=>"local"===e)),d=e.some((({source:e})=>"current"===e))}else{const e=l(n,r,t);i=e.some((({source:e})=>"global"===e)),u=e.some((({source:e})=>"local"===e)),d=e.some((({source:e})=>"current"===e))}return i||u||d?o?(0,e.createElement)("div",{className:c.indicatorDots},!!d&&(0,e.createElement)("div",{title:(0,s.__)("Has local styles","generateblocks-pro"),className:f(c.dot,c.current)}),!!u&&(0,e.createElement)("div",{title:(0,s.__)("Has inherited local styles","generateblocks-pro"),className:f(c.dot,c.local)}),!!i&&(0,e.createElement)("div",{title:(0,s.__)("Has inherited global styles","generateblocks-pro"),className:c.dot})):(0,e.createElement)("span",{title:(0,s.__)("Styles exist for this at-rule","generateblocks-pro"),className:f(c.dot,{[c.current]:d,[c.local]:u&&!d,[c.global]:i&&!u&&!d})}):null}function it({allStyles:t,nestedRule:r="",shouldShowRule:n,defaultAtRules:o}){const a=T(),{getValueSources:l}=a;let i=!1,u=!1,d=!1;if(r){var p;const e=null!==(p=t?.[r])&&void 0!==p?p:{},a=Object.entries(e).filter((([e])=>e.startsWith("@")));a&&a.forEach((t=>{var a;const s=null!==(a=t?.[0])&&void 0!==a?a:"",c=o.find((e=>e.value===s)),p=l(r,s,e);c&&n(c)||(i=p.some((({source:e})=>"global"===e)),u=p.some((({source:e})=>"local"===e)),d=p.some((({source:e})=>"current"===e)))}))}else{const e=t&&Object.entries(t).filter((([e])=>e.startsWith("@")));e&&e.forEach((e=>{var t;const a=null!==(t=e?.[0])&&void 0!==t?t:"",s=o.find((e=>e.value===a)),c=l(r,a);s&&n(s)||(i=c.some((({source:e})=>"global"===e)),u=c.some((({source:e})=>"local"===e)),d=c.some((({source:e})=>"current"===e)))}))}return i||u||d?(0,e.createElement)("span",{title:(0,s.__)("Styles exist for other at-rules","generateblocks-pro"),className:f(c.dot,{[c.current]:d,[c.local]:u&&!d,[c.global]:i&&!u&&!d})}):null}function ct({atRule:t,onAtRuleChange:r,onNestedRuleChange:n,defaultAtRules:l,allStyles:i,showAtRuleOptions:u,setShowAtRuleOptions:m,onUpdateKey:f,nestedRule:g,onDeleteStyle:b,allowCustomAtRule:v}){var y;const[w,k]=(0,a.useState)([]),[E,S]=(0,a.useState)(!1),[x,C]=(0,a.useState)(""),_=(0,a.useCallback)((e=>{var r;return e.show||!!e.icon&&(Object.keys(null!==(r=i?.[e.value])&&void 0!==r?r:{})?.length>0||e.value===t)}),[i,t]),O=(0,a.useMemo)((()=>{var e;const t=[];l.forEach((e=>{e.value&&t.push(e.value)}));const r=g?null!==(e=i?.[g])&&void 0!==e?e:{}:i;return Object.keys(r)?.forEach((e=>{e.startsWith("@")&&!t.includes(e)&&t.push(e)})),w.forEach((e=>{t.includes(e)||t.push(e)})),Array.from(new Set(t))}),[i,w]),T=l.some((e=>e.value===t)),I=null!==(y=l.find((e=>e.value===t))?.label)&&void 0!==y?y:t;return(0,e.createElement)("div",{className:c.atRules},(0,e.createElement)(o.ButtonGroup,null,l.map((n=>_(n)?(0,e.createElement)(o.Button,{key:n.label,onClick:()=>{n.value!==t&&(r(n.value),m(!1))},isPressed:n.value===t,icon:n.icon,label:n.label,size:"compact",className:c.atRuleButton},(0,e.createElement)(lt,{allStyles:i,atRule:n.value,nestedRule:g})):null)),(0,e.createElement)(o.Button,{onClick:()=>m(!u),isPressed:u||!T,label:(0,s.__)("Manage at-rules","generateblocks-pro"),size:"compact",className:c.manageAtRules},(0,e.createElement)(d,{icon:p,size:"18"}),(0,e.createElement)(it,{allStyles:i,nestedRule:g,shouldShowRule:_,defaultAtRules:l}))),(0,e.createElement)("div",{className:c.atRulePreview},(0,e.createElement)("span",{className:c.preview,style:{fontFamily:I.startsWith("@")?"":"inherit"},title:t},I)),u&&(0,e.createElement)(e.Fragment,null,!E&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(L,{atRule:t,onAtRuleChange:r,onNestedRuleChange:n,allStyles:i,setShowAtRuleOptions:m,defaultAtRules:l,activeAtRules:O,showBuildAtRule:E,setShowBuildAtRule:S,editAtRule:x,setEditAtRule:C,nestedRule:g,onDeleteStyle:b,allowCustomAtRule:v}),(0,e.createElement)("div",{className:c.actions},(0,e.createElement)(o.Button,{variant:"tertiary",size:"compact",onClick:()=>m(!1)},(0,s.__)("Cancel","generateblocks-pro")),!!v&&(0,e.createElement)(o.Button,{variant:"primary",size:"compact",showTooltip:!0,label:(0,s.__)("Add a new custom at-rule","generateblocks-pro"),icon:h,onClick:()=>{S(!0)}},(0,s.__)("New","generateblocks-pro")))),!!E&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.Notice,{className:c.notice,isDismissible:!1},x?(0,s.sprintf)(
// translators: %s: selector name.
// translators: %s: selector name.
(0,s.__)("You are editing an at-rule: %s","generateblocks-pro"),x):(0,s.__)("You are creating a new custom at-rule.","generateblocks-pro")),(0,e.createElement)(st,{editAtRule:x,setShowBuildAtRule:S,allStyles:i,onAtRuleChange:r,setTempAtRule:function(e){k([...w,e])},removeTempAtRule:function(e){k(w.filter((t=>t!==e)))},nestedRule:g,onUpdateKey:f,defaultAtRules:l,setShowAtRuleOptions:m,setEditAtRule:C}))))}const ut=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"}));function dt(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}function pt(){return pt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pt.apply(null,arguments)}var ht=r(5556),mt=r.n(ht);r(4363);const ft=e=>"object"==typeof e&&null!=e&&1===e.nodeType,gt=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,bt=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const r=getComputedStyle(e,null);return gt(r.overflowY,t)||gt(r.overflowX,t)||(e=>{const t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},vt=(e,t,r,n,o,a,s,l)=>a<e&&s>t||a>e&&s<t?0:a<=e&&l<=r||s>=t&&l>=r?a-e-n:s>t&&l<r||a<e&&l>r?s-t+o:0,yt=e=>{const t=e.parentElement;return null==t?e.getRootNode().host||null:t};var wt=function(){return wt=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},wt.apply(this,arguments)};Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var kt=0;function Et(){}function St(e,t,r){return e===t||t instanceof r.Node&&e.contains&&e.contains(t)}function xt(e,t){var r;function n(){r&&clearTimeout(r)}function o(){for(var o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];n(),r=setTimeout((function(){r=null,e.apply(void 0,a)}),t)}return o.cancel=n,o}function Ct(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return t.some((function(t){return t&&t.apply(void 0,[e].concat(n)),e.preventDownshiftDefault||e.hasOwnProperty("nativeEvent")&&e.nativeEvent.preventDownshiftDefault}))}}function _t(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){t.forEach((function(t){"function"==typeof t?t(e):t&&(t.current=e)}))}}function Ot(e,t){return e&&t?Object.keys(e).reduce((function(r,n){return r[n]=function(e,t){return void 0!==e[t]}(t,n)?t[n]:e[n],r}),{}):e}function Tt(e,t,r,n,o){void 0===o&&(o=!1);var a=r.length;if(0===a)return-1;var s=a-1;("number"!=typeof e||e<0||e>s)&&(e=t>0?-1:s+1);var l=e+t;l<0?l=o?s:0:l>s&&(l=o?0:s);var i=It(l,t<0,r,n,o);return-1===i?e>=a?-1:e:i}function It(e,t,r,n,o){void 0===o&&(o=!1);var a=r.length;if(t){for(var s=e;s>=0;s--)if(!n(r[s],s))return s}else for(var l=e;l<a;l++)if(!n(r[l],l))return l;return o?It(t?a-1:0,t,r,n):-1}function Rt(e,t,r,n){return void 0===n&&(n=!0),r&&t.some((function(t){return t&&(St(t,e,r)||n&&St(t,r.document.activeElement,r))}))}var Mt=xt((function(e){Pt(e).textContent=""}),500);function Pt(e){var t=e.getElementById("a11y-status-message");return t||((t=e.createElement("div")).setAttribute("id","a11y-status-message"),t.setAttribute("role","status"),t.setAttribute("aria-live","polite"),t.setAttribute("aria-relevant","additions text"),Object.assign(t.style,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px"}),e.body.appendChild(t),t)}var At={highlightedIndex:-1,isOpen:!1,selectedItem:null,inputValue:""};var Lt=xt((function(e,t){!function(e,t){e&&t&&(Pt(t).textContent=e,Mt(t))}(e,t)}),200),Nt="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?e.useLayoutEffect:e.useEffect,zt="useId"in t()?function(r){var n=r.id,o=r.labelId,a=r.menuId,s=r.getItemId,l=r.toggleButtonId,i=r.inputId,c="downshift-"+t().useId();return n||(n=c),(0,e.useRef)({labelId:o||n+"-label",menuId:a||n+"-menu",getItemId:s||function(e){return n+"-item-"+e},toggleButtonId:l||n+"-toggle-button",inputId:i||n+"-input"}).current}:function(t){var r=t.id,n=void 0===r?"downshift-"+String(kt++):r,o=t.labelId,a=t.menuId,s=t.getItemId,l=t.toggleButtonId,i=t.inputId;return(0,e.useRef)({labelId:o||n+"-label",menuId:a||n+"-menu",getItemId:s||function(e){return n+"-item-"+e},toggleButtonId:l||n+"-toggle-button",inputId:i||n+"-input"}).current};function Dt(e){return""+e.slice(0,1).toUpperCase()+e.slice(1)}function Ft(t){var r=(0,e.useRef)(t);return r.current=t,r}function Bt(t,r,n,o){var a=(0,e.useRef)(),s=(0,e.useRef)(),l=(0,e.useCallback)((function(e,r){s.current=r,e=Ot(e,r.props);var n=t(e,r);return r.props.stateReducer(e,pt({},r,{changes:n}))}),[t]),i=(0,e.useReducer)(l,r,n),c=i[0],u=i[1],d=Ft(r),p=(0,e.useCallback)((function(e){return u(pt({props:d.current},e))}),[d]),h=s.current;return(0,e.useEffect)((function(){var e=Ot(a.current,null==h?void 0:h.props);h&&a.current&&!o(e,c)&&function(e,t,r){var n=e.props,o=e.type,a={};Object.keys(t).forEach((function(n){!function(e,t,r,n){var o=t.props,a=t.type,s="on"+Dt(e)+"Change";o[s]&&void 0!==n[e]&&n[e]!==r[e]&&o[s](pt({type:a},n))}(n,e,t,r),r[n]!==t[n]&&(a[n]=r[n])})),n.onStateChange&&Object.keys(a).length&&n.onStateChange(pt({type:o},a))}(h,e,c),a.current=c}),[c,h,o]),[c,p]}var jt={itemToString:function(e){return e?String(e):""},itemToKey:function(e){return e},stateReducer:function(e,t){return t.changes},scrollIntoView:function(e,t){if(e){var r=((e,t)=>{var r,n,o,a;if("undefined"==typeof document)return[];const{scrollMode:s,block:l,inline:i,boundary:c,skipOverflowHiddenElements:u}=t,d="function"==typeof c?c:e=>e!==c;if(!ft(e))throw new TypeError("Invalid target");const p=document.scrollingElement||document.documentElement,h=[];let m=e;for(;ft(m)&&d(m);){if(m=yt(m),m===p){h.push(m);break}null!=m&&m===document.body&&bt(m)&&!bt(document.documentElement)||null!=m&&bt(m,u)&&h.push(m)}const f=null!=(n=null==(r=window.visualViewport)?void 0:r.width)?n:innerWidth,g=null!=(a=null==(o=window.visualViewport)?void 0:o.height)?a:innerHeight,{scrollX:b,scrollY:v}=window,{height:y,width:w,top:k,right:E,bottom:S,left:x}=e.getBoundingClientRect(),{top:C,right:_,bottom:O,left:T}=(e=>{const t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);let I="start"===l||"nearest"===l?k-C:"end"===l?S+O:k+y/2-C+O,R="center"===i?x+w/2-T+_:"end"===i?E+_:x-T;const M=[];for(let e=0;e<h.length;e++){const t=h[e],{height:r,width:n,top:o,right:a,bottom:c,left:u}=t.getBoundingClientRect();if("if-needed"===s&&k>=0&&x>=0&&S<=g&&E<=f&&k>=o&&S<=c&&x>=u&&E<=a)return M;const d=getComputedStyle(t),m=parseInt(d.borderLeftWidth,10),C=parseInt(d.borderTopWidth,10),_=parseInt(d.borderRightWidth,10),O=parseInt(d.borderBottomWidth,10);let T=0,P=0;const A="offsetWidth"in t?t.offsetWidth-t.clientWidth-m-_:0,L="offsetHeight"in t?t.offsetHeight-t.clientHeight-C-O:0,N="offsetWidth"in t?0===t.offsetWidth?0:n/t.offsetWidth:0,z="offsetHeight"in t?0===t.offsetHeight?0:r/t.offsetHeight:0;if(p===t)T="start"===l?I:"end"===l?I-g:"nearest"===l?vt(v,v+g,g,C,O,v+I,v+I+y,y):I-g/2,P="start"===i?R:"center"===i?R-f/2:"end"===i?R-f:vt(b,b+f,f,m,_,b+R,b+R+w,w),T=Math.max(0,T+v),P=Math.max(0,P+b);else{T="start"===l?I-o-C:"end"===l?I-c+O+L:"nearest"===l?vt(o,c,r,C,O+L,I,I+y,y):I-(o+r/2)+L/2,P="start"===i?R-u-m:"center"===i?R-(u+n/2)+A/2:"end"===i?R-a+_+A:vt(u,a,n,m,_+A,R,R+w,w);const{scrollLeft:e,scrollTop:s}=t;T=0===z?0:Math.max(0,Math.min(s+T/z,t.scrollHeight-r/z+L)),P=0===N?0:Math.max(0,Math.min(e+P/N,t.scrollWidth-n/N+A)),I+=s-T,R+=e-P}M.push({el:t,top:T,left:P})}return M})(e,{boundary:t,block:"nearest",scrollMode:"if-needed"});r.forEach((function(e){var t=e.el,r=e.top,n=e.left;t.scrollTop=r,t.scrollLeft=n}))}},environment:"undefined"==typeof window?void 0:window};function Vt(e,t,r){void 0===r&&(r=At);var n=e["default"+Dt(t)];return void 0!==n?n:r[t]}function Wt(e,t,r){void 0===r&&(r=At);var n=e[t];if(void 0!==n)return n;var o=e["initial"+Dt(t)];return void 0!==o?o:Vt(e,t,r)}function Ut(e){var t=Wt(e,"selectedItem"),r=Wt(e,"isOpen"),n=function(e){var t=Wt(e,"highlightedIndex");return t>-1&&e.isItemDisabled(e.items[t],t)?-1:t}(e),o=Wt(e,"inputValue");return{highlightedIndex:n<0&&t&&r?e.items.findIndex((function(r){return e.itemToKey(r)===e.itemToKey(t)})):n,isOpen:r,selectedItem:t,inputValue:o}}function Ht(e,t,r){var n=e.items,o=e.initialHighlightedIndex,a=e.defaultHighlightedIndex,s=e.isItemDisabled,l=e.itemToKey,i=t.selectedItem,c=t.highlightedIndex;return 0===n.length?-1:void 0===o||c!==o||s(n[o],o)?void 0===a||s(n[a],a)?i?n.findIndex((function(e){return l(i)===l(e)})):r<0&&!s(n[n.length-1],n.length-1)?n.length-1:r>0&&!s(n[0],0)?0:-1:a:o}var $t=function(){return Et};function qt(r,n,o,a){void 0===a&&(a={});var s,l=a.document,i=(s=t().useRef(!0),t().useEffect((function(){return s.current=!1,function(){s.current=!0}}),[]),s.current);(0,e.useEffect)((function(){if(r&&!i&&l){var e=r(n);Lt(e,l)}}),o),(0,e.useEffect)((function(){return function(){var e,t;Lt.cancel(),(t=null==(e=l)?void 0:e.getElementById("a11y-status-message"))&&t.remove()}}),[l])}var Gt=Et;function Zt(e,t,r){var n;return void 0===r&&(r=!0),pt({isOpen:!1,highlightedIndex:-1},(null==(n=e.items)?void 0:n.length)&&t>=0&&pt({selectedItem:e.items[t],isOpen:Vt(e,"isOpen"),highlightedIndex:Vt(e,"highlightedIndex")},r&&{inputValue:e.itemToString(e.items[t])}))}function Kt(e,t){return e.isOpen===t.isOpen&&e.inputValue===t.inputValue&&e.highlightedIndex===t.highlightedIndex&&e.selectedItem===t.selectedItem}function Yt(e){var t=Vt(e,"highlightedIndex");return t>-1&&e.isItemDisabled(e.items[t],t)?-1:t}var Qt=pt({},{environment:mt().shape({addEventListener:mt().func.isRequired,removeEventListener:mt().func.isRequired,document:mt().shape({createElement:mt().func.isRequired,getElementById:mt().func.isRequired,activeElement:mt().any.isRequired,body:mt().any.isRequired}).isRequired,Node:mt().func.isRequired}),itemToString:mt().func,itemToKey:mt().func,stateReducer:mt().func},{getA11yStatusMessage:mt().func,highlightedIndex:mt().number,defaultHighlightedIndex:mt().number,initialHighlightedIndex:mt().number,isOpen:mt().bool,defaultIsOpen:mt().bool,initialIsOpen:mt().bool,selectedItem:mt().any,initialSelectedItem:mt().any,defaultSelectedItem:mt().any,id:mt().string,labelId:mt().string,menuId:mt().string,getItemId:mt().func,toggleButtonId:mt().string,onSelectedItemChange:mt().func,onHighlightedIndexChange:mt().func,onStateChange:mt().func,onIsOpenChange:mt().func,scrollIntoView:mt().func});wt(wt({},Qt),{items:mt().array.isRequired,isItemDisabled:mt().func});var Xt=wt(wt({},jt),{isItemDisabled:function(){return!1}}),Jt=Et,er=0,tr=1,rr=2,nr=3,or=4,ar=5,sr=6,lr=7,ir=8,cr=9,ur=10,dr=11,pr=12,hr=13,mr=14,fr=15,gr=16,br=17,vr=18,yr=19,wr=20,kr=21,Er=Object.freeze({__proto__:null,FunctionCloseMenu:br,FunctionOpenMenu:gr,FunctionReset:kr,FunctionSelectItem:yr,FunctionSetHighlightedIndex:vr,FunctionSetInputValue:wr,FunctionToggleMenu:fr,ItemClick:mr,ItemMouseMove:hr,MenuMouseLeave:pr,ToggleButtonBlur:dr,ToggleButtonClick:er,ToggleButtonKeyDownArrowDown:tr,ToggleButtonKeyDownArrowUp:rr,ToggleButtonKeyDownCharacter:nr,ToggleButtonKeyDownEnd:sr,ToggleButtonKeyDownEnter:lr,ToggleButtonKeyDownEscape:or,ToggleButtonKeyDownHome:ar,ToggleButtonKeyDownPageDown:ur,ToggleButtonKeyDownPageUp:cr,ToggleButtonKeyDownSpaceButton:ir});function Sr(e,t){var r,n,o=t.type,a=t.props,s=t.altKey;switch(o){case mr:n={isOpen:Vt(a,"isOpen"),highlightedIndex:Yt(a),selectedItem:a.items[t.index]};break;case nr:var l=t.key,i=""+e.inputValue+l;n={inputValue:i,highlightedIndex:function(e){for(var t=e.keysSoFar,r=e.highlightedIndex,n=e.items,o=e.itemToString,a=e.isItemDisabled,s=t.toLowerCase(),l=0;l<n.length;l++){var i=(l+r+(t.length<2?1:0))%n.length,c=n[i];if(void 0!==c&&o(c).toLowerCase().startsWith(s)&&!a(c,i))return i}return r}({keysSoFar:i,highlightedIndex:!e.isOpen&&e.selectedItem?a.items.findIndex((function(t){return a.itemToKey(t)===a.itemToKey(e.selectedItem)})):e.highlightedIndex,items:a.items,itemToString:a.itemToString,isItemDisabled:a.isItemDisabled}),isOpen:!0};break;case tr:n={highlightedIndex:e.isOpen?Tt(e.highlightedIndex,1,a.items,a.isItemDisabled):s&&null==e.selectedItem?-1:Ht(a,e,1),isOpen:!0};break;case rr:n=e.isOpen&&s?Zt(a,e.highlightedIndex,!1):{highlightedIndex:e.isOpen?Tt(e.highlightedIndex,-1,a.items,a.isItemDisabled):Ht(a,e,-1),isOpen:!0};break;case lr:case ir:n=Zt(a,e.highlightedIndex,!1);break;case ar:n={highlightedIndex:It(0,!1,a.items,a.isItemDisabled),isOpen:!0};break;case sr:n={highlightedIndex:It(a.items.length-1,!0,a.items,a.isItemDisabled),isOpen:!0};break;case cr:n={highlightedIndex:Tt(e.highlightedIndex,-10,a.items,a.isItemDisabled)};break;case ur:n={highlightedIndex:Tt(e.highlightedIndex,10,a.items,a.isItemDisabled)};break;case or:n={isOpen:!1,highlightedIndex:-1};break;case dr:n=pt({isOpen:!1,highlightedIndex:-1},e.highlightedIndex>=0&&(null==(r=a.items)?void 0:r.length)&&{selectedItem:a.items[e.highlightedIndex]});break;case yr:n={selectedItem:t.selectedItem};break;default:return function(e,t,r){var n,o=t.type,a=t.props;switch(o){case r.ItemMouseMove:n={highlightedIndex:t.disabled?-1:t.index};break;case r.MenuMouseLeave:n={highlightedIndex:-1};break;case r.ToggleButtonClick:case r.FunctionToggleMenu:n={isOpen:!e.isOpen,highlightedIndex:e.isOpen?-1:Ht(a,e,0)};break;case r.FunctionOpenMenu:n={isOpen:!0,highlightedIndex:Ht(a,e,0)};break;case r.FunctionCloseMenu:n={isOpen:!1};break;case r.FunctionSetHighlightedIndex:n={highlightedIndex:a.isItemDisabled(a.items[t.highlightedIndex],t.highlightedIndex)?-1:t.highlightedIndex};break;case r.FunctionSetInputValue:n={inputValue:t.inputValue};break;case r.FunctionReset:n={highlightedIndex:Yt(a),isOpen:Vt(a,"isOpen"),selectedItem:Vt(a,"selectedItem"),inputValue:Vt(a,"inputValue")};break;default:throw new Error("Reducer called without proper action type.")}return pt({},e,n)}(e,t,Er)}return pt({},e,n)}var xr=["onClick"],Cr=["onMouseLeave","refKey","ref"],_r=["onBlur","onClick","onPress","onKeyDown","refKey","ref"],Or=["item","index","onMouseMove","onClick","onMouseDown","onPress","refKey","disabled","ref"];function Tr(t){void 0===t&&(t={}),Jt(t,Tr);var r=pt({},Xt,t),n=r.scrollIntoView,o=r.environment,a=r.getA11yStatusMessage,s=function(e,t,r,n){var o=Bt(e,t,r,n),a=o[0],s=o[1];return[Ot(a,t),s]}(Sr,r,Ut,Kt),l=s[0],i=s[1],c=l.isOpen,u=l.highlightedIndex,d=l.selectedItem,p=l.inputValue,h=(0,e.useRef)(null),m=(0,e.useRef)(null),f=(0,e.useRef)({}),g=(0,e.useRef)(null),b=zt(r),v=Ft({state:l,props:r}),y=(0,e.useCallback)((function(e){return f.current[b.getItemId(e)]}),[b]);qt(a,l,[c,u,d,p],o);var w=function(t){var r=t.highlightedIndex,n=t.isOpen,o=t.itemRefs,a=t.getItemNodeFromIndex,s=t.menuElement,l=t.scrollIntoView,i=(0,e.useRef)(!0);return Nt((function(){r<0||!n||!Object.keys(o.current).length||(!1===i.current?i.current=!0:l(a(r),s))}),[r]),i}({menuElement:m.current,highlightedIndex:u,isOpen:c,itemRefs:f,scrollIntoView:n,getItemNodeFromIndex:y});(0,e.useEffect)((function(){return g.current=xt((function(e){e({type:wr,inputValue:""})}),500),function(){g.current.cancel()}}),[]),(0,e.useEffect)((function(){p&&g.current(i)}),[i,p]),Gt({props:r,state:l}),(0,e.useEffect)((function(){Wt(r,"isOpen")&&h.current&&h.current.focus()}),[]);var k=function(t,r,n){var o=(0,e.useRef)({isMouseDown:!1,isTouchMove:!1,isTouchEnd:!1});return(0,e.useEffect)((function(){if(!t)return Et;var e=n.map((function(e){return e.current}));function a(){o.current.isTouchEnd=!1,o.current.isMouseDown=!0}function s(n){o.current.isMouseDown=!1,Rt(n.target,e,t)||r()}function l(){o.current.isTouchEnd=!1,o.current.isTouchMove=!1}function i(){o.current.isTouchMove=!0}function c(n){o.current.isTouchEnd=!0,o.current.isTouchMove||Rt(n.target,e,t,!1)||r()}return t.addEventListener("mousedown",a),t.addEventListener("mouseup",s),t.addEventListener("touchstart",l),t.addEventListener("touchmove",i),t.addEventListener("touchend",c),function(){t.removeEventListener("mousedown",a),t.removeEventListener("mouseup",s),t.removeEventListener("touchstart",l),t.removeEventListener("touchmove",i),t.removeEventListener("touchend",c)}}),[n,t,r]),o.current}(o,(0,e.useCallback)((function(){v.current.state.isOpen&&i({type:dr})}),[i,v]),(0,e.useMemo)((function(){return[m,h]}),[m.current,h.current])),E=$t("getMenuProps","getToggleButtonProps");(0,e.useEffect)((function(){c||(f.current={})}),[c]);var S=(0,e.useMemo)((function(){return{ArrowDown:function(e){e.preventDefault(),i({type:tr,altKey:e.altKey})},ArrowUp:function(e){e.preventDefault(),i({type:rr,altKey:e.altKey})},Home:function(e){e.preventDefault(),i({type:ar})},End:function(e){e.preventDefault(),i({type:sr})},Escape:function(){v.current.state.isOpen&&i({type:or})},Enter:function(e){e.preventDefault(),i({type:v.current.state.isOpen?lr:er})},PageUp:function(e){v.current.state.isOpen&&(e.preventDefault(),i({type:cr}))},PageDown:function(e){v.current.state.isOpen&&(e.preventDefault(),i({type:ur}))}," ":function(e){e.preventDefault();var t=v.current.state;t.isOpen?t.inputValue?i({type:nr,key:" "}):i({type:ir}):i({type:er})}}}),[i,v]),x=(0,e.useCallback)((function(){i({type:fr})}),[i]),C=(0,e.useCallback)((function(){i({type:br})}),[i]),_=(0,e.useCallback)((function(){i({type:gr})}),[i]),O=(0,e.useCallback)((function(e){i({type:vr,highlightedIndex:e})}),[i]),T=(0,e.useCallback)((function(e){i({type:yr,selectedItem:e})}),[i]),I=(0,e.useCallback)((function(){i({type:kr})}),[i]),R=(0,e.useCallback)((function(e){i({type:wr,inputValue:e})}),[i]),M=(0,e.useCallback)((function(e){var t=void 0===e?{}:e,r=t.onClick,n=dt(t,xr);return pt({id:b.labelId,htmlFor:b.toggleButtonId,onClick:Ct(r,(function(){var e;null==(e=h.current)||e.focus()}))},n)}),[b]),P=(0,e.useCallback)((function(e,t){var r,n=void 0===e?{}:e,o=n.onMouseLeave,a=n.refKey,s=void 0===a?"ref":a,l=n.ref,c=dt(n,Cr),u=(void 0===t?{}:t).suppressRefError;return E("getMenuProps",void 0!==u&&u,s,m),pt(((r={})[s]=_t(l,(function(e){m.current=e})),r.id=b.menuId,r.role="listbox",r["aria-labelledby"]=c&&c["aria-label"]?void 0:""+b.labelId,r.onMouseLeave=Ct(o,(function(){i({type:pr})})),r),c)}),[i,E,b]);return{getToggleButtonProps:(0,e.useCallback)((function(e,t){var r,n=void 0===e?{}:e,o=n.onBlur,a=n.onClick;n.onPress;var s=n.onKeyDown,l=n.refKey,c=void 0===l?"ref":l,u=n.ref,d=dt(n,_r),p=(void 0===t?{}:t).suppressRefError,m=void 0!==p&&p,f=v.current.state,g=pt(((r={})[c]=_t(u,(function(e){h.current=e})),r["aria-activedescendant"]=f.isOpen&&f.highlightedIndex>-1?b.getItemId(f.highlightedIndex):"",r["aria-controls"]=b.menuId,r["aria-expanded"]=v.current.state.isOpen,r["aria-haspopup"]="listbox",r["aria-labelledby"]=d&&d["aria-label"]?void 0:""+b.labelId,r.id=b.toggleButtonId,r.role="combobox",r.tabIndex=0,r.onBlur=Ct(o,(function(){f.isOpen&&!k.isMouseDown&&i({type:dr})})),r),d);return d.disabled||(g.onClick=Ct(a,(function(){i({type:er})})),g.onKeyDown=Ct(s,(function(e){var t=function(e){var t=e.key,r=e.keyCode;return r>=37&&r<=40&&0!==t.indexOf("Arrow")?"Arrow"+t:t}(e);t&&S[t]?S[t](e):function(e){return/^\S{1}$/.test(e)}(t)&&i({type:nr,key:t})}))),E("getToggleButtonProps",m,c,h),g}),[i,b,v,k,E,S]),getLabelProps:M,getMenuProps:P,getItemProps:(0,e.useCallback)((function(e){var t,r=void 0===e?{}:e,n=r.item,o=r.index,a=r.onMouseMove,s=r.onClick,l=r.onMouseDown;r.onPress;var c=r.refKey,u=void 0===c?"ref":c,d=r.disabled,p=r.ref,h=dt(r,Or);void 0!==d&&console.warn('Passing "disabled" as an argument to getItemProps is not supported anymore. Please use the isItemDisabled prop from useSelect.');var m=v.current,g=m.state,y=m.props,E=function(e,t,r,n){var o,a;if(void 0===e){if(void 0===t)throw new Error(n);o=r[t],a=t}else a=void 0===t?r.indexOf(e):t,o=e;return[o,a]}(n,o,y.items,"Pass either item or index to getItemProps!"),S=E[0],x=E[1],C=y.isItemDisabled(S,x),_=pt(((t={})[u]=_t(p,(function(e){e&&(f.current[b.getItemId(x)]=e)})),t["aria-disabled"]=C,t["aria-selected"]=S===g.selectedItem,t.id=b.getItemId(x),t.role="option",t),h);return C||(_.onClick=Ct(s,(function(){i({type:mr,index:x})}))),_.onMouseMove=Ct(a,(function(){k.isTouchEnd||x===g.highlightedIndex||(w.current=!1,i({type:hr,index:x,disabled:C}))})),_.onMouseDown=Ct(l,(function(e){return e.preventDefault()})),_}),[v,b,k,w,i]),toggleMenu:x,openMenu:_,closeMenu:C,setHighlightedIndex:O,selectItem:T,reset:I,setInputValue:R,highlightedIndex:u,isOpen:c,selectedItem:d,inputValue:p}}Tr.stateChangeTypes=Er,pt({},Qt,{items:mt().array.isRequired,isItemDisabled:mt().func,inputValue:mt().string,defaultInputValue:mt().string,initialInputValue:mt().string,inputId:mt().string,onInputValueChange:mt().func}),pt({},jt,{isItemDisabled:function(){return!1}}),mt().array,mt().array,mt().array,mt().func,mt().number,mt().number,mt().number,mt().func,mt().func,mt().string,mt().string;const Ir=()=>(0,e.createElement)("svg",{"aria-hidden":"true",focusable:"false",width:"16",height:"16",viewBox:"0 0 16 16"},(0,e.createElement)("path",{d:"M9.414 8l3.293-3.293-1.414-1.414L8 6.586 4.707 3.293 3.293 4.707 6.586 8l-3.293 3.293 1.414 1.414L8 9.414l3.293 3.293 1.414-1.414L9.414 8z"})),Rr=()=>(0,e.createElement)("svg",{"aria-hidden":"true",focusable:"false",width:"16",height:"16",viewBox:"0 0 16 16"},(0,e.createElement)("path",{d:"M1 7h14v2H1z"})),Mr=()=>(0,e.createElement)("svg",{"aria-hidden":"true",focusable:"false",width:"16",height:"16",viewBox:"0 0 16 16"},(0,e.createElement)("path",{d:"M0 7h4v2H0zm6 0h4v2H6zm6 0h4v2h-4z"})),Pr=()=>(0,e.createElement)("svg",{"aria-hidden":"true",focusable:"false",width:"16",height:"16",viewBox:"0 0 16 16"},(0,e.createElement)("path",{d:"M1 7h2v2H1zm4 0h2v2H5zm4 0h2v2H9zm4 0h2v2h-2z"})),Ar=()=>(0,e.createElement)("svg",{"aria-hidden":"true",focusable:"false",width:"16",height:"16",viewBox:"0 0 256 256",style:{opacity:.1}},(0,e.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,e.createElement)("rect",{x:"32",y:"32",width:"192",height:"192",rx:"16"})),Lr=()=>(0,e.createElement)("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fillRule:"evenodd","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M18.895,1.105C20.368,2.579 20.368,17.421 18.895,18.895C17.421,20.368 2.579,20.368 1.105,18.895C-0.368,17.421 -0.368,2.579 1.105,1.105C2.579,-0.368 17.421,-0.368 18.895,1.105ZM17.116,2.884C18.295,4.063 18.295,15.937 17.116,17.116C15.937,18.295 4.063,18.295 2.884,17.116C1.705,15.937 1.705,4.063 2.884,2.884C4.063,1.705 15.937,1.705 17.116,2.884Z"}),(0,e.createElement)("path",{d:"M15.93,4.07C16.912,5.053 16.912,14.947 15.93,15.93C14.947,16.912 5.053,16.912 4.07,15.93C3.088,14.947 3.088,5.053 4.07,4.07C5.053,3.088 14.947,3.088 15.93,4.07Z"})),Nr=({size:t,...r})=>(0,e.createElement)("svg",{viewBox:"0 0 20 20",width:t||20,height:t||20,...r},(0,e.createElement)("path",{d:"M5 6l5 5 5-5 2 1-7 7-7-7 2-1z",fill:"#0a0a0a",style:{transformOrigin:"center"}})),zr=({size:t,...r})=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:t||24,height:t||24,"aria-hidden":"true",focusable:"false",...r},(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5A6.5 6.5 0 0 1 6.93 7.931l9.139 9.138A6.473 6.473 0 0 1 12 18.5Zm5.123-2.498a6.5 6.5 0 0 0-9.124-9.124l9.124 9.124ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Z"})),Dr=({size:t,...r})=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:t||24,height:t||24,"aria-hidden":"true",focusable:"false",...r},(0,e.createElement)("path",{d:"M14 22H2V10h12v12ZM3.999 12v8h8v-8h-8ZM22 22h-6V10h6v12Zm-4-10v8h2v-8h-2ZM22 8H2V2h20v6ZM4 4v2h16V4H4Z"})),Fr=({size:t,...r})=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:t||24,height:t||24,"aria-hidden":"true",focusable:"false",fillRule:"evenodd",...r},(0,e.createElement)("path",{d:"M7.95 22v-6.95H2v-2.1h5.95V5H6l3-3 3 3h-1.95v7.95H19V11l3 3-3 3v-1.95h-8.95V22h-2.1Z"})),Br=({size:t,...r})=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:t||24,height:t||24,"aria-hidden":"true",focusable:"false",fillRule:"evenodd",...r},(0,e.createElement)("path",{d:"M22 2v20H2V2h20Zm-2 2H4v16h16V4Z"}),(0,e.createElement)("path",{d:"M16.586 18H7.414l2-2h5.172l2 2ZM8 9.414v5.172l-2 2V7.414l2 2Zm10 7.172-2-2V9.414l2-2v9.172ZM14.586 8H9.414l-2-2h9.172l-2 2Z"})),jr=({size:t,...r})=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:t||24,height:t||24,"aria-hidden":"true",focusable:"false",fillRule:"evenodd",...r},(0,e.createElement)("path",{d:"M2 6.036v-4.03c4.298 0 8.596-.026 12.893.001 3.746.071 7.083 3.41 7.107 7.199v12.8h-4.03v-2H20c0-3.645.067-7.29-.002-10.935-.084-2.665-2.439-5.013-5.13-5.064-3.618-.023-7.235-.001-10.852-.001H4v2.03H2Z"}),(0,e.createElement)("path",{d:"M4 10.036v-2H2v2h2ZM4 14.036v-2H2v2h2ZM4 18.031v-2H2v2h2ZM4 22.006v-2H2v2h2Z"}),(0,e.createElement)("path",{d:"M2 20.006h2v2H2zM6 20.006h2v2H6zM10 20.006h2v2h-2zM13.996 20.006h2v2h-2z"})),Vr=({size:t,...r})=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:t||24,height:t||24,"aria-hidden":"true",focusable:"false",...r},(0,e.createElement)("path",{d:"m8.824 5.832 4.448 12.164H11.11l-1.304-3.554H5.43a70.44 70.44 0 0 1-.661 1.768c-.214.607-.429 1.197-.643 1.786H2.001L6.448 5.832h2.376ZM7.609 8.386l-1.536 4.323h3.09L7.609 8.386ZM22.002 17.764a3.626 3.626 0 0 1-.446.232 2.065 2.065 0 0 1-.34.125c-.107.036-.196.036-.268.054h-.178c-.215 0-.411-.036-.572-.125a1.361 1.361 0 0 1-.464-.286 2.916 2.916 0 0 1-.34-.447 2.712 2.712 0 0 1-.232-.518c-.268.268-.518.501-.75.661a4.194 4.194 0 0 1-.679.429 2.666 2.666 0 0 1-.679.214 3.101 3.101 0 0 1-.714.072c-.393 0-.768-.072-1.126-.197a2.638 2.638 0 0 1-.893-.536 2.148 2.148 0 0 1-.607-.821 2.698 2.698 0 0 1-.214-1.09c0-.339.071-.643.232-.893.143-.232.357-.464.607-.643.25-.196.536-.339.858-.482.321-.125.66-.25.982-.357.339-.108.679-.215 1-.322.322-.089.608-.196.858-.304.25-.107.464-.232.607-.375a.622.622 0 0 0 .232-.482.808.808 0 0 0-.143-.482 1.185 1.185 0 0 0-.357-.304 2.376 2.376 0 0 0-.5-.161 3.087 3.087 0 0 0-.536-.053c-.179 0-.357.018-.536.071a1.628 1.628 0 0 0-.464.232 1.41 1.41 0 0 0-.375.411c-.09.161-.161.357-.179.59h-2.09c.036-.518.161-.965.375-1.34.215-.393.5-.697.84-.947.339-.25.714-.446 1.143-.571a5.085 5.085 0 0 1 1.286-.179c.464 0 .929.054 1.358.161.428.107.821.268 1.143.5.339.214.607.518.804.857.196.358.285.786.285 1.269v4.322a.42.42 0 0 0 .125.304.363.363 0 0 0 .322.161c.018 0 .071-.018.179-.036.125-.018.267-.072.446-.161v1.447Zm-3.126-4.019a3.016 3.016 0 0 1-.518.214c-.214.072-.446.125-.696.197-.25.071-.501.143-.769.214-.25.072-.464.161-.678.268a1.481 1.481 0 0 0-.5.357.66.66 0 0 0-.197.483c0 .178.036.339.089.464a.814.814 0 0 0 .233.304.706.706 0 0 0 .321.178c.125.036.25.072.375.072.393 0 .804-.143 1.215-.411.429-.268.804-.679 1.125-1.233v-1.107Z",style:{fillRule:"nonzero"}})),Wr=({size:t,...r})=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:t||24,height:t||24,"aria-hidden":"true",focusable:"false",...r},(0,e.createElement)("path",{d:"m17.588 3.708.721.595L4.303 18.31l-.595-.721-.533-.98L16.608 3.176l.98.532ZM14.013 2.202l.565.176-12.2 12.2-.176-.565-.191-1.896L12.116 2.011l1.897.191ZM8.696 2.603 2.602 8.696c.979-2.868 3.238-5.134 6.094-6.093ZM20.29 6.41l.56 1.031L7.441 20.85l-1.032-.56-.677-.559L19.731 5.732l.559.678ZM21.795 9.985l.194 1.921L11.906 21.99l-1.921-.194-.545-.169L21.626 9.441l.169.544ZM21.393 15.31c-.978 2.863-3.236 5.128-6.083 6.083l6.083-6.083Z"})),Ur=({size:t,...r})=>(0,e.createElement)("svg",{width:t||24,height:t||24,viewBox:"0 0 24 24",fillRule:"evenodd","aria-hidden":"true",focusable:"false",...r},(0,e.createElement)("path",{d:"M14.694 9.306 21.976 12l-7.282 2.694L12 21.976l-2.694-7.282L2.024 12l7.282-2.694L12 2.024l2.694 7.282Zm-3.832 1.556L7.787 12l3.075 1.138L12 16.213l1.138-3.075L16.213 12l-3.075-1.138L12 7.787l-1.138 3.075Z"}),(0,e.createElement)("path",{d:"m18.746 3.024.81 2.19 2.19.81-2.19.81-.81 2.19-.81-2.19-2.19-.81 2.19-.81.81-2.19ZM18.746 14.976l.81 2.19 2.19.81-2.19.81-.81 2.19-.81-2.19-2.19-.81 2.19-.81.81-2.19Z"})),Hr=({size:t,...r})=>(0,e.createElement)("svg",{width:t||24,height:t||24,viewBox:"0 0 24 24","aria-hidden":"true",focusable:"false",...r},(0,e.createElement)("path",{d:"m14.879 16.293 1.414 1.415L12 22l-4.293-4.292 1.415-1.415L12 19.172l2.879-2.879ZM6.293 7.708l1.414 1.414L4.829 12l2.878 2.879-1.414 1.414L2 12l4.293-4.292ZM22 12l-4.293 4.293-1.414-1.414L19.172 12l-2.879-2.878 1.414-1.414L22 12Zm-5.707-5.707-1.414 1.415L12 4.829 9.122 7.708 7.707 6.293 12 2l4.293 4.293ZM14.879 12.05l-2.829 2.829-2.928-2.928 2.829-2.829 2.928 2.928Zm-2.929-.099.099.099-.099-.099Z"})),$r=({size:t,...r})=>(0,e.createElement)("svg",{width:t||24,height:t||24,viewBox:"0 0 24 24","aria-hidden":"true",focusable:"false",...r},(0,e.createElement)("path",{d:"M3.013 4h2v2h-2zM3.013 18h2v2h-2zM7.013 4h2v2h-2zM7.013 18h2v2h-2zM11.013 4h2v2h-2zM11.013 18h2v2h-2zM15.008 4h2v2h-2zM15.008 18h2v2h-2zM18.983 4h2v2h-2zM18.983 18h2v2h-2zM20.998 15.12H2.999l4.284-4.286 2.144 2.143 4.714-4.715 6.857 6.858Z"})),qr=({size:t,...r})=>(0,e.createElement)("svg",{viewBox:"0 0 24 24",width:t||24,height:t||24,"aria-hidden":"true",focusable:"false",...r},(0,e.createElement)("rect",{x:"7.052",y:"10.981",width:"2",height:"2"}),(0,e.createElement)("rect",{x:"11.052",y:"10.981",width:"2",height:"2"}),(0,e.createElement)("rect",{x:"15.052",y:"10.981",width:"2",height:"2"})),Gr={button:"nreeyXSWPBRlv8jkLC8M",control:"Or_Depww4Dl2tnsupiKF",open:"K0zIxiVCJdA93tgldk2t",icon:"lDlqzHA7NgkNU_P4MpHZ",dropdown:"KGoOFMahj6DRm9hWX3yN",hidden:"L11lIHzZcuGiDnUohCbr",item:"Yl9KmgnirQtaceNZ7VXO",highlighted:"fB5BEefTiiqDpr_xuiU1",selected:"LQt1eaZZxUYKRUIOQDRq","has-icon":"Np2n25Dbyx1V3Jy6lgQC",help:"gJvWeIIe98GRS6fCLdxg"};function Zr({item:t}){return(0,e.createElement)("span",null,t.label)}function Kr({label:t,onChange:r,id:n,help:a,className:l="",items:i=[],selectedItem:c=null,selectedStyle:u="highlight",defaultText:p=(0,s.__)("Select…","generateblocks-pro"),"aria-labelledby":h,"aria-label":m,itemToString:f=e=>e?e.label:"",ItemComponent:b=Zr}){const v={items:i,itemToString:f,selectedItem:c};r&&(v.onSelectedItemChange=({selectedItem:e})=>{r(e)});const{isOpen:y,getToggleButtonProps:w,getLabelProps:k,getMenuProps:E,highlightedIndex:S,getItemProps:x}=Tr(v),C=w({id:n,"aria-describedby":a&&n?`${n}__help`:void 0}),_="icon"===u;return(0,e.createElement)(o.BaseControl,{className:g(Gr.control,l),label:t,id:C.id,help:a},(0,e.createElement)("div",{className:Gr.header},t&&(0,e.createElement)("div",{...k({className:Gr.label})},t),(0,e.createElement)("button",{className:g(Gr.button,y&&Gr.open),...C},c?(0,e.createElement)(b,{item:c}):p,(0,e.createElement)("span",{className:Gr.icon},(0,e.createElement)(Nr,{size:"12"})))),(0,e.createElement)("ul",{"aria-labelledby":h,"aria-label":m,className:g(Gr.dropdown,!y&&Gr.hidden),...E()},y&&i.map(((t,r)=>{const n=c===t,o=S===r;return(0,e.createElement)("li",{key:t.id,className:g(o&&Gr.highlighted,!_&&n&&Gr.selected,Gr.item,_&&Gr["has-icon"]),...x({item:t,index:r})},(0,e.createElement)(b,{item:t}),_&&n&&(0,e.createElement)(d,{icon:ut,size:"18"}))}))))}const Yr=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})),Qr=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})),Xr={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};var Jr,en=new Uint8Array(16);function tn(){if(!Jr&&!(Jr="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Jr(en)}for(var rn=[],nn=0;nn<256;++nn)rn.push((nn+256).toString(16).slice(1));const on=function(e,t,r){if(Xr.randomUUID&&!t&&!e)return Xr.randomUUID();var n=(e=e||{}).random||(e.rng||tn)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){r=r||0;for(var o=0;o<16;++o)t[r+o]=n[o];return t}return function(e,t=0){return(rn[e[t+0]]+rn[e[t+1]]+rn[e[t+2]]+rn[e[t+3]]+"-"+rn[e[t+4]]+rn[e[t+5]]+"-"+rn[e[t+6]]+rn[e[t+7]]+"-"+rn[e[t+8]]+rn[e[t+9]]+"-"+rn[e[t+10]]+rn[e[t+11]]+rn[e[t+12]]+rn[e[t+13]]+rn[e[t+14]]+rn[e[t+15]]).toLowerCase()}(n)},an=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"})),sn=window.wp.editPost,ln={header:"d6IaRlgpXYjnlVSSBZbF",label:"h9_Upmcm6HAUNV72DLz4",options:"DwVQnOo0yINdlNOPpDLA",dropdown:"NPCIBLGznvPXoQWQ2d1j",popover:"znyJh6Gb9sBewM2CJY27",control:"Q41i8GM5WFAsOoP_rrsd",inline:"TanlZUNa_jkvLI9Q0SqN",description:"rg6VrdW6xZWTdxL9WEoN",close:"q5LmcwVcCToPJgnmeBHg",pill:"LjE2_QrnyKD_A3DsT_96",local:"Hh_rZP02AHuvSh6iTJhp",dot:"vXWcuZ6bAzoORtHmh38X",current:"obgIlrcfV2ziN8rX1Wq7",labelWrap:"Qx8BDO9Gjqk3jT4LDAtE",popoverContent:"mNNZp3sqkZoVppOXnsnH",resetButton:"GsIooHOw6DV995RwnXQY",indent:"MQD8Li3oa4TIMY9UNXVR",rule:"kxFwi891g5VHKrQmuI6F",button:"gHNc3ZPvLWQ95o0VECJO",css:"gID9KrphRIDXui6W17Hm",indicatorDots:"BoDJhE00NUOnkYlGCEkW"},cn=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"m19 7-3-3-8.5 8.5-1 4 4-1L19 7Zm-7 11.5H5V20h7v-1.5Z"})),un=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"}));function dn({group:t,editStyle:r,setSearch:n,setLocalTab:a,cancelEditStyle:l,onAtRuleChange:i,onNestedRuleChange:c,canManageStyles:u,openGeneralSidebar:d,isNested:p,currentSelector:h,onClose:m,scope:f}){const b=t[0].selector,v=t[0].nestedSelector,y=""!==t[0].atRule?t[0].atRule:null,w=(0,e.createElement)("div",{className:g(ln.rules,p&&ln.indent)},t.map((({property:t,valueRaw:p,source:g,atRule:v,selector:y})=>(0,e.createElement)("div",{key:t,className:ln.rule,tabIndex:"0"},(0,e.createElement)("span",{"data-property":t},Re(t)),": ",(0,e.createElement)("span",{"data-value":!0},p),";",u&&(0,e.createElement)(o.Button,{className:ln.button,variant:"link",label:(0,s.__)("Go to style source","generateblocks-pro"),showTooltip:!0,size:"small",iconSize:"18",icon:cn,onClick:async()=>{try{const e={atRule:v,nestedRule:"local"===g&&y!==h?y:""};"global"===g?(await r(y||b,e),n(t,g),void 0!==e.atRule&&requestAnimationFrame((()=>i(e.atRule))),void 0!==e.nestedRule&&c(e.nestedRule)):"local"===g&&("global"!==f&&(await l(),a("styles"),d("edit-post/block")),n(t,"global"===f?"global":g),void 0!==e.atRule&&requestAnimationFrame((()=>i(e.atRule))),void 0!==e.nestedRule&&c(e.nestedRule))}catch(e){console.error(e)}m()}})))));function k(){let e=b;return v&&(v.startsWith("&")?e+=v.replace("&",""):e+=" "+v),e}return y?(0,e.createElement)("div",{className:ln.group,"data-at-rule-declaration":!0},(0,e.createElement)("div",{"data-at-rule":y},y," ","{"),(0,e.createElement)("div",{className:ln.indent},(0,e.createElement)("span",{"data-selector":!0},k())," ",(0,e.createElement)("span",{"data-bracket":"open"},"{")),w,(0,e.createElement)("div",{className:ln.indent,"data-bracket":"close"},"}"),(0,e.createElement)("div",{"data-bracket":"close"},"}")):(0,e.createElement)("div",{"data-declaration":!0,className:ln.rules},(0,e.createElement)("div",null,(0,e.createElement)("span",{"data-selector":!0},k())," ",(0,e.createElement)("span",{"data-bracket":"open"},"{")),w,(0,e.createElement)("div",{"data-bracket":"close"},"}"))}function pn({controlInheritedSources:t,matchType:r}){const n=T(),{onEditStyle:a,setSearch:l,setLocalTab:i,cancelEditStyle:c,onAtRuleChange:u,canManageStyles:d,currentSelector:p,onNestedRuleChange:h,scope:m}=n,{openGeneralSidebar:f}=(0,y.useDispatch)(sn.store),b=function(e,t,r){return e.filter((e=>e.hasInheritedValue&&e.source===r)).map((e=>({atRule:e.inheritedAtRule||"",selector:e.classNameSelector||t,nestedSelector:e.inheritedNestedRule||"",property:e.property,valueRaw:e.value,source:e.source}))).reduce(((e,t)=>{const r=`${t.atRule}::${t.selector}::${t.nestedSelector}`;return e[r]||(e[r]=[]),e[r].push(t),e}),{})}(t,p,r);return Object.keys(b).length>0&&r?(0,e.createElement)(o.Dropdown,{className:g(ln.popover,ln[r]),contentClassName:ln.popoverContent,renderToggle:({onToggle:t})=>(0,e.createElement)(o.Button,{className:g(ln.dot,ln[r]),onClick:()=>{t()},title:"current"===r?(0,s.__)("Has local styles","generateblocks-pro"):"global"===r?(0,s.__)("Has inherited global styles","generateblocks-pro"):"local"===r?(0,s.__)("Has inherited local styles","generateblocks-pro"):void 0}),renderContent:({onClose:t})=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.Button,{icon:un,label:(0,s.__)("Close","generateblocks-pro"),size:"small",className:ln.close,onClick:t}),(0,e.createElement)("div",{className:ln.css},Object.values(b).map(((r,n)=>(0,e.createElement)(dn,{key:n,group:r,editStyle:a,setSearch:l,setLocalTab:i,cancelEditStyle:c,onAtRuleChange:u,onNestedRuleChange:h,canManageStyles:d,openGeneralSidebar:f,isNested:"all"!==r[0].atRule,currentSelector:p,onClose:t,scope:m})))))}):null}const hn=(0,a.memo)((function({dropdownOptions:t,dropdownChildren:r,allowCustomValue:n,usingCustomValue:a,setUsingCustomValue:l,learnMoreUrl:c,learnMoreLabel:u,beforeDropdownMenu:d,menuVisible:p=!1}){return(0,e.createElement)("div",{className:ln.options},d,p&&(0,e.createElement)(o.DropdownMenu,{className:ln.dropdown,icon:i,label:(0,s.__)("More options","generateblocks-pro"),controls:t,popoverProps:{className:ln.popover}},(({onClose:t})=>(0,e.createElement)(e.Fragment,null,r&&r({onClose:t}),n&&(0,e.createElement)(o.MenuGroup,null,(0,e.createElement)(o.MenuItem,{onClick:()=>{l(!a),t()},suffix:a?ut:""},(0,s.__)("Enter Custom Value","generateblocks-pro"))),c&&u&&(0,e.createElement)(o.MenuGroup,null,(0,e.createElement)(o.MenuItem,{icon:Qr,iconSize:10,onClick:()=>{window.open(c,"_blank"),t()}},u))))))})),mn={control:"bjI5HQwkZuJ9WqqaE1s1",multiRow:"NOqroOzaVqguMQYYG7Gv",button:"ljk8t8yFtJTlmjEk0oEm",fallback:"lb2wubM1CCV9vIcdKOjD"};function fn({buttons:t,value:r,placeholder:n="",onChange:a,className:s,multiRow:l}){return(0,e.createElement)(o.ButtonGroup,{className:g("gb-button-icon-control",mn.control,s,l&&mn.multiRow)},Object.values(t).map((t=>(0,e.createElement)(o.Button,{key:t.value,isPressed:t.value===r,onClick:()=>{a(t.value!==r?t.value:"")},label:t.label,showTooltip:!!t.icon,className:g(mn.button,!r&&n===t.value&&mn.fallback)},t.icon?t.icon:t.label))))}const gn=["label","hideLabelFromVision","value","help","id","className","onChange","type"],bn=(0,a.forwardRef)((function(t,r){const{id:n,children:l,label:i,dropdownOptions:c,dropdownChildren:u,learnMoreLabel:d,learnMoreUrl:p,style:h,beforeDropdownMenu:m,afterLabel:f,onVisibilityChange:b,value:v,cssProp:y=!1,as:w=null,allowCustomValue:E=!1,hasCustomValue:S=!1,customValueHelp:x=(0,s.__)("Enter a custom value.","generateblocks-pro"),onChange:C,searchKeywords:_=[],className:O="",alwaysVisible:I=!1,...R}=t,M=T(),{atRule:P,controlFilters:A,controlIsVisible:L,inheritedSources:z}=M,D=z.filter((e=>(Array.isArray(y)?y:[y]).includes(e.property))),F={current:0,local:1,global:2},B=D.filter((e=>e.hasInheritedValue)).sort(((e,t)=>F[e.source]-F[t.source])),j=B.map((({source:e})=>e)),V=new Set(j),W=Array.from(V),U=`control-wrapper-${on()}`,H=n||`control-${on()}`,[$,q]=(0,a.useState)(S&&""!==v),G=E&&$?o.TextControl:w,Z=p||u||E,[K,Y]=(0,a.useState)(v),Q=(0,a.useRef)(!1),[X,J]=(0,a.useState)({[P]:!1}),ee=(0,a.useRef)(),te=r||ee,re=(0,a.useMemo)((()=>(0,k.debounce)(C,200)),[C]);(0,a.useEffect)((()=>{Q.current?Q.current=!1:K!==v&&Y(v)}),[v]);const ne={...R,onChange:e=>{const t=void 0===e?"":e;Q.current=!0,Y(t),re(t);const r="show-with-value"===A?.activeFilter;J((e=>({...e,[P]:!t&&v&&r})))},onBlur:e=>{const t=_e(e.target.value);t!==e.target.value&&(Q.current=!0,Y(t),C(t))},value:K},oe=a.Children.count(l)>0;if(w&&oe)throw new Error((0,s.__)("Control component cannot have both a component and children.","generateblocks-pro"));const ae={className:ln.label},se=![o.RangeControl].includes(w);se?w===o.AnglePickerControl&&i&&(ne.label=""):(ne.label=i||ne["aria-label"],ne.hideLabelFromVision=!0),se?(ne.id=H,ae.htmlFor=H):ae["aria-hidden"]="true",$&&(ne.help=x,Object.keys(ne).forEach((e=>{gn.includes(e)||delete ne[e]})));let le=[..._];if("string"==typeof y?le.push(y):Array.isArray(y)?le.push(...y):"object"==typeof y&&le.push(...Object.keys(y)),w===o.SelectControl){const{options:e=[]}=ne;le.push(...e.map((e=>e.value)))}if(w===fn){const{buttons:e=[]}=ne;le.push(...e.map((e=>e.value)))}if(le=Array.from(new Set([...le,...le.map((e=>Re(e)))])),E&&w===o.SelectControl){const{options:e=[]}=ne,t=K.trim();if(e.every((e=>e.value!==t))&&!e.some((e=>e.value===t))){const r=[{label:(0,s.sprintf)(
// Translators: %s is the value of the select control.
// Translators: %s is the value of the select control.
(0,s.__)(" %s (Custom)","generateblocks-pro"),t),value:t},...e];ne.options=r}}!ne.placeholder&&y&&(ne.placeholder=function(e,t){const r=t.find((t=>t.property===e&&"current"!==t.source));return r?r.value:""}(y,D),"opacity"===y&&ne.placeholder&&(ne.placeholder=100*parseFloat(ne.placeholder)));const ie={componentProps:ne,props:t,wrapperRef:te,label:i,labelProps:ae,controlId:H,unusedProps:R,cssProp:y,searchKeywords:le,alwaysVisible:I,matchTypes:W},ce=(0,N.applyFilters)("generateblocks.control.props",ne,ie),ue=L(ie,ce),de=I||X[P]||ue;if(!de)return null;const pe={menuVisible:Z,dropdownOptions:c,dropdownChildren:u,allowCustomValue:E,usingCustomValue:$,setUsingCustomValue:q,learnMoreUrl:p,learnMoreLabel:d,beforeDropdownMenu:m};if(!pe.learnMoreLabel&&"string"==typeof y){const e=y.replace(/([A-Z])/g," $1").trim();pe.learnMoreLabel=(0,s.sprintf)(
// Translators: %s is the CSS property.
// Translators: %s is the CSS property.
(0,s.__)("Learn more about %s","generateblocks-pro"),e.slice(0,1).toUpperCase()+e.slice(1))}return(0,e.createElement)("div",{className:g("gb-styles-builder-control",ln.control,!i&&ln.inline,de?"visible":"hidden",O),id:oe?H:U,ref:te,style:h},i&&(0,e.createElement)("div",{className:g("gb-styles-builder-control__header",ln.header)},(0,e.createElement)("div",{className:g(ln.labelWrap,"gb-styles-builder-control__header--label")},(0,e.createElement)("label",{...ae}," ",i),f,W.length>0&&(0,e.createElement)("div",{className:ln.indicatorDots},W.map((t=>(0,e.createElement)(pn,{key:t,controlInheritedSources:D,matchType:t}))))),(0,e.createElement)(hn,{...pe})),!l&&G&&(0,e.createElement)(G,{...ce}),l,!i&&(0,e.createElement)(hn,{...pe}))}));bn.Description=function({label:t,children:r,onClick:n}){return r&&(0,e.createElement)(o.MenuGroup,{label:t},(0,e.createElement)("div",{className:ln.description},r,n&&(0,e.createElement)(o.Button,{className:ln.close,variant:"none",onClick:n,label:(0,s.__)("Close","generateblocks-pro"),icon:an})))};const vn={list:"eyQghrRSdEyEHPvOQgfy",options:"QO4bDCM6YiGwcdKbp4Wm",swatch:"i2vhl6ML7PkPat1EruMQ",circle:"QTRPENvCem5DB7jNijKR","swatch-wrapper":"m2zkErnLcYgBKreyi49t",item:"ajY26UkdoWVD_SZjYjVa",css:"wde1QvsjBt8aNX1Ss07o",control:"IepWCSsyOrDYMjofnMXj",icons:"tYMExdHcmrcL8vkF9g8z",toggle:"L2ap4dHZ7x1MNxvO3bWv",body:"lTlz7QghzuR4VYGrVUnO",dropdown:"RQidt_3JwhROE12PBlzJ","dropdown-popover":"cfpbQekxj1ZZhXVq7SAU","is-disabled":"Cd414jOuo9q0ON1WtUjv","dropdown-content":"M33l5OtEK3YhurHPuXa_","dropdown-actions":"EFQ_5GZwwc461_7q1k_z",edit:"y4WiNqbamdmYsPs6l9w6","modal-description":"DGhDf9hBWOmd6EMZ61h6","modal-actions":"KK79N5fvcfhmfwuEE5BM","modal-confirm":"G1I6h4pm0MCT_zr3uhHm"};function yn({children:t,label:r,dropdownControls:n,dropdownChildren:a,onAdd:l,onClickDone:i,onClickDelete:c,isEditing:u,id:d,cssProp:p,searchKeywords:m=[],showAdd:f=!0,items:g=[]}){const b=`effect-control-${on()}`;return(0,e.createElement)(bn,{label:r,beforeDropdownMenu:(0,e.createElement)("div",{className:vn.icons},u?(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.Button,{variant:"primary",size:"small",icon:ut,onClick:i,label:(0,s.__)("Done","generateblocks-pro"),showTooltip:!0}),(0,e.createElement)(o.Button,{variant:"tertiary",size:"small",isDestructive:!0,icon:Yr,onClick:c,label:(0,s.__)("Delete","generateblocks-pro"),showTooltip:!0})):f&&(0,e.createElement)(o.Button,{variant:"tertiary",size:"small",icon:h,onClick:l,label:(0,s.__)("Add new","generateblocks-pro")})),id:d||b,className:`gb-effects-control ${vn.control}`,dropdownControls:n,dropdownChildren:a,popoverProps:{className:vn["dropdown-popover"]},searchKeywords:m,cssProp:p,value:g.length>0?g.length:""},(0,e.createElement)("div",{className:vn.body,style:{display:g.length>0?"block":"none"}},t))}function wn({children:t}){return(0,e.createElement)(b.Stack,{className:vn.edit,gap:"12px","data-component":"EffectEdit"},t)}yn.LearnMore=({learnMoreLabel:t,learnMoreURL:r,onClose:n})=>t&&r&&(0,e.createElement)(o.MenuGroup,null,(0,e.createElement)(o.MenuItem,{icon:Qr,iconSize:10,onClick:()=>{window.open(r,"_blank"),n()}},t)),yn.DeleteAll=function({label:t,content:r,confirmDelete:n,items:a,setConfirmDelete:l,onClose:i,onDelete:c}){return(0,e.createElement)(e.Fragment,null,n?(0,e.createElement)("div",{className:vn["dropdown-content"]},r,(0,e.createElement)("div",{className:vn["dropdown-actions"]},(0,e.createElement)(o.Button,{variant:"secondary",size:"compact",onClick:()=>{l(!1),i()}},(0,s.__)("Cancel","generateblocks-pro")),(0,e.createElement)(o.Button,{isDestructive:!0,variant:"secondary",size:"compact",onClick:()=>{c([]),l(!1),i()}},(0,s.__)("Confirm","generateblocks-pro")))):(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuItem,{icon:Yr,onClick:()=>{a.length>0&&l(!0)},className:g(0===a.length&&vn["is-disabled"]),tabIndex:a.length>0?0:-1},t)))},yn.PasteModal=function({showPasteStyles:t,setShowPasteStyles:r,property:n,onAddStyles:l,errorMessage:i,learnMoreUrl:c=`https://developer.mozilla.org/en-US/docs/Web/CSS/${n}`}){const[u,d]=(0,a.useState)(""),[p,h]=(0,a.useState)(!0),m=n.charAt(0).toUpperCase()+n.slice(1);return!!t&&(0,e.createElement)(o.Modal,{title:(0,s.sprintf)(
// Translators: %s: CSS property string in title case
// Translators: %s: CSS property string in title case
(0,s.__)("Paste %s Styles","generateblocks-pro"),m),isDismissible:!0,onRequestClose:()=>r(!1),style:{maxWidth:"425px",width:"425px"}},(0,e.createElement)(o.TextareaControl,{rows:5,value:u,onChange:e=>{d(e)},help:i}),(0,e.createElement)("div",{className:vn["modal-description"]},(0,a.createInterpolateElement)(
// Translators: %s is the learn more link, property prop is the inner text.
// Translators: %s is the learn more link, property prop is the inner text.
(0,s.__)("Paste in valid CSS <Link /> styles to add to the current list.","generateblocks-pro"),{Link:(0,e.createElement)((function(){return(0,e.createElement)("a",{href:c,target:"_blank",rel:"noreferrer"},n)}),null)})),(0,e.createElement)("div",{className:vn["modal-confirm"]},(0,e.createElement)(o.CheckboxControl,{checked:p,label:(0,s.sprintf)(
// Translators: %s: CSS property string
// Translators: %s: CSS property string
(0,s.__)("Replace %s styles.","generateblocks-pro"),n),help:(0,s.sprintf)(
// Translators: %s: CSS property string
// Translators: %s: CSS property string
(0,s.__)("If checked this will replace all existing %s styles.","generateblocks-pro"),n),onChange:e=>{h(e)}})),(0,e.createElement)("div",{className:vn["modal-actions"]},(0,e.createElement)(o.Button,{variant:"secondary",onClick:()=>r(!1)},(0,s.__)("Cancel","generateblocks-pro")),(0,e.createElement)(o.Button,{disabled:!u||0===u.length,variant:"primary",onClick:()=>{l&&l({pastedValue:u,replaceStyles:p})&&d("")}},(0,s.__)("Add Styles","generateblocks-pro"))))};const kn=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.5h11a.5.5 0 0 1 .5.5v11a.5.5 0 0 1-.5.5H5a.5.5 0 0 1-.5-.5V5a.5.5 0 0 1 .5-.5ZM3 5a2 2 0 0 1 2-2h11a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5Zm17 3v10.75c0 .69-.56 1.25-1.25 1.25H6v1.5h12.75a2.75 2.75 0 0 0 2.75-2.75V8H20Z"})),En=(0,e.createElement)(l.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)(l.Path,{d:"M4.67 10.664s-2.09 1.11-2.917 1.582l.494.87 1.608-.914.002.002c.343.502.86 1.17 1.563 1.84.348.33.742.663 1.185.976L5.57 16.744l.858.515 1.02-1.701a9.1 9.1 0 0 0 4.051 1.18V19h1v-2.263a9.1 9.1 0 0 0 4.05-1.18l1.021 1.7.858-.514-1.034-1.723c.442-.313.837-.646 1.184-.977.703-.669 1.22-1.337 1.563-1.839l.002-.003 1.61.914.493-.87c-1.75-.994-2.918-1.58-2.918-1.58l-.003.005a8.29 8.29 0 0 1-.422.689 10.097 10.097 0 0 1-1.36 1.598c-1.218 1.16-3.042 2.293-5.544 2.293-2.503 0-4.327-1.132-5.546-2.293a10.099 10.099 0 0 1-1.359-1.599 8.267 8.267 0 0 1-.422-.689l-.003-.005Z"})),Sn=(0,e.createElement)(l.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)(l.Path,{d:"M3.99961 13C4.67043 13.3354 4.6703 13.3357 4.67017 13.3359L4.67298 13.3305C4.67621 13.3242 4.68184 13.3135 4.68988 13.2985C4.70595 13.2686 4.7316 13.2218 4.76695 13.1608C4.8377 13.0385 4.94692 12.8592 5.09541 12.6419C5.39312 12.2062 5.84436 11.624 6.45435 11.0431C7.67308 9.88241 9.49719 8.75 11.9996 8.75C14.502 8.75 16.3261 9.88241 17.5449 11.0431C18.1549 11.624 18.6061 12.2062 18.9038 12.6419C19.0523 12.8592 19.1615 13.0385 19.2323 13.1608C19.2676 13.2218 19.2933 13.2686 19.3093 13.2985C19.3174 13.3135 19.323 13.3242 19.3262 13.3305L19.3291 13.3359C19.3289 13.3357 19.3288 13.3354 19.9996 13C20.6704 12.6646 20.6703 12.6643 20.6701 12.664L20.6697 12.6632L20.6688 12.6614L20.6662 12.6563L20.6583 12.6408C20.6517 12.6282 20.6427 12.6108 20.631 12.5892C20.6078 12.5459 20.5744 12.4852 20.5306 12.4096C20.4432 12.2584 20.3141 12.0471 20.1423 11.7956C19.7994 11.2938 19.2819 10.626 18.5794 9.9569C17.1731 8.61759 14.9972 7.25 11.9996 7.25C9.00203 7.25 6.82614 8.61759 5.41987 9.9569C4.71736 10.626 4.19984 11.2938 3.85694 11.7956C3.68511 12.0471 3.55605 12.2584 3.4686 12.4096C3.42484 12.4852 3.39142 12.5459 3.36818 12.5892C3.35656 12.6108 3.34748 12.6282 3.34092 12.6408L3.33297 12.6563L3.33041 12.6614L3.32948 12.6632L3.32911 12.664C3.32894 12.6643 3.32879 12.6646 3.99961 13ZM11.9996 16C13.9326 16 15.4996 14.433 15.4996 12.5C15.4996 10.567 13.9326 9 11.9996 9C10.0666 9 8.49961 10.567 8.49961 12.5C8.49961 14.433 10.0666 16 11.9996 16Z"})),xn=(0,a.forwardRef)((({items:t,className:r,...n},o)=>(0,e.createElement)(b.SortableList,{...n,items:t,className:g("gb-effect-list",vn.list,r),ref:o})));xn.Item=function({hidden:t,onDuplicate:r,onHidden:n,onEdit:a,css:l="",label:i="",swatchColor:c=null,canDuplicate:u=!0,canHide:d=!1,canEdit:p=!0}){return(0,e.createElement)("div",{className:`gb-effect-list-item ${vn.item}`},c&&(0,e.createElement)("div",{className:vn["swatch-wrapper"]},(0,e.createElement)("div",{className:vn.swatch},(0,e.createElement)("div",{className:vn.circle,style:{backgroundColor:c}}))),(0,e.createElement)("div",{title:l,className:vn.css,style:t?{opacity:"0.4"}:{}},(0,e.createElement)("span",null,i.length?i:l)),(0,e.createElement)("div",{className:vn.options},u&&(0,e.createElement)(o.Button,{label:(0,s.__)("Duplicate","generateblocks-pro"),showTooltip:!0,variant:"tertiary",size:"small",icon:kn,onClick:r}),d&&(0,e.createElement)(o.Button,{label:t?(0,s.__)("Show","generateblocks-pro"):(0,s.__)("Hide","generateblocks-pro"),showTooltip:!0,variant:"tertiary",size:"small",icon:t?En:Sn,onClick:n}),p&&(0,e.createElement)(o.Button,{label:(0,s.__)("Edit","generateblocks-pro"),showTooltip:!0,variant:"tertiary",size:"small",icon:cn,onClick:a})))};const Cn=(0,e.createElement)(l.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)(l.Path,{d:"M10 17.5H14V16H10V17.5ZM6 6V7.5H18V6H6ZM8 12.5H16V11H8V12.5Z"})),On={alignItems:"DGnsDpe0ln3pwUmBPMPS",flexColumn:"ksu9XcWRWBBYwjQmyz_r",justifyContent:"fBpbqdmtWNwCttKF5t1t",flexReverse:"dxQutEBCwV8aFcqn2Ygj",buttonGroup:"qS4QcaxHacgE4HGcXd2i",isInherited:"Rd3WMLBcBK25aVOljFLX",presets:"xRkpPFF0vXTFjdwljgq9",presetButton:"X_9OuEPw7pGG9mJq2vD9",preview:"hW2KN7X6gTThqxcId8uW",gridTemplateColumns:"jzHOmWMKNU_fTKpTGm3b"},Tn=[{value:"flex-start",icon:(0,e.createElement)("svg",{"aria-hidden":"true",style:{width:"16px",height:"16px"},viewBox:"0 0 16 16"},(0,e.createElement)("path",{fill:"currentColor",d:"M0 0h16v1H0z"}),(0,e.createElement)("path",{fill:"currentColor",stroke:"currentColor",d:"M3.5 2.5h3v7h-3zm5 0h3v5h-3z"})),label:"Align Start"},{value:"center",icon:(0,e.createElement)("svg",{"aria-hidden":"true",style:{width:"16px",height:"16px"},viewBox:"0 0 16 16"},(0,e.createElement)("path",{fill:"currentColor",stroke:"currentColor",d:"M3.5 3.5h3v8h-3zm5 1h3v6h-3z"}),(0,e.createElement)("path",{fill:"currentColor",d:"M0 7h16v1H0z"})),label:"Align Center"},{value:"flex-end",icon:(0,e.createElement)("svg",{"aria-hidden":"true",style:{width:"16px",height:"16px"},viewBox:"0 0 16 16"},(0,e.createElement)("path",{fill:"currentColor",d:"M0 15h16v1H0z"}),(0,e.createElement)("path",{fill:"currentColor",stroke:"currentColor",d:"M3.5 6.5h3v7h-3zm5 2h3v5h-3z"})),label:"Align End"},{value:"stretch",icon:(0,e.createElement)("svg",{"aria-hidden":"true",style:{width:"16px",height:"16px"},viewBox:"0 0 16 16"},(0,e.createElement)("path",{fill:"currentColor",d:"M0 0h16v1H0zm0 15h16v1H0z"}),(0,e.createElement)("path",{fill:"currentColor",stroke:"currentColor",d:"M3.5 2.5h3v11h-3zm5 0h3v11h-3z"})),label:"Stretch"},{value:"baseline",icon:(0,e.createElement)("svg",{"aria-hidden":"true",style:{width:"16px",height:"16px"},viewBox:"0 0 16 16"},(0,e.createElement)("path",{fill:"currentColor",d:"M0 7h16v1H0z"}),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 3H8v7h4V3zm-1 1H9v3h2V4zM7 3H3v9h4V3zM6 4H4v3h2V4z",fill:"currentColor"})),label:"Baseline"}];function In({value:t,onChange:r,flexDirection:n}){const o=(0,a.useMemo)((()=>Tn.every((e=>e.value!==t))),[t]);return(0,e.createElement)(bn,{as:fn,allowCustomValue:!0,hasCustomValue:o,label:"Align Items",id:"gblocks-align-items",value:t,onChange:r,buttons:Tn,cssProp:"alignItems",className:g(On.alignItems,n.includes("column")&&On.flexColumn,n.includes("reverse")&&On.flexReverse),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/align-items"})}function Rn({value:t,onChange:r}){const n=(0,a.useMemo)((()=>[{label:"Row",value:"row"},{label:"Column",value:"column"},{label:"Row Reverse",value:"row-reverse"},{label:"Column Reverse",value:"column-reverse"}]),[t]),o=(0,a.useMemo)((()=>n.every((e=>e.value!==t))),[t]);return(0,e.createElement)(bn,{as:fn,multiRow:!0,allowCustomValue:!0,hasCustomValue:o,label:"Flex Direction",id:"gblocks-flex-direction",value:t,onChange:r,buttons:n,cssProp:"flexDirection",learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/flex-direction"})}const Mn=[{label:"No Wrap",value:"nowrap"},{label:"Wrap",value:"wrap"},{label:"Reverse Wrap",value:"wrap-reverse"}];function Pn({value:t,onChange:r}){const n=(0,a.useMemo)((()=>Mn.every((e=>e.value!==t))),[t]);return(0,e.createElement)(bn,{as:fn,allowCustomValue:!0,hasCustomValue:n,label:"Flex Wrap",id:"gblocks-flex-wrap",value:t,onChange:r,buttons:Mn,cssProp:"flexWrap",learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/flex-wrap"})}function An({onClick:t,value:r}){const n={"1fr":1,"repeat(2, minmax(0, 1fr))":2,"repeat(3, minmax(0, 1fr))":3,"repeat(4, minmax(0, 1fr))":4,"1fr 3fr":2,"3fr 1fr":2,"1fr 1fr 2fr":3,"1fr 2fr 1fr":3,"2fr 1fr 1fr":3,"1fr 3fr 1fr":3,"repeat(5, minmax(0, 1fr))":5,"repeat(6, minmax(0, 1fr))":6};return(0,e.createElement)("div",{className:On.presets},Object.keys(n).map((a=>(0,e.createElement)(o.Button,{label:a,showTooltip:!0,key:`layout-${a}`,className:On.presetButton,onClick:()=>t(a),isPressed:a===r,style:{"--grid-template-columns":a}},Array.from({length:n[a]},((t,r)=>(0,e.createElement)("div",{key:`layout-${r}`,className:On.preview})))))))}function Ln({value:t,onChange:r,onShortcutChange:n}){const[l,i]=(0,a.useState)(!1);return(0,e.createElement)(bn,{label:"Grid Template Columns",id:"grid-template-columns",value:t,cssProp:"gridTemplateColumns"},(0,e.createElement)("div",{className:On.gridTemplateColumns},(0,e.createElement)(bn,{as:o.TextControl,value:t,onChange:r,cssProp:!1,alwaysVisible:!0}),(0,e.createElement)(o.Button,{size:"small",onClick:()=>i(!l),icon:()=>(0,e.createElement)("svg",{style:{width:"100%"},xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256"},(0,e.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,e.createElement)("rect",{x:"32",y:"56",width:"192",height:"144",rx:"8",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"12"}),(0,e.createElement)("line",{x1:"96",y1:"56",x2:"96",y2:"200",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"12"}),(0,e.createElement)("line",{x1:"160",y1:"56",x2:"160",y2:"200",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"12"}),(0,e.createElement)("line",{x1:"32",y1:"104",x2:"224",y2:"104",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"12"}),(0,e.createElement)("line",{x1:"32",y1:"152",x2:"224",y2:"152",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"12"})),isPressed:l,label:(0,s.__)("Choose a preset","generateblocks-pro"),showTooltip:!0})),!!l&&(0,e.createElement)(An,{value:t,onClick:n}))}const Nn=[{value:"flex-start",icon:(0,e.createElement)("svg",{"aria-hidden":"true",style:{width:"16px",height:"16px"},viewBox:"0 0 16 16"},(0,e.createElement)("path",{fill:"currentColor",stroke:"currentColor",d:"M2.5 4.5h3v7h-3zm5 0h3v7h-3z"}),(0,e.createElement)("path",{fill:"currentColor",d:"M0 0h1v16H0z"})),label:"Flex Start"},{value:"center",icon:(0,e.createElement)("svg",{"aria-hidden":"true",style:{width:"16px",height:"16px"},viewBox:"0 0 16 16"},(0,e.createElement)("path",{fill:"currentColor",stroke:"currentColor",d:"M2.5 4.5h3v7h-3zm7 0h3v7h-3z"}),(0,e.createElement)("path",{fill:"currentColor",d:"M7 0h1v16H7z"})),label:"Center"},{value:"flex-end",icon:(0,e.createElement)("svg",{"aria-hidden":"true",style:{width:"16px",height:"16px"},viewBox:"0 0 16 16"},(0,e.createElement)("path",{fill:"currentColor",d:"M15 0h1v16h-1z"}),(0,e.createElement)("path",{fill:"currentColor",stroke:"currentColor",d:"M5.5 4.5h3v7h-3zm5 0h3v7h-3z"})),label:"Flex End"},{value:"space-between",icon:(0,e.createElement)("svg",{"aria-hidden":"true",style:{width:"16px",height:"16px"},viewBox:"0 0 16 16"},(0,e.createElement)("path",{fill:"currentColor",d:"M15 0h1v16h-1zM0 0h1v16H0z"}),(0,e.createElement)("path",{fill:"currentColor",stroke:"currentColor",d:"M10.5 4.5h3v7h-3zm-8 0h3v7h-3z"})),label:"Space Between"},{value:"space-around",icon:(0,e.createElement)("svg",{"aria-hidden":"true",style:{width:"16px",height:"16px"},viewBox:"0 0 16 16"},(0,e.createElement)("path",{fill:"currentColor",d:"M15 0h1v16h-1zM0 0h1v16H0z"}),(0,e.createElement)("path",{fill:"currentColor",stroke:"currentColor",d:"M9.5 4.5h3v7h-3zm-6 0h3v7h-3z"})),label:"Space Around"}];function zn({value:t,onChange:r,flexDirection:n}){const o=(0,a.useMemo)((()=>Nn.every((e=>e.value!==t))),[t]);return(0,e.createElement)(bn,{as:fn,allowCustomValue:!0,hasCustomValue:o,label:"Justify Content",id:"gblocks-justify-content",value:t,onChange:r,buttons:Nn,cssProp:"justifyContent",className:g(On.justifyContent,n.includes("column")&&On.flexColumn,n.includes("reverse")&&On.flexReverse),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/justify-content"})}const Dn=function(t,r){var n,o=(n=(0,e.useRef)(!0)).current?(n.current=!1,!0):n.current;(0,e.useEffect)((function(){if(!o)return t()}),r)},Fn=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M12 4.75a7.25 7.25 0 100 14.5 7.25 7.25 0 000-14.5zM3.25 12a8.75 8.75 0 1117.5 0 8.75 8.75 0 01-17.5 0zM12 8.75a1.5 1.5 0 01.167 2.99c-.465.052-.917.44-.917 1.01V14h1.5v-.845A3 3 0 109 10.25h1.5a1.5 1.5 0 011.5-1.5zM11.25 15v1.5h1.5V15h-1.5z"})),Bn={wrapper:"fZXUsJMC68oVMMjhAles",action:"oUsDPDZdj47l4bSWTerz",overrideAction:"RfNmIUCzCqXw8Igzp76y",units:"q8s_InDlwvSwUGvaSaTw",popover:"h5QejAYc8dCRNvEl2vqo",disabled:"hs89aYXLzRTKTiZe7Kgu"};function jn({value:t,onChange:r,units:n=[],disabled:a}){if(!n.length)return null;const l=[...n];return l.includes(t)||(l[l.length-1]=t),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.DropdownMenu,{className:Bn.units,label:(0,s.__)("Select a unit","generateblocks-pro"),icon:null,toggleProps:{children:t||String.fromCharCode(8212),disabled:a},popoverProps:{className:Bn.popover,focusOnMount:!0,noArrow:!1}},(({onClose:n})=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuGroup,null,l.map((a=>(0,e.createElement)(o.MenuItem,{key:a,onClick:()=>{r(a),n()},isSelected:a===t,variant:a===t?"primary":""},a||String.fromCharCode(8212)))),(0,e.createElement)(o.MenuItem,{onClick:()=>{window.open("https://developer.mozilla.org/en-US/docs/Learn/CSS/Building_blocks/Values_and_units","_blank").focus()},label:(0,s.__)("Learn more about units","generateblocks-pro"),showTooltip:!0},Fn))))))}const Vn=["px","em","%","rem","vw","vh","ch"],Wn=new RegExp(`(${["px","em","%","rem","vw","vh","ch","cm","mm","in","pt","pc","ex","lh","rlh","vmin","vmax","vb","vi","svw","svh","svb","svi","svmax","svmin","lvw","lvh","lvb","lvi","lvmax","lvmin","dvw","dvh","dvb","dvi","dvmax","dvmin","fr","s","ms"].join("|")})`);function Un(e,t=""){var r,n;if(""===e)return{value:"",unit:t};if(0===e||"0"===e)return{value:"0",unit:""};if(!ee(e)&&"-"!==e)return{value:e,unit:"none"};const o=e?e.toString().toLowerCase().split(Wn).filter((e=>""!==e)):[];return{value:null!==(r=o[0]?.trim())&&void 0!==r?r:"",unit:null!==(n=o[1]?.trim())&&void 0!==n?n:""}}function Hn(t){var r,n;const{label:s,units:l=Vn,defaultUnit:i,step:c,id:u,max:d,min:p=0,disabled:h=!1,onChange:m=e=>e,help:f="",onFocus:b=()=>null,onBlur:v=()=>null,overrideAction:y=()=>null,allowOtherUnits:w=!0}=t,k=(0,a.useCallback)((e=>{L(!0),m(e)}),[m]),E=null!==(r=t?.value?.toString())&&void 0!==r?r:"",S=null!==(n=t?.placeholder?.toString())&&void 0!==n?n:"",x=0===l?.length||!l,C="string"==typeof i?i:l[0],{value:_="",unit:O=""}=(0,a.useMemo)((()=>Un(E,C)),[E,C]),T=(0,a.useMemo)((()=>E?{value:"",unit:""}:Un(S,C)),[E,S,C]),[I,R]=(0,a.useState)(_),[M,P]=(0,a.useState)(E?O:Un(S,C).unit||C),[A,L]=(0,a.useState)(!1),N=(0,a.useCallback)((function(e){const t=e.key,r=e.target.value;if(isNaN(r))return;let n=r;switch(t){case"ArrowUp":n=+r+1,k(`${n}${O}`),R(n);break;case"ArrowDown":n=+r-1,k(`${n}${O}`),R(n)}}),[O,k]),z=(0,a.useCallback)((function(e){let{value:t="",unit:r=""}=Un(e.toString());if(R(t),""===t){if(k(""),S&&""!==S){const{unit:e}=Un(S,C);P(""!==e?e:C)}else"none"===M&&P(C);return}const n=(x?[]:[...l,"none"]).includes(r)||w;r&&n||(r="none"!==M||isNaN(t)?M:C),P(r),k(r&&"none"!==r&&!x?`${t}${r}`:t)}),[O,M,l,w,k,C]),D=(0,a.useCallback)((function(e){l.length>1&&l.includes(e)&&(P(e),k(`${_}${e}`))}),[I,l,P,k]);return(0,a.useEffect)((()=>{if(A)return void L(!1);const{value:e="",unit:t=""}=Un(E,C);I!==e&&R(e),E&&M!==t&&P(t)}),[E]),Dn((()=>{if(E||!S)return;const{unit:e}=Un(S,C);P(""!==S&&""!==e?e:C)}),[E,S,C]),(0,e.createElement)(o.BaseControl,{label:s,help:f,id:u,className:g("gb-unit-control",h&&Bn.disabled),__nextHasNoMarginBottom:!0},(0,e.createElement)("div",{className:Bn.wrapper,"data-component":"UnitControl"},(0,e.createElement)(o.TextControl,{type:"text",value:I,placeholder:T?.value,min:p,max:d,step:c,autoComplete:"off",disabled:h,onKeyDown:N,onChange:z,onFocus:b,onBlur:v,__nextHasNoMarginBottom:!0}),(0,e.createElement)("div",{className:Bn.action},!!y&&(0,e.createElement)("div",{className:Bn.overrideAction},y(z)),"none"!==M&&(0,e.createElement)(jn,{value:M,disabled:h||1===l.length||""===I,units:l,onChange:D}))))}Hn.defaultUnits=Vn;const $n=(0,e.createElement)(l.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)(l.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})),qn=(0,e.createElement)(l.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)(l.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})),Gn={details:"PVe8nn2o9P_cK6P7iOX_",summary:"pXIMJtVbgyXqQma1V8w6",filtersActive:"Z24CXS5Cd1cw7Na5FNvW",summaryIcon:"dQYeYurfhKQh65UZ0rW4",separator:"yv4NC3BzfcDnblUJohDW"},Zn={panel:"wlrBvQZLTl7jMxGI1gW2",filtersActive:"jknengJetIonG12tKRI0",indicatorDots:"E4chNgG0LgbOKjQvOv9G",dot:"NPh3cJKwg7oEp8DLv03_",local:"oO1XnU74yOZeIHTfBxLA",current:"YvGRsHRWCDT6Iz_0jAUp"};function Kn({cssProps:t}){const r=T(),{inheritedSources:n}=r,o=n.filter((e=>t.includes(e.property))),a=o.some((e=>"global"===e.source)),l=o.some((e=>"local"===e.source)),i=o.some((e=>"current"===e.source));return a||l||i?(0,e.createElement)("div",{className:Zn.indicatorDots},!!i&&(0,e.createElement)("div",{title:(0,s.__)("Has local styles","generateblocks-pro"),className:g(Zn.dot,Zn.current)}),!!l&&(0,e.createElement)("div",{title:(0,s.__)("Has inherited local styles","generateblocks-pro"),className:g(Zn.dot,Zn.local)}),!!a&&(0,e.createElement)("div",{title:(0,s.__)("Has inherited global styles","generateblocks-pro"),className:Zn.dot})):null}function Yn({filtersActive:t,scope:r,panel:n,search:o="",filter:s,atRule:l,id:i,currentSelector:c,...u}){const d=!(!t&&!o),p=c?`gb-styles-builder-open-panels-${r}-${function(e){let t=0;for(let r=0;r<e.length;r++)t=(31*t+e.charCodeAt(r))%1e5;return t.toString(36)}(c)}`:`gb-styles-builder-open-panels-${r}`,h=JSON.parse(sessionStorage.getItem(p))||[],m=h?.includes(i)||!1,f=(0,a.useRef)(null),[b,v]=(0,a.useState)(!0);return(0,a.useEffect)((()=>{if(f.current&&d){const e=f.current.querySelector(".gb-styles-builder-control");v(!!e)}else v(!0)}),[o,s,d]),b?(0,e.createElement)("div",{ref:f,className:g(Zn.panel,(t||o)&&Zn.filtersActive,Zn[`panel-${i}`])},(0,e.createElement)(n,{...u,opened:d,atRule:l,scrollAfterOpen:!1,filtersActive:t,search:o,initialOpen:m,onToggle:()=>{if(d)return;const e=JSON.parse(sessionStorage.getItem(p))||[],t=e.includes(i)?e.filter((e=>e!==i)):[...e,i];!function(e,t,r=25){if(0===t.length)return void sessionStorage.removeItem(e);const n=Object.keys(sessionStorage).filter((e=>e.startsWith("gb-styles-builder-open-panels-")));n.length>=r&&n.slice(0,n.length-r+1).forEach((e=>sessionStorage.removeItem(e))),sessionStorage.setItem(e,JSON.stringify(t))}(p,t)}})):null}function Qn({children:t,title:r,onClick:n,open:l=!0,filtersActive:i=!1,search:c="",cssProps:u=[]}){if(!r)throw new Error("PanelSection requires a title");const p=(0,a.useRef)(null);let h;return(i||l)&&(h=!0),(0,e.createElement)("details",{ref:p,className:g("gb-panel-section",Gn.details,(i||c)&&Gn.filtersActive),open:h,onToggle:()=>{if(n){const e=p.current?.hasAttribute("open");n(e)}}},(0,e.createElement)("summary",{className:Gn.summary},r,(0,e.createElement)(o.VisuallyHidden,null,(0,s.__)("style controls","generateblocks-pro")),!!u.length>0&&(0,e.createElement)(Kn,{cssProps:u}),(0,e.createElement)("div",{className:Gn.summaryIcon},(0,e.createElement)(d,{icon:l?$n:qn,size:"18"}))),!!h&&(0,e.createElement)(b.Stack,{gap:"10px",className:g(Gn.children,"gb-styles-builder-panel__content")},t))}function Xn({value:t,onChange:r}){const n=(0,a.useMemo)((()=>[{label:"Stretch",value:"stretch"},{label:"Center",value:"center"},{label:"Start",value:"start"},{label:"End",value:"end"}]),[t]),o=(0,a.useMemo)((()=>n.every((e=>e.value!==t))),[t]);return(0,e.createElement)(bn,{as:fn,allowCustomValue:!0,hasCustomValue:o,label:"Justify Items",id:"gblocks-justify-items",value:t,onChange:r,buttons:n,cssProp:"justifyItems",learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/justify-items"})}function Jn({value:t,onChange:r}){const n=(0,a.useMemo)((()=>[{label:"Stretch",value:"stretch"},{label:"Center",value:"center"},{label:"Start",value:"start"},{label:"End",value:"end"}]),[t]),o=(0,a.useMemo)((()=>n.every((e=>e.value!==t))),[t]);return(0,e.createElement)(bn,{as:fn,allowCustomValue:!0,hasCustomValue:o,label:"Justify Self",id:"gblocks-justify-self",value:t,onChange:r,buttons:n,cssProp:"justifySelf",learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/justify-self"})}function eo({value:t,onChange:r}){const n=(0,a.useMemo)((()=>[{label:"Stretch",value:"stretch"},{label:"Center",value:"center"},{label:"Start",value:"start"},{label:"End",value:"end"}]),[t]),o=(0,a.useMemo)((()=>n.every((e=>e.value!==t))),[t]);return(0,e.createElement)(bn,{as:fn,allowCustomValue:!0,hasCustomValue:o,label:"Align Self",id:"gblocks-align-self",value:t,onChange:r,buttons:n,cssProp:"alignSelf",learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/align-self"})}const to="gbp-panel-state-layout";function ro(t){const{styles:r,onStyleChange:n,filtersActive:l,search:i,opened:c,scrollAfterOpen:u,onToggle:d,initialOpen:p,allowCustomAtRule:h}=t,{alignItems:m="",columnGap:f="",containerName:g="",containerType:v="",display:y="",flexBasis:w="",flexDirection:k="",flexGrow:E="",flexShrink:S="",flexWrap:x="",gridAutoFlow:C="",gridColumn:O="",gridRow:T="",gridTemplateColumns:I="",gridTemplateRows:R="",justifyContent:M="",order:P="",rowGap:A="",float:L="",clear:N="",justifyItems:z="",justifySelf:D="",alignSelf:F=""}=r,B=sessionStorage.getItem(to),[j,V]=(0,a.useState)((()=>B&&!l?JSON.parse(B):{alignment:!1,grid:!1,flex:!1,containerQueries:!1,floats:!1})),{onSectionToggle:W}=_({sectionState:j,setSectionState:V,filtersActive:l,search:i,storageKey:to});return(0,e.createElement)(o.PanelBody,{title:(0,s.__)("Layout","generateblocks-pro"),initialOpen:p,opened:!!c||void 0,icon:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Dr,{size:"20"}),(0,e.createElement)(Kn,{cssProps:Ze})),scrollAfterOpen:u,onToggle:d},(0,e.createElement)(b.Stack,{gap:"12px",className:"gb-styles-builder-panel__content"},(0,e.createElement)(bn,{as:o.SelectControl,allowCustomValue:!0,label:"Display",value:y,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"Block",value:"block"},{label:"Flex",value:"flex"},{label:"Grid",value:"grid"},{label:"Inline",value:"inline"},{label:"Inline Block",value:"inline-block"},{label:"Inline Flex",value:"inline-flex"},{label:"Inline Grid",value:"inline-grid"},{label:"List Item",value:"list-item"},{label:"None",value:"none"}],onChange:e=>n("display",e),searchKeywords:["block","inline-block","flex","inline-flex","inline-grid","grid","inline","list-item"],cssProp:"display"}),(0,e.createElement)(Qn,{title:"Alignment",onClick:e=>W("alignment",e),open:j.alignment,filtersActive:l,search:i,cssProps:["alignItems","justifyContent","justifyItems","justifySelf","alignSelf"]},(0,e.createElement)(In,{flexDirection:k,value:m,onChange:e=>n("alignItems",e)}),(0,e.createElement)(zn,{flexDirection:k,value:M,onChange:e=>n("justifyContent",e)}),(0,e.createElement)(Xn,{value:z,onChange:e=>n("justifyItems",e)}),(0,e.createElement)(Jn,{value:D,onChange:e=>n("justifySelf",e)}),(0,e.createElement)(eo,{value:F,onChange:e=>n("alignSelf",e)})),(0,e.createElement)(Qn,{title:"Grid Layout",onClick:e=>W("grid",e),open:j.grid,filtersActive:l,search:i,cssProps:["gridTemplateColumns","gridTemplateRows","columnGap","rowGap","gridAutoFlow","gridColumn","gridRow","order"]},(0,e.createElement)(Ln,{value:I,onShortcutChange:e=>{n("gridTemplateColumns",e!==I?e:"")},onChange:e=>{n("gridTemplateColumns",e)}}),(0,e.createElement)(bn,{as:o.TextControl,label:"Grid Template Rows",value:R,onChange:e=>n("gridTemplateRows",e),cssProp:"gridTemplateRows"}),(0,e.createElement)(o.Flex,null,(0,e.createElement)(bn,{as:Hn,label:"Column Gap",id:"gblocks-column-gap",value:f,onChange:e=>n("columnGap",e),cssProp:"columnGap"}),(0,e.createElement)(bn,{as:Hn,label:"Row Gap",id:"gblocks-row-gap",value:A,onChange:e=>n("rowGap",e),cssProp:"rowGap"})),(0,e.createElement)(bn,{as:o.TextControl,label:"Grid Auto Flow",value:C,onChange:e=>n("gridAutoFlow",e),cssProp:"gridAutoFlow"}),(0,e.createElement)(o.Flex,null,(0,e.createElement)(bn,{as:o.TextControl,label:"Grid Column",id:"gblocks-grid-column",value:O,onChange:e=>n("gridColumn",e),cssProp:"gridColumn"}),(0,e.createElement)(bn,{as:o.TextControl,label:"Grid Row",id:"gblocks-grid-row",value:T,onChange:e=>n("gridRow",e),cssProp:"gridRow"})),(0,e.createElement)(bn,{as:Hn,value:P,cssProp:"order",units:[],onChange:e=>n("order",e),label:(0,s.__)("Order","generateblocks-pro")})),(0,e.createElement)(Qn,{title:"Flex Layout",onClick:e=>W("flex",e),open:j.flex,filtersActive:l,search:i,cssProps:["flexDirection","flexWrap","columnGap","rowGap","flexGrow","flexShrink","flexBasis","order"]},(0,e.createElement)(Rn,{value:k,onChange:e=>{n("flexDirection",e!==k?e:"")}}),(0,e.createElement)(Pn,{value:x,onChange:e=>n("flexWrap",e!==x?e:"")}),(0,e.createElement)(o.Flex,null,(0,e.createElement)(bn,{as:Hn,label:"Column Gap",id:"gblocks-column-gap",value:f,onChange:e=>n("columnGap",e),cssProp:"columnGap"}),(0,e.createElement)(bn,{as:Hn,label:"Row Gap",id:"gblocks-row-gap",value:A,onChange:e=>n("rowGap",e),cssProp:"rowGap"})),(0,e.createElement)(o.Flex,null,(0,e.createElement)(bn,{as:o.TextControl,label:"Flex Grow",id:"gblocks-flex-grow",value:E,onChange:e=>n("flexGrow",e),cssProp:"flexGrow"}),(0,e.createElement)(bn,{as:o.TextControl,label:"Flex Shrink",id:"gblocks-flex-shrink",value:S,onChange:e=>n("flexShrink",e),cssProp:"flexShrink"})),(0,e.createElement)(bn,{as:Hn,label:"Flex Basis",id:"gblocks-flex-basis",value:w,onChange:e=>n("flexBasis",e),cssProp:"flexBasis"}),(0,e.createElement)(bn,{as:Hn,value:P,cssProp:"order",units:[],onChange:e=>n("order",e),label:(0,s.__)("Order","generateblocks-pro")})),!!h&&(0,e.createElement)(Qn,{title:"Container Queries",onClick:e=>W("containerQueries",e),open:j.containerQueries,filtersActive:l,search:i,cssProps:["containerType","containerName"]},(0,e.createElement)(bn,{as:o.SelectControl,allowCustomValue:!0,label:"Container Type",value:v,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"Size",value:"size"},{label:"Inline Size",value:"inline-size"},{label:"Normal",value:"normal"}],onChange:e=>n("containerType",e),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_containment/Container_queries",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro"),cssProp:"containerType"}),(0,e.createElement)(bn,{as:o.TextControl,label:"Container Name",id:"gblocks-container-name",value:g,onChange:e=>n("containerName",e),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_containment/Container_queries",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro"),cssProp:"containerName"})),(0,e.createElement)(Qn,{title:"Floats",onClick:e=>W("floats",e),open:j.floats,filtersActive:l,search:i,cssProps:["float","clear"]},(0,e.createElement)(bn,{as:o.SelectControl,allowCustomValue:!0,label:"Float",value:L,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"None",value:"none"},{label:"Left",value:"left"},{label:"Right",value:"right"},{label:"Inline Start",value:"inline-start"},{label:"Inline End",value:"inline-end"}],onChange:e=>n("float",e),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/float",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro"),cssProp:"float"}),(0,e.createElement)(bn,{as:o.SelectControl,allowCustomValue:!0,label:"Clear",value:N,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"None",value:"none"},{label:"Left",value:"left"},{label:"Right",value:"right"},{label:"Both",value:"both"}],onChange:e=>n("clear",e),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/clear",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro"),cssProp:"clear"}))))}const no={panel:"taLUtwOtvKmnT3LLpB38",filtersActive:"KGx5ibYI5pBUErEOl3zg"};function oo(t){const{styles:r,onStyleChange:n,opened:a,scrollAfterOpen:l,filtersActive:i,search:c,onToggle:u,initialOpen:d}=t,{width:p="",height:h="",minWidth:m="",minHeight:f="",maxWidth:v="",maxHeight:y="",aspectRatio:w=""}=r;return(0,e.createElement)(o.PanelBody,{title:(0,s.__)("Sizing","generateblocks-pro"),initialOpen:d,icon:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Fr,{size:"20"}),(0,e.createElement)(Kn,{cssProps:Ke})),opened:!!a||void 0,scrollAfterOpen:l,className:g(no.panel,(c||i)&&no.filtersActive),onToggle:u},(0,e.createElement)(b.Stack,{gap:"12px",wrap:!0,className:"gb-styles-builder-panel__content"},(0,e.createElement)(b.Stack,{gap:"12px",wrap:!0,layout:"flex",direction:"horizontal",className:"gb-styles-builder-panel__content"},(0,e.createElement)(bn,{as:Hn,label:"Width",id:"width",value:p,onChange:e=>n("width",e),cssProp:"width"}),(0,e.createElement)(bn,{as:Hn,label:"Height",id:"height",value:h,onChange:e=>n("height",e),cssProp:"height"}),(0,e.createElement)(bn,{as:Hn,label:"Min Width",id:"min-width",value:m,onChange:e=>n("minWidth",e),cssProp:"minWidth"}),(0,e.createElement)(bn,{as:Hn,label:"Min Height",id:"min-height",value:f,onChange:e=>n("minHeight",e),cssProp:"minHeight"}),(0,e.createElement)(bn,{as:Hn,label:"Max Width",id:"max-width",value:v,onChange:e=>n("maxWidth",e),cssProp:"maxWidth"}),(0,e.createElement)(bn,{as:Hn,label:"Max Height",id:"max-height",value:y,onChange:e=>n("maxHeight",e),cssProp:"maxHeight"})),(0,e.createElement)(bn,{as:o.TextControl,label:"Aspect Ratio",id:"gblocks-aspect-ratio",value:w,onChange:e=>n("aspectRatio",e),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/aspect-ratio",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro"),cssProp:"aspectRatio",__nextHasNoMarginBottom:!0})))}const ao=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M10 17.389H8.444A5.194 5.194 0 1 1 8.444 7H10v1.5H8.444a3.694 3.694 0 0 0 0 7.389H10v1.5ZM14 7h1.556a5.194 5.194 0 0 1 0 10.39H14v-1.5h1.556a3.694 3.694 0 0 0 0-7.39H14V7Zm-4.5 6h5v-1.5h-5V13Z"})),so=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"m7.5 6h9v-1.5h-9zm0 13.5h9v-1.5h-9zm-3-3h1.5v-9h-1.5zm13.5-9v9h1.5v-9z"})),lo={component:"KvFQvAxVA7e4KZQkv45F"};function io({sync:t,setSync:r,syncTypes:n=["all","axis"],onClick:a=()=>{}}){const l=n.includes("axis"),i=n.includes("all");return(i||l)&&(0,e.createElement)("div",{className:lo.component},i&&(0,e.createElement)(o.Button,{icon:ao,isPressed:"all"===t,size:"small",iconSize:18,onClick:()=>{if("all"===t)return r(!1),void a(!1);r("all"),a("all")},label:(0,s.__)("Link all sides","generateblocks-pro"),showTooltip:!0}),l&&(0,e.createElement)(o.Button,{icon:so,isPressed:"axis"===t,size:"small",iconSize:18,onClick:()=>{if("axis"===t)return r(!1),void a(!1);r("axis"),a("axis")},label:(0,s.__)("Link axis","generateblocks-pro"),showTooltip:!0}))}const co={control:"q3XdHoq3cwkMbEh5XJzu",inputs:"l8duJULoQr0EMHZqlRJB",circle:"h5SWpbIIvS08bNBD9bQ1",syncActive:"QHMCQbD8RVXZKj19Ay3f",top:"dM62oj2S1zQvo8yoF56S",left:"ROcuB3cnY1IvDcsvcEFp",right:"ZHkI45LbcqAarwwDxrfV",bottom:"bFf3U6RwbqyyYjszpKpV",label:"ji6lQSdPbcind_gRfpK8",syncItems:"waVsUgSx9MnDoL0HxEZ1"};function uo(e){const t={};let r;return e.forEach((e=>{e&&(t[e]?t[e]+=1:t[e]=1,(!r||t[e]>t[r])&&(r=e))})),r||""}function po(e){return Object.values(e).reduce(((e,{prop:t,value:r})=>(e[t]=r,e)),{})}function ho(e,t=[]){var r,n;return null!==(n=(null!==(r=e.find((e=>t.includes(e[0].toLowerCase().trim()))))&&void 0!==r?r:["",""])[1].value)&&void 0!==n?n:""}function mo(t){const{onChange:r,id:n,units:o,label:l="",cssProps:i={},layout:c="circle",syncTypes:u=["all","axis"]}=t,[d,p]=(0,a.useState)(!1),[h,m]=(0,a.useState)(""),f=u.includes("axis"),b=u.includes("all"),v=Object.entries(i),y=(0,a.useMemo)((()=>b&&"all"===d?uo(v.map((e=>e[1].value))):""),[v,d]),w=(0,a.useMemo)((()=>f&&"axis"===d?ho(v,["top","bottom"]):""),[v,f,d]),k=(0,a.useMemo)((()=>f&&"axis"===d?ho(v,["left","right"]):""),[v,f,d]);(0,a.useEffect)((()=>{v.some((e=>!e[1].value))||d||v.every((e=>e[1].value===v[0][1].value))&&!d&&p("all")}),[JSON.stringify(v)]),(0,a.useEffect)((()=>{document.getElementById(h)?.focus()}),[d]);const E=po(i),S=Object.keys(E),x=[i?.Top?.prop,i?.Bottom?.prop].filter(Boolean),C=[i?.Left?.prop,i?.Right?.prop].filter(Boolean);return(0,e.createElement)(bn,{id:n,value:Object.values(E).filter(Boolean).join(","),searchKeywords:Object.keys(i),cssProp:S,className:g(co.control),label:l,beforeDropdownMenu:(0,e.createElement)(io,{sync:d,setSync:p,syncTypes:u,onClick:e=>{if("axis"===e){const e=ho(v,["top","bottom"]),t=ho(v,["left","right"]),n={...i};for(const r in n)["top","bottom"].includes(r.toLowerCase().trim())&&(n[r].value=e),["left","right"].includes(r.toLowerCase().trim())&&(n[r].value=t);const o=po(n);r(o)}if("all"===e){const e=uo(v.map((e=>e[1].value)));if(!e)return;const t={...i};for(const r in t)t[r].value=e;const n=po(t);r(n)}}})},(0,e.createElement)("div",{className:g(co.inputs,co[c],d&&co.syncActive)},"all"===d&&(0,e.createElement)(bn,{as:Hn,id:`${n}-all-control`,value:y,units:o,onChange:e=>{const t={...i};for(const r in t)t[r].value=e;const n=po(t);r(n)},onFocus:()=>m(`${n}-all-control`),label:(0,s.__)("All sides","generateblocks-pro"),cssProp:S,searchKeywords:[l]}),"axis"===d&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(bn,{as:Hn,label:(0,s.__)("Vertical","generateblocks-pro"),id:`${n}-vertical-control`,value:w,units:o,onChange:e=>{const t={...i};for(const r in t)["top","bottom"].includes(r.toLowerCase().trim())&&(t[r].value=e);const n=po(t);r(n)},onFocus:()=>m(`${n}-vertical-control`),cssProp:x,searchKeywords:[l]}),(0,e.createElement)(bn,{as:Hn,label:(0,s.__)("Horizontal","generateblocks-pro"),id:`${n}-horizontal-control`,value:k,units:o,onChange:e=>{const t={...i};for(const r in t)["left","right"].includes(r.toLowerCase().trim())&&(t[r].value=e);const n=po(t);r(n)},onFocus:()=>m(`${n}-horizontal-control`),cssProp:C,searchKeywords:[l]})),!1===d&&v.map((t=>{const n=t[1].value,a=t[1].prop,s=`${a}-control`,i=t[0].toLowerCase().trim();return(0,e.createElement)(bn,{key:s,className:g(co[i]),as:Hn,id:s,value:n,units:o,onChange:e=>{r({[a]:e})},onFocus:()=>m(s),label:t[0],cssProp:a,searchKeywords:[l]})}))))}function fo(t){const{styles:r,onStyleChange:n,opened:l,scrollAfterOpen:i,onToggle:c,initialOpen:u}=t,{marginTop:d="",marginRight:p="",marginBottom:h="",marginLeft:m="",paddingTop:f="",paddingRight:g="",paddingBottom:v="",paddingLeft:y=""}=r,w=(0,a.useMemo)((()=>({Top:{prop:"paddingTop",value:f},Right:{prop:"paddingRight",value:g},Bottom:{prop:"paddingBottom",value:v},Left:{prop:"paddingLeft",value:y}})),[f,g,v,y]),k=(0,a.useMemo)((()=>({Top:{prop:"marginTop",value:d},Right:{prop:"marginRight",value:p},Bottom:{prop:"marginBottom",value:h},Left:{prop:"marginLeft",value:m}})),[d,p,h,m]);return(0,e.createElement)(o.PanelBody,{title:(0,s.__)("Spacing","generateblocks-pro"),initialOpen:u,icon:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Br,{size:"20"}),(0,e.createElement)(Kn,{cssProps:Ye})),opened:!!l||void 0,scrollAfterOpen:i,onToggle:c},(0,e.createElement)(b.Stack,{gap:"12px",className:"gb-styles-builder-panel__content"},(0,e.createElement)(mo,{id:"padding",label:"Padding",cssProps:w,onChange:n}),(0,e.createElement)(mo,{id:"margin",label:"Margin",cssProps:k,onChange:n})))}var go={grad:.9,turn:360,rad:360/(2*Math.PI)},bo=function(e){return"string"==typeof e?e.length>0:"number"==typeof e},vo=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=Math.pow(10,t)),Math.round(r*e)/r+0},yo=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=1),e>r?r:e>t?e:t},wo=function(e){return(e=isFinite(e)?e%360:0)>0?e:e+360},ko=function(e){return{r:yo(e.r,0,255),g:yo(e.g,0,255),b:yo(e.b,0,255),a:yo(e.a)}},Eo=function(e){return{r:vo(e.r),g:vo(e.g),b:vo(e.b),a:vo(e.a,3)}},So=/^#([0-9a-f]{3,8})$/i,xo=function(e){var t=e.toString(16);return t.length<2?"0"+t:t},Co=function(e){var t=e.r,r=e.g,n=e.b,o=e.a,a=Math.max(t,r,n),s=a-Math.min(t,r,n),l=s?a===t?(r-n)/s:a===r?2+(n-t)/s:4+(t-r)/s:0;return{h:60*(l<0?l+6:l),s:a?s/a*100:0,v:a/255*100,a:o}},_o=function(e){var t=e.h,r=e.s,n=e.v,o=e.a;t=t/360*6,r/=100,n/=100;var a=Math.floor(t),s=n*(1-r),l=n*(1-(t-a)*r),i=n*(1-(1-t+a)*r),c=a%6;return{r:255*[n,l,s,s,i,n][c],g:255*[i,n,n,l,s,s][c],b:255*[s,s,i,n,n,l][c],a:o}},Oo=function(e){return{h:wo(e.h),s:yo(e.s,0,100),l:yo(e.l,0,100),a:yo(e.a)}},To=function(e){return{h:vo(e.h),s:vo(e.s),l:vo(e.l),a:vo(e.a,3)}},Io=function(e){return _o((r=(t=e).s,{h:t.h,s:(r*=((n=t.l)<50?n:100-n)/100)>0?2*r/(n+r)*100:0,v:n+r,a:t.a}));var t,r,n},Ro=function(e){return{h:(t=Co(e)).h,s:(o=(200-(r=t.s))*(n=t.v)/100)>0&&o<200?r*n/100/(o<=100?o:200-o)*100:0,l:o/2,a:t.a};var t,r,n,o},Mo=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Po=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Ao=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Lo=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,No={string:[[function(e){var t=So.exec(e);return t?(e=t[1]).length<=4?{r:parseInt(e[0]+e[0],16),g:parseInt(e[1]+e[1],16),b:parseInt(e[2]+e[2],16),a:4===e.length?vo(parseInt(e[3]+e[3],16)/255,2):1}:6===e.length||8===e.length?{r:parseInt(e.substr(0,2),16),g:parseInt(e.substr(2,2),16),b:parseInt(e.substr(4,2),16),a:8===e.length?vo(parseInt(e.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(e){var t=Ao.exec(e)||Lo.exec(e);return t?t[2]!==t[4]||t[4]!==t[6]?null:ko({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:void 0===t[7]?1:Number(t[7])/(t[8]?100:1)}):null},"rgb"],[function(e){var t=Mo.exec(e)||Po.exec(e);if(!t)return null;var r,n,o=Oo({h:(r=t[1],n=t[2],void 0===n&&(n="deg"),Number(r)*(go[n]||1)),s:Number(t[3]),l:Number(t[4]),a:void 0===t[5]?1:Number(t[5])/(t[6]?100:1)});return Io(o)},"hsl"]],object:[[function(e){var t=e.r,r=e.g,n=e.b,o=e.a,a=void 0===o?1:o;return bo(t)&&bo(r)&&bo(n)?ko({r:Number(t),g:Number(r),b:Number(n),a:Number(a)}):null},"rgb"],[function(e){var t=e.h,r=e.s,n=e.l,o=e.a,a=void 0===o?1:o;if(!bo(t)||!bo(r)||!bo(n))return null;var s=Oo({h:Number(t),s:Number(r),l:Number(n),a:Number(a)});return Io(s)},"hsl"],[function(e){var t=e.h,r=e.s,n=e.v,o=e.a,a=void 0===o?1:o;if(!bo(t)||!bo(r)||!bo(n))return null;var s=function(e){return{h:wo(e.h),s:yo(e.s,0,100),v:yo(e.v,0,100),a:yo(e.a)}}({h:Number(t),s:Number(r),v:Number(n),a:Number(a)});return _o(s)},"hsv"]]},zo=function(e,t){for(var r=0;r<t.length;r++){var n=t[r][0](e);if(n)return[n,t[r][1]]}return[null,void 0]},Do=function(e,t){var r=Ro(e);return{h:r.h,s:yo(r.s+100*t,0,100),l:r.l,a:r.a}},Fo=function(e){return(299*e.r+587*e.g+114*e.b)/1e3/255},Bo=function(e,t){var r=Ro(e);return{h:r.h,s:r.s,l:yo(r.l+100*t,0,100),a:r.a}},jo=function(){function e(e){this.parsed=function(e){return"string"==typeof e?zo(e.trim(),No.string):"object"==typeof e&&null!==e?zo(e,No.object):[null,void 0]}(e)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return e.prototype.isValid=function(){return null!==this.parsed},e.prototype.brightness=function(){return vo(Fo(this.rgba),2)},e.prototype.isDark=function(){return Fo(this.rgba)<.5},e.prototype.isLight=function(){return Fo(this.rgba)>=.5},e.prototype.toHex=function(){return t=(e=Eo(this.rgba)).r,r=e.g,n=e.b,a=(o=e.a)<1?xo(vo(255*o)):"","#"+xo(t)+xo(r)+xo(n)+a;var e,t,r,n,o,a},e.prototype.toRgb=function(){return Eo(this.rgba)},e.prototype.toRgbString=function(){return t=(e=Eo(this.rgba)).r,r=e.g,n=e.b,(o=e.a)<1?"rgba("+t+", "+r+", "+n+", "+o+")":"rgb("+t+", "+r+", "+n+")";var e,t,r,n,o},e.prototype.toHsl=function(){return To(Ro(this.rgba))},e.prototype.toHslString=function(){return t=(e=To(Ro(this.rgba))).h,r=e.s,n=e.l,(o=e.a)<1?"hsla("+t+", "+r+"%, "+n+"%, "+o+")":"hsl("+t+", "+r+"%, "+n+"%)";var e,t,r,n,o},e.prototype.toHsv=function(){return e=Co(this.rgba),{h:vo(e.h),s:vo(e.s),v:vo(e.v),a:vo(e.a,3)};var e},e.prototype.invert=function(){return Vo({r:255-(e=this.rgba).r,g:255-e.g,b:255-e.b,a:e.a});var e},e.prototype.saturate=function(e){return void 0===e&&(e=.1),Vo(Do(this.rgba,e))},e.prototype.desaturate=function(e){return void 0===e&&(e=.1),Vo(Do(this.rgba,-e))},e.prototype.grayscale=function(){return Vo(Do(this.rgba,-1))},e.prototype.lighten=function(e){return void 0===e&&(e=.1),Vo(Bo(this.rgba,e))},e.prototype.darken=function(e){return void 0===e&&(e=.1),Vo(Bo(this.rgba,-e))},e.prototype.rotate=function(e){return void 0===e&&(e=15),this.hue(this.hue()+e)},e.prototype.alpha=function(e){return"number"==typeof e?Vo({r:(t=this.rgba).r,g:t.g,b:t.b,a:e}):vo(this.rgba.a,3);var t},e.prototype.hue=function(e){var t=Ro(this.rgba);return"number"==typeof e?Vo({h:e,s:t.s,l:t.l,a:t.a}):vo(t.h)},e.prototype.isEqual=function(e){return this.toHex()===Vo(e).toHex()},e}(),Vo=function(e){return e instanceof jo?e:new jo(e)};const Wo=[];function Uo(){return(Uo=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function Ho(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)t.indexOf(r=a[n])>=0||(o[r]=e[r]);return o}function $o(t){var r=(0,e.useRef)(t),n=(0,e.useRef)((function(e){r.current&&r.current(e)}));return r.current=t,n.current}var qo=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=1),e>r?r:e<t?t:e},Go=function(e){return"touches"in e},Zo=function(e){return e&&e.ownerDocument.defaultView||self},Ko=function(e,t,r){var n=e.getBoundingClientRect(),o=Go(t)?function(e,t){for(var r=0;r<e.length;r++)if(e[r].identifier===t)return e[r];return e[0]}(t.touches,r):t;return{left:qo((o.pageX-(n.left+Zo(e).pageXOffset))/n.width),top:qo((o.pageY-(n.top+Zo(e).pageYOffset))/n.height)}},Yo=function(e){!Go(e)&&e.preventDefault()},Qo=e.memo((function(t){var r=t.onMove,n=t.onKey,o=Ho(t,["onMove","onKey"]),a=(0,e.useRef)(null),s=$o(r),l=$o(n),i=(0,e.useRef)(null),c=(0,e.useRef)(!1),u=(0,e.useMemo)((function(){var e=function(e){Yo(e),(Go(e)?e.touches.length>0:e.buttons>0)&&a.current?s(Ko(a.current,e,i.current)):r(!1)},t=function(){return r(!1)};function r(r){var n=c.current,o=Zo(a.current),s=r?o.addEventListener:o.removeEventListener;s(n?"touchmove":"mousemove",e),s(n?"touchend":"mouseup",t)}return[function(e){var t=e.nativeEvent,n=a.current;if(n&&(Yo(t),!function(e,t){return t&&!Go(e)}(t,c.current)&&n)){if(Go(t)){c.current=!0;var o=t.changedTouches||[];o.length&&(i.current=o[0].identifier)}n.focus(),s(Ko(n,t,i.current)),r(!0)}},function(e){var t=e.which||e.keyCode;t<37||t>40||(e.preventDefault(),l({left:39===t?.05:37===t?-.05:0,top:40===t?.05:38===t?-.05:0}))},r]}),[l,s]),d=u[0],p=u[1],h=u[2];return(0,e.useEffect)((function(){return h}),[h]),e.createElement("div",Uo({},o,{onTouchStart:d,onMouseDown:d,className:"react-colorful__interactive",ref:a,onKeyDown:p,tabIndex:0,role:"slider"}))})),Xo=function(e){return e.filter(Boolean).join(" ")},Jo=function(t){var r=t.color,n=t.left,o=t.top,a=void 0===o?.5:o,s=Xo(["react-colorful__pointer",t.className]);return e.createElement("div",{className:s,style:{top:100*a+"%",left:100*n+"%"}},e.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:r}}))},ea=function(e,t,r){return void 0===t&&(t=0),void 0===r&&(r=Math.pow(10,t)),Math.round(r*e)/r},ta=(Math.PI,function(e){var t=e.s,r=e.v,n=e.a,o=(200-t)*r/100;return{h:ea(e.h),s:ea(o>0&&o<200?t*r/100/(o<=100?o:200-o)*100:0),l:ea(o/2),a:ea(n,2)}}),ra=function(e){var t=ta(e);return"hsl("+t.h+", "+t.s+"%, "+t.l+"%)"},na=function(e){var t=ta(e);return"hsla("+t.h+", "+t.s+"%, "+t.l+"%, "+t.a+")"},oa=function(e){var t=e.r,r=e.g,n=e.b,o=e.a,a=Math.max(t,r,n),s=a-Math.min(t,r,n),l=s?a===t?(r-n)/s:a===r?2+(n-t)/s:4+(t-r)/s:0;return{h:ea(60*(l<0?l+6:l)),s:ea(a?s/a*100:0),v:ea(a/255*100),a:o}},aa=e.memo((function(t){var r=t.hue,n=t.onChange,o=Xo(["react-colorful__hue",t.className]);return e.createElement("div",{className:o},e.createElement(Qo,{onMove:function(e){n({h:360*e.left})},onKey:function(e){n({h:qo(r+360*e.left,0,360)})},"aria-label":"Hue","aria-valuenow":ea(r),"aria-valuemax":"360","aria-valuemin":"0"},e.createElement(Jo,{className:"react-colorful__hue-pointer",left:r/360,color:ra({h:r,s:100,v:100,a:1})})))})),sa=e.memo((function(t){var r=t.hsva,n=t.onChange,o={backgroundColor:ra({h:r.h,s:100,v:100,a:1})};return e.createElement("div",{className:"react-colorful__saturation",style:o},e.createElement(Qo,{onMove:function(e){n({s:100*e.left,v:100-100*e.top})},onKey:function(e){n({s:qo(r.s+100*e.left,0,100),v:qo(r.v-100*e.top,0,100)})},"aria-label":"Color","aria-valuetext":"Saturation "+ea(r.s)+"%, Brightness "+ea(r.v)+"%"},e.createElement(Jo,{className:"react-colorful__saturation-pointer",top:1-r.v/100,left:r.s/100,color:ra(r)})))}));var la="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,ia=new Map,ca=function(t){var r=t.className,n=t.hsva,o=t.onChange,a={backgroundImage:"linear-gradient(90deg, "+na(Object.assign({},n,{a:0}))+", "+na(Object.assign({},n,{a:1}))+")"},s=Xo(["react-colorful__alpha",r]),l=ea(100*n.a);return e.createElement("div",{className:s},e.createElement("div",{className:"react-colorful__alpha-gradient",style:a}),e.createElement(Qo,{onMove:function(e){o({a:e.left})},onKey:function(e){o({a:qo(n.a+e.left)})},"aria-label":"Alpha","aria-valuetext":l+"%","aria-valuenow":l,"aria-valuemin":"0","aria-valuemax":"100"},e.createElement(Jo,{className:"react-colorful__alpha-pointer",left:n.a,color:na(n)})))},ua=function(t){var n,o=t.className,a=t.colorModel,s=t.color,l=void 0===s?a.defaultColor:s,i=t.onChange,c=Ho(t,["className","colorModel","color","onChange"]),u=(0,e.useRef)(null);n=u,la((function(){var e=n.current?n.current.ownerDocument:document;if(void 0!==e&&!ia.has(e)){var t=e.createElement("style");t.innerHTML='.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>\')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}',ia.set(e,t);var o=r.nc;o&&t.setAttribute("nonce",o),e.head.appendChild(t)}}),[]);var d=function(t,r,n){var o=$o(n),a=(0,e.useState)((function(){return t.toHsva(r)})),s=a[0],l=a[1],i=(0,e.useRef)({color:r,hsva:s});(0,e.useEffect)((function(){if(!t.equal(r,i.current.color)){var e=t.toHsva(r);i.current={hsva:e,color:r},l(e)}}),[r,t]),(0,e.useEffect)((function(){var e;(function(e,t){if(e===t)return!0;for(var r in e)if(e[r]!==t[r])return!1;return!0})(s,i.current.hsva)||t.equal(e=t.fromHsva(s),i.current.color)||(i.current={hsva:s,color:e},o(e))}),[s,t,o]);var c=(0,e.useCallback)((function(e){l((function(t){return Object.assign({},t,e)}))}),[]);return[s,c]}(a,l,i),p=d[0],h=d[1],m=Xo(["react-colorful",o]);return e.createElement("div",Uo({},c,{ref:u,className:m}),e.createElement(sa,{hsva:p,onChange:h}),e.createElement(aa,{hue:p.h,onChange:h}),e.createElement(ca,{hsva:p,onChange:h,className:"react-colorful__last-control"}))},da={defaultColor:"rgba(0, 0, 0, 1)",toHsva:function(e){var t=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return t?oa({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:void 0===t[7]?1:Number(t[7])/(t[8]?100:1)}):{h:0,s:0,v:0,a:1}},fromHsva:function(e){var t=function(e){var t=e.h,r=e.s,n=e.v,o=e.a;t=t/360*6,r/=100,n/=100;var a=Math.floor(t),s=n*(1-r),l=n*(1-(t-a)*r),i=n*(1-(1-t+a)*r),c=a%6;return{r:ea(255*[n,l,s,s,i,n][c]),g:ea(255*[i,n,n,l,s,s][c]),b:ea(255*[s,s,i,n,n,l][c]),a:ea(o,2)}}(e);return"rgba("+t.r+", "+t.g+", "+t.b+", "+t.a+")"},equal:function(e,t){return e.replace(/\s/g,"")===t.replace(/\s/g,"")}},pa=function(t){return e.createElement(ua,Uo({},t,{colorModel:da}))};const ha=({color:t,onChange:r,rgbColor:n})=>{const[o,s]=(0,a.useState)(t);return((t,r=0,n=Wo)=>{const o=(0,e.useRef)(!0),a=((t,r=0)=>{const n=(0,e.useRef)(0),o=(0,e.useCallback)(((...e)=>{cancelAnimationFrame(n.current);const o=performance.now(),a=s=>{s-o<r?n.current=requestAnimationFrame(a):t(...e)};n.current=requestAnimationFrame(a)}),[t,r]);return(0,e.useEffect)((()=>()=>cancelAnimationFrame(n.current)),[]),o})(t,r);(0,e.useEffect)((()=>{o.current?o.current=!1:a()}),n)})((0,a.useCallback)((()=>{r(o)}),[r,o]),200,[o]),(0,e.createElement)(pa,{color:n,onChange:s})},ma={control:"DcQRWNkPqGQzBQkVJVeb",toggleButton:"HrW1D0SiV8V3gbb8mNQe",toggleIndicator:"PsHhQvH8L6kBjsHPfX0G",content:"YjWehTjwXw5wYbKQhM5Z",inputWrapper:"UoCzeUkbdHYgh6iFJqGR",input:"ty8BtGIe90stbjowsfTG",clear:"pdN7PyEOwwDomSuZp8eE",palette:"XBe3eCUlWUSlqMBd2ljI",opacity:"m3vqikQtI7E7cLjA7zww"},fa="gb-color-picker-palettes";function ga(e){return e?.color?.startsWith("var(")?{...e,color:e.color}:{...e,color:`var(--wp--preset--color--${e.slug}, ${e.color})`}}function ba(t){const{value:r,onChange:n,label:l,tooltip:i,"aria-label":c,colors:u,renderToggle:d,onClick:p}=t,h=(0,a.useRef)(null),m=(0,a.useMemo)((()=>function(e){let t=e;if(String(t).startsWith("var(")){const e=t.match(/\(([^)]+)\)/);if(e){const r=e[1].split(",").map((e=>e.trim())),n=r[0],o=r[1]||"#000000";t=getComputedStyle(document.documentElement).getPropertyValue(n)||o}}return Vo(t).toRgbString()}(r)),[r]),f=function(){const[e=[],t=[]]=(0,z.useSettings)("color.palette.custom","color.palette.theme");return(0,a.useMemo)((()=>[...e.map(ga),...t.map(ga)]),[e,t])}(),b=(0,N.applyFilters)("generateblocks.components.colorPalettes",u||f,t,f);(0,a.useEffect)((()=>{sessionStorage.setItem(fa,JSON.stringify(b))}),[b]);const v=on();return(0,e.createElement)(o.BaseControl,{id:v,label:l,className:g("gb-color-picker",ma.control),"data-gb-control":"ColorPickerControl"},(0,e.createElement)(o.Dropdown,{className:ma.toggle,contentClassName:ma.content,placement:"top left",renderToggle:({isOpen:t,onToggle:n})=>{if(d)return d({isOpen:t,onToggle:n});const a=(0,e.createElement)(o.Button,{className:ma.toggleButton,onClick:()=>{n(),p&&p()},"aria-expanded":t,"aria-label":c},(0,e.createElement)("span",{className:ma.toggleIndicator,style:{background:null!=r?r:null}}));return(0,e.createElement)(e.Fragment,null,i?(0,e.createElement)(o.Tooltip,{text:i},a):a)},renderContent:()=>{const t=sessionStorage.getItem(fa),a=sessionStorage?JSON.parse(t):b;return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(ha,{color:r,rgbColor:m,onChange:e=>{Vo(e).isValid()&&(e=1===Vo(e).alpha()?Vo(e).toHex():e),n(e)}}),(0,e.createElement)("div",{className:ma.inputWrapper},(0,e.createElement)(o.TextControl,{ref:h,className:ma.input,type:"text",value:r,onChange:e=>{!e.startsWith("#")&&/^([0-9A-F]{3}){1,2}$/i.test(e)&&(e="#"+e),n(e)},onBlur:()=>{Vo(r).isValid()&&1===Vo(r).alpha()&&n(Vo(r).toHex())}}),(0,e.createElement)(o.Button,{size:"small",variant:"secondary",className:ma.clear,onClick:()=>{n("")}},(0,s.__)("Clear","generateblocks"))),(0,e.createElement)(o.BaseControl,{className:ma.palette},(0,e.createElement)(z.ColorPalette,{colors:a,value:r||"",onChange:e=>{n(e)},disableCustomColors:!0,clearable:!1})))}}))}const va=[{icon:(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M13 5.5H4V4h9v1.5Zm7 7H4V11h16v1.5Zm-7 7H4V18h9v1.5Z"})),title:"Align text left",value:"left"},{icon:(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M7.5 5.5h9V4h-9v1.5Zm-3.5 7h16V11H4v1.5Zm3.5 7h9V18h-9v1.5Z"})),title:"Align text center",value:"center"},{icon:(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M11.111 5.5H20V4h-8.889v1.5ZM4 12.5h16V11H4v1.5Zm7.111 7H20V18h-8.889v1.5Z"})),title:"Align text right",value:"right"},{icon:(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M4 12.8h16v-1.5H4v1.5zm0 7h12.4v-1.5H4v1.5zM4 4.3v1.5h16V4.3H4z"})),title:"Justify text",value:"justify"}];function ya({value:t,onChange:r,cssProp:n}){const o=(0,a.useMemo)((()=>va.every((e=>e.value!==t))),[t]);return(0,e.createElement)(bn,{allowCustomValue:!0,hasCustomValue:o,as:fn,label:"Text Alignment",id:"text-align",buttons:va,value:t,onChange:r,cssProp:n})}const wa=window.wp.apiFetch;var ka=r.n(wa);const Ea={preview:"S9I7lNPZbl2LyF1NIjze",fallback:"V7cVm1RogUEPkIhgEkmo"},Sa=(0,a.memo)((function({font:t,loading:r="lazy",fallbackText:n="",showPreviewSvg:a=!1}){const{name:l,alias:i,preview:c,fontFamily:u,cssVariable:d=""}=t;return(0,e.createElement)("div",{className:Ea.preview},a&&c?(0,e.createElement)(e.Fragment,null,(0,e.createElement)("img",{className:Ea.image,src:c,alt:(0,s.__)("A graphical rendering of the font family","gp-premium"),height:"23",loading:r}),(0,e.createElement)(o.VisuallyHidden,null,n||l)):(0,e.createElement)("span",{className:Ea.fallback,style:{fontFamily:d?`var(${d})`:u}},l," ",i&&`(${i})`))})),xa="undefined"==typeof AbortController?void 0:new AbortController,Ca="generatepress-font-library";let _a=null;const Oa=(0,a.memo)(Kr),Ta=(0,a.memo)((function({item:t}){return t?(0,e.createElement)(Sa,{font:t}):null}));function Ia(){const{typographyFontFamilyList:e}=generateBlocksEditor,t=[{id:"default",name:(0,s.__)("Default","generateblocks-pro"),fontFamily:""},{id:"inherit",name:"Inherit",fontFamily:"inherit"},{id:"system",name:"System Font",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'}];return e?(e.forEach((e=>{var r;(null!==(r=e?.options)&&void 0!==r?r:[]).forEach((({label:e,value:r},n)=>{if(t.some((e=>e.fontFamily===r)))return;const o=n+1;t.push({id:`system-${o}`,name:e,fontFamily:r})}))})),t):[]}function Ra({onStyleChange:t,fontFamily:r="",cssProp:n=""}){var o;const[l,i]=(0,a.useState)((()=>_a||Ia())),[c,u]=(0,a.useState)(r),[d,p]=(0,a.useState)(!1),[h,m]=(0,a.useState)(null),f=null!==(o=window?.generateBlocksEditor?.hasGPFontLibrary)&&void 0!==o&&o,g=(0,a.useMemo)((()=>"custom"===h?.id?[h,...l]:l),[h,l.length]),b=(0,a.useMemo)((()=>{const e=r.match(/var\((--[^)]+)\)/);return e?e[1]:null}),[r]);return(0,a.useEffect)((()=>{f&&!_a&&async function(){try{const e=await ka()({path:"/generatepress-font-library/v1/get-fonts",method:"GET"});if(!e.response)throw new Error("Request failed");const t=[...Ia(),...e.response];_a=t,i(t)}catch(e){"AbortError"===e.name&&console.error("Request has been aborted"),xa?.abort()}}()}),[]),(0,a.useEffect)((()=>{c!==r&&u(r)}),[c,r]),(0,a.useEffect)((()=>{let e=l.find((e=>b===e?.cssVariable||r===e?.fontFamily));if(e)return p(!1),void m(e);if(e={id:"custom",name:r,fontFamily:r},b){const t=b?getComputedStyle(document.documentElement).getPropertyValue(b):null;if(t)return void(e.name=t);p(!0)}e.name=e.name.split(",")[0].trim(),m(e)}),[r,l,b]),(0,e.createElement)(bn,{className:"gb-styles-builder__font-family",as:Oa,label:"Font Family",id:"gblocks-font-family",value:c,onChange:e=>{let r="string"==typeof e?e:e?.fontFamily;e?.cssVariable&&(r=`var(${e.cssVariable})`),t("fontFamily",r)},items:g,itemToString:e=>e?.fontFamily||e?.cssVariable?e.cssVariable||e.fontFamily:"",ItemComponent:Ta,allowCustomValue:!0,hasCustomValue:d,selectedItem:h,setSelectedItem:m,dropdownChildren:({onClose:t})=>(0,e.createElement)(bn.Description,{label:(0,s.__)("About Font Family","generateblocks-pro"),onClick:t},(0,s.__)("Select a font from the list or enter your own custom value.","generateblocks-pro")),learnMoreLabel:!!f&&(0,s.__)("Manage Fonts","generateblocks-pro"),learnMoreUrl:!!f&&`/wp-admin/themes.php?page=${Ca}`,cssProp:n,selectedStyle:"icon"})}const Ma=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M6.1 6.8L2.1 18h1.6l1.1-3h4.3l1.1 3h1.6l-4-11.2H6.1zm-.8 6.8L7 8.9l1.7 4.7H5.3zm15.1-.7c-.4-.5-.9-.8-1.6-1 .4-.2.7-.5.8-.9.2-.4.3-.9.3-1.4 0-.9-.3-1.6-.8-2-.6-.5-1.3-.7-2.4-.7h-3.5V18h4.2c1.1 0 2-.3 2.6-.8.6-.6 1-1.4 1-2.4-.1-.8-.3-1.4-.6-1.9zm-5.7-4.7h1.8c.6 0 1.1.1 *******.******* 1.3 0 .6-.2 1.1-.5 1.3-.3.2-.8.4-1.4.4h-1.8V8.2zm4 8c-.4.3-.9.5-1.5.5h-2.6v-3.8h2.6c1.4 0 2 .6 2 *******-.1 1-.5 1.4z"})),Pa=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M11 16.8c-.1-.1-.2-.3-.3-.5v-2.6c0-.9-.1-1.7-.3-2.2-.2-.5-.5-.9-.9-1.2-.4-.2-.9-.3-1.6-.3-.5 0-1 .1-1.5.2s-.9.3-1.2.6l.2 1.2c.4-.3.7-.4 1.1-.5.3-.1.7-.2 1-.2.6 0 1 .1 1.3.4.3.2.4.7.4 1.4-1.2 0-2.3.2-3.3.7s-1.4 1.1-1.4 2.1c0 .7.2 1.2.7 1.6.4.4 1 .6 1.8.6.9 0 1.7-.4 2.4-1.2.1.3.2.5.4.7.1.2.3.3.6.4.3.1.6.1 1.1.1h.1l.2-1.2h-.1c-.4.1-.6 0-.7-.1zM9.2 16c-.2.3-.5.6-.9.8-.3.1-.7.2-1.1.2-.4 0-.7-.1-.9-.3-.2-.2-.3-.5-.3-.9 0-.6.2-1 .7-1.3.5-.3 1.3-.4 2.5-.5v2zm10.6-3.9c-.3-.6-.7-1.1-1.2-1.5-.6-.4-1.2-.6-1.9-.6-.5 0-.9.1-1.4.3-.4.2-.8.5-1.1.8V6h-1.4v12h1.3l.2-1c.2.4.6.6 1 .8.4.2.9.3 1.4.3.7 0 1.2-.2 1.8-.5.5-.4 1-.9 1.3-1.5.3-.6.5-1.3.5-2.1-.1-.6-.2-1.3-.5-1.9zm-1.7 4c-.4.5-.9.8-1.6.8s-1.2-.2-1.7-.7c-.4-.5-.7-1.2-.7-2.1 0-.9.2-1.6.7-2.1.4-.5 1-.7 1.7-.7s1.2.3 1.6.8c.4.5.6 1.2.6 2s-.2 1.4-.6 2z"})),Aa=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M7.1 6.8L3.1 18h1.6l1.1-3h4.3l1.1 3h1.6l-4-11.2H7.1zm-.8 6.8L8 8.9l1.7 4.7H6.3zm14.5-1.5c-.3-.6-.7-1.1-1.2-1.5-.6-.4-1.2-.6-1.9-.6-.5 0-.9.1-1.4.3-.4.2-.8.5-1.1.8V6h-1.4v12h1.3l.2-1c.2.4.6.6 1 .8.4.2.9.3 1.4.3.7 0 1.2-.2 1.8-.5.5-.4 1-.9 1.3-1.5.3-.6.5-1.3.5-2.1-.1-.6-.2-1.3-.5-1.9zm-1.7 4c-.4.5-.9.8-1.6.8s-1.2-.2-1.7-.7c-.4-.5-.7-1.2-.7-2.1 0-.9.2-1.6.7-2.1.4-.5 1-.7 1.7-.7s1.2.3 1.6.8c.4.5.6 1.2.6 2 .1.8-.2 1.4-.6 2z"})),La=[{label:"None",value:"none",icon:(0,e.createElement)(zr,null)},{label:"Uppercase",value:"uppercase",icon:Ma},{label:"Lowercase",value:"lowercase",icon:Pa},{label:"Capitalize",value:"capitalize",icon:Aa}];function Na({value:t,onChange:r,cssProp:n}){const o=(0,a.useMemo)((()=>La.every((e=>e.value!==t))),[t]);return(0,e.createElement)(bn,{as:fn,allowCustomValue:!0,hasCustomValue:o,label:"Text Transform",id:"gblocks-text-transform",value:t,buttons:La,onChange:r,cssProp:n})}const za=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M7 18v1h10v-1H7zm5-2c1.5 0 2.6-.4 3.4-1.2.8-.8 1.1-2 1.1-3.5V5H15v5.8c0 1.2-.2 2.1-.6 2.8-.4.7-1.2 1-2.4 1s-2-.3-2.4-1c-.4-.7-.6-1.6-.6-2.8V5H7.5v6.2c0 1.5.4 2.7 1.1 3.5.8.9 1.9 1.3 3.4 1.3z"})),Da=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M9.1 9v-.5c0-.6.2-1.1.7-1.4.5-.3 1.2-.5 2-.5.7 0 1.4.1 2.1.3.7.2 1.4.5 2.1.9l.2-1.9c-.6-.3-1.2-.5-1.9-.7-.8-.1-1.6-.2-2.4-.2-1.5 0-2.7.3-3.6 1-.8.7-1.2 1.5-1.2 2.6V9h2zM20 12H4v1h8.3c.3.1.6.2.8.3.5.2.9.5 1.1.8.3.3.4.7.4 1.2 0 .7-.2 1.1-.8 1.5-.5.3-1.2.5-2.1.5-.8 0-1.6-.1-2.4-.3-.8-.2-1.5-.5-2.2-.8L7 18.1c.5.2 1.2.4 2 .6.8.2 1.6.3 2.4.3 1.7 0 3-.3 3.9-1 .9-.7 1.3-1.6 1.3-2.8 0-.9-.2-1.7-.7-2.2H20v-1z"})),Fa=[{label:"None",value:"none",icon:(0,e.createElement)(zr,null)},{label:"Underline",value:"underline",icon:za},{label:"Overline",value:"overline",icon:(0,e.createElement)((({size:t,...r})=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:t||24,height:t||24,"aria-hidden":"true",focusable:"false",...r},(0,e.createElement)("path",{d:"M7 4v1h10V4H7zm5 14c1.5 0 2.6-.4 3.4-1.2.8-.8 1.1-2 1.1-3.5V7H15v5.8c0 1.2-.2 2.1-.6 2.8-.4.7-1.2 1-2.4 1s-2-.3-2.4-1c-.4-.7-.6-1.6-.6-2.8V7H7.5v6.2c0 1.5.4 2.7 1.1 3.5.8.9 1.9 1.3 3.4 1.3z"}))),null)},{label:"Line-through",value:"line-through",icon:Da}];function Ba({value:t,onChange:r,cssProp:n}){return(0,e.createElement)(bn,{as:fn,allowCustomValue:!0,label:"Text Decoration",id:"gblocks-text-decoration",value:t,onChange:r,buttons:Fa,cssProp:n})}const ja=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M12.5 5L10 19h1.9l2.5-14z"})),Va=[{label:"None",value:"none",icon:(0,e.createElement)(zr,null)},{label:"Normal",value:"normal",icon:Aa},{label:"Italic",value:"italic",icon:ja}];function Wa({value:t,onChange:r,cssProp:n}){const o=(0,a.useMemo)((()=>Va.every((e=>e.value!==t))),[t]);return(0,e.createElement)(bn,{as:fn,allowCustomValue:!0,hasCustomValue:o,label:"Font Style",id:"gblocks-font-style",value:t,onChange:r,buttons:Va,cssProp:n})}function Ua(t){const{styles:r,onStyleChange:n,opened:a,scrollAfterOpen:l,onToggle:i,initialOpen:c}=t,{fontSize:u="",color:d="",lineHeight:p="",letterSpacing:h="",textAlign:m="",fontWeight:f="",fontStyle:g="",textDecoration:v="",fontFamily:y="",textTransform:w="",whiteSpace:k=""}=r;return(0,e.createElement)(o.PanelBody,{title:(0,s.__)("Typography","generateblocks-pro"),initialOpen:c,icon:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Vr,{size:"20"}),(0,e.createElement)(Kn,{cssProps:Xe})),opened:!!a||void 0,scrollAfterOpen:l,onToggle:i},(0,e.createElement)(b.Stack,{gap:"12px",className:"gb-styles-builder-panel__content"},(0,e.createElement)(bn,{as:ba,label:"Text Color",value:d,onChange:e=>n("color",e),cssProp:"color"}),(0,e.createElement)(bn,{as:Hn,label:"Font Size",id:"gblocks-font-size",value:u,onChange:e=>n("fontSize",e),fallbackProp:!0,cssProp:"fontSize"}),(0,e.createElement)(bn,{as:o.SelectControl,allowCustomValue:!0,label:"Font Weight",id:"gblocks-font-weight",value:f,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"Normal",value:"normal"},{label:"Bold",value:"bold"},{label:"100",value:"100"},{label:"200",value:"200"},{label:"300",value:"300"},{label:"400",value:"400"},{label:"500",value:"500"},{label:"600",value:"600"},{label:"700",value:"700"},{label:"800",value:"800"},{label:"900",value:"900"}],onChange:e=>n("fontWeight",e),cssProp:"fontWeight"}),(0,e.createElement)(Wa,{value:g,onChange:e=>n("fontStyle",e),cssProp:"fontStyle"}),(0,e.createElement)(ya,{value:m,onChange:e=>n("textAlign",e),cssProp:"textAlign"}),(0,e.createElement)(Na,{value:w,onChange:e=>n("textTransform",e),cssProp:"textTransform"}),(0,e.createElement)(Ba,{value:v,onChange:e=>n("textDecoration",e),cssProp:"textDecoration"}),(0,e.createElement)(o.Flex,null,(0,e.createElement)(bn,{as:Hn,label:"Line Height",id:"gblocks-line-height",value:p,onChange:e=>n("lineHeight",e),cssProp:"lineHeight",units:[...Hn.defaultUnits,""],defaultUnit:""}),(0,e.createElement)(bn,{as:Hn,label:"Letter Spacing",id:"gblocks-letter-spacing",value:h,onChange:e=>n("letterSpacing",e),cssProp:"letterSpacing"})),(0,e.createElement)(Ra,{fontFamily:y,onStyleChange:n,cssProp:"fontFamily"}),(0,e.createElement)(bn,{id:"gblocks-white-space",as:o.SelectControl,allowCustomValue:!0,label:"White Space",cssProp:"whiteSpace",value:k,onChange:e=>n("whiteSpace",e),options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"normal",value:"normal"},{label:"nowrap",value:"nowrap"},{label:"pre",value:"pre"},{label:"pre-wrap",value:"pre-wrap"}]})))}const Ha={control:"HfjAcyFx5zC9Hy9bkBv7",gradient:"LeK2Q_yAy49y5LBOXOvA",preview:"Qyn19J8atbeEKaox6SWR",bar:"QTpJWv9fFZM3mGHru1KA",settings:"fCoUw35IQm11e_bHQ3Fz",setting:"UdFJZW0qovEaL7mF0jQL","gb-styles-builder-control":"V0XZrG9MqzfFC7J5TxUY",remove:"IqPx_loSJR6esSKljXry",stops:"sYLSFf0LyP5UswQWoI9s",preset:"SJnAnIwAmHXEQ5vtsezA"};function $a({onChange:t,setCurrentGradient:r,currentGradient:n,index:l}){const{colorStops:i}=n,c=i[l],[u,d]=(0,a.useState)(c.value),[p,h]=(0,a.useState)(c.length?c.length.value:void 0);return(0,a.useEffect)((()=>{t&&t({value:u},void 0!==p?{type:"%",value:p}:void 0)}),[u,p]),(0,e.createElement)(b.Stack,{direction:"horizontal",layout:"flex"},(0,e.createElement)(bn,{as:ba,"aria-label":"Color",value:u,onChange:d,cssProp:!1}),(0,e.createElement)("div",{style:{flexGrow:1}},(0,e.createElement)(bn,{as:o.RangeControl,value:void 0!==p?parseInt(p):void 0,hideLabelFromVision:!0,onChange:h,allowReset:!0,cssProp:!1})),(0,e.createElement)(o.Button,{disabled:i.length<3,tooltip:i.length<3?(0,s.__)("At least 2 color stops are required","generateblocks-pro"):null,className:Ha.remove,icon:un,onClick:()=>{r((e=>{const t=[...e.colorStops];return t.splice(l,1),{...e,colorStops:t}}))},"aria-label":(0,s.sprintf)(
// translators: %s is the color stop number.
// translators: %s is the color stop number.
(0,s.__)("Remove stop %s","generateblocks-pro"),l)}))}function qa({onChange:t,value:r="",presets:n=[],selectingPreset:l,setSelectingPreset:i=!1}){const[c,u]=(0,a.useState)(ie(r)),{type:d,angle:p,colorStops:h=[]}=c,m=(0,N.applyFilters)("generateblocks.editor.gradientPresets",n);return(0,a.useEffect)((()=>{try{t(function(e){const{type:t,angle:r,colorStops:n}=e;return`${t}(${r?`${r}, `:""}${n.map((e=>`${e.value}${e.length?` ${e.length.value}%`:""}`)).join(", ")})`}(c))}catch(e){console.error(e.message)}}),[c]),(0,e.createElement)("div",{className:Ha.gradient},(0,e.createElement)(b.Stack,{gap:"12px"},(0,e.createElement)("div",{className:Ha.preview},(0,e.createElement)("div",{className:Ha.bar,style:{backgroundImage:r}})),l&&m?(0,e.createElement)(e.Fragment,null,m.map((t=>(0,e.createElement)("div",{className:Ha.preset,key:t.name},(0,e.createElement)("span",{className:ln.label},t.name),(0,e.createElement)(b.Stack,{direction:"horizontal",layout:"flex",gap:"2px"},t.gradients.map((t=>(0,e.createElement)(o.Button,{key:t.slug,onClick:()=>{u(ie(t.gradient))}},(0,e.createElement)("span",{style:{background:t.gradient?t.gradient:null}})))))))),(0,e.createElement)(o.Button,{variant:"link",onClick:()=>i(!1)},(0,s.__)("Back","generateblocks-pro"))):(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:Ha.settings},(0,e.createElement)(b.Stack,{layout:"flex",direction:"horizontal",wrap:!1},(0,e.createElement)(bn,{as:o.SelectControl,label:(0,s.__)("Type","generateblocks-pro"),value:d,options:[{label:"Linear",value:"linear-gradient"},{label:"Radial",value:"radial-gradient"}],onChange:e=>{u((t=>({...t,type:e,angle:"radial-gradient"===e?void 0:t.angle||"90deg"})))},style:{flexGrow:1}}),"linear-gradient"===d&&(0,e.createElement)(bn,{as:o.AnglePickerControl,label:"Angle",value:p?parseFloat(p):0,onChange:e=>{u((t=>({...t,angle:`${e}deg`})))}}))),(0,e.createElement)("ul",{className:Ha.stops,role:"list"},h.map(((t,r)=>(0,e.createElement)("li",{key:r,style:{marginBottom:0}},(0,e.createElement)($a,{index:r,currentGradient:c,setCurrentGradient:u,colorStop:t,onChange:(e,t)=>{u((n=>{const o=[...n.colorStops],a=o[r];return o[r]={...a,...e,length:t},{...n,colorStops:o}}))}})))),(0,e.createElement)(o.Button,{variant:"primary",size:"compact",onClick:()=>{u((e=>({...e,colorStops:[...e.colorStops,{type:"rgb",value:"rgb(0, 0, 0)",length:{type:"%",value:0}}]})))}},(0,s.__)("Add Color","generateblocks-pro"))))))}const Ga={left:0,center:.5,top:0,right:1,bottom:1};function Za(e,{width:t,height:r}){var n,o;const a=document.createElement("div");if(a.style.backgroundPosition=e,""===a.style.backgroundPosition)return e;t&&(a.style.width=`${t}px`),r&&(a.style.height=`${r}px`),document.body.appendChild(a);const s=getComputedStyle(a),l=s.backgroundPositionX,i=s.backgroundPositionY;document.body.removeChild(a);let c=null,u=null;return l.endsWith("%")?c=parseFloat(l)/100:l.endsWith("px")?c=parseFloat(i)/t:l in Ga&&(c=Ga[l]),i.endsWith("%")?u=parseFloat(i)/100:i.endsWith("px")?u=parseFloat(i)/t:i in Ga&&(u=Ga[l]),{x:null!==(n=parseFloat(Math.round(100*c)/100))&&void 0!==n?n:.5,y:null!==(o=parseFloat(Math.round(100*u)/100))&&void 0!==o?o:.5}}const Ka={control:"VMzuFN8N5ptRywxczeK0",selectButton:"JhggAesSNsaxP2lu1dte",url:"xqfxX1np8PIcrMakVTOs",flex:"mLVgQDS13NdTPGCc9guV"};function Ya({onUrlChange:t,onPositionChange:r,onSizeChange:n,onMediaSelect:l,url:i,urlInputLabel:c,imageSizeLabel:u=(0,s.__)("Media Size","generateblocks-pro"),position:d="center",size:p="full"}){const h=te("/plugins/generateblocks-pro/dist/assets/placeholder-lg.min.jpg"),m=(0,a.useRef)(null),[f,v]=(0,a.useState)({width:240,height:131}),[y,w]=(0,a.useState)((()=>Za(d,f))),E=(0,s.__)("Enter any valid background position.","generateblocks-pro"),[S,x]=(0,a.useState)(E),[C,_,O]=(0,k.useDebouncedInput)(d),[T,I]=(0,a.useState)((()=>{var e;return null!==(e=JSON.parse(sessionStorage.getItem("gb_image_sizes")))&&void 0!==e?e:{}})),[R,M]=(0,a.useState)((()=>function(e){if(e.startsWith("url(")){const t=e.match(/url\((.*?)\)/);return(t?t[1]:"").replace('"',"").replace("'","")}return e}(i))),[P,A]=(0,a.useState)(!0),[L,N]=(0,a.useState)(R),D=(0,a.useMemo)((()=>{var e;return null!==(e=function(e,t){const r=Object.keys(e).find((r=>Object.values(e[r]).some((e=>e.url===t))));return r?e[r]:null}(T,R))&&void 0!==e?e:{}}),[T,R]),F=(0,a.useMemo)((()=>{if(!Object.keys(D).length)return[{label:(0,s.__)("Current size","generateblocks-pro"),value:""}];const e=Object.keys(D).map((e=>({label:e.charAt(0).toUpperCase()+e.slice(1),value:e})));return e.length||e.unshift({label:(0,s.__)("Full","generateblocks-pro"),value:""}),e}),[D]),B=(0,a.useMemo)((()=>{var e;return Object.keys(D).length&&null!==(e=Object.entries(D).find((([,e])=>e.url===R))[0])&&void 0!==e?e:""}),[D,R]),j=(0,a.useCallback)((function(e){const t=`${Math.round(100*e.x)}% ${Math.round(100*e.y)}%`;w(e),_(t)}),[r]),V=(0,a.useCallback)((function(e){const t=function(e){const t=document.createElement("div");return t.style.backgroundPosition=e,""!==t.style.backgroundPosition}(e);_(e),t?(x(E),w(Za(e,f))):x((0,s.__)("Invalid background position.","generateblocks-pro"))}),[]),W=(0,a.useCallback)((function(e){t&&(t(e),M(e),N(e)),A(!0)}),[t]);return(0,a.useLayoutEffect)((()=>{if(!m.current)return;const e=m.current.querySelector(".components-focal-point-picker__media--image");function t(){R.startsWith("var(")||"none"===R||A(!1),N(h),e.style.width="240px",e.style.height="131px"}if(e){e.addEventListener("error",t);const r=e.clientWidth||240,n=e.clientHeight||131;v({width:r,height:n})}return()=>{e&&e.removeEventListener("error",t)}}),[R]),Dn((()=>{r&&r(C)}),[O]),(0,e.createElement)("div",{className:g("gb-image-control",Ka.control),ref:m},(0,e.createElement)(b.Stack,{gap:"12px"},(0,e.createElement)("div",{className:Ka.flex},(0,e.createElement)(bn,{as:o.TextControl,className:Ka.url,type:"text",value:R,onChange:W,label:c,help:!P&&(0,s.__)("Could not load image.","generateblocks-pro")}),(0,e.createElement)(z.MediaUploadCheck,null,(0,e.createElement)(z.MediaUpload,{title:(0,s.__)("Choose Media","generateblocks-pro"),onSelect:e=>{var t,r;const n={...T,[e.id]:e.sizes};sessionStorage.setItem("gb_image_sizes",JSON.stringify(n)),I(n);const o=null!==(t=null!==(r=e.sizes?.[p].url)&&void 0!==r?r:e?.url)&&void 0!==t?t:"";W(o),l&&l(e)},allowedTypes:["image"],value:R,modalClass:"gb-image-control-modal",render:({open:t})=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.Tooltip,{text:(0,s.__)("Open the Media Library","generateblocks-pro")},(0,e.createElement)(o.Button,{variant:"secondary",onClick:()=>{t()},className:Ka.selectButton},(0,s.__)("Select","generateblocks-pro"))))}))),"none"!==R&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(z.MediaUploadCheck,null,(0,e.createElement)(bn,{as:o.SelectControl,value:B,options:F,disabled:!Object.keys(D).length,onChange:e=>{var t;(null!==(t=D?.[e])&&void 0!==t?t:null)&&W(D?.[e].url),n&&n(e)},label:u,help:Object.keys(D).length?(0,s.__)("Sets the size of the image chosen from the Media Library.","generateblocks-pro"):(0,s.__)("Size information is not available. Try re-inserting the image from the Media Library.","generateblocks-pro")})),(0,e.createElement)(o.FocalPointPicker,{url:L,value:y,onDragStart:j,onDrag:j,onChange:j}),(0,e.createElement)(bn,{as:o.TextControl,label:"Background Position",value:C,help:S,onChange:V}))))}const Qa=[{label:"Default",value:""},{label:"Normal",value:"normal"},{label:"Multiply",value:"multiply"},{label:"Screen",value:"screen"},{label:"Overlay",value:"overlay"},{label:"Darken",value:"darken"},{label:"Lighten",value:"lighten"},{label:"Color-dodge",value:"color-dodge"},{label:"Color-burn",value:"color-burn"},{label:"Hard-light",value:"hard-light"},{label:"Soft-light",value:"soft-light"},{label:"Difference",value:"difference"},{label:"Exclusion",value:"exclusion"},{label:"Hue",value:"hue"},{label:"Saturation",value:"saturation"},{label:"Color",value:"color"},{label:"Luminosity",value:"luminosity"}],Xa={image:(0,s.__)("Choose a background image from the Media Library or enter a URL to a remote image.","generateblocks-pro"),gradient:(0,s.__)("Use this to create a linear or radial gradient. To overlay a gradient over another background, the gradient colors must be semi-transparent.","generateblocks-pro"),overlay:(0,s.__)("Use this to overlay a single color over one or more backgrounds. The chosen color must semi-transparent.","generateblocks-pro"),none:(0,s.__)("Disables the background for this at-rule (sets background: none","generateblocks-pro")},Ja="linear-gradient(to right, rgba(0, 0, 0, 1) 0%,rgba(10, 10, 10, 0.5) 100%)";function es({currentOption:t,findBackgroundIndex:r,setSettings:n}){const l=(0,z.useSetting)("color.gradients")||[],[i,c]=(0,a.useState)(!1),u=[];l.length&&u.push({name:(0,s.__)("Theme.JSON Presets","generateblocks-pro"),gradients:l});const{styles:d,index:p}=r(),{type:h,backgroundImage:m="",backgroundSize:f="",backgroundRepeat:g="",backgroundPosition:b="",backgroundBlendMode:v="",backgroundAttachment:y="",overlayColor:w=""}=t,[k,E]=(0,a.useState)((()=>({prevType:h,prevValue:m}))),[S,x]=(0,a.useState)((()=>"gradient"===h?m:Ja)),[C,_]=(0,a.useState)((()=>w||"rgba(0, 0, 0, .25)"));(0,a.useEffect)((()=>{if("gradient"===h&&m!==S){const e=[...d];e[p]={...e[p],backgroundImage:S},n(e)}}),[h,m,S,p,d,n]);const O=(0,a.useCallback)((e=>{const t=d[p].backgroundImage,{prevType:r="",prevValue:o=""}=k,a=[...d];a[p]={...a[p],type:e},["overlay","gradient"].includes(e)?("overlay"===e&&(a[p].overlayColor=C,a[p].backgroundImage="overlay"===r?o:`linear-gradient(to left, ${C} 0%, ${C} 100%)`),"gradient"===e&&(a[p].backgroundImage="gradient"===r?o:Ja)):"image"===e?a[p].backgroundImage="image"===r?o:"":"none"===e&&(a[p].backgroundImage="none"),E({prevType:h,prevValue:t}),n(a)}),[d,p,C,h,k,n]);return t&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(wn,null,(0,e.createElement)(bn,{alwaysVisible:!0,as:o.SelectControl,options:[{label:(0,s.__)("Image","generateblocks-pro"),value:"image"},{label:(0,s.__)("Gradient","generateblocks-pro"),value:"gradient"},{label:(0,s.__)("Overlay","generateblocks-pro"),value:"overlay"},{label:(0,s.__)("None","generateblocks-pro"),value:"none"}],label:(0,s.__)("Background Type","generateblocks-pro"),onChange:O,dropdownChildren:({onClose:t})=>(0,e.createElement)(bn.Description,{label:(0,s.__)("About Background Types","generateblocks-pro"),onClick:t},(0,e.createElement)("dl",null,(0,e.createElement)("dt",null,(0,s.__)("Image","generateblocks-pro")," ","image"===h&&(0,s.__)("(Current)","generateblocks-pro")),(0,e.createElement)("dd",null,Xa.image),(0,e.createElement)("dt",null,(0,s.__)("Gradient","generateblocks-pro")," ","gradient"===h&&(0,s.__)("(Current)","generateblocks-pro")),(0,e.createElement)("dd",null,Xa.gradient),(0,e.createElement)("dt",null,(0,s.__)("Overlay","generateblocks-pro")," ","overlay"===h&&(0,s.__)("(Current)","generateblocks-pro")),(0,e.createElement)("dd",null,Xa.overlay))),value:h}),"overlay"===h&&(0,e.createElement)(bn,{alwaysVisible:!0,as:ba,label:(0,s.__)("Overlay Color","generateblocks-pro"),help:(0,e.createElement)(e.Fragment,null,!C.includes("rgba")&&!C.includes("hsla")&&(0,s.__)("The chosen color must have some transparency for the background(s) beneath to be visible.","generateblocks-pro")),value:C,onChange:e=>{d[p].overlayColor=e,d[p].backgroundImage=`linear-gradient(to left, ${e} 0%, ${e} 100%)`,_(e),n(d)},dropdownChildren:({onClose:t})=>(0,e.createElement)(bn.Description,{label:(0,s.__)("About Background Overlay","generateblocks-pro"),onClick:t},Xa.overlay)}),"image"===h&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(bn,{alwaysVisible:!0,as:Ya,label:"Background Image",url:m,position:b,onUrlChange:e=>{const t=e.startsWith("http")||e.startsWith("://")||e.startsWith("/");d[p].backgroundImage=t?`url(${e})`:e,n(d)},onPositionChange:e=>{d[p].backgroundPosition=e,n(d)},dropdownChildren:({onClose:t})=>(0,e.createElement)(bn.Description,{label:(0,s.__)("About Background Image","generateblocks-pro"),onClick:t},Xa.image)}),"none"!==m&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(bn,{alwaysVisible:!0,allowCustomValue:!0,as:o.SelectControl,label:"Repeat",value:g,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"repeat",value:"repeat"},{label:"repeat-x",value:"repeat-x"},{label:"repeat-y",value:"repeat-y"},{label:"round",value:"round"},{label:"space",value:"space"},{label:"no-repeat",value:"no-repeat"}],dropdownChildren:({onClose:t})=>(0,e.createElement)(bn.Description,{label:(0,s.__)("About Background Repeat","generateblocks-pro"),onClick:t},(0,s.__)("The background-repeat CSS property sets how background images are repeated. A background image can be repeated along the horizontal and vertical axes, or not repeated at all.","generateblocks-pro")),onChange:(e=0)=>{d[p].backgroundRepeat=e,n(d)},learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/background-repeat",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro")}),(0,e.createElement)(bn,{alwaysVisible:!0,as:o.SelectControl,allowCustomValue:!0,label:"Size",value:f,placeholder:(0,s.__)("100px 50%, cover, contain, etc","generateblocks-pro"),options:[{id:1,label:(0,s.__)("Default","generateblocks-pro"),value:""},{id:2,label:"cover",value:"cover"},{id:3,label:"contain",value:"contain"},{id:4,label:"auto",value:"auto"}],onChange:e=>{d[p].backgroundSize=e,n(d)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:o.SelectControl,allowCustomValue:!0,label:"Attachment Type",value:y,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"Fixed",value:"fixed"},{label:"Scroll with Content (scroll)",value:"scroll"},{label:"Local",value:"local"}],dropdownChildren:({onClose:t})=>(0,e.createElement)(bn.Description,{label:(0,s.__)("About Background Attachment","generateblocks-pro"),onClick:t},(0,s.__)("The background-attachment CSS property sets whether a background image's position is fixed within the viewport, or scrolls with its containing block.","generateblocks-pro")),onChange:(e=0)=>{d[p].backgroundAttachment=e,n(d)},learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/background-attachment",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro")}))),"gradient"===h&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(bn,{alwaysVisible:!0,as:qa,selectingPreset:i,setSelectingPreset:c,presets:u,label:"Gradient",onChange:x,value:m.includes("gradient")?m:Ja,dropdownChildren:({onClose:t})=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(bn.Description,{label:(0,s.__)("About Gradients","generateblocks-pro"),onClick:t},Xa.gradient),(0,e.createElement)(o.MenuGroup,null,(0,e.createElement)(o.MenuItem,{onClick:()=>{t(),c(!i)}},i?(0,s.__)("Cancel select preset","generateblocks-pro"):(0,s.__)("Select preset","generateblocks-pro")))),learnMoreLabel:(0,s.__)("Learn more about gradients.","generateblocks-pro"),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/gradient"})),"none"!==m&&(0,e.createElement)(bn,{alwaysVisible:!0,as:o.SelectControl,allowCustomValue:!0,label:"Blend Mode",value:v,options:Qa,onChange:(e=0)=>{d[p].backgroundBlendMode=e,n(d)},dropdownChildren:({onClose:t})=>(0,e.createElement)(bn.Description,{label:(0,s.__)("About Background Blend Mode","generateblocks-pro"),onClick:t},(0,s.__)("The background-blend-mode CSS property sets how an element's background images should blend with each other and with the element's background color.","generateblocks-pro")),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/background-blend-mode",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro")})))}function ts(e){const{background:t="",backgroundBlendMode:r="",backgroundAttachment:n="",backgroundImage:o="",backgroundPosition:a="",backgroundSize:s="",backgroundRepeat:l=""}=e;if(Object.values(e).every((e=>""===e)))return[];if(t){const e=r.split(",");return(i=t,(i.match(ae)||[]).map((e=>{const t=document.createElement("div");if(t.style.background=e,""===t.style.background)return{type:"image"};const{backgroundAttachment:r="",backgroundImage:n="",backgroundSize:o="",backgroundRepeat:a="",backgroundPosition:s=""}=t.style,l=n.includes("gradient("),i=n.startsWith("url(");if(!l&&!i)return{type:"image"};let c="image",u="";if(l){c="gradient";const e=n.match(oe);e&&e.every((t=>t===e[0]))&&(c="overlay",u=e[0])}return{type:c,backgroundAttachment:"initial"===r?"":r,backgroundImage:n.replaceAll('"',"").replaceAll("'",""),backgroundSize:o,backgroundRepeat:a,backgroundPosition:s,overlayColor:u}}))).map(((t,r)=>(t.backgroundBlendMode=e[r]||"normal","image"===t.type&&(t.media={id:0}),t)))}var i;return Te({backgroundBlendMode:r,backgroundAttachment:n,backgroundImage:o,backgroundPosition:a,backgroundSize:s,backgroundRepeat:l})}const rs=({onStyleChange:t,value:r,atRule:n})=>{var l;const[i,c]=(0,a.useState)(!1),[u,d]=(0,a.useState)((()=>ts(r))),[p,h]=(0,a.useState)(null),m=u.some((e=>"none"===e.backgroundImage)),f=(0,a.useRef)(!1),g=null!==(l=u[p])&&void 0!==l?l:null,b=null!==g;(0,a.useEffect)((()=>{f.current?f.current=!1:d(ts(r))}),[r]);const v=(0,k.useDebounce)(t,0),y=(0,a.useCallback)((function(e){f.current=!0,d(e),v({backgroundAttachment:e.reduce(((e,t)=>{const{backgroundAttachment:r=""}=t;return e.length>0?`${e},${r.trim()}`:`${r.trim()}`}),""),backgroundImage:e.reduce(((e,t)=>{const{backgroundImage:r=""}=t;return e.length>0?`${e},${r.trim()}`:`${r.trim()}`}),""),backgroundSize:e.reduce(((e,t)=>{const{backgroundSize:r=""}=t;return e.length>0?`${e},${r.trim()}`:`${r.trim()}`}),""),backgroundRepeat:e.reduce(((e,t)=>{const{backgroundRepeat:r=""}=t;return e.length>0?`${e},${r.trim()}`:`${r.trim()}`}),""),backgroundPosition:e.reduce(((e,t)=>{const{backgroundPosition:r=""}=t;return e.length>0?`${e},${r.trim()}`:`${r.trim()}`}),""),backgroundBlendMode:e.reduce(((e,t)=>{const{backgroundBlendMode:r="normal"}=t;return e.length>0?`${e},${r.trim()}`:`${r.trim()}`}),"").trim()})}),[v]),w=(0,a.useCallback)((()=>{const e=[...u];return{styles:e,index:e[p]?p:-1}}),[u,p]),{styles:E,index:S}=w();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(yn,{id:"BackgroundControl",items:u,label:(0,s.__)("Backgrounds","generateblocks-pro"),isEditing:b,onClickDelete:()=>{E.splice(S,1),y(E),h(null)},onClickDone:()=>{h(null)},showAdd:!m,dropdownChildren:({onClose:t})=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuGroup,{label:(0,s.__)("Options","generateblocks-pro")},(0,e.createElement)(yn.DeleteAll,{label:(0,s.__)("Delete all background styles","generateblocks-pro"),content:(0,s.__)("This will delete all background styles for the current selector. This operation cannot be undone.","generateblocks-pro"),items:u,onDelete:()=>{y([]),h(null)},onClose:t,confirmDelete:i,setConfirmDelete:c})),!i&&(0,e.createElement)(yn.LearnMore,{learnMoreLabel:(0,s.__)("Learn more about Backgrounds","generateblocks-pro"),learnMoreURL:"https://developer.mozilla.org/en-US/docs/Web/CSS/background",onClose:t})),onAdd:()=>{const e=u.some((e=>"image"===e.type));h(0),y([e?le():se(n),...u])},learnMoreLabel:(0,s.__)("Learn more about Backgrounds","generateblocks-pro"),learnMoreURL:"https://developer.mozilla.org/en-US/docs/Web/CSS/background",help:m&&(0,s.__)("Remove the disabled background to enable.","generateblocks-pro"),searchKeywords:["image","gradient","blend mode","bg"],cssProp:Object.keys(r)},(0,e.createElement)("div",{className:Ha.control},(0,e.createElement)("div",{className:Ha.wrapper},!b&&(0,e.createElement)("div",null,(0,e.createElement)(xn,{items:u,dragHandleLabel:(0,s.__)("Reorder Background","generateblocks-pro"),setItems:e=>{y(e)},itemComponent:function({item:t,index:r}){const n=function(e={}){var t;return[["backgroundImage",null!==(t=e.backgroundImage)&&void 0!==t?t:""],...Object.entries(e).filter((([e])=>e.startsWith("background")&&"backgroundImage"!==e))].reduce(((e,[t,r])=>t.startsWith("background")&&r?e?`${e}\n${ne(t)}: ${r};`:`${ne(t)}: ${r};`:e),"")}(t),o=n.length>0?n:(0,s.__)("Empty","generateblocks-pro");return(0,e.createElement)(xn.Item,{label:t.label,css:o,canDuplicate:n.length>0,onDuplicate:()=>{const e=[...u,{...t}];h(e.length-1),y(e)},onEdit:()=>{h(r)}})},dragHandle:!0})),b&&(0,e.createElement)(es,{isEditing:b,currentOption:g,findBackgroundIndex:w,setSettings:y})))))};function ns(t){const{styles:r,onStyleChange:n,nestedRule:l,atRule:i,opened:c,scrollAfterOpen:u,onToggle:d,initialOpen:p}=t,{background:h="",backgroundColor:m="",backgroundClip:f="",backgroundOrigin:g="",backgroundBlendMode:v="",backgroundAttachment:y="",backgroundImage:w="",backgroundPosition:k="",backgroundSize:E="",backgroundRepeat:S=""}=r,x=(0,a.useMemo)((()=>({background:h,backgroundBlendMode:v,backgroundAttachment:y,backgroundImage:w,backgroundPosition:k,backgroundSize:E,backgroundRepeat:S})),[h,v,y,w,k,E,S]);return(0,e.createElement)(o.PanelBody,{title:(0,s.__)("Backgrounds","generateblocks-pro"),initialOpen:p,icon:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Wr,{size:"20"}),(0,e.createElement)(Kn,{cssProps:Je})),opened:!!c||void 0,scrollAfterOpen:u,onToggle:d},(0,e.createElement)(b.Stack,{gap:"12px",className:"gb-styles-builder-panel__content"},(0,e.createElement)(rs,{value:x,onStyleChange:n,nestedRule:l,atRule:i}),(0,e.createElement)(bn,{as:ba,label:"Background Color",value:m,cssProp:"backgroundColor",onChange:e=>n("backgroundColor",e),searchKeywords:["bg"]}),(0,e.createElement)(bn,{allowCustomValue:!0,as:o.SelectControl,label:"Background Clip",value:f,cssProp:"backgroundClip",options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"border-box",value:"border-box"},{label:"padding-box",value:"padding-box"},{label:"content-box",value:"content-box"},{label:"text",value:"text"}],onChange:e=>n("backgroundClip",e),learnMoreLabel:(0,s.__)("Learn more about this property","generateblocks-pro"),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/background-clip",dropdownChildren:()=>(0,e.createElement)(o.MenuGroup,{label:(0,s.__)("About Background Clip","generateblocks-pro")},(0,e.createElement)("p",{style:{padding:"0 8px"}},(0,s.__)("This property only will apply if a background color or image is set, or if the element has a border with transparency.","generateblocks-pro")))}),(0,e.createElement)(bn,{allowCustomValue:!0,as:o.SelectControl,label:"Background Origin",value:g,cssProp:"backgroundOrigin",options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"border-box",value:"border-box"},{label:"padding-box",value:"padding-box"},{label:"content-box",value:"content-box"}],onChange:e=>n("backgroundOrigin",e),learnMoreLabel:(0,s.__)("Learn more about this property","generateblocks-pro"),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/background-origin",dropdownChildren:()=>(0,e.createElement)(o.MenuGroup,{label:(0,s.__)("About Background Origin","generateblocks-pro")},(0,e.createElement)("p",{style:{padding:"0 8px"}},(0,s.__)("This property property sets the background's origin: from the border start, inside the border, or inside the padding.","generateblocks-pro")))})))}const os=window.lodash,as={parentControl:"rVp8A7qMeK7XONfixmdK",control:"yJ7i1If3EmgDCPFiBjAA",dropdown:"Lm7UyY73DpULpJmOppaV",colors:"xGubhiiSX3QglXXt2KzZ",sync:"Mb7DqNgS6fQz6oE6FA3h",flexShrink:"M7WCVsmJLrgpHs9Rz8HK"};function ss({icon:t}){return"border-none"===t?(0,e.createElement)(Ir,null):"border-solid"===t?(0,e.createElement)(Rr,null):"border-dashed"===t?(0,e.createElement)(Mr,null):"border-dotted"===t?(0,e.createElement)(Pr,null):"border-default"===t?(0,e.createElement)(Ar,null):"borders"===t?(0,e.createElement)(Lr,null):void 0}function ls({value:t,onChange:r}){const n={none:(0,e.createElement)(ss,{icon:"border-none"}),solid:(0,e.createElement)(ss,{icon:"border-solid"}),dashed:(0,e.createElement)(ss,{icon:"border-dashed"}),dotted:(0,e.createElement)(ss,{icon:"border-dotted"})};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.DropdownMenu,{className:as.dropdown,icon:n[t]||(0,e.createElement)(ss,{icon:"border-default"}),label:(0,s.__)("Select a style","generateblocks-pro")},(({onClose:t})=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuGroup,null,(0,e.createElement)(o.MenuItem,{icon:(0,e.createElement)(ss,{icon:"border-default"}),onClick:()=>{r(""),t()}},(0,s.__)("Default","generateblocks-pro")),(0,e.createElement)(o.MenuItem,{icon:(0,e.createElement)(ss,{icon:"border-none"}),onClick:()=>{r("none"),t()}},"None"),(0,e.createElement)(o.MenuItem,{icon:(0,e.createElement)(ss,{icon:"border-solid"}),onClick:()=>{r("solid"),t()}},"Solid"),(0,e.createElement)(o.MenuItem,{icon:(0,e.createElement)(ss,{icon:"border-dashed"}),onClick:()=>{r("dashed"),t()}},"Dashed"),(0,e.createElement)(o.MenuItem,{icon:(0,e.createElement)(ss,{icon:"border-dotted"}),onClick:()=>{r("dotted"),t()}},"Dotted"))))))}function is(t){const r=["borderTop","borderRight","borderBottom","borderLeft"],[n,l]=(0,a.useState)("all"),{styles:i,onStyleChange:c,opened:u,scrollAfterOpen:d,onToggle:p,initialOpen:h}=t,{border:m="",borderTopWidth:f="",borderTopStyle:v="",borderTopColor:y="",borderRightWidth:w="",borderRightStyle:k="",borderRightColor:E="",borderBottomWidth:S="",borderBottomStyle:x="",borderBottomColor:C="",borderLeftWidth:_="",borderLeftStyle:O="",borderLeftColor:I="",borderTopLeftRadius:R="",borderTopRightRadius:M="",borderBottomRightRadius:P="",borderBottomLeftRadius:A=""}=i,L={border:m,borderTopWidth:f,borderTopStyle:v,borderTopColor:y,borderRightWidth:w,borderRightStyle:k,borderRightColor:E,borderBottomWidth:S,borderBottomStyle:x,borderBottomColor:C,borderLeftWidth:_,borderLeftStyle:O,borderLeftColor:I},N={borderTop:"Top",borderRight:"Right",borderBottom:"Bottom",borderLeft:"Left"},z=T(),{search:D="",activeFilter:F=""}=z?.controlFilters;(0,a.useEffect)((()=>{if("all"===n&&(D||F))return void l(!1);const e=r.map((e=>Object.entries(L).reduce(((t,[r,n])=>{if(r.startsWith(e)&&n){const o=r.replace(e,"");return{...t,[o]:n}}return t}),{})));e.every((t=>(0,os.isEqual)(t,e[0])))||l(!1)}),[D,F]);const B=(0,a.useCallback)((function(){const e=r.filter((e=>{return L[e+"Width"]||(t=L[e+"Width"],isNaN(parseFloat(t))&&isFinite(t));var t}));if(!e.length)return;const t=e[0],n=Object.entries(L).reduce(((e,[r,n])=>(r.startsWith(t)&&(e[r.replace(t,"")]=n),e)),{}),o=Object.entries(n).reduce(((e,[t,n])=>n?(r.forEach((r=>{e[r+t]=n})),e):e),{});c(o)}),[L,c]),j={"Top Left":{prop:"borderTopLeftRadius",value:R},"Top Right":{prop:"borderTopRightRadius",value:M},"Bottom Right":{prop:"borderBottomRightRadius",value:P},"Bottom Left":{prop:"borderBottomLeftRadius",value:A}},V=(0,a.useMemo)((()=>n?m:Object.values(L).filter(Boolean).join(",")),[L,n]);return(0,e.createElement)(o.PanelBody,{title:(0,s.__)("Borders","generateblocks-pro"),initialOpen:h,icon:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(jr,{size:"20"}),(0,e.createElement)(Kn,{cssProps:Qe})),opened:!!u||void 0,scrollAfterOpen:d,onToggle:p},(0,e.createElement)(b.Stack,{gap:"12px",className:"gb-styles-builder-panel__content"},(0,e.createElement)(bn,{label:"Border",id:"gblocks-border",className:g("gb-borders",as.parentControl),beforeDropdownMenu:(0,e.createElement)(io,{sync:n,setSync:l,syncTypes:["all"],onClick:e=>{e&&B()}}),searchKeywords:Object.keys(L),cssProp:Object.keys(L),value:V},(0,e.createElement)(b.Stack,{gap:"12px"},r.map(((t,r)=>{if(n&&r>0)return null;const o=n?(0,s.__)("All sides","generateblocks-pro"):N[t],a=n?Object.keys(L):[t+"Width",t+"Style",t+"Color"],l=n?V:[L[t+"Width"],L[t+"Style"],L[t+"Color"]].filter(Boolean).join(","),i=n?"all-sides":t;return(0,e.createElement)(bn,{key:i,label:o,searchKeywords:a,value:l},(0,e.createElement)("div",{className:as.control,"data-border-area":i},(0,e.createElement)(bn,{as:Hn,id:"gblocks-"+t+"-width",value:L[t+"Width"]||"",cssProp:t+"Width",alwaysVisible:!0,"aria-label":`border ${t} width`,onChange:e=>{const r={[t+"Width"]:e};n&&(r.borderRightWidth=e,r.borderBottomWidth=e,r.borderLeftWidth=e),e?L[t+"Style"]||(r[t+"Style"]="solid",n&&(r.borderRightStyle="solid",r.borderBottomStyle="solid",r.borderLeftStyle="solid")):(r[t+"Style"]="",n&&(r.borderRightStyle="",r.borderBottomStyle="",r.borderLeftStyle="")),c(r)}}),(0,e.createElement)(bn,{as:ls,value:L[t+"Style"],cssProp:t+"Style",alwaysVisible:!0,onChange:e=>{const r={[t+"Style"]:e};n&&(r.borderRightStyle=e,r.borderBottomStyle=e,r.borderLeftStyle=e),c(r)},"aria-label":`border ${t} style`,className:as.flexShrink}),(0,e.createElement)(bn,{as:ba,value:L[t+"Color"]||"",cssProp:t+"Color",alwaysVisible:!0,alpha:!0,onChange:e=>{const r={[t+"Color"]:e};n&&(r.borderRightColor=e,r.borderBottomColor=e,r.borderLeftColor=e),c(r)},"aria-label":`border ${t} color`})))})))),(0,e.createElement)(mo,{id:"border-radius",label:"Border Radius",onChange:c,cssProps:j,syncTypes:["all"],layout:"corners",className:"border-radius"})))}function cs(t){const{styles:r,onStyleChange:n,opened:l,scrollAfterOpen:i,onToggle:c,initialOpen:u}=t,{position:d="",overflowX:p="",overflowY:h="",zIndex:m="",top:f="",right:g="",bottom:v="",left:y=""}=r,w=[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"visible",value:"visible"},{label:"hidden",value:"hidden"},{label:"clip",value:"clip"},{label:"scroll",value:"scroll"},{label:"auto",value:"auto"}],k=(0,a.useMemo)((()=>({Top:{prop:"top",value:f},Right:{prop:"right",value:g},Bottom:{prop:"bottom",value:v},Left:{prop:"left",value:y}})),[f,g,v,y]);return(0,e.createElement)(o.PanelBody,{title:(0,s.__)("Position","generateblocks-pro"),initialOpen:u,icon:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Hr,{size:"20"}),(0,e.createElement)(Kn,{cssProps:et})),opened:!!l||void 0,scrollAfterOpen:i,onToggle:c},(0,e.createElement)(b.Stack,{gap:"12px",className:"gb-styles-builder-panel__content"},(0,e.createElement)(bn,{allowCustomValue:!0,as:o.SelectControl,label:"position",value:d,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"Relative",value:"relative"},{label:"Absolute",value:"absolute"},{label:"Sticky",value:"sticky"},{label:"Fixed",value:"fixed"},{label:"Static",value:"static"}],onChange:e=>n("position",e),cssProp:"position"}),(0,e.createElement)(mo,{id:"inset",cssProps:k,onChange:n,syncTypes:["all"],label:"Inset"}),(0,e.createElement)(bn,{allowCustomValue:!0,as:o.SelectControl,label:"Overflow-X",value:p,options:w,onChange:e=>n("overflowX",e),cssProp:"overflowX"}),(0,e.createElement)(bn,{allowCustomValue:!0,as:o.SelectControl,label:"Overflow-Y",value:h,options:w,onChange:e=>n("overflowY",e),cssProp:"overflowY"}),(0,e.createElement)(bn,{as:o.TextControl,label:"z-index",value:m,cssProp:"zIndex",onChange:e=>{n("zIndex",e),d||n("position","relative")}})))}function us({opacity:t,onStyleChange:r}){let n=100*parseFloat(t);return isNaN(n)&&(n=t),(0,e.createElement)(bn,{as:Hn,label:"Opacity",value:n,step:1,cssProp:"opacity",onChange:e=>{let t=parseFloat(e)/100;isNaN(t)&&(t=e),r("opacity",t)},min:0,max:100,allowOtherUnits:!1,units:["%"]})}const ds=e=>e.replace(/-webkit-box-shadow:[^;]+;|-moz-box-shadow:[^;]+;/g,""),ps=e=>e.replace(/box-shadow/g,"").replace(/webkit/g,"").replace(/\r?\n|\r/g,"").replace(/;/g,"").replace(/:/g,"").trim(),hs=(e=[])=>e.reduce(((e,t)=>{if(t.hidden)return e;const{offsetX:r="",offsetY:n="",blur:o="",spread:a="",color:s="",inset:l=!1}=t,i=`${l?"inset":""} ${r} ${n} ${o} ${a} ${s}`.trim();return i.trim().length?e.length>0?`${e}, ${i}`:`${i}`:e}),"").replace(/,$/,"").trim(),ms=function(){const e=/,(?![^\(]*\))/,t=/\s(?![^(]*\))/,r=/^[0-9]+[a-zA-Z%]+$/,n=e=>{const r=e.split(t),n=r.includes("inset"),o=r.slice(-1)[0],s=a(o)?void 0:o,l=r.filter((e=>"inset"!==e)).filter((e=>e!==s)),[i,c,u,d]=l;return{inset:n,offsetX:i,offsetY:c,blur:u,spread:d,color:s}},o=e=>{const{inset:t,offsetX:r=0,offsetY:n=0,blur:o=0,spread:a,color:s}=e||{};return[t?"inset":null,r,n,o,a,s].filter((e=>null!=e)).map((e=>(""+e).trim())).join(" ")},a=e=>r.test(e);return{parse:t=>t?t.split(e).map((e=>e.trim())).map(n):[],stringify:e=>e?.length?e.map(o).join(", "):""}}(),fs=["px","em","rem","vw","vh","ch"];function gs({findBoxShadowIndex:t,setSettings:r,currentOption:n=null}){const{styles:a,index:s}=t();return n&&(0,e.createElement)(wn,null,(0,e.createElement)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},(0,e.createElement)(o.CheckboxControl,{checked:n.inset,label:"Inset",onChange:e=>{a[s].inset=e,r(a)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:ba,value:n?.color||"rgba(0,0,0,.1)",tooltip:"Box Shadow Color",onChange:e=>{a[s].color=e,r(a)}})),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,label:"x-Offset",units:fs,value:n.offsetX,onChange:e=>{a[s].offsetX=e,r(a)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,label:"y-Offset",units:fs,value:n.offsetY,onChange:e=>{a[s].offsetY=e,r(a)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,label:"blur",units:fs,value:n?.blur,onChange:e=>{a[s].blur=e,r(a)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,label:"spread",units:fs,value:n?.spread,onChange:e=>{a[s].spread=e,r(a)}}))}function bs({onStyleChange:t,value:r}){var n;const[l,i]=(0,a.useState)(!1),[c,u]=(0,a.useState)(""),[d,p]=(0,a.useState)(!1),[h,m]=(0,a.useState)((()=>ms.parse(r))),[f,g]=(0,a.useState)(null),b=(0,a.useRef)(!1),v=null!==(n=h[f])&&void 0!==n?n:null,y=null!==v;(0,a.useEffect)((()=>{b.current?b.current=!1:m(ms.parse(r))}),[r]);const w=(0,k.useDebounce)(t,0),E=(0,a.useCallback)((function(e){b.current=!0,m(e),w("boxShadow",hs(e))}),[w]),S=(0,a.useCallback)((()=>{const e=[...h];return{styles:e,index:e[f]?f:-1}}),[h,f]),{styles:x,index:C}=S();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(yn,{items:h,label:"Box Shadow",searchKeywords:["shadows","box-shadow"],cssProp:"boxShadow",onAdd:()=>{const e=[...h,{offsetX:"10px",offsetY:"10px",blur:"5px",spread:"7px",color:"rgba(0,0,0,0.1)",inset:!1}];g(e.length-1),E(e)},isEditing:y,onClickDelete:()=>{x.splice(C,1),E(x),g(null)},onClickDone:()=>{g(null)},dropdownChildren:({onClose:t})=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuGroup,{label:(0,s.__)("Options","generateblocks-pro")},!d&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuItem,{onClick:()=>{i(!0),t()}},(0,s.__)("Add via paste…","generateblocks-pro"))),(0,e.createElement)(yn.DeleteAll,{label:(0,s.__)("Delete all box shadow styles","generateblocks-pro"),content:(0,s.__)("This will delete all box shadow styles for the current selector. This operation cannot be undone.","generateblocks-pro"),items:h,onDelete:e=>{E(e),g(null)},onClose:t,confirmDelete:d,setConfirmDelete:p})),!d&&(0,e.createElement)(yn.LearnMore,{learnMoreLabel:(0,s.__)("Learn more about CSS Box Shadows","generateblocks-pro"),learnMoreURL:"https://developer.mozilla.org/en-US/docs/Web/CSS/box-shadow",onClose:t}))},(0,e.createElement)("div",{className:"gb-box-shadow"},(0,e.createElement)("div",{className:"gb-box-shadow__wrapper"},!y&&(0,e.createElement)("div",null,(0,e.createElement)(xn,{items:h,dragHandleLabel:(0,s.__)("Reorder Box Shadow","generateblocks-pro"),setItems:e=>{E(e)},itemComponent:function({item:t,index:r}){const n=hs([t]),o=n.length>0?n:(0,s.__)("Invalid box shadow","generateblocks-pro");return(0,e.createElement)(xn.Item,{css:o,swatchColor:t.color,canDuplicate:n.length>0,onDuplicate:()=>{const e=[...h,{...t}];g(e.length-1),E(e)},onEdit:()=>{g(r)}})},dragHandle:!0})),(0,e.createElement)(gs,{isEditing:y,currentOption:v,findBoxShadowIndex:S,setSettings:E}))),(0,e.createElement)(yn.PasteModal,{property:"box-shadow",errorMessage:c,showPasteStyles:l,setShowPasteStyles:i,onAddStyles:({pastedValue:e,replaceStyles:t})=>{if(!e||0===e.length)return!1;try{const r=ms.parse(ps(ds(e))),n=t?[...r]:[...h,...r];return E(n),i(!1),u(""),!0}catch(e){return u((0,s.__)("Error parsing pasted styles. Please check your CSS and try again.","generateblocks-pro")),!1}}})))}var vs=r(9456);function ys(e){return e?e.split(",").map((e=>{const t=document.createElement("div");if(t.style.transition=e,""===t.style.transition)return!1;const{transitionProperty:r="",transitionDuration:n="",transitionTimingFunction:o="",transitionDelay:a=""}=t.style;return{transitionProperty:r,transitionDuration:n,transitionTimingFunction:o,transitionDelay:a}})).filter(Boolean):[]}const ws=e=>e.replace(/transition/g,"").replace(/webkit/g,"").replace(/\r?\n|\r/g,"").replace(/;/g,"").replace(/:/g,"").trim(),ks=(e=[])=>e.reduce(((e,t)=>{if(t.hidden)return e;const{transitionProperty:r,transitionDuration:n,transitionDelay:o,transitionTimingFunction:a}=t,s=`${r} ${n} ${a} ${o}`.trim();return s.length?e.length>0?`${e}, ${s}`:`${s}`:e}),"").replace(/,$/,"").trim(),Es=(e="")=>{const t=e.match(/^cubic-bezier\((\d+(?:\.\d+)?),\s*(\d+(?:\.\d+)?),\s*(\d+(?:\.\d+)?),\s*(\d+(?:\.\d+)?)\)$/);if(t){const[,e,,r]=t;return e<0||e>1||r<0||r>1?[!1,(0,s.__)("Invalid cubic-bezier definition.","generateblocks-pro")]:[!0,""]}const r=e.match(/^steps\(([^,]+),\s*(.+)\)$/);if(r){const[,e,t]=r,n=parseInt(e,10),o=["jump-start","jump-end","jump-none","jump-both","start","end"];return isNaN(n)||n<1||!o.includes(t)?[!1,(0,s.__)("Invalid steps definition.","generateblocks-pro")]:[!0,""]}return e.startsWith("var(--")||["ease","linear","ease-in","ease-out","ease-in-out","initial","inherit"].includes(e)?[!0,""]:[!1,(0,s.__)("Unknown timing function.","generateblocks-pro")]},Ss=["s"];function xs({currentOption:t,findTransitionIndex:r,setSettings:n,cssPropertyError:a,cssTimingError:l}){const{styles:i,index:c}=r();if(t){var u;let e=!1;vs.all.includes(null!==(u=t.transitionProperty)&&void 0!==u?u:"")||(i[c].transitionProperty="all",e=!0),Es(t.transitionTimingFunction)||(i[c].transitionTimingFunction="ease",e=!0),e&&n(i)}return t&&(0,e.createElement)(wn,null,(0,e.createElement)(bn,{as:Hn,label:"Duration",units:Ss,value:t.transitionDuration,defaultUnitValue:"s",onChange:e=>{i[c].transitionDuration=e,n(i)},alwaysVisible:!0,allowOtherUnits:!1}),(0,e.createElement)(bn,{as:Hn,label:"Delay",units:Ss,value:t.transitionDelay,defaultUnitValue:"s",onChange:e=>{i[c].transitionDelay=e,n(i)},alwaysVisible:!0,allowOtherUnits:!1}),(0,e.createElement)(bn,{as:o.TextControl,label:"Transition Property",value:t.transitionProperty,onChange:e=>{i[c].transitionProperty=e,n(i)},help:a,alwaysVisible:!0}),(0,e.createElement)(bn,{alwaysVisible:!0,as:o.SelectControl,allowCustomValue:!0,label:"Timing Function",value:t.transitionTimingFunction,options:[{label:"Ease",value:"ease"},{label:"Linear",value:"linear"},{label:"Ease-in",value:"ease-in"},{label:"Ease-out",value:"ease-out"},{label:"Ease-in-out",value:"ease-in-out"},{label:"Step-start",value:"step-start"},{label:"Step-end",value:"step-end"},{label:"Initial",value:"initial"},{label:"Inherit",value:"inherit"}],help:l,customValueHelp:l||(0,s.__)("Enter any valid CSS timing function.","generateblocks-pro"),onChange:e=>{i[c].transitionTimingFunction=e,n(i)}}))}function Cs({onStyleChange:t,value:r}){var n;const[l,i]=(0,a.useState)(!1),[c,u]=(0,a.useState)(""),[d,p]=(0,a.useState)(!1),[h,m]=(0,a.useState)((()=>ys(r))),[f,g]=(0,a.useState)(null),[b,v]=(0,a.useState)(null),[y,w]=(0,a.useState)(null),E=(0,a.useRef)(!1),S=null!==(n=h[y])&&void 0!==n?n:null,x=null!==S;(0,a.useEffect)((()=>{E.current?E.current=!1:m(ys(r))}),[r]);const C=(0,k.useDebounce)(t,0),_=(0,a.useCallback)((function(e){E.current=!0,m(e),C("transition",ks(e))}),[C]),O=(0,a.useCallback)((()=>{const e=[...h];return{styles:e,index:e[y]?y:-1}}),[h,y]),{styles:T,index:I}=O();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(yn,{items:h,label:"Transition",cssProp:"transition",searchKeywords:["tween","timing","duration","delay","timing-function"],onAdd:()=>{const e=[...h,{transitionProperty:"all",transitionDuration:"0.5s",transitionDelay:"0s",transitionTimingFunction:"ease"}];w(e.length-1),_(e)},isEditing:x,onClickDelete:()=>{T.splice(I,1),_(T),w(null)},onClickDone:()=>{const e=Es(S.transitionTimingFunction);let t=!1;g(null),v(null),vs.all.includes(S.transitionProperty)||(g((0,s.__)("Please enter a valid CSS property!","generateblocks-pro")),t=!0),e[0]||(v(e[1]),t=!0),t||(g(null),v(null)),w(null)},dropdownChildren:({onClose:t})=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuGroup,{label:(0,s.__)("Options","generateblocks-pro")},!d&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuItem,{onClick:()=>{i(!0),t()}},(0,s.__)("Paste in transition styles","generateblocks-pro"))),(0,e.createElement)(yn.DeleteAll,{label:(0,s.__)("Delete all transition styles","generateblocks-pro"),content:(0,s.__)("This will delete all transition styles for the current selector. This operation cannot be undone.","generateblocks-pro"),items:h,onDelete:e=>{_(e),w(null)},onClose:t,confirmDelete:d,setConfirmDelete:p})),!d&&(0,e.createElement)(yn.LearnMore,{learnMoreLabel:(0,s.__)("Learn more about CSS Transitions","generateblocks-pro"),learnMoreURL:"https://developer.mozilla.org/en-US/docs/Web/CSS/transition",onClose:t}))},(0,e.createElement)("div",{className:"gb-transition"},(0,e.createElement)("div",{className:"gb-transition__wrapper"},!x&&(0,e.createElement)("div",null,(0,e.createElement)(xn,{items:h,dragHandleLabel:(0,s.__)("Reorder Transition","generateblocks-pro"),setItems:e=>{_(e)},itemComponent:function({item:t,index:r}){const n=ks([t]),o=n.length>0?n:(0,s.__)("Invalid transition","generateblocks-pro");return(0,e.createElement)(xn.Item,{css:o,swatchColor:t.color,onDuplicate:()=>{const e=[...h,{...t}];w(e.length-1),_(e)},onEdit:()=>{w(r)}})},dragHandle:!0})),(0,e.createElement)(xs,{currentOption:S,findTransitionIndex:O,setSettings:_,cssPropertyError:f,cssTimingError:b}))),(0,e.createElement)(yn.PasteModal,{property:"transition",errorMessage:c,showPasteStyles:l,setShowPasteStyles:i,onAddStyles:({pastedValue:e,replaceStyles:t})=>{if(!e||0===e.length)return!1;try{const r=ys(ws(e)).map((e=>{const{transitionProperty:t,transitionDuration:r,transitionDelay:n,transitionTimingFunction:o}=e;return{transitionProperty:t,transitionDuration:r,transitionDelay:n,transitionTimingFunction:o}})),n=t?[...r]:[...h,...r];return _(n),i(!1),u(""),!0}catch(e){return u((0,s.__)("Error parsing pasted styles. Please check your CSS and try again.","generateblocks-pro")),console.error(e),!1}}})))}function _s(e=[]){return e.reduce(((e,t)=>{if(t?.hidden)return e;const{type:r,value:n,dropShadow:{xOffset:o="",yOffset:a="",blur:s="",color:l=""}={}}=t;let i=`${r}(${n})`;return"drop-shadow"===r&&(i=`${r}(${o} ${a} ${s} ${l})`),e.length>0?`${e} ${i}`:`${i}`}),"").trim()}function Os(e){const t=e.match(/\b(blur|brightness|contrast|drop-shadow|grayscale|hue-rotate|invert|opacity|saturate|sepia)\(([^)]+)\)/g),r=[];return t?(t.forEach((e=>{const t=e.split("(")[0],n=e.match(/\(([^)]+)\)/)[1];if("drop-shadow"===t){const e=function(e){const t=e.split(" ").filter(Boolean);if(4!==t.length)return null;let r=t.pop();return/^#(?:[0-9a-fA-F]{3}){1,2}$/.test(r)||(r="#cccccc"),{xOffset:t[0],yOffset:t[1],blur:t[2],color:r}}(n);e&&r.push({type:"drop-shadow",dropShadow:e})}else(function(e){const t=document.createElement("div");return t.style.filter=e,""!==t.style.filter})(e)&&r.push({type:t,value:n,dropShadow:{}})})),Array.from(new Set(r.map(JSON.stringify))).map(JSON.parse)):[]}const Ts={blur:["px"],brightness:["%"],contrast:["%"],"drop-shadow":["px","em","rem","vw","vh","ch"],grayscale:["%"],"hue-rotate":["deg"],invert:["%"],opacity:["%"],saturate:["%"],sepia:["%"]},Is={blur:"5px",brightness:"100%",contrast:"100%","drop-shadow":{xOffset:"10px",yOffset:"10px",blur:"5px",color:"#cccccc"},grayscale:"100%","hue-rotate":"0deg",invert:"100%",opacity:"100%",saturate:"100%",sepia:"100%"},Rs={blur:"Blur",brightness:"Brightness",contrast:"Contrast","drop-shadow":"Drop Shadow",grayscale:"Grayscale","hue-rotate":"Hue Rotate",invert:"Invert",opacity:"Opacity",saturate:"Saturate",sepia:"Sepia"};function Ms({findFilterIndex:t,setSettings:r,transitionSettings:n,onStyleChange:a,currentOption:l=null}){const{styles:i,index:c}=t(),u=!n.some((e=>["all","filter"].includes(e.transitionProperty)));return l&&(0,e.createElement)(wn,null,(0,e.createElement)(bn,{as:o.SelectControl,label:(0,s.__)("Filter type","generateblocks-pro"),value:l.type,options:[{label:Rs.blur,value:"blur"},{label:Rs.brightness,value:"brightness"},{label:Rs.contrast,value:"contrast"},{label:Rs["drop-shadow"],value:"drop-shadow"},{label:Rs.grayscale,value:"grayscale"},{label:Rs["hue-rotate"],value:"hue-rotate"},{label:Rs.invert,value:"invert"},{label:Rs.opacity,value:"opacity"},{label:Rs.saturate,value:"saturate"},{label:Rs.sepia,value:"sepia"}],onChange:e=>{i[c].type=e,i[c].value=Is[e],r(i)},alwaysVisible:!0}),"blur"===l.type&&(0,e.createElement)(bn,{as:Hn,label:Rs.blur,units:Ts[l.type],value:l.value,onChange:e=>{i[c].value=e,r(i)},alwaysVisible:!0}),"brightness"===l.type&&(0,e.createElement)(bn,{as:Hn,label:Rs.brightness,units:Ts[l.type],value:l.value,onChange:e=>{i[c].value=e,r(i)},alwaysVisible:!0}),"contrast"===l.type&&(0,e.createElement)(bn,{as:Hn,label:Rs.contrast,units:Ts[l.type],value:l.value,onChange:e=>{i[c].value=e,r(i)},alwaysVisible:!0}),"drop-shadow"===l.type&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(bn,{as:Hn,allowCustomValue:!0,label:"X-Offset",units:Ts[l.type],value:l.dropShadow.xOffset[0],onChange:e=>{i[c].dropShadow.xOffset=e,r(i)},alwaysVisible:!0}),(0,e.createElement)(bn,{as:Hn,allowCustomValue:!0,label:"Y-Offset",units:Ts[l.type],value:l.dropShadow.yOffset[0],onChange:e=>{i[c].dropShadow.yOffset=e,r(i)},alwaysVisible:!0}),(0,e.createElement)(bn,{as:Hn,allowCustomValue:!0,label:"Blur",units:Ts[l.type],value:l.dropShadow.blur[0],onChange:e=>{i[c].dropShadow.blur=e,r(i)},alwaysVisible:!0}),(0,e.createElement)(bn,{as:ba,value:l.dropShadow.color,label:"Color",tooltip:(0,s.__)("Filter color","generateblocks-pro"),onChange:e=>{i[c].dropShadow.color=e,r(i)},alwaysVisible:!0})),"grayscale"===l.type&&(0,e.createElement)(bn,{as:Hn,label:Rs.grayscale,allowCustomValue:!0,units:Ts[l.type],value:l.value,onChange:e=>{i[c].value=e,r(i)},alwaysVisible:!0}),"hue-rotate"===l.type&&(0,e.createElement)(bn,{as:Hn,label:Rs["hue-rotate"],allowCustomValue:!0,units:Ts[l.type],value:l.value,onChange:e=>{i[c].value=e,r(i)},alwaysVisible:!0}),"invert"===l.type&&(0,e.createElement)(bn,{as:Hn,label:Rs.invert,units:Ts[l.type],value:l.value,onChange:e=>{i[c].value=e,r(i)},alwaysVisible:!0}),"opacity"===l.type&&(0,e.createElement)(bn,{as:Hn,label:Rs.opacity,units:Ts[l.type],value:l.value,onChange:e=>{i[c].value=e,r(i)},alwaysVisible:!0}),"saturate"===l.type&&(0,e.createElement)(bn,{as:Hn,label:Rs.saturate,units:Ts[l.type],value:l.value,onChange:e=>{i[c].value=e,r(i)},alwaysVisible:!0}),"sepia"===l.type&&(0,e.createElement)(bn,{as:Hn,label:Rs.sepia,units:Ts[l.type],value:l.value,onChange:e=>{i[c].value=e,r(i)}}),u&&(0,e.createElement)(o.Button,{style:{marginTop:"10px"},label:(0,s.__)("Automatically add a smooth transition for filters","generateblocks-pro"),showTooltip:!0,variant:"secondary",size:"compact",icon:h,onClick:()=>{const e=[...n,{transitionProperty:"all",transitionDuration:"0.5s",transitionDelay:"0s",transitionTimingFunction:"ease",transitionProperty:"filter"}];a("transition",ks(e),e)}},"Transition"))}function Ps({onStyleChange:t,value:r="",transition:n=""}){var l;const i=(0,a.useMemo)((()=>ys(n)),[n]),[c,u]=(0,a.useState)(!1),[d,p]=(0,a.useState)(""),[h,m]=(0,a.useState)(!1),[f,g]=(0,a.useState)((()=>Os(r))),[b,v]=(0,a.useState)(null),y=(0,a.useRef)(!1),w=null!==(l=f[b])&&void 0!==l?l:null,E=(0,a.useMemo)((()=>null!==w),[w]);(0,a.useEffect)((()=>{y.current?y.current=!1:g(Os(r))}),[r]);const S=(0,k.useDebounce)(t,0),x=(0,a.useCallback)((function(e){y.current=!0,g(e),S("filter",_s(e))}),[S]),C=(0,a.useCallback)((()=>{const e=[...f];return{styles:e,index:e[b]?b:-1}}),[f,b]),{styles:_,index:O}=C();return(0,e.createElement)(yn,{items:f,label:"Filter",cssProp:"filter",isEditing:E,onClickDelete:()=>{_.splice(O,1),x(_),v(null)},onClickDone:()=>{v(null)},dropdownChildren:({onClose:t})=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuGroup,{label:(0,s.__)("Options","generateblocks-pro")},!h&&(0,e.createElement)(o.MenuItem,{onClick:()=>{u(!0),t()}},(0,s.__)("Add via paste…","generateblocks-pro")),(0,e.createElement)(yn.DeleteAll,{label:(0,s.__)("Delete all filter styles","generateblocks-pro"),content:(0,s.__)("This will delete all filter styles for the current selector. This operation cannot be undone.","generateblocks-pro"),items:f,onDelete:e=>{x(e),v(null)},onClose:t,confirmDelete:h,setConfirmDelete:m})),!h&&(0,e.createElement)(yn.LearnMore,{learnMoreLabel:(0,s.__)("Learn more about CSS Filters","generateblocks-pro"),learnMoreURL:"https://developer.mozilla.org/en-US/docs/Web/CSS/filter",onClose:t})),onAdd:()=>{const e=[...f,{type:"blur",value:"5px",dropShadow:{xOffset:"10px",yOffset:"10px",blur:"5px",color:"#cccccc"}}];v(e.length-1),x(e)},learnMoreLabel:(0,s.__)("Learn more about CSS Filters","generateblocks-pro"),learnMoreURL:"https://developer.mozilla.org/en-US/docs/Web/CSS/filter"},(0,e.createElement)("div",{className:"gb-filter"},(0,e.createElement)("div",{className:"gb-filter__wrapper"},!E&&(0,e.createElement)("div",null,(0,e.createElement)(xn,{items:f,dragHandleLabel:(0,s.__)("Reorder Filter","generateblocks-pro"),setItems:e=>{x(e)},itemComponent:function({item:t,index:r}){const n=_s([t]),o=n.length>0?n:(0,s.__)("Invalid transform","generateblocks-pro");return(0,e.createElement)(xn.Item,{css:o,swatchColor:"drop-shadow"===t.type&&t?.dropShadow?.color,onDuplicate:()=>{const e=[...f,{...t}];v(e.length-1),x(e)},onEdit:()=>{v(r)}})},dragHandle:!0})),E&&(0,e.createElement)(Ms,{currentOption:w,findFilterIndex:C,settings:f,setSettings:x,transitionSettings:i,onStyleChange:t}))),(0,e.createElement)(yn.PasteModal,{property:"filter",errorMessage:d,showPasteStyles:c,setShowPasteStyles:u,onAddStyles:({pastedValue:e,replaceStyles:t})=>{if(!e||0===e.length)return!1;try{const r=Os(e),n=t?[...r]:[...f,...r];return x(n),u(!1),p(""),!0}catch(e){return p((0,s.__)("Error parsing pasted styles. Please check your CSS and try again.","generateblocks-pro")),!1}}}))}const As=e=>e.replace(/-webkit-text-shadow:[^;]+;|-moz-text-shadow:[^;]+;/g,""),Ls=e=>e.replace(/text-shadow/g,"").replace(/webkit/g,"").replace(/\r?\n|\r/g,"").replace(/;/g,"").replace(/:/g,"").trim(),Ns=(e=[])=>e.reduce(((e,t)=>{if(t.hidden)return e;const{offsetX:r="",offsetY:n="",blur:o="",color:a=""}=t,s=`${a} ${r} ${n} ${o}`.trim();return s.trim().length?e.length>0?`${e}, ${s}`:`${s}`:e}),"").replace(/,$/,"").trim(),zs=function(){const e=/,(?![^\(]*\))/,t=/\s(?![^(]*\))/,r=/^[0-9]+[a-zA-Z%]+$/,n=e=>{const r=e.split(t),n=a(r[0])?void 0:r[0],o=r.filter((e=>e!==n)),[s,l,i]=o;return{offsetX:s,offsetY:l,blur:i,color:n}},o=e=>{const{offsetX:t=0,offsetY:r=0,blur:n=0,color:o}=e||{};return[o,t,r,n].filter((e=>null!=e)).map((e=>(""+e).trim())).join(" ")},a=e=>r.test(e);return{parse:t=>t?t.split(e).map((e=>e.trim())).map(n):[],stringify:e=>e?.length?e.map(o).join(", "):""}}(),Ds=["px","em","rem","vw","vh","ch"];function Fs({findTextShadowIndex:t,setSettings:r,currentOption:n=null}){const{styles:o,index:a}=t();return n&&(0,e.createElement)(wn,null,(0,e.createElement)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"}},(0,e.createElement)(bn,{alwaysVisible:!0,as:ba,value:n?.color||"rgba(0,0,0,1)",tooltip:"Text Shadow Color",onChange:e=>{o[a].color=e,r(o)}})),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,label:"x-Offset",units:Ds,value:n.offsetX,onChange:e=>{o[a].offsetX=e,r(o)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,label:"y-Offset",units:Ds,value:n.offsetY,onChange:e=>{o[a].offsetY=e,r(o)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,label:"blur",units:Ds,value:n?.blur,onChange:e=>{o[a].blur=e,r(o)}}))}function Bs({onStyleChange:t,value:r}){var n;const[l,i]=(0,a.useState)(!1),[c,u]=(0,a.useState)(""),[d,p]=(0,a.useState)(!1),[h,m]=(0,a.useState)((()=>zs.parse(r))),[f,g]=(0,a.useState)(null),b=(0,a.useRef)(!1),v=null!==(n=h[f])&&void 0!==n?n:null,y=null!==v;(0,a.useEffect)((()=>{b.current?b.current=!1:m(zs.parse(r))}),[r]);const w=(0,k.useDebounce)(t,0),E=(0,a.useCallback)((function(e){b.current=!0,m(e),w("textShadow",Ns(e))}),[w]),S=(0,a.useCallback)((()=>{const e=[...h];return{styles:e,index:e[f]?f:-1}}),[h,f]),{styles:x,index:C}=S();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(yn,{items:h,label:"Text Shadow",searchKeywords:["shadows","text-shadow"],cssProp:"textShadow",onAdd:()=>{const e=[...h,{offsetX:"1px",offsetY:"1px",blur:"2px",color:"rgba(0,0,0,1)"}];g(e.length-1),E(e)},isEditing:y,onClickDelete:()=>{x.splice(C,1),E(x),g(null)},onClickDone:()=>{g(null)},dropdownChildren:({onClose:t})=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuGroup,{label:(0,s.__)("Options","generateblocks-pro")},!d&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuItem,{onClick:()=>{i(!0),t()}},(0,s.__)("Add via paste…","generateblocks-pro"))),(0,e.createElement)(yn.DeleteAll,{label:(0,s.__)("Delete all text shadow styles","generateblocks-pro"),content:(0,s.__)("This will delete all text shadow styles for the current selector. This operation cannot be undone.","generateblocks-pro"),items:h,onDelete:e=>{E(e),g(null)},onClose:t,confirmDelete:d,setConfirmDelete:p})),!d&&(0,e.createElement)(yn.LearnMore,{learnMoreLabel:(0,s.__)("Learn more about CSS Text Shadows","generateblocks-pro"),learnMoreURL:"https://developer.mozilla.org/en-US/docs/Web/CSS/text-shadow",onClose:t}))},(0,e.createElement)("div",{className:"gb-text-shadow"},(0,e.createElement)("div",{className:"gb-text-shadow__wrapper"},!y&&(0,e.createElement)("div",null,(0,e.createElement)(xn,{items:h,dragHandleLabel:(0,s.__)("Reorder Text Shadow","generateblocks-pro"),setItems:e=>{E(e)},itemComponent:function({item:t,index:r}){const n=Ns([t]),o=n.length>0?n:(0,s.__)("Invalid text shadow","generateblocks-pro");return(0,e.createElement)(xn.Item,{css:o,swatchColor:t.color,canDuplicate:n.length>0,onDuplicate:()=>{const e=[...h,{...t}];g(e.length-1),E(e)},onEdit:()=>{g(r)}})},dragHandle:!0})),(0,e.createElement)(Fs,{isEditing:y,currentOption:v,findTextShadowIndex:S,setSettings:E}))),(0,e.createElement)(yn.PasteModal,{property:"text-shadow",errorMessage:c,showPasteStyles:l,setShowPasteStyles:i,onAddStyles:({pastedValue:e,replaceStyles:t})=>{if(!e||0===e.length)return!1;try{const r=zs.parse(Ls(As(e))),n=t?[...r]:[...h,...r];return E(n),i(!1),u(""),!0}catch(e){return u((0,s.__)("Error parsing pasted styles. Please check your CSS and try again.","generateblocks-pro")),!1}}})))}const js=["perspective","rotate","rotate3d","scale","scale3d","skew","translate3d"];function Vs(e=[]){return e.reduce(((e,t)=>{const{hidden:r=!1,values:n=[],type:o=""}=t,a=`${o}(${n.join(", ").trim()})`;return r?e:a?e.length>0?`${e} ${a}`:`${a}`:e}),"").trim()}function Ws(e=""){const t=function(e){const t=document.createElement("div");return t.style.transform=e,t.style.transform}(e);if(!t)return null;if(["unset","inherit","initial"].includes(t))return{[t]:!0};const r=t.replaceAll(/var\(([^)]+)\)/g,"$1").match(/([A-z\d]+)\(([^)]+)\)/g);return r?r.reduce(((e,t)=>{if(!t)return e;const[r,n]=t.match(/(^[a-z\d]*)|\(([^)]+)\)/g),o=n.replace("(","").replace(")","").split(",").map((e=>{const t=e.trim();return t.startsWith("--")?`var(${t})`:t}));return{...e,[r]:{type:r,values:o,cssText:`${r}${n}`}}}),{}):null}const Us={perspective:["px","em","%","vh","vw"],rotate:["deg"],rotate3d:["deg"],scale:[],scale3d:[],skew:["deg"],translate3d:["px","em","%","vh","vw"]},Hs={perspective:{cssText:"perspective(0px)",values:["0px"]},rotate:{cssText:"rotate(0deg)",values:["0deg"]},scale:{cssText:"scale(1)",values:["1"]},scale3d:{cssText:"scale3d(1, 1, 1)",values:["1","1","1"]},skew:{cssText:"skew(0deg)",values:["0deg"]},translate3d:{cssText:"translate3d(0px, 0px, 0px)",values:["0px","0px","0px"]},rotate3d:{cssText:"rotate3d(0, 0, 0, 0deg)",values:["0","0","0","0deg"]},custom:{cssText:"",values:[""]}},$s={perspective:"Perspective",translate3d:"Translate",rotate:"Rotate",rotate3d:"Rotate (3d)",scale:"Scale",scale3d:"Scale (3d)",skew:"Skew",custom:(0,s.__)("Custom","generateblocks-pro")},qs=(0,s.__)("Enter any valid transform value here.","generateblocks-pro");function Gs({findTransformIndex:t,setSettings:r,settings:n,onStyleChange:l,setNextEditIndex:i,currentOption:c=null,transitionSettings:u=[]}){var d,p;const[m,f]=(0,a.useState)(qs),{styles:g,index:b}=t(),v=null!==(d=c?.values)&&void 0!==d?d:[],y=null!==(p=c?.cssText)&&void 0!==p?p:"";let w=c?.type||"custom";const k=!u.some((e=>["all","transform"].includes(e.transitionProperty))),E=!js.includes(w);return E&&"custom"!==w&&(w="custom"),c&&(0,e.createElement)(wn,null,(0,e.createElement)(bn,{alwaysVisible:!0,as:o.SelectControl,label:(0,s.__)("Transform type","generateblocks-pro"),value:w,options:[{label:$s.perspective,value:"perspective",disabled:n.some((e=>"perspective"===e.type))},{label:$s.rotate,value:"rotate"},{label:$s.rotate3d,value:"rotate3d"},{label:$s.translate3d,value:"translate3d"},{label:$s.scale,value:"scale"},{label:$s.scale3d,value:"scale3d"},{label:$s.skew,value:"skew"},{label:$s.custom,value:"custom"}],onChange:e=>{g[b].type=e,"custom"!==e&&(g[b]={...g[b],...Hs[e]}),"perspective"===e&&b>0&&(g.unshift(g.splice(b,1)[0]),i(0)),r(g)}}),E&&(0,e.createElement)(bn,{alwaysVisible:!0,as:o.TextControl,label:$s[w],help:m||qs,placeholder:"e.g. rotate(45deg)",onChange:(e="")=>{if(""===e)return g[b].values=[""],g[b].cssText="",r(g),void f(qs);const t=Ws(e);if(null===t)return void f((0,s.__)("Invalid value"));if(Object.keys(t).length>1)return void f((0,s.__)("Only one transform value is allowed.","generateblocks-pro"));m!==qs&&f(qs);const n=Object.keys(t)[0];g[b]={type:n,...t[n]},r(g)},value:y}),"translate3d"===w&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:Us[w],label:`${$s[w]} X`,value:v[0],onChange:e=>{g[b].values[0]=e,r(g)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:Us[w],label:`${$s[w]} Y`,value:v[1],onChange:e=>{g[b].values[1]=e,r(g)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:Us[w],label:`${$s[w]} Z`,value:v[2],onChange:e=>{g[b].values[2]=e,r(g)}})),["rotate","skew"].includes(w)&&(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:Us[w],label:$s[w],value:v[0],onChange:(e=0)=>{g[b].values[0]=e,r(g)}}),"scale"===w&&(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:Us[w],label:$s[w],value:v[0],onChange:e=>{g[b].values[0]=e,r(g)}}),"perspective"===w&&(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:Us[w],label:$s[w],value:v[0],onChange:e=>{g[b].values[0]=e,r(g)}}),"rotate3d"===w&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:[],label:`${$s[w]} X`,value:v[0],onChange:e=>{g[b].values[0]=e,r(g)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:[],label:`${$s[w]} Y`,value:v[1],onChange:e=>{g[b].values[1]=e,r(g)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:[],label:`${$s[w]} Z`,value:v[2],onChange:e=>{g[b].values[2]=e,r(g)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:Us[w],label:`${$s[w]} Angle`,value:v[3],onChange:e=>{g[b].values[3]=e,r(g)}})),"scale3d"===w&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:Us[w],label:`${$s[w]} X`,value:v[0],onChange:e=>{g[b].values[0]=e,r(g)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:Us[w],label:`${$s[w]} Y`,value:v[1],onChange:e=>{g[b].values[1]=e,r(g)}}),(0,e.createElement)(bn,{alwaysVisible:!0,as:Hn,units:Us[w],label:`${$s[w]} Z`,value:v[2],onChange:e=>{g[b].values[2]=e,r(g)}})),k&&(0,e.createElement)(o.Button,{label:(0,s.__)("Automatically add a smooth transition for transforms","generateblocks-pro"),showTooltip:!0,variant:"secondary",size:"compact",icon:h,onClick:()=>{const e=[...u,{transitionProperty:"all",transitionDuration:"0.5s",transitionDelay:"0s",transitionTimingFunction:"ease",transitionProperty:"transform"}],t=ks(e);l("transition",t)}},"Transition"))}function Zs(e){if(!e)return[];const t=Ws(e);return null===t?[]:Object.entries(t).map((([e,t])=>{const{disabled:r=!1}=t;return{type:e,...t,id:on(),disabled:"perspective"===e||r}}))}function Ks({onStyleChange:t,value:r="",transition:n=""}){var l;const i=(0,a.useMemo)((()=>ys(n)),[n]),[c,u]=(0,a.useState)((()=>Zs(r))),[d,p]=(0,a.useState)(!1),[h,m]=(0,a.useState)(""),[f,g]=(0,a.useState)(!1),[b,v]=(0,a.useState)(null),y=(0,a.useRef)(!1),w=null!==(l=c[b])&&void 0!==l?l:null,E=null!==w;(0,a.useEffect)((()=>{y.current?y.current=!1:u(Zs(r))}),[r]);const S=(0,k.useDebounce)(t,0),x=(0,a.useCallback)((function(e){y.current=!0,u(e),S("transform",Vs(e))}),[S]),C=(0,a.useCallback)((()=>{const e=[...c];return{styles:e,index:e[b]?b:-1}}),[c,b]),{styles:_,index:O}=C();return(0,e.createElement)(yn,{items:c,label:"Transform",isEditing:E,cssProp:"transform",onClickDelete:()=>{_.splice(O,1),x(_),v(null)},onClickDone:()=>{v(null)},dropdownChildren:({onClose:t})=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(o.MenuGroup,{label:(0,s.__)("Options","generateblocks-pro")},!f&&(0,e.createElement)(o.MenuItem,{onClick:()=>{p(!0),t()}},(0,s.__)("Add via paste…","generateblocks-pro")),(0,e.createElement)(yn.DeleteAll,{label:(0,s.__)("Delete all transform styles","generateblocks-pro"),content:(0,s.__)("This will delete all transform styles for the current selector. This operation cannot be undone.","generateblocks-pro"),items:c,onDelete:e=>{x(e),v(null)},onClose:t,confirmDelete:f,setConfirmDelete:g})),!f&&(0,e.createElement)(yn.LearnMore,{learnMoreLabel:(0,s.__)("Learn more about CSS Transforms","generateblocks-pro"),learnMoreURL:"https://developer.mozilla.org/en-US/docs/Web/CSS/transform",onClose:t})),onAdd:()=>{const e=[...c,{type:"translate3d",values:["0px","0px","0px"],cssText:"translate3d(0px, 0px, 0px)"}];v(e.length-1),x(e)},learnMoreLabel:(0,s.__)("Learn more about CSS Transforms","generateblocks-pro"),learnMoreURL:"https://developer.mozilla.org/en-US/docs/Web/CSS/transform"},(0,e.createElement)("div",{className:"gb-transform"},(0,e.createElement)("div",{className:"gb-transform__wrapper"},!E&&(0,e.createElement)("div",null,(0,e.createElement)(xn,{items:c,dragHandleLabel:(0,s.__)("Reorder Transform","generateblocks-pro"),setItems:e=>{x(e.map((e=>({...e,disabled:"perspective"===e.type}))))},itemComponent:function({item:t,index:r}){const n=Vs([t]),o=n.length>0?n:(0,s.__)("Invalid transform","generateblocks-pro"),{type:a,color:l}=t;return(0,e.createElement)(xn.Item,{css:o,swatchColor:l,canDuplicate:"perspective"!==a,onDuplicate:()=>{const e=[...c,{...t}];v(e.length-1),x(e)},onEdit:()=>{v(r)}})},dragHandle:!0})),E&&(0,e.createElement)(Gs,{isEditing:E,currentOption:w,findTransformIndex:C,settings:c,setSettings:x,transitionSettings:i,onStyleChange:t,setNextEditIndex:v}))),(0,e.createElement)(yn.PasteModal,{property:"transform",errorMessage:h,showPasteStyles:d,setShowPasteStyles:p,onAddStyles:({pastedValue:e,replaceStyles:t})=>{if(!e||0===e.length)return!1;try{const r=Zs(e),n=t?[...r]:[...c,...r],o=n.findIndex((({type:e})=>"perspective"===e));return o>0&&n.unshift(n.splice(o,1)[0]),x(n),p(!1),m(""),!0}catch(e){return m((0,s.__)("Error parsing pasted styles. Please check your CSS and try again.","generateblocks-pro")),!1}}}))}function Ys(t){const{styles:r,onStyleChange:n,atRule:a,nestedRule:l,opened:i,scrollAfterOpen:c,onToggle:u,initialOpen:d}=t,{backdropFilter:p="",boxShadow:h="",filter:m="",mixBlendMode:f="",opacity:g="",transform:v="",transformOrigin:y="",transition:w="",visibility:k="",textShadow:E=""}=r;let S=parseFloat(g);return isNaN(S)&&(S=g),(0,e.createElement)(o.PanelBody,{title:(0,s.__)("Effects","generateblocks-pro"),initialOpen:d,icon:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Ur,{size:"20"}),(0,e.createElement)(Kn,{cssProps:tt})),open,opened:!!i||void 0,scrollAfterOpen:c,onToggle:u},(0,e.createElement)(b.Stack,{gap:"12px",className:"gb-styles-builder-panel__content"},(0,e.createElement)(bs,{value:h,onStyleChange:n,atRule:a,nestedRule:l}),(0,e.createElement)(Cs,{value:w,onStyleChange:n,atRule:a,nestedRule:l}),(0,e.createElement)(Ps,{value:m,transition:w,onStyleChange:n,atRule:a,nestedRule:l}),(0,e.createElement)(Bs,{value:E,onStyleChange:n,atRule:a,nestedRule:l}),(0,e.createElement)(Ks,{value:v,transformOrigin:y,transition:w,onStyleChange:n,atRule:a,nestedRule:l}),(0,e.createElement)(bn,{as:o.TextControl,label:"Transform Origin",value:y,placeholder:"ex: center, 50% -100%, etc.",onChange:e=>n("transformOrigin",e),cssProp:"transformOrigin",learnMoreLabel:(0,s.__)("Learn more about transform origin","generateblocks-pro"),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/transform-origin"}),(0,e.createElement)(us,{onStyleChange:n,opacity:g}),(0,e.createElement)(bn,{allowCustomValue:!0,as:o.SelectControl,label:"Visibility",value:k,cssProp:"visibility",options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"Visible",value:"visible"},{label:"Hidden",value:"hidden"}],onChange:e=>n("visibility",e),help:(0,s.__)('Using the "hidden" value may cause some block editor elements to become invisible.',"generateblocks-pro")}),(0,e.createElement)(bn,{as:o.SelectControl,allowCustomValue:!0,label:"Mix Blend Mode",value:f,cssProp:"mixBlendMode",options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"Normal",value:"normal"},{label:"Multiply",value:"multiply"},{label:"Screen",value:"screen"},{label:"Overlay",value:"overlay"},{label:"Darken",value:"darken"},{label:"Lighten",value:"lighten"},{label:"Color Dodge",value:"color-dodge"},{label:"Color Burn",value:"color-burn"},{label:"Hard Light",value:"hard-light"},{label:"Soft Light",value:"soft-light"},{label:"Difference",value:"difference"},{label:"Exclusion",value:"exclusion"},{label:"Hue",value:"hue"},{label:"Saturation",value:"saturation"},{label:"Color",value:"color"},{label:"Luminosity",value:"luminosity"}],onChange:e=>n("mixBlendMode",e)}),(0,e.createElement)(bn,{as:o.TextControl,label:"Backdrop Filter",value:p,cssProp:"backdropFilter",onChange:e=>n("backdropFilter",e)})))}function Qs(t){const{styles:r,onStyleChange:n,opened:a,scrollAfterOpen:l,onToggle:i,initialOpen:c}=t,{objectFit:u="",objectPosition:d=""}=r;return(0,e.createElement)(o.PanelBody,{title:(0,s.__)("Media","generateblocks-pro"),initialOpen:c,icon:(0,e.createElement)(e.Fragment,null,(0,e.createElement)($r,{size:"20"}),(0,e.createElement)(Kn,{cssProps:rt})),opened:!!a||void 0,scrollAfterOpen:l,onToggle:i},(0,e.createElement)(b.Stack,{gap:"12px",className:"gb-styles-builder-panel__content"},(0,e.createElement)(bn,{as:o.SelectControl,label:"Object Fit",id:"gblocks-object-fit",allowCustomValue:!0,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"fill",value:"fill"},{label:"contain",value:"contain"},{label:"cover",value:"cover"},{label:"none",value:"none"},{label:"scale-down",value:"scale-down"}],value:u,onChange:e=>n("objectFit",e),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro"),cssProp:"objectFit"}),(0,e.createElement)(bn,{as:o.TextControl,label:"Object Position",id:"gblocks-object-position",value:d,onChange:e=>n("objectPosition",e),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/object-position",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro"),cssProp:"objectPosition"})))}const Xs=(0,e.createElement)(l.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(l.Path,{d:"M3 6v11.5h8V6H3Zm11 3h7V7.5h-7V9Zm7 3.5h-7V11h7v1.5ZM14 16h7v-1.5h-7V16Z"}));function Js(t){const{styles:r,onStyleChange:n,opened:a,scrollAfterOpen:l,onToggle:i,initialOpen:c}=t,{listStyleType:u="",listStyleImage:p="",listStylePosition:h="",display:m=""}=r;return(0,e.createElement)(o.PanelBody,{title:(0,s.__)("Lists","generateblocks-pro"),initialOpen:c,icon:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(d,{icon:Xs,size:"20"}),(0,e.createElement)(Kn,{cssProps:nt})),opened:!!a||void 0,scrollAfterOpen:l,onToggle:i},(0,e.createElement)(b.Stack,{gap:"12px",className:"gb-styles-builder-panel__content"},(0,e.createElement)(bn,{as:o.SelectControl,label:"List Style Type",id:"gblocks-list-style-type",options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"none",value:"none"},{label:"disc",value:"disc"},{label:"circle",value:"circle"},{label:"square",value:"square"},{label:"decimal",value:"decimal"},{label:"decimal-leading-zero",value:"decimal-leading-zero"},{label:"lower-roman",value:"lower-roman"},{label:"upper-roman",value:"upper-roman"},{label:"lower-greek",value:"lower-greek"}],value:u,cssProp:"listStyleType",allowCustomValue:!0,onChange:e=>n("listStyleType",e)}),(0,e.createElement)(bn,{as:o.TextControl,label:"List Style Image",id:"gblocks-list-style-image",value:p,allowCustomValue:!0,cssProp:"listStyleImage",onChange:e=>n("listStyleImage",e),help:(0,s.__)("The URL of an image to be used as the list item marker.","generateblocks-pro")}),(0,e.createElement)(bn,{id:"gblocks-list-style-position",label:"List Style Position",as:o.SelectControl,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"Inside",value:"inside"},{label:"Outside",value:"outside"}],help:(0,s.__)("The position of the list item marker.","generateblocks-pro"),allowCustomValue:!0,value:h,cssProp:"listStylePosition",onChange:e=>n("listStylePosition",e)}),!["","list-item"].includes(m)&&(0,e.createElement)(o.Notice,{status:"warning",isDismissible:!1},(0,s.__)("List properties will not apply unless the element's display property is set to list-item","generateblocks-pro"))))}function el(t){const{styles:r,onStyleChange:n,nestedRule:a,opened:l,scrollAfterOpen:i,onToggle:c,initialOpen:u}=t,{content:d="",pointerEvents:p="",fill:h="",stroke:m="",cursor:f=""}=r;return(0,e.createElement)(o.PanelBody,{title:(0,s.__)("More","generateblocks-pro"),initialOpen:u,icon:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(qr,{size:"20"}),(0,e.createElement)(Kn,{cssProps:ot})),opened:!!l||void 0,scrollAfterOpen:i,onToggle:c},(0,e.createElement)(b.Stack,{gap:"12px",className:"gb-styles-builder-panel__content"},(a.includes(":before")||a.includes(":after"))&&(0,e.createElement)(bn,{as:o.TextControl,label:"Content",id:"gblocks-content",value:d,cssProp:"content",onChange:e=>n("content",e),help:(0,s.__)("The content property is used with the ::before and ::after pseudo-elements and must be wrapped in quotes.","generateblocks-pro")}),(0,e.createElement)(bn,{as:o.SelectControl,label:"Pointer Events",id:"gblocks-pointer-events",allowCustomValue:!0,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"auto",value:"auto"},{label:"none",value:"none"}],value:p,cssProp:"pointerEvents",onChange:e=>n("pointerEvents",e),dropdownChildren:({onClose:t})=>(0,e.createElement)(bn.Description,{label:(0,s.__)("About Pointer Events","generateblocks-pro"),onClick:t},(0,s.__)("Use this property to control whether an element can be the target for click/keyboard events. See the documentation link below for a complete list (note: some only apply to SVG tags).","generateblocks-pro")),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/pointer-events",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro")}),(0,e.createElement)(bn,{as:o.SelectControl,label:"Cursor",id:"gblocks-cursor",allowCustomValue:!0,options:[{label:(0,s.__)("Default","generateblocks-pro"),value:""},{label:"auto",value:"auto"},{label:"inherit",value:"inherit"},{label:"none",value:"none"},{value:"pointer",label:"pointer"},{value:"not-allowed",label:"not-allowed"},{value:"grab",label:"grab"},{value:"grabbing",label:"grabbing"},{value:"help",label:"help"}],value:f,cssProp:"cursor",onChange:e=>n("cursor",e),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/cursor",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro")}),(0,e.createElement)(bn,{as:ba,label:"Fill Color",id:"gblocks-fill",allowCustomValue:!0,value:h,cssProp:"fill",onChange:e=>n("fill",e),dropdownChildren:({onClose:t})=>(0,e.createElement)(bn.Description,{label:(0,s.__)("About Fill","generateblocks-pro"),onClick:t},(0,s.__)("Set the color of an svg element or svg child element.","generateblocks-pro")),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/fill",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro")}),(0,e.createElement)(bn,{as:ba,label:"Stroke Color",id:"gblocks-stroke",allowCustomValue:!0,value:m,cssProp:"stroke",onChange:e=>n("stroke",e),dropdownChildren:({onClose:t})=>(0,e.createElement)(bn.Description,{label:(0,s.__)("About Stroke","generateblocks-pro"),onClick:t},(0,s.__)("Set the stroke (border) color of an svg element or svg child element.","generateblocks-pro")),learnMoreUrl:"https://developer.mozilla.org/en-US/docs/Web/CSS/stroke",learnMoreLabel:(0,s.__)("Learn more about this property.","generateblocks-pro")})))}const tl={controls:"UpY27ESKG0MH0I3zY4y3",noResults:"cw8TTcVKYQowFKNIpJnj",filtersActive:"R6D4mxNKTdE4e0Fh7EaO",addMargin:"dv6Uo2lTG7l7hn1_XAfL",vertical:"QE0fl2OWZTpvOzxsPRyo",isSearching:"azjtFI8yAKasaqqYnToD"};class rl extends a.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(e,t){console.error(e,t.componentStack)}render(){return this.state.hasError?this.props.fallback:this.props.children}}function nl({styles:t,settings:r,onStyleChange:n,nestedRule:l,atRule:i,deviceType:c,filters:u,dispatchFilters:d,scope:p="",allowCustomAtRule:h,currentSelector:m}){var f,b;const v=(0,a.useMemo)((()=>Object.values(t).filter((e=>"string"==typeof e)).length>0),[t]),y=!!u.activeFilter,w={styles:t,settings:r,onStyleChange:n,nestedRule:l,atRule:i,deviceType:c,filtersActive:y,filter:null!==(f=u.activeFilter)&&void 0!==f?f:"",search:null!==(b=u.search)&&void 0!==b?b:"",scope:p,allowCustomAtRule:h,currentSelector:m};return(0,e.createElement)("div",{className:g("gb-inspector-controls",tl.controls,(u.search||y)&&"gb-inspector-controls--filtering")},(0,e.createElement)(rl,{fallback:(0,s.__)("An error has occured, please reload and try again","generateblocks-pro")},!!u.search&&(0,e.createElement)(o.Notice,{className:g(tl.isSearching),isDismissible:!0,onDismiss:()=>{d({type:"RESET"})}},(0,s.sprintf)(/* translators: %s: search term */ /* translators: %s: search term */
(0,s.__)("Searching for: %s","generateblocks-pro"),u.search)),(0,e.createElement)(Yn,{id:"layout",panel:ro,...w}),(0,e.createElement)(Yn,{id:"sizing",panel:oo,...w}),(0,e.createElement)(Yn,{id:"spacing",panel:fo,...w}),(0,e.createElement)(Yn,{id:"typography",panel:Ua,...w}),(0,e.createElement)(Yn,{id:"backgrounds",panel:ns,...w}),(0,e.createElement)(Yn,{id:"borders",panel:is,...w}),(0,e.createElement)(Yn,{id:"position",panel:cs,...w}),(0,e.createElement)(Yn,{id:"effects",panel:Ys,...w}),(0,e.createElement)(Yn,{id:"media",panel:Qs,...w}),(0,e.createElement)(Yn,{id:"lists",panel:Js,...w}),(0,e.createElement)(Yn,{id:"more",panel:el,...w}),!v&&"show-with-value"===u.activeFilter&&(0,e.createElement)(o.Notice,{className:g(tl.addMargin,tl.vertical),isDismissible:!1},(0,s.__)("No styles set.","generateblocks-pro")," ",(0,e.createElement)(o.Button,{className:tl.resetButton,variant:"link",onClick:()=>{d({type:"RESET"})}},(0,s.__)("Reset filters","generateblocks-pro"))),v&&(0,e.createElement)(o.Notice,{className:g(tl.addMargin,tl.vertical,tl.noResults,(y||u.search)&&tl.filtersActive),isDismissible:!1},(0,s.__)("No results found.","generateblocks-pro"))))}const ol=r(8907),al=["color","font","fontFamily","fontSize","fontStyle","fontWeight","lineHeight","textAlign","textIndent","textTransform","visibility","whiteSpace","wordSpacing","letterSpacing","direction","cursor"],sl=e=>{if("string"!=typeof e)return{value:e,important:!1};const t=e.trim(),r=t.endsWith("!important");return{value:t,important:r}};function ll(e){const t=e.match(/min-width:\s*(\d*\.?\d+)/),r=e.match(/max-width:\s*(\d*\.?\d+)/);return{minWidth:t?parseFloat(t[1]):0,maxWidth:r?parseFloat(r[1]):1/0}}function il(e,t){switch(t.type){case"SET_SEARCH":const{query:r}=t.payload;return function({query:e,type:t}){const r=new URLSearchParams(window.location.search);r.set(`gb-${t}-styles-search`,e),window.history.replaceState(null,"",`${window.location.pathname}?${r.toString()}`)}(t.payload),{...e,search:r};case"SET_FILTER":!function({activeFilter:e,type:t}){const r=new URLSearchParams(window.location.search);r.set(`gb-${t}-styles-filter`,e),window.history.replaceState(null,"",`${window.location.pathname}?${r.toString()}`)}(t.payload);const{activeFilter:n}=t.payload;return{...e,activeFilter:n};case"RESET":return{search:"",activeFilter:""};default:return e}}function cl(e,t){return Array.isArray(e)?e.some((e=>cl(e,t))):"string"==typeof e?e.toLowerCase().includes(t.toLowerCase()):!!(0,a.isValidElement)(e)&&ul(e,t)}function ul(e,t){const{children:r}=e.props;return"string"==typeof r?r.toLowerCase().includes(t.toLowerCase()):!!Array.isArray(r)&&r.some((e=>"string"==typeof e?e.toLowerCase().includes(t.toLowerCase()):(0,a.isValidElement)(e)&&ul(e,t)))}function dl(t){const{settings:r,onStyleChange:n,nestedRule:l,onNestedRuleChange:i,onAtRuleChange:u,onEditStyle:d,atRule:p,currentSelector:h,onUpdateKey:m,onDeleteStyle:f,styles:b={},allStyles:v={},customAtRules:E=[],selectorShortcuts:S={},visibleSelectors:x=[],scope:C="",canManageStyles:_=!0,setLocalTab:T=()=>{},cancelEditStyle:I=()=>{},allowCustomAtRule:R=!0,allowCustomAdvancedSelector:P=!0,appliedGlobalStyles:A=[]}=t,[L,N]=(0,a.useState)(!1),[z,D]=(0,a.useState)(!1),F=(0,a.useMemo)((()=>Ue(p)),[p]),B=E.length>0?E:$e,[j,V]=(0,a.useReducer)(il,{search:"",activeFilter:""},(()=>{var e,t;const r=new URLSearchParams(window.location.search);return{search:null!==(e=r.get(`gb-${C}-styles-search`))&&void 0!==e?e:"",activeFilter:null!==(t=r.get(`gb-${C}-styles-filter`))&&void 0!==t?t:""}})),W=!!j.activeFilter,U=(0,a.useCallback)(((e,t=C)=>{V({type:"SET_SEARCH",payload:{query:e,type:t}})}),[V,C]),H=(0,a.useCallback)((0,k.useDebounce)(V,200),[V]);(0,a.useEffect)((()=>()=>{H.cancel?.()}),[H]);const $=(0,a.useMemo)((()=>j.search?j.search.split(",").filter(Boolean):[]),[j.search]),q=(0,a.useMemo)((()=>j.activeFilter),[j.activeFilter]),G=(0,a.useCallback)(((e,t)=>{if(!q&&0===$.length)return!0;const{label:r="",searchKeywords:n=[],matchTypes:o=[],cssProp:a}=e;let s=!0;if(q)switch(q){case"show-with-value":s=""!==t?.value||void 0===t?.value;break;case"show-inherited":s=o.includes("local")||o.includes("global")||!1===a}return s&&0!==$.length?$.length>1?$.some((e=>cl([r,...n],e.trim()))):cl([r,...n],$[0]):s}),[q,$]),Z=function(){const e=(0,y.useSelect)((e=>{const{getEntityRecords:t,getEntityRecordEdits:r}=e(w.store);return{records:t("postType","gblocks_styles",{per_page:-1}),getEntityRecordEdits:r}}),[]);return(0,a.useMemo)((()=>null===e.records?[]:e.records.map((t=>{var r,n;const o=e.getEntityRecordEdits("postType","gblocks_styles",t.id),a=null!==(r=o?.gb_style_selector)&&void 0!==r?r:t.gb_style_selector,s=null!==(n=o?.gb_style_data)&&void 0!==n?n:t.gb_style_data;return{classNameSelector:a,className:a.replace(".",""),id:t.id,styles:s}}))),[e.records])}();function K(e="",t="",r=null){return function(e={},t="",r="",n,o=[],a=[],s,l={}){const i=Array.isArray(n)?n:[n],c=o.some((e=>e.classNameSelector===s)),u=c&&0===Object.keys(e).length?o.find((e=>e.classNameSelector===s))?.styles||{}:e,d=(e,t)=>e&&Object.keys(e).some((e=>!e.match(/^[@.&]/)&&e===t)),p=!c&&a.length?o.filter((e=>a.includes(e.className))).reverse():[],h=new Map,m=(e,n,o=!1)=>{const a=`${JSON.stringify(e)}-${n}-${o}`;if(h.has(a))return h.get(a);let s={nest:"",at:"",val:null,important:!1};const l=r?Math.min(ll(r).maxWidth,Math.max(ll(r).minWidth,1200)):1200;if(t){const r=Object.keys(e).filter((t=>"object"==typeof e[t]&&!t.startsWith("@")));for(const o of r)if(t.includes(o)){if(e[o]?.[n]){const{value:t,important:r}=sl(e[o][n]);return s={nest:o,at:"",val:t,important:r},h.set(a,s),s}const t=Object.keys(e[o]||{}).filter((e=>e.startsWith("@media")));for(const r of t){const t=r.replace("@media ","");if(ol.match(t,{width:`${l}px`})&&e[o][r]?.[n]){const{value:t,important:l}=sl(e[o][r][n]);return s={nest:o,at:r,val:t,important:l},h.set(a,s),s}}}}if(r&&e[r]){if(t){const o=Object.keys(e[r]).filter((t=>"object"==typeof e[r][t]&&!t.startsWith("@")));for(const l of o)if(t.includes(l)&&e[r][l]?.[n]){const{value:t,important:o}=sl(e[r][l][n]);if(o)return s={nest:l,at:r,val:t,important:o},h.set(a,s),s}}if(e[r][n]){const{value:t,important:o}=sl(e[r][n]);if(o)return s={nest:"",at:r,val:t,important:o},h.set(a,s),s}}if(r&&e[r]){if(t){const o=Object.keys(e[r]).filter((t=>"object"==typeof e[r][t]&&!t.startsWith("@")));for(const l of o)if(t.includes(l)&&e[r][l]?.[n]){const{value:t,important:o}=sl(e[r][l][n]);return s={nest:l,at:r,val:t,important:o},h.set(a,s),s}}if(e[r][n]){const{value:t,important:o}=sl(e[r][n]);return s={nest:"",at:r,val:t,important:o},h.set(a,s),s}}const i=Object.keys(e).filter((e=>e.startsWith("@"))).sort(((e,t)=>{const r=parseFloat(e.match(/max-width:(\d*\.?\d+)/)?.[1])||1/0,n=parseFloat(t.match(/max-width:(\d*\.?\d+)/)?.[1])||1/0;if(r!==n)return r-n;const o=parseFloat(e.match(/min-width:(\d*\.?\d+)/)?.[1])||0;return(parseFloat(t.match(/min-width:(\d*\.?\d+)/)?.[1])||0)-o}));if(r)for(const r of i){const o=r.replace("@media ","");if(ol.match(o,{width:`${l}px`})&&e[r]){if(t){const o=Object.keys(e[r]).filter((t=>"object"==typeof e[r][t]&&!t.startsWith("@")));for(const l of o)if(t.includes(l)&&e[r][l]?.[n]){const{value:t,important:o}=sl(e[r][l][n]);if(o)return s={nest:l,at:r,val:t,important:o},h.set(a,s),s}}if(e[r][n]){const{value:t,important:o}=sl(e[r][n]);if(o)return s={nest:"",at:r,val:t,important:o},h.set(a,s),s}}}if(r)for(const r of i){const o=r.replace("@media ","");if(ol.match(o,{width:`${l}px`})&&e[r]){if(t){const o=Object.keys(e[r]).filter((t=>"object"==typeof e[r][t]&&!t.startsWith("@")));for(const l of o)if(t.includes(l)&&e[r][l]?.[n]){const{value:t,important:o}=sl(e[r][l][n]);return s={nest:l,at:r,val:t,important:o},h.set(a,s),s}}if(e[r][n]){const{value:t,important:o}=sl(e[r][n]);return s={nest:"",at:r,val:t,important:o},h.set(a,s),s}}}if(d(e,n)){const{value:r,important:l}=sl(e[n]);if(l&&(!t||al.includes(n)||o))return s={nest:"",at:"",val:r,important:l},h.set(a,s),s}if(!r)for(const r of i){const o=r.replace("@media ","");if(ol.match(o,{width:"1200px"})&&e[r]){if(t){const o=Object.keys(e[r]).filter((t=>"object"==typeof e[r][t]&&!t.startsWith("@")));for(const l of o)if(t.includes(l)&&e[r][l]?.[n]){const{value:t,important:o}=sl(e[r][l][n]);if(o)return s={nest:l,at:r,val:t,important:o},h.set(a,s),s}}if(e[r][n]){const{value:t,important:o}=sl(e[r][n]);if(o)return s={nest:"",at:r,val:t,important:o},h.set(a,s),s}}}if(d(e,n)){const{value:r,important:a}=sl(e[n]);(!t||al.includes(n)||o&&!t)&&(s={nest:"",at:"",val:r,important:a})}if(!s.val&&!r)for(const r of i){const o=r.replace("@media ","");if(ol.match(o,{width:"1200px"})&&e[r]){if(t){const o=Object.keys(e[r]).filter((t=>"object"==typeof e[r][t]&&!t.startsWith("@")));for(const l of o)if(t.includes(l)&&e[r][l]?.[n]){const{value:t,important:o}=sl(e[r][l][n]);return s={nest:l,at:r,val:t,important:o},h.set(a,s),s}}if(e[r][n]){const{value:t,important:o}=sl(e[r][n]);s={nest:"",at:r,val:t,important:o};break}}}return h.set(a,s),s};return i.map((e=>{if(!Array.isArray(l)&&void 0!==l[e]){const{value:n,important:o}=sl(l[e]);if(o)return{hasInheritedValue:!0,source:"current",classNameSelector:null,inheritedNestedRule:t,inheritedAtRule:r,property:e,value:n}}const{nest:n,at:o,val:a,important:i}=m(u,e,!1);if(a&&i){const i=""!==n||""!==o||""!==t||""!==r||l[e]?"local":"current";return{hasInheritedValue:!0,source:i,classNameSelector:"current"===i?null:s,inheritedNestedRule:n,inheritedAtRule:o,property:e,value:a}}if(p.length)for(const r of p){const n=r.styles||{},{nest:o,at:a,val:s,important:l}=m(n,e,!0);if(s&&l)return{hasInheritedValue:!0,source:"global",classNameSelector:r.classNameSelector,inheritedNestedRule:o||(t&&r.classNameSelector===t?t:""),inheritedAtRule:a,property:e,value:s}}if(!Array.isArray(l)&&void 0!==l[e]){const{value:n}=sl(l[e]);return{hasInheritedValue:!0,source:"current",classNameSelector:null,inheritedNestedRule:t,inheritedAtRule:r,property:e,value:n}}if(a){const i=""!==n||""!==o||""!==t||""!==r||l[e]?"local":"current";return{hasInheritedValue:!0,source:i,classNameSelector:"current"===i?null:s,inheritedNestedRule:n,inheritedAtRule:o,property:e,value:a}}if(p.length)for(const r of p){const n=r.styles||{},{nest:o,at:a,val:s}=m(n,e,!0);if(s)return{hasInheritedValue:!0,source:"global",classNameSelector:r.classNameSelector,inheritedNestedRule:o||(t&&r.classNameSelector===t?t:""),inheritedAtRule:a,property:e,value:s}}return{hasInheritedValue:!1,source:null,classNameSelector:null,inheritedNestedRule:"",inheritedAtRule:"",property:e,value:null}}))}(v||{},e,t,at,Z||[],A||[],h,null!==r?r:b)}const Y=K(l,p),Q={atRule:F,currentSelector:h,canManageStyles:_,onEditStyle:d,setLocalTab:T,cancelEditStyle:I,onAtRuleChange:u,controlFilters:j,controlIsVisible:G,setSearch:U,inheritedSources:Y,onNestedRuleChange:i,getValueSources:K,scope:C};return(0,e.createElement)(O.Provider,{value:Q},(0,e.createElement)("div",{className:g("gb-styles-builder",c.component)},(0,e.createElement)("div",{className:c.header},(0,e.createElement)(M,{allStyles:v,onUpdateKey:m,onNestedRuleChange:i,currentSelector:h,nestedRule:l,showSelectorOptions:L,setShowSelectorOptions:N,onDeleteStyle:f,selectorShortcuts:S,visibleSelectors:x,allowCustomAdvancedSelector:P,atRule:p}),!L&&(0,e.createElement)("div",{className:c.atRuleFilters},!!B.length>0&&(0,e.createElement)(ct,{atRule:F,onAtRuleChange:u,onNestedRuleChange:i,defaultAtRules:B,allStyles:v,showAtRuleOptions:z,setShowAtRuleOptions:D,onUpdateKey:m,nestedRule:l,onDeleteStyle:f,allowCustomAtRule:R,styles:b})),!L&&!z&&(0,e.createElement)("div",{className:c.filters},(0,e.createElement)(o.SearchControl,{__nextHasNoMarginBottom:!0,value:j.search,onChange:e=>{H({type:"SET_SEARCH",payload:{query:e,type:C}})},label:(0,s.__)("Search Controls","generateblocks-pro"),hideLabelFromVision:!0,placeholder:(0,s.__)("Search controls…","generateblocks-pro"),className:g(c.search,j.search&&c.searchActive)}),(0,e.createElement)(o.DropdownMenu,{variant:"link",size:"small",icon:Cn,label:(0,s.__)("Filter Controls","generateblocks-pro"),className:c.filtersDropdown,popoverProps:{className:c.filtersPopover},toggleProps:{size:"compact",isPressed:W,iconSize:"18"}},(({onClose:t})=>(0,e.createElement)(o.MenuGroup,{label:(0,s.__)("Controls Visibility","generateblocks-pro")},(0,e.createElement)(o.MenuItemsChoice,{value:j.activeFilter,choices:[{label:(0,s.__)("Show all","generateblocks-pro"),value:""},{label:(0,s.__)("Show controls with value","generateblocks-pro"),value:"show-with-value"},{label:(0,s.__)("Show inherited values","generateblocks-pro"),value:"show-inherited"}],onSelect:e=>{V({type:"SET_FILTER",payload:{activeFilter:e,type:C}}),t()}})))))),!z&&!L&&(0,e.createElement)(nl,{currentSelector:h,settings:r,styles:b,onStyleChange:n,onNestedRuleChange:i,nestedRule:l,atRule:F,filters:j,dispatchFilters:V,scope:C,allowCustomAtRule:R}),(0,e.createElement)("div",{className:c.legend},(0,e.createElement)("div",null,(0,e.createElement)("span",{className:g(c.dot,c.current)}),(0,s.__)("Local style","generateblocks-pro")),(0,e.createElement)("div",null,(0,e.createElement)("span",{className:g(c.dot,c.local)}),(0,s.__)("Inherited style","generateblocks-pro")),!!A.length&&(0,e.createElement)("div",null,(0,e.createElement)("span",{className:g(c.dot,c.global)}),(0,s.__)("Global style","generateblocks-pro")))))}function pl(e){const t=e.replace(/^@media\s+/,"").trim(),r={mobile:375,tablet:768,laptop:1366,desktop:1920};if(r[t.toLowerCase()])return r[t.toLowerCase()];const n=t.match(/max-width:\s*(\d+)px/),o=t.match(/min-width:\s*(\d+)px/);let a=null,s=null;return n&&(a=parseInt(n[1])),o&&(s=parseInt(o[1])),a&&s?a-s<0?1200:Math.round((a+s)/2):a?a<320?375:a:s?s>1920?1920:s:1200}const hl={data:{},settings:{}},ml={setStyles:e=>({type:"SET_DATA",payload:e}),addStyle:(e,t,r,n)=>({type:"ADD_STYLE",payload:{property:e,value:t,atRule:r,nestedRule:n}}),updateKey:(e,t,r)=>({type:"UPDATE_KEY",payload:{oldKey:e,newKey:t,nestedRule:r}}),deleteStyle:(e,t)=>({type:"DELETE_STYLE",payload:{key:e,nestedRule:t}})},fl={getStyles:(e,t="",r="")=>Be(e.data,t,r)};function gl(e=hl,t){if("SET_DATA"===t.type)return Object.assign({},e,{data:t.payload});if("ADD_STYLE"===t.type){const{property:r,value:n,atRule:o,nestedRule:a}=t.payload;let{data:s}=e;return"object"==typeof r?Object.entries(r).forEach((([e,t])=>{s=ze(s,e,t,o,a)})):Array.isArray(r)?r.forEach((e=>{s=ze(s,e.property,e.value,o,a)})):s=ze(e.data,r,n,o,a),{...e,data:s}}if("UPDATE_KEY"===t.type){const{oldKey:r,newKey:n,nestedRule:o}=t.payload,a=De(e.data,r,n,o);return{...e,data:a}}if("DELETE_STYLE"===t.type){const{key:r,nestedRule:n}=t.payload,o=Fe(e.data,r,n);return{...e,data:o}}return{...e}}const bl="",vl={setNestedRule:e=>({type:"SET_DATA",payload:e})},yl={getNestedRule:e=>e};function wl(e=bl,t){return"SET_DATA"===t.type?t.payload:e}const kl={data:{}},El={setCurrentStyle:e=>({type:"SET_DATA",payload:e})},Sl={currentStyle:e=>e.data};function xl(e=kl,t){return"SET_DATA"===t.type?Object.assign({},e,{data:t.payload}):{...e}}const Cl="",_l={setAtRule:e=>({type:"SET_DATA",payload:e})},Ol={getAtRule:e=>e};function Tl(e=Cl,t){return"SET_DATA"===t.type?t.payload:e}const Il="",Rl={setFilters:e=>({type:"SET_DATA",payload:e})},Ml={getFilters:e=>e};function Pl(e=Il,t){return"SET_DATA"===t.type?t.payload:e}})(),n})(),e.exports=t()}},t={},r=function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}(7189);(window.gbp=window.gbp||{}).stylesBuilder=r})();