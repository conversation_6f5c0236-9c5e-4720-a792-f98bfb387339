(()=>{"use strict";var e={n:t=>{var s=t&&t.__esModule?()=>t.default:()=>t;return e.d(s,{a:s}),s},d:(t,s)=>{for(var n in s)e.o(s,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:s[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.React,s=window.wp.i18n,n=window.wp.components,a=window.wp.element,l=window.wp.apiFetch;var i=e.n(l);class o extends a.Component{constructor(){super(...arguments),this.state={isLicenseKeyAPILoaded:!1,isAPISaving:!1,licenseSettings:generateBlocksProSettings.licenseSettings}}componentDidMount(){this.setState({isLicenseKeyAPILoaded:!0})}getSetting(e,t){let s=t;return void 0!==this.state.licenseSettings[e]&&(s=this.state.licenseSettings[e]),s}updateSettings(e){this.setState({isAPISaving:!0});const t=e.target.nextElementSibling;t.classList.remove("gblocks-action-message--show"),i()({path:"/generateblocks-pro/v1/license",method:"POST",data:{licenseSettings:this.state.licenseSettings}}).then((e=>{this.setState({isAPISaving:!1}),t.classList.add("gblocks-action-message--show"),e.success&&e.response?(t.classList.remove("gblocks-action-message--error"),"valid"===e.response.license?t.textContent=(0,s.__)("License key activated.","generateblocks-pro"):"deactivated"===e.response.license?t.textContent=(0,s.__)("License key deactivated.","generateblocks-pro"):t.textContent=e.response,this.setState({licenseSettings:{...this.state.licenseSettings,status:e.response.license}}),setTimeout((function(){t.classList.remove("gblocks-action-message--show")}),3e3)):(t.classList.add("gblocks-action-message--error"),t.textContent=e.response)}))}render(){return this.state.isLicenseKeyAPILoaded?(0,t.createElement)(a.Fragment,null,(0,t.createElement)("div",{className:"generateblocks-settings-main"},(0,t.createElement)(n.PanelBody,{title:(0,s.__)("License Key","generateblocks-pro")},(0,t.createElement)("div",{className:"gblocks-dashboard-panel-row-wrapper"},(0,t.createElement)(n.PanelRow,null,(0,t.createElement)(n.BaseControl,{id:"gblocks-license-key-area",className:"gblocks-license-key-area"},(0,t.createElement)(n.Notice,{className:"gblocks-licensing-notice",isDismissible:!1,status:"valid"===this.state.licenseSettings.status?"success":"warning"},"valid"===this.state.licenseSettings.status?(0,t.createElement)("span",null,(0,s.__)("Receiving updates","generateblocks-pro")):(0,t.createElement)("span",null,(0,s.__)("Not receiving updates","generateblocks-pro"))),(0,t.createElement)(n.TextControl,{type:"password",autoComplete:"off",placeholder:(0,s.__)("Enter your license key here…","generateblocks-pro"),value:this.getSetting("key"),onChange:e=>{this.setState({licenseSettings:{...this.state.licenseSettings,key:e}})}}))),""!==this.state.licenseSettings.key&&(0,t.createElement)(n.PanelRow,null,(0,t.createElement)(n.BaseControl,null,(0,t.createElement)(n.ToggleControl,{label:(0,s.__)("Receive development version updates"),help:(0,s.__)("Receive alpha, beta, and release candidate updates. This should only be used on staging/development websites.","generateblocks-pro"),checked:!!this.getSetting("beta"),onChange:e=>{this.setState({licenseSettings:{...this.state.licenseSettings,beta:e}})}}))),(0,t.createElement)(n.PanelRow,null,(0,t.createElement)("div",{className:"gblocks-action-button"},(0,t.createElement)(n.Button,{isPrimary:!0,disabled:this.state.isAPISaving,onClick:e=>this.updateSettings(e)},this.state.isAPISaving&&(0,t.createElement)(n.Spinner,null),!this.state.isAPISaving&&(0,s.__)("Save","generateblocks-pro")),(0,t.createElement)("span",{className:"gblocks-action-message"}))))))):(0,t.createElement)(n.Placeholder,{className:"gblocks-settings-placeholder"},(0,t.createElement)(n.Spinner,null))}}window.addEventListener("DOMContentLoaded",(()=>{(0,a.render)((0,t.createElement)(o,null),document.getElementById("gblocks-license-key-settings"))}));class c extends a.Component{constructor(){super(...arguments),this.state={isAPISaving:!1,isTemplateLibraryAPILoaded:!1,isSyncingLibrary:!1,enableRemoteTemplates:generateBlocksProSettings.adminSettings.enable_remote_templates,enableLocalTemplates:generateBlocksProSettings.adminSettings.enable_local_templates}}componentDidMount(){this.setState({isTemplateLibraryAPILoaded:!0})}getSetting(e,t){let s=t;return void 0!==this.state.settings[e]&&(s=this.state.settings[e]),s}updateSettings(e){this.setState({isAPISaving:!0});const t=e.target.nextElementSibling;i()({path:"/generateblocks-pro/v1/template-library",method:"POST",data:{enableRemoteTemplates:this.state.enableRemoteTemplates,enableLocalTemplates:this.state.enableLocalTemplates}}).then((e=>{this.setState({isAPISaving:!1}),t.classList.add("gblocks-action-message--show"),t.textContent=e.response,e.success&&e.response?setTimeout((function(){t.classList.remove("gblocks-action-message--show")}),3e3):t.classList.add("gblocks-action-message--error")}))}render(){return generateBlocksProSettings.useLegacyPatternLibrary?this.state.isTemplateLibraryAPILoaded?(0,t.createElement)(a.Fragment,null,(0,t.createElement)("div",{className:"generateblocks-settings-main"},(0,t.createElement)(n.PanelBody,{title:(0,s.__)("Pattern Library (Legacy)","generateblocks-pro")},(0,t.createElement)("div",{className:"gblocks-dashboard-panel-row-wrapper"},(0,t.createElement)(n.PanelRow,null,(0,t.createElement)(n.ToggleControl,{label:(0,s.__)("Enable Local Patterns","generateblocks-pro"),checked:!!this.state.enableLocalTemplates,onChange:e=>{this.setState({enableLocalTemplates:e})}}),(0,t.createElement)(n.ToggleControl,{label:(0,s.__)("Enable Remote Patterns","generateblocks-pro"),checked:!!this.state.enableRemoteTemplates,onChange:e=>{this.setState({enableRemoteTemplates:e})}}),!!this.state.enableRemoteTemplates&&(0,t.createElement)(n.BaseControl,{id:"gblocks-sync-template-library",className:"gblocks-sync-template-library",help:(0,s.__)("The pattern library syncs once a day by default. Clicking this button will force it to re-sync.","generateblocks-pro")},(0,t.createElement)(n.Button,{isSecondary:!0,onClick:e=>{this.setState({isSyncingLibrary:!0});const t=e.target.nextElementSibling;i()({path:"/generateblocks-pro/v1/sync_template_library",method:"POST"}).then((e=>{this.setState({isSyncingLibrary:!1}),t.classList.add("gblocks-action-message--show"),e.success&&e.response?(t.textContent=(0,s.__)("Remote patterns synced.","generateblocks-pro"),setTimeout((function(){t.classList.remove("gblocks-action-message--show")}),3e3)):(t.classList.add("gblocks-action-message--error"),t.textContent=e)}))}},this.state.isSyncingLibrary&&(0,t.createElement)(n.Spinner,null),!this.state.isSyncingLibrary&&(0,s.__)("Sync Remote Patterns","generateblocks-pro")),(0,t.createElement)("span",{className:"gblocks-action-message"}))),(0,t.createElement)("div",{className:"gblocks-action-button"},(0,t.createElement)(n.Button,{isPrimary:!0,disabled:this.state.isAPISaving,onClick:e=>this.updateSettings(e)},this.state.isAPISaving&&(0,t.createElement)(n.Spinner,null),!this.state.isAPISaving&&(0,s.__)("Save","generateblocks-pro")),(0,t.createElement)("span",{className:"gblocks-action-message"})))))):(0,t.createElement)(n.Placeholder,{className:"gblocks-settings-placeholder"},(0,t.createElement)(n.Spinner,null)):null}}window.addEventListener("DOMContentLoaded",(()=>{(0,a.render)((0,t.createElement)(c,null),document.getElementById("gblocks-template-library-settings"))}))})();