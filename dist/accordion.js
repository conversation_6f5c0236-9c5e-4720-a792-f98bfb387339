(()=>{const t=(t,e)=>{const o=t.closest(".gb-accordion__item"),i=o.querySelector(".gb-accordion__content"),c=o.querySelector(".gb-accordion__toggle"),n=o.closest(".gb-accordion"),a=o.getAttribute("data-transition")||n.getAttribute("data-transition"),s=e.target.closest(".gb-accordion__content");if(s&&(s.style.maxHeight=null),n&&null===n.getAttribute("data-accordion-multiple-open")){const t=(r=o,Array.prototype.filter.call(r.parentNode.children,(t=>t!==r)));t&&t.forEach((t=>{if(t.classList.contains("gb-accordion__item-open")){if("slide"===a){const e=t.querySelector(".gb-accordion__content");e&&(e.style.maxHeight=e.scrollHeight+"px",setTimeout((()=>{t.classList.remove("gb-accordion__item-open"),e.style.maxHeight=null}),10))}else t.classList.remove("gb-accordion__item-open");const e=t.querySelector(".gb-accordion__toggle");e&&(e.setAttribute("aria-expanded",!1),e.classList.remove("gb-block-is-current"))}}))}var r;o.classList.contains("gb-accordion__item-open")?"slide"===a?(i.style.maxHeight=i.scrollHeight+"px",setTimeout((()=>{c.setAttribute("aria-expanded",!1),c.classList.remove("gb-block-is-current"),o.classList.remove("gb-accordion__item-open"),i.style.maxHeight=null}),10)):(c.setAttribute("aria-expanded",!1),c.classList.remove("gb-block-is-current"),o.classList.remove("gb-accordion__item-open")):"slide"===a?(i.style.maxHeight=i.scrollHeight+"px",setTimeout((()=>{c.setAttribute("aria-expanded",!0),c.classList.add("gb-block-is-current"),o.classList.add("gb-accordion__item-open"),i.addEventListener("transitionend",(t=>{"max-height"===t.propertyName&&(i.style.maxHeight=null)}),{once:!0})}),10)):(c.setAttribute("aria-expanded",!0),c.classList.add("gb-block-is-current"),o.classList.add("gb-accordion__item-open"),o.classList.add("gb-accordion__item-transition"),setTimeout((()=>o.classList.remove("gb-accordion__item-transition")),100))};function e(){const t=window.location.hash;if(t){const e=document.getElementById(String(t).replace("#",""));if(e&&e.classList.contains("gb-accordion__item")){const t=e.querySelector(".gb-accordion__toggle"),o=e.querySelector(".gb-accordion__content");o.style.transition="none",t&&(e.classList.contains("gb-accordion__item-open")||t.click(),e.scrollIntoView(),o.style.transition="")}}}document.addEventListener("click",(e=>{const o=e.target.closest(".gb-accordion__item .gb-accordion__toggle");o&&t(o,e)})),document.addEventListener("keydown",(e=>{const o=e.target.closest(".gb-accordion__item .gb-accordion__toggle");o&&"BUTTON"!==o.tagName.toUpperCase()&&(" "!==e.key&&"Enter"!==e.key&&"Spacebar"!==e.key||(e.preventDefault(),t(o,e)))})),window.addEventListener("hashchange",e),document.addEventListener("DOMContentLoaded",e);const o=document.querySelectorAll(".gb-accordion__item");o&&o.forEach((t=>{const e=t.querySelector(".gb-accordion__toggle"),o=t.querySelector(".gb-accordion__content"),i=e.getAttribute("id"),c=o.getAttribute("id");i&&o.setAttribute("aria-labelledby",i),c&&e.setAttribute("aria-controls",c),t.classList.contains("gb-accordion__item-open")&&e?e.setAttribute("aria-expanded",!0):e&&e.setAttribute("aria-expanded",!1)}))})();