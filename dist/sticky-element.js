(()=>{"use strict";new class{constructor(){this.elements=document.querySelectorAll("[data-gb-is-sticky]"),this.lastScrollY=window.scrollY,this.rafId=null,this.wpAdminBarOffset=0,this.isTicking=!1,this.lastTriggerScrollY=new Map,this.lastScrollDirection=new Map,this.scrollDirectionChangeY=new Map,this.initializedElements=new Set,this.mediaQueryHandlers=new Map,this.originalStyles=new Map,this.elementMetadata=new Map,this.activeAnimations=new Map,this.stickyBaseZIndex=100,this.scrollStopTimeout=null,this.scrollStopDelay=150,this.isScrolling=!1,this.domCache=new Map,this.isReducedMotion=window.matchMedia("(prefers-reduced-motion: reduce)").matches,this.resizeHandler=()=>this.handleResize(),this.scrollHandler=()=>this.requestTick(),this.init()}getSafeZIndex(t){return t&&"auto"!==t&&""!==t?t:this.stickyBaseZIndex}getCachedValue(t,e){return this.domCache.has(t)||this.domCache.set(t,e()),this.domCache.get(t)}clearCache(){this.domCache.clear()}init(){const t=document.querySelector("#wpadminbar");if(t){const e=new MutationObserver((()=>{this.updateAdminBarOffset(),this.requestTick()}));e.observe(t,{attributes:!0,childList:!0,subtree:!0}),this.elementMetadata.set("adminBarObserver",e)}this.updateAdminBarOffset();const e=window.scrollY;this.elements.forEach((t=>{this.initializeElement(t,e)})),window.addEventListener("scroll",this.scrollHandler,{passive:!0}),window.addEventListener("resize",this.resizeHandler,{passive:!0}),this.updateHeaderClasses(this.lastScrollY)}initializeElement(t,e){const i=t.dataset.gbStickyHeaderType||"always",s="scroll-up"===i,n="past-threshold"===i,a="always"===i||!s&&!n,l=this.isReducedMotion?"none":t.dataset.gbStickyAnimation||"slide",o=parseInt(t.dataset.gbStickySpeed)||200;let r=null;if(t.dataset.gbStickyThreshold)try{r=this.parseThresholdValue(t.dataset.gbStickyThreshold)}catch(e){console.warn("StickyElement: Invalid threshold value",t.dataset.gbStickyThreshold),r=200}else n&&(r=200);const d=t.style.position,h=t.style.top,c=t.style.zIndex,p=t.style.left,m=t.style.right,g=t.style.width;this.originalStyles.set(t,{originalPosition:d,originalTop:h,originalZIndex:c,originalLeft:p,originalRight:m,originalWidth:g}),this.elementMetadata.set(t,{headerType:i,isScrollUpType:s,isPastThresholdType:n,isAlwaysType:a,animationType:l,animationSpeed:o,stickyThreshold:r,placeholder:null,wasSticking:!1,isAnimatedSticky:!1});const y=t.dataset.gbStickyBreakpoint;let u=!0;if(y)try{const e=window.matchMedia(`(${y.trim()})`);u=e.matches;const i=()=>{this.handleMediaQueryChange(t,e)};e.addEventListener("change",i),this.mediaQueryHandlers.set(t,{mql:e,handler:i})}catch(t){u=!1}u&&(this.initializeElementPositioning(t,e),this.initializedElements.add(t))}initializeElementPositioning(t,e){this.updateAdminBarOffset();const i=this.elementMetadata.get(t),{isScrollUpType:s,isPastThresholdType:n}=i,a=this.getCachedValue(t,(()=>{const e=getComputedStyle(t),s=t.offsetHeight,n=parseFloat(e.marginBottom)||0,a=s+n;let l=i.placeholder;l&&l.parentNode||(l=document.createElement("div"),l.classList.add("gb-sticky-placeholder"),l.style.boxSizing=e.boxSizing,t.parentNode.insertBefore(l,t.nextSibling),i.placeholder=l),l.style.height=`${a}px`,l.style.display="none";const o=t.style.position,r=t.style.width,d=t.style.maxWidth;t.style.position="static",t.style.width="",t.style.maxWidth="";const h=t.getBoundingClientRect(),c=h.top+window.scrollY,p=h.width;t.style.position=o,t.style.width=r,t.style.maxWidth=d;const m=t.closest('[style*="max-width"], [style*="width"], .container, .wrapper')||document.body,g=m.getBoundingClientRect(),y=getComputedStyle(m),u=parseFloat(y.paddingLeft)||0,f=parseFloat(y.borderLeftWidth)||0,w=parseFloat(y.paddingRight)||0,S=parseFloat(y.borderRightWidth)||0,k=h.left-(g.left+u+f),T=g.width-(u+w+f+S);return{originalHeight:s,marginBottom:n,naturalTop:c,naturalWidth:p,naturalLeft:k,container:m,isFullWidth:Math.abs(p-T)<2||p>=window.innerWidth}}));let l,o;Object.assign(i,a),l=Math.max(0,a.naturalTop-this.wpAdminBarOffset),o=!s&&!n&&(a.naturalTop<=this.wpAdminBarOffset?e>0:e>=l),this.elementMetadata.set(t,{...i,triggerPoint:l,wasSticking:o,isAnimatedSticky:!1}),this.applyStickyStyles(t,o),s&&(this.lastTriggerScrollY.delete(t),this.lastScrollDirection.delete(t),this.scrollDirectionChangeY.delete(t))}updateAdminBarOffset(){this.wpAdminBarOffset=this.getCachedValue("adminBarOffset",(()=>{const t=document.querySelector("#wpadminbar");return t&&"fixed"===getComputedStyle(t).position&&t.offsetHeight>0?t.offsetHeight:0}))}animateElementIn(t){const e=this.elementMetadata.get(t);if(!e)return;const{animationType:i,animationSpeed:s}=e;if(this.cancelExistingAnimation(t),this.applyStickyStylesForAnimation(t),"none"===i)return;let n;try{if("fade"===i)t.style.opacity="0",t.getBoundingClientRect(),n=t.animate([{opacity:0},{opacity:1}],{duration:s,easing:"cubic-bezier(0.4, 0, 0.2, 1)",fill:"forwards"}),n.addEventListener("finish",(()=>{t.style.opacity="",this.activeAnimations.delete(t)}));else{const e=parseInt(t.style.top)||0,i=e-t.offsetHeight;t.style.top=`${i}px`,t.getBoundingClientRect(),n=t.animate([{top:`${i}px`},{top:`${e}px`}],{duration:s,easing:"cubic-bezier(0.4, 0, 0.2, 1)",fill:"forwards"}),n.addEventListener("finish",(()=>{t.style.top=`${e}px`,this.activeAnimations.delete(t)}))}this.activeAnimations.set(t,n)}catch(e){console.warn("StickyElement: Animation failed",e),t.style.opacity=""}return n}animateElementOut(t,e){const i=this.elementMetadata.get(t);if(!i)return;const{animationType:s,animationSpeed:n}=i;if(this.cancelExistingAnimation(t),"none"===s)return void(e&&e());let a;try{if("fade"===s)a=t.animate([{opacity:1},{opacity:0}],{duration:n,easing:"cubic-bezier(0.4, 0, 0.2, 1)",fill:"forwards"});else{const e=parseInt(t.style.top)||0,i=e-t.offsetHeight;a=t.animate([{top:`${e}px`},{top:`${i}px`}],{duration:n,easing:"cubic-bezier(0.4, 0, 0.2, 1)",fill:"forwards"})}this.activeAnimations.set(t,a),a.addEventListener("finish",(()=>{e&&e(),this.activeAnimations.delete(t)}))}catch(t){console.warn("StickyElement: Animation failed",t),e&&e()}return a}cancelExistingAnimation(t){const e=this.activeAnimations.get(t);if(e){try{e.cancel()}catch(t){}this.activeAnimations.delete(t)}t.style.opacity=""}applyStickyStylesForAnimation(t){const e=this.elementMetadata.get(t);if(!e)return;const{placeholder:i,naturalLeft:s,naturalWidth:n,container:a,originalHeight:l,marginBottom:o,isFullWidth:r}=e,{originalZIndex:d}=this.originalStyles.get(t);if(i&&a&&void 0!==l&&void 0!==o){const e=a.getBoundingClientRect(),h=getComputedStyle(a),c=parseFloat(h.paddingLeft)||0,p=parseFloat(h.borderLeftWidth)||0;let m,g,y;r?(g="0px",y="0px",m="auto"):(g=`${e.left+c+p+s}px`,m=`${n}px`,y=""),i.style.display="block",i.style.height=`${l+o}px`,t.style.position="fixed",t.style.top=`${this.wpAdminBarOffset}px`,t.style.zIndex=this.getSafeZIndex(d),t.classList.add("gb-is-sticky"),t.style.left=g,t.style.right=y,t.style.width=m}}isElementInViewport(t){const e=t.getBoundingClientRect();return e.top>=0&&e.left>=0&&e.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&e.right<=(window.innerWidth||document.documentElement.clientWidth)}applyStickyStyles(t,e){const i=this.elementMetadata.get(t);if(!i)return;const{placeholder:s,naturalLeft:n,naturalWidth:a,container:l,originalHeight:o,marginBottom:r,isFullWidth:d}=i;if(!s||!l||void 0===o||void 0===r)return;const h=this.originalStyles.get(t);if(!h)return;const{originalPosition:c,originalTop:p,originalZIndex:m,originalLeft:g,originalRight:y,originalWidth:u}=h;if(e){const e=l.getBoundingClientRect(),i=getComputedStyle(l),h=parseFloat(i.paddingLeft)||0,c=parseFloat(i.borderLeftWidth)||0;let p,g,y;d?(g="0px",y="0px",p="auto"):(g=`${e.left+h+c+n}px`,p=`${a}px`,y=""),s.style.display="block",s.style.height=`${o+r}px`,t.style.position="fixed",t.style.top=`${this.wpAdminBarOffset}px`,t.style.zIndex=this.getSafeZIndex(m),t.classList.add("gb-is-sticky"),t.style.left=g,t.style.right=y,t.style.width=p}else t.classList.remove("gb-is-sticky"),t.style.position=c||"",t.style.top=p||"",t.style.zIndex=m||"",t.style.left=g||"",t.style.right=y||"",t.style.width=u||"",t.style.transform="",t.style.opacity="",s.style.display="none",this.cancelExistingAnimation(t)}parseThresholdValue(t){const e=t.trim().match(/^(-?\d*\.?\d+)(px|rem|em|%|vh|vw|vmin|vmax)?$/i);if(!e)throw new Error(`Invalid threshold format: ${t}`);const i=parseFloat(e[1]);switch((e[2]||"px").toLowerCase()){case"px":default:return i;case"rem":return i*parseFloat(getComputedStyle(document.documentElement).fontSize);case"em":return 16*i;case"%":case"vh":return i/100*window.innerHeight;case"vw":return i/100*window.innerWidth;case"vmin":return i/100*Math.min(window.innerWidth,window.innerHeight);case"vmax":return i/100*Math.max(window.innerWidth,window.innerHeight)}}handleMediaQueryChange(t,e){const i=this.elementMetadata.get(t);if(!i)return;const s=this.originalStyles.get(t);if(!s)return;const{originalPosition:n,originalTop:a,originalZIndex:l,originalLeft:o,originalRight:r,originalWidth:d}=s,{placeholder:h}=i;if(e.matches&&!this.initializedElements.has(t))this.initializeElementPositioning(t,window.scrollY),this.initializedElements.add(t);else if(!e.matches&&this.initializedElements.has(t)){t.style.position=n||"",t.style.top=a||"",t.style.zIndex=l||"",t.style.left=o||"",t.style.right=r||"",t.style.width=d||"",t.style.transform="",t.style.opacity="",t.classList.remove("gb-is-sticky"),h&&(h.style.display="none"),h&&h.parentNode&&h.parentNode.removeChild(h),this.lastTriggerScrollY.delete(t),this.lastScrollDirection.delete(t),this.scrollDirectionChangeY.delete(t),this.initializedElements.delete(t),this.cancelExistingAnimation(t);const e=i.headerType,s=i.stickyThreshold;this.elementMetadata.set(t,{headerType:e,isScrollUpType:"scroll-up"===e,isPastThresholdType:"past-threshold"===e,isAlwaysType:"always"===e||!e||"scroll-up"!==e&&"past-threshold"!==e,stickyThreshold:s,placeholder:null,wasSticking:!1,isAnimatedSticky:!1})}}requestTick(){this.isTicking||(this.rafId=requestAnimationFrame((()=>this.update())),this.isTicking=!0)}update(){this.updateAdminBarOffset(),this.updateHeaderClasses(window.scrollY),this.isTicking=!1}handleResize(){this.clearCache(),this.updateAdminBarOffset(),this.elements.forEach((t=>{if(this.initializedElements.has(t)){const e=this.elementMetadata.get(t);if(e&&e.isPastThresholdType&&t.dataset.gbStickyThreshold)try{const i=this.parseThresholdValue(t.dataset.gbStickyThreshold);e.stickyThreshold=i}catch(t){console.warn("StickyElement: Threshold recalculation failed on resize")}this.initializeElementPositioning(t,window.scrollY)}})),this.requestTick()}updateHeaderClasses(t){if(0===this.initializedElements.size)return void(this.lastScrollY=t);this.isScrolling=!0;const e=t>this.lastScrollY?"down":"up";this.currentScrollDirection=e,this.scrollStopTimeout&&clearTimeout(this.scrollStopTimeout),this.scrollStopTimeout=setTimeout((()=>{this.isScrolling=!1,this.handleScrollStop(t,this.currentScrollDirection)}),this.scrollStopDelay),this.elements.forEach((e=>{if(!this.initializedElements.has(e))return;const i=this.elementMetadata.get(e);if(!i)return;const{triggerPoint:s,isAlwaysType:n,naturalTop:a}=i,l=e.classList.contains("gb-is-sticky");let o;o=n?a<=this.wpAdminBarOffset?-1:a-this.wpAdminBarOffset:a;const r=t<o;if(r&&l)return this.cancelExistingAnimation(e),this.applyStickyStyles(e,!1),this.elementMetadata.get(e).wasSticking=!1,void(this.elementMetadata.get(e).isAnimatedSticky=!1);if(n&&!r){const n=a<=this.wpAdminBarOffset?t>0:t>=s;n!==i.wasSticking&&(this.applyStickyStyles(e,n),this.elementMetadata.get(e).wasSticking=n)}})),this.lastScrollY=t}handleScrollStop(t,e){this.elements.forEach((i=>{if(!this.initializedElements.has(i))return;const s=this.elementMetadata.get(i);if(!s)return;const{triggerPoint:n,isScrollUpType:a,isPastThresholdType:l,stickyThreshold:o,naturalTop:r}=s;if(!a&&!l)return;if(t<=r)return;const d=i.classList.contains("gb-is-sticky"),h=t>n;if(a){const t=this.isElementInViewport(i);"up"!==e||!h||t||d?"down"===e&&d&&this.animateElementOut(i,(()=>{this.applyStickyStyles(i,!1),this.elementMetadata.get(i).wasSticking=!1})):(this.animateElementIn(i),this.elementMetadata.get(i).wasSticking=!0)}else if(l){const e=t>n+o;e&&!s.wasSticking?(this.animateElementIn(i),this.elementMetadata.get(i).wasSticking=!0,this.elementMetadata.get(i).isAnimatedSticky=!0):!e&&s.wasSticking&&this.animateElementOut(i,(()=>{this.applyStickyStyles(i,!1),this.elementMetadata.get(i).wasSticking=!1,this.elementMetadata.get(i).isAnimatedSticky=!1}))}}))}destroy(){this.elements.forEach((t=>{const e=this.originalStyles.get(t),i=this.elementMetadata.get(t);if(!e||!i)return;const{originalPosition:s,originalTop:n,originalZIndex:a,originalLeft:l,originalRight:o,originalWidth:r}=e,{placeholder:d}=i;this.cancelExistingAnimation(t),t.style.position=s||"",t.style.top=n||"",t.style.zIndex=a||"",t.style.left=l||"",t.style.right=o||"",t.style.width=r||"",t.style.transform="",t.style.opacity="",t.classList.remove("gb-is-sticky"),d&&(d.style.display="none"),d&&d.parentNode&&d.parentNode.removeChild(d),this.lastTriggerScrollY.delete(t),this.lastScrollDirection.delete(t),this.scrollDirectionChangeY.delete(t),this.initializedElements.delete(t),this.elementMetadata.delete(t)})),window.removeEventListener("scroll",this.scrollHandler,{passive:!0}),window.removeEventListener("resize",this.resizeHandler,{passive:!0}),this.rafId&&cancelAnimationFrame(this.rafId),this.mediaQueryHandlers.forEach((({mql:t,handler:e})=>t.removeEventListener("change",e))),this.mediaQueryHandlers.clear(),this.originalStyles.clear(),this.activeAnimations.clear(),this.lastTriggerScrollY.clear(),this.lastScrollDirection.clear(),this.scrollDirectionChangeY.clear();const t=this.elementMetadata.get("adminBarObserver");t&&t.disconnect(),this.elementMetadata.clear(),this.domCache.clear()}}})();