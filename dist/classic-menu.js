(()=>{"use strict";var e,t={};function n(e){if("Tab"!==e.key&&9!==e.keyCode)return;e.preventDefault();const t=e.currentTarget,n=t.querySelectorAll('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]'),o=Array.from(n).filter((e=>null!==e.offsetParent&&"hidden"!==getComputedStyle(e).visibility&&"none"!==getComputedStyle(e).display));if(0===o.length)return;const i=o[0],s=o[o.length-1],a=document.activeElement;if(t.contains(a))if(e.shiftKey)if(a===i)s.focus();else{const e=o.indexOf(a);e>0&&o[e-1].focus()}else if(a===s)i.focus();else{const e=o.indexOf(a);e<o.length-1&&o[e+1].focus()}else i.focus()}function o(e){const t=e.querySelectorAll(".gb-menu-toggle"),o=e.querySelector(".gb-menu-container"),i=o.querySelector(".gb-menu"),s=i.querySelectorAll(".menu-item"),a=i.querySelectorAll(".menu-item > a"),r=i.querySelectorAll(".gb-submenu-toggle"),l=e.closest("body");requestAnimationFrame((()=>{l.removeAttribute("data-gb-menu-open"),e.classList.remove("gb-navigation--open"),o&&(o.classList.remove("gb-menu-container--toggled"),o.removeEventListener("keydown",n)),t.forEach((e=>{e&&(e.classList.remove("gb-menu-toggle--toggled"),e.ariaExpanded="false",(e.offsetHeight>0||e.offsetWidth>0)&&e.focus())})),s?.length>0&&s.forEach((e=>{e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open")})),a?.length>0&&a.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")})),r?.length>0&&r.forEach((e=>{e.hasAttribute("aria-expanded")&&e.setAttribute("aria-expanded","false")}))}))}function i(e,t=null){if(!e)return;const n=e.querySelectorAll(".menu-item.gb-sub-menu--open");n&&Array.from(n).filter((e=>!e.contains(t))).forEach((e=>{const t=e.querySelector("a"),n=e.querySelector(".gb-submenu-toggle");e.classList.remove("current-menu-item"),e.classList.remove("gb-sub-menu--open"),e.setAttribute("aria-current","false"),t&&t.hasAttribute("aria-expanded")&&t.setAttribute("aria-expanded","false"),n&&n.hasAttribute("aria-expanded")&&n.setAttribute("aria-expanded","false")}))}function s(e,t=!1){if(e){t&&t.preventDefault();const n=e.closest(".gb-navigation"),o=e.closest(".menu-item"),s="true"===e.getAttribute("aria-expanded");i(n,o),e.setAttribute("aria-expanded",s?"false":"true"),o.classList.toggle("current-menu-item"),o.classList.toggle("gb-sub-menu--open")}}function a(e,t=!1){if(e){t&&t.preventDefault();const n=t.type,o=e.closest(".gb-menu-container--toggled"),i=e.closest(".gb-menu--hover");if("click"===n&&i&&!o)return;const s=e.closest(".menu-item"),a="true"===e.getAttribute("aria-expanded");e.setAttribute("aria-expanded",a?"false":"true"),s.classList.toggle("current-menu-item"),s.classList.toggle("gb-sub-menu--open")}}function r(e){e&&e.forEach((e=>{var t;const n=e.querySelector(".gb-menu-toggle"),i=e.querySelector(".gb-menu-container"),s=null!==(t=e.getAttribute("data-gb-mobile-breakpoint"))&&void 0!==t?t:"",a=window.matchMedia(`(max-width: ${s})`);n&&i&&n.setAttribute("aria-controls",i.id),e.classList.toggle("gb-navigation--mobile",a.matches),i.classList.toggle("gb-menu-container--mobile",a.matches),a.addEventListener("change",(t=>{e.classList.toggle("gb-navigation--mobile",t.matches),i.classList.toggle("gb-menu-container--mobile",t.matches),o(e)})),setTimeout((()=>{const t=e.querySelector(".gb-menu");if(t){const e=t.querySelectorAll(".menu-item-has-children");e.length>0&&requestAnimationFrame((()=>{e.forEach((e=>{const n=e.querySelector("a"),o=t.classList.contains("gb-menu--click")?n:e.querySelector(".gb-submenu-toggle");if(o){o.setAttribute("aria-controls",`sub-menu-${e.id}`),o.setAttribute("aria-label",`Toggle submenu for ${n.textContent}`);const t=e.querySelector(".gb-sub-menu");t&&(t.id=`sub-menu-${e.id}`)}}))}))}}),0)}))}function l(){let e=document.querySelectorAll(".gb-navigation");if(!e.length){const t=window.frameElement;if(t&&t.id&&t.id.startsWith("pattern-"))return void new MutationObserver(((t,n)=>{e=document.querySelectorAll(".gb-navigation"),e.length&&(n.disconnect(),r(e))})).observe(document.body,{childList:!0,subtree:!0})}r(e),function(){const e=document.querySelectorAll(".gb-navigation--mobile");e&&e.length&&e.forEach((e=>{e.addEventListener("click",(t=>{const n=t.target.closest('a[href*="#"]');if(!n)return;const i=n.getAttribute("href").match(/#(.+)$/);if(i){const t=i[1];document.getElementById(t)&&setTimeout((()=>{o(e)}),50)}}))}))}()}t.d=(e,n)=>{for(var o in n)t.o(n,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:n[o]})},t.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),window.myNavigationScriptInitialized||(window.myNavigationScriptInitialized=!0,document.addEventListener("click",(e=>{const t=e.target;!function(e){if(e){var t;const i=e.closest(".gb-navigation");if(!i)return;i.classList.contains("gb-navigation--open")?o(i):function(e){const t=e.querySelectorAll(".gb-menu-toggle"),o=e.querySelector(".gb-menu-container"),i=e.getAttribute("data-gb-mobile-menu-type"),s=o.querySelector(".gb-menu-toggle:not(.gb-menu-toggle--clone)"),a=s||"full-overlay"!==i?null:o.querySelector("*"),r=e.closest("body");let l=!1;requestAnimationFrame((()=>{if(e.classList.add("gb-navigation--open"),r.setAttribute("data-gb-menu-open",i),t.forEach((e=>{if(e&&(e.classList.add("gb-menu-toggle--toggled"),e.ariaExpanded="true",!s&&o&&"full-overlay"===i)){a&&(a.style.opacity="0");const t=e.closest(".editor-styles-wrapper"),n=o.querySelector(".gb-menu-toggle--clone");if(t&&n){const t=e.attributes;for(const e of t)n.setAttribute(e.name,e.value);n.innerHTML=e.innerHTML,n.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),l=!0}else if(!n){const t=e.cloneNode(!0);t.classList.add("gb-menu-toggle","gb-menu-toggle--toggled","gb-menu-toggle--clone"),o.insertAdjacentElement("afterbegin",t),l=!0}}})),l&&a?requestAnimationFrame((()=>{!function(e,t=()=>{}){const n=e.querySelector(".gb-menu-container .gb-menu-toggle");if(n){var o,i;const s=window.getComputedStyle(n),a=null!==(o=parseInt(s?.top,10))&&void 0!==o?o:0,r=null!==(i=parseInt(s?.height,10))&&void 0!==i?i:0;requestAnimationFrame((()=>{e.style.setProperty("--gb-menu-toggle-offset",r+2*a+"px"),t()}))}}(e,(()=>{a.style.opacity=""}))})):a&&"0"===a.style.opacity&&(a.style.opacity=""),o){o.classList.add("gb-menu-container--toggled");const e=o.querySelector('a[href], button:not([disabled]), input:not([disabled]), textarea:not([disabled]), select:not([disabled]), [role="button"]:not([disabled]), [tabindex="0"]');e?e.focus():o.focus(),o.addEventListener("keydown",n)}})),"partial-overlay"===i&&function(e){const t=(n=function(e){var t;const n=null!==(t=e.getAttribute("data-gb-menu-toggle-anchor"))&&void 0!==t?t:"";let o=".gb-navigation";return n?o=n:e.closest(".gb-site-header")&&(o=".gb-site-header"),e.closest(o)}(e))?n.getBoundingClientRect().bottom:0;var n;requestAnimationFrame((()=>e.style.setProperty("--gb-menu-offset",t+"px")))}(e)}(i);const s=null!==(t=window.frameElement)&&void 0!==t&&t;if(s&&s.id&&s.id.startsWith("pattern-"))if(i.classList.contains("gb-navigation--open")){const e=s.getAttribute("data-gb-original-height");e&&(s.style.height=e)}else s.style.height&&parseInt(s.style.height,10)<800&&(s.setAttribute("data-gb-original-height",s.style.height),requestAnimationFrame((()=>s.style.height="800px")))}}(t.closest(".gb-menu-toggle")),s(t.closest(".gb-menu--click .menu-item-has-children > a"),e),a(t.closest(".gb-submenu-toggle"),e);const r=document.querySelector(".menu-item.gb-sub-menu--open");r&&!r.contains(e.target)&&i(r.closest(".gb-navigation:not(.gb-navigation--open)"))})),document.addEventListener("keydown",(e=>{const t="Escape"===e.key,n="Enter"===e.key,r=" "===e.key,l="Tab"===e.key;if((n||r)&&(a(e.target.closest(".gb-submenu-toggle"),e),s(e.target.closest(".gb-menu--click .menu-item-has-children > a"),e)),l){const e=document.querySelector(".gb-sub-menu--open");e&&setTimeout((()=>{const t=document.activeElement;t.closest(".gb-sub-menu--open")||i(e.closest(".gb-navigation"),t)}),0)}if(t){const t=e.target.closest(".gb-sub-menu--open");if(t){i(t.closest(".gb-navigation"));const e=t.querySelector(".gb-submenu-toggle");e&&e.focus()}else{const e=document.querySelector(".gb-navigation--open");e&&o(e)}}})),window.addEventListener("pagehide",(()=>{const e=document.querySelectorAll(".gb-navigation--open");e.length&&e.forEach((e=>o(e)))})),e=()=>{document.querySelector(".editor-styles-wrapper, .wp-admin")?window.addEventListener("load",l):l()},"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",e):e()))})();