(()=>{var e={9224:e=>{var t;globalThis,t=()=>(()=>{var e={703:(e,t,n)=>{"use strict";var r=n(414);function o(){}function l(){}l.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,l,i){if(i!==r){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:l,resetWarningCache:o};return n.PropTypes=n,n}},697:(e,t,n)=>{e.exports=n(703)()},414:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},921:(e,t)=>{"use strict";Symbol.for("react.element"),Symbol.for("react.portal"),Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.provider"),Symbol.for("react.context"),Symbol.for("react.server_context"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.suspense_list"),Symbol.for("react.memo"),Symbol.for("react.lazy"),Symbol.for("react.offscreen"),Symbol.for("react.module.reference")},864:(e,t,n)=>{"use strict";n(921)},251:(e,t,n)=>{"use strict";var r=n(196),o=Symbol.for("react.element"),l=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,n){var r,s={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)l.call(t,r)&&!a.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===s[r]&&(s[r]=t[r]);return{$$typeof:o,type:e,key:u,ref:c,props:s,_owner:i.current}}},893:(e,t,n)=>{"use strict";e.exports=n(251)},196:e=>{"use strict";e.exports=window.React}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nc=void 0;var r={};return(()=>{"use strict";n.r(r),n.d(r,{Autocomplete:()=>Mt,Checkbox:()=>Vt,ColorPicker:()=>Yn,IconControl:()=>Hi,IconLibrary:()=>Ai,IconModal:()=>Bi,MultiSelect:()=>Dt,OpenPanel:()=>Li,SelectMeta:()=>Ui,SelectPost:()=>ja,SelectPostType:()=>Ga,SelectTaxonomy:()=>Ua,SelectTerm:()=>qa,SelectUser:()=>Ka,SortableList:()=>Fl,SortableListItem:()=>Pl,Stack:()=>Nl,Table:()=>Fi,useOption:()=>Ya,usePostMeta:()=>Xa,usePostRecord:()=>Wa,usePostTypes:()=>Ha,useTaxonomies:()=>es,useTermRecord:()=>$a,useTerms:()=>Za,useUserRecord:()=>Qa,useUsers:()=>Ja});var e=n(196),t=n.n(e);const o=window.wp.element,l=window.wp.components,i=window.wp.i18n,a=window.wp.primitives;var s=n(893);const u=(0,s.jsx)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(a.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"})});function c(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}function d(){return d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},d.apply(null,arguments)}var g=n(697),h=n.n(g);n(864);const p=e=>"object"==typeof e&&null!=e&&1===e.nodeType,f=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,m=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const n=getComputedStyle(e,null);return f(n.overflowY,t)||f(n.overflowX,t)||(e=>{const t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},v=(e,t,n,r,o,l,i,a)=>l<e&&i>t||l>e&&i<t?0:l<=e&&a<=n||i>=t&&a>=n?l-e-r:i>t&&a<n||l<e&&a>n?i-t+o:0,b=e=>{const t=e.parentElement;return null==t?e.getRootNode().host||null:t};var y=function(){return y=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},y.apply(this,arguments)};Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var w=0;function C(){}function S(e,t,n){return e===t||t instanceof n.Node&&e.contains&&e.contains(t)}function x(e,t){var n;function r(){n&&clearTimeout(n)}function o(){for(var o=arguments.length,l=new Array(o),i=0;i<o;i++)l[i]=arguments[i];r(),n=setTimeout((function(){n=null,e.apply(void 0,l)}),t)}return o.cancel=r,o}function I(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return t.some((function(t){return t&&t.apply(void 0,[e].concat(r)),e.preventDownshiftDefault||e.hasOwnProperty("nativeEvent")&&e.nativeEvent.preventDownshiftDefault}))}}function R(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){t.forEach((function(t){"function"==typeof t?t(e):t&&(t.current=e)}))}}function E(e,t){return e&&t?Object.keys(e).reduce((function(n,r){return n[r]=M(t,r)?t[r]:e[r],n}),{}):e}function M(e,t){return void 0!==e[t]}function _(e){var t=e.key,n=e.keyCode;return n>=37&&n<=40&&0!==t.indexOf("Arrow")?"Arrow"+t:t}function k(e,t,n,r,o){void 0===o&&(o=!1);var l=n.length;if(0===l)return-1;var i=l-1;("number"!=typeof e||e<0||e>i)&&(e=t>0?-1:i+1);var a=e+t;a<0?a=o?i:0:a>i&&(a=o?0:i);var s=D(a,t<0,n,r,o);return-1===s?e>=l?-1:e:s}function D(e,t,n,r,o){void 0===o&&(o=!1);var l=n.length;if(t){for(var i=e;i>=0;i--)if(!r(n[i],i))return i}else for(var a=e;a<l;a++)if(!r(n[a],a))return a;return o?D(t?l-1:0,t,n,r):-1}function O(e,t,n,r){return void 0===r&&(r=!0),n&&t.some((function(t){return t&&(S(t,e,n)||r&&S(t,n.document.activeElement,n))}))}var P=x((function(e){F(e).textContent=""}),500);function F(e){var t=e.getElementById("a11y-status-message");return t||((t=e.createElement("div")).setAttribute("id","a11y-status-message"),t.setAttribute("role","status"),t.setAttribute("aria-live","polite"),t.setAttribute("aria-relevant","additions text"),Object.assign(t.style,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px"}),e.body.appendChild(t),t)}var T={highlightedIndex:-1,isOpen:!1,selectedItem:null,inputValue:""};var N=x((function(e,t){!function(e,t){e&&t&&(F(t).textContent=e,P(t))}(e,t)}),200),L="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?e.useLayoutEffect:e.useEffect,V="useId"in t()?function(n){var r=n.id,o=n.labelId,l=n.menuId,i=n.getItemId,a=n.toggleButtonId,s=n.inputId,u="downshift-"+t().useId();return r||(r=u),(0,e.useRef)({labelId:o||r+"-label",menuId:l||r+"-menu",getItemId:i||function(e){return r+"-item-"+e},toggleButtonId:a||r+"-toggle-button",inputId:s||r+"-input"}).current}:function(t){var n=t.id,r=void 0===n?"downshift-"+String(w++):n,o=t.labelId,l=t.menuId,i=t.getItemId,a=t.toggleButtonId,s=t.inputId;return(0,e.useRef)({labelId:o||r+"-label",menuId:l||r+"-menu",getItemId:i||function(e){return r+"-item-"+e},toggleButtonId:a||r+"-toggle-button",inputId:s||r+"-input"}).current};function A(e,t,n,r){var o,l;if(void 0===e){if(void 0===t)throw new Error(r);o=n[t],l=t}else l=void 0===t?n.indexOf(e):t,o=e;return[o,l]}function z(e){return""+e.slice(0,1).toUpperCase()+e.slice(1)}function B(t){var n=(0,e.useRef)(t);return n.current=t,n}function H(t,n,r,o){var l=(0,e.useRef)(),i=(0,e.useRef)(),a=(0,e.useCallback)((function(e,n){i.current=n,e=E(e,n.props);var r=t(e,n);return n.props.stateReducer(e,d({},n,{changes:r}))}),[t]),s=(0,e.useReducer)(a,n,r),u=s[0],c=s[1],g=B(n),h=(0,e.useCallback)((function(e){return c(d({props:g.current},e))}),[g]),p=i.current;return(0,e.useEffect)((function(){var e=E(l.current,null==p?void 0:p.props);p&&l.current&&!o(e,u)&&function(e,t,n){var r=e.props,o=e.type,l={};Object.keys(t).forEach((function(r){!function(e,t,n,r){var o=t.props,l=t.type,i="on"+z(e)+"Change";o[i]&&void 0!==r[e]&&r[e]!==n[e]&&o[i](d({type:l},r))}(r,e,t,n),n[r]!==t[r]&&(l[r]=n[r])})),r.onStateChange&&Object.keys(l).length&&r.onStateChange(d({type:o},l))}(p,e,u),l.current=u}),[u,p,o]),[u,h]}var j={itemToString:function(e){return e?String(e):""},itemToKey:function(e){return e},stateReducer:function(e,t){return t.changes},scrollIntoView:function(e,t){if(e){var n=((e,t)=>{var n,r,o,l;if("undefined"==typeof document)return[];const{scrollMode:i,block:a,inline:s,boundary:u,skipOverflowHiddenElements:c}=t,d="function"==typeof u?u:e=>e!==u;if(!p(e))throw new TypeError("Invalid target");const g=document.scrollingElement||document.documentElement,h=[];let f=e;for(;p(f)&&d(f);){if(f=b(f),f===g){h.push(f);break}null!=f&&f===document.body&&m(f)&&!m(document.documentElement)||null!=f&&m(f,c)&&h.push(f)}const y=null!=(r=null==(n=window.visualViewport)?void 0:n.width)?r:innerWidth,w=null!=(l=null==(o=window.visualViewport)?void 0:o.height)?l:innerHeight,{scrollX:C,scrollY:S}=window,{height:x,width:I,top:R,right:E,bottom:M,left:_}=e.getBoundingClientRect(),{top:k,right:D,bottom:O,left:P}=(e=>{const t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);let F="start"===a||"nearest"===a?R-k:"end"===a?M+O:R+x/2-k+O,T="center"===s?_+I/2-P+D:"end"===s?E+D:_-P;const N=[];for(let e=0;e<h.length;e++){const t=h[e],{height:n,width:r,top:o,right:l,bottom:u,left:c}=t.getBoundingClientRect();if("if-needed"===i&&R>=0&&_>=0&&M<=w&&E<=y&&R>=o&&M<=u&&_>=c&&E<=l)return N;const d=getComputedStyle(t),p=parseInt(d.borderLeftWidth,10),f=parseInt(d.borderTopWidth,10),m=parseInt(d.borderRightWidth,10),b=parseInt(d.borderBottomWidth,10);let k=0,D=0;const O="offsetWidth"in t?t.offsetWidth-t.clientWidth-p-m:0,P="offsetHeight"in t?t.offsetHeight-t.clientHeight-f-b:0,L="offsetWidth"in t?0===t.offsetWidth?0:r/t.offsetWidth:0,V="offsetHeight"in t?0===t.offsetHeight?0:n/t.offsetHeight:0;if(g===t)k="start"===a?F:"end"===a?F-w:"nearest"===a?v(S,S+w,w,f,b,S+F,S+F+x,x):F-w/2,D="start"===s?T:"center"===s?T-y/2:"end"===s?T-y:v(C,C+y,y,p,m,C+T,C+T+I,I),k=Math.max(0,k+S),D=Math.max(0,D+C);else{k="start"===a?F-o-f:"end"===a?F-u+b+P:"nearest"===a?v(o,u,n,f,b+P,F,F+x,x):F-(o+n/2)+P/2,D="start"===s?T-c-p:"center"===s?T-(c+r/2)+O/2:"end"===s?T-l+m+O:v(c,l,r,p,m+O,T,T+I,I);const{scrollLeft:e,scrollTop:i}=t;k=0===V?0:Math.max(0,Math.min(i+k/V,t.scrollHeight-n/V+P)),D=0===L?0:Math.max(0,Math.min(e+D/L,t.scrollWidth-r/L+O)),F+=i-k,T+=e-D}N.push({el:t,top:k,left:D})}return N})(e,{boundary:t,block:"nearest",scrollMode:"if-needed"});n.forEach((function(e){var t=e.el,n=e.top,r=e.left;t.scrollTop=n,t.scrollLeft=r}))}},environment:"undefined"==typeof window?void 0:window};function K(e,t,n){void 0===n&&(n=T);var r=e["default"+z(t)];return void 0!==r?r:n[t]}function G(e,t,n){void 0===n&&(n=T);var r=e[t];if(void 0!==r)return r;var o=e["initial"+z(t)];return void 0!==o?o:K(e,t,n)}function q(e,t,n){var r=e.items,o=e.initialHighlightedIndex,l=e.defaultHighlightedIndex,i=e.isItemDisabled,a=e.itemToKey,s=t.selectedItem,u=t.highlightedIndex;return 0===r.length?-1:void 0===o||u!==o||i(r[o],o)?void 0===l||i(r[l],l)?s?r.findIndex((function(e){return a(s)===a(e)})):n<0&&!i(r[r.length-1],r.length-1)?r.length-1:n>0&&!i(r[0],0)?0:-1:l:o}var U=function(){return C};function W(t,n,r,o){void 0===o&&(o={});var l=o.document,i=Y();(0,e.useEffect)((function(){if(t&&!i&&l){var e=t(n);N(e,l)}}),r),(0,e.useEffect)((function(){return function(){var e,t;N.cancel(),(t=null==(e=l)?void 0:e.getElementById("a11y-status-message"))&&t.remove()}}),[l])}var Q=C;function $(e,t,n){var r;return void 0===n&&(n=!0),d({isOpen:!1,highlightedIndex:-1},(null==(r=e.items)?void 0:r.length)&&t>=0&&d({selectedItem:e.items[t],isOpen:K(e,"isOpen"),highlightedIndex:K(e,"highlightedIndex")},n&&{inputValue:e.itemToString(e.items[t])}))}function X(e,t){return e.isOpen===t.isOpen&&e.inputValue===t.inputValue&&e.highlightedIndex===t.highlightedIndex&&e.selectedItem===t.selectedItem}function Y(){var e=t().useRef(!0);return t().useEffect((function(){return e.current=!1,function(){e.current=!0}}),[]),e.current}function J(e){var t=K(e,"highlightedIndex");return t>-1&&e.isItemDisabled(e.items[t],t)?-1:t}var Z=d({},{environment:h().shape({addEventListener:h().func.isRequired,removeEventListener:h().func.isRequired,document:h().shape({createElement:h().func.isRequired,getElementById:h().func.isRequired,activeElement:h().any.isRequired,body:h().any.isRequired}).isRequired,Node:h().func.isRequired}),itemToString:h().func,itemToKey:h().func,stateReducer:h().func},{getA11yStatusMessage:h().func,highlightedIndex:h().number,defaultHighlightedIndex:h().number,initialHighlightedIndex:h().number,isOpen:h().bool,defaultIsOpen:h().bool,initialIsOpen:h().bool,selectedItem:h().any,initialSelectedItem:h().any,defaultSelectedItem:h().any,id:h().string,labelId:h().string,menuId:h().string,getItemId:h().func,toggleButtonId:h().string,onSelectedItemChange:h().func,onHighlightedIndexChange:h().func,onStateChange:h().func,onIsOpenChange:h().func,scrollIntoView:h().func});y(y({},Z),{items:h().array.isRequired,isItemDisabled:h().func}),y(y({},j),{isItemDisabled:function(){return!1}});var ee=0,te=1,ne=2,re=3,oe=4,le=5,ie=6,ae=7,se=8,ue=9,ce=10,de=11,ge=12,he=13,pe=14,fe=15,me=16,ve=17,be=18,ye=19,we=20,Ce=21,Se=22,xe=Object.freeze({__proto__:null,ControlledPropUpdatedSelectedItem:Se,FunctionCloseMenu:ve,FunctionOpenMenu:me,FunctionReset:Ce,FunctionSelectItem:ye,FunctionSetHighlightedIndex:be,FunctionSetInputValue:we,FunctionToggleMenu:fe,InputBlur:ue,InputChange:se,InputClick:ce,InputKeyDownArrowDown:ee,InputKeyDownArrowUp:te,InputKeyDownEnd:oe,InputKeyDownEnter:ae,InputKeyDownEscape:ne,InputKeyDownHome:re,InputKeyDownPageDown:ie,InputKeyDownPageUp:le,ItemClick:he,ItemMouseMove:ge,MenuMouseLeave:de,ToggleButtonClick:pe});function Ie(e){var t=function(e){var t=G(e,"selectedItem"),n=G(e,"isOpen"),r=function(e){var t=G(e,"highlightedIndex");return t>-1&&e.isItemDisabled(e.items[t],t)?-1:t}(e),o=G(e,"inputValue");return{highlightedIndex:r<0&&t&&n?e.items.findIndex((function(n){return e.itemToKey(n)===e.itemToKey(t)})):r,isOpen:n,selectedItem:t,inputValue:o}}(e),n=t.selectedItem,r=t.inputValue;return""===r&&n&&void 0===e.defaultInputValue&&void 0===e.initialInputValue&&void 0===e.inputValue&&(r=e.itemToString(n)),d({},t,{inputValue:r})}d({},Z,{items:h().array.isRequired,isItemDisabled:h().func,inputValue:h().string,defaultInputValue:h().string,initialInputValue:h().string,inputId:h().string,onInputValueChange:h().func});var Re=C,Ee=d({},j,{isItemDisabled:function(){return!1}});function Me(e,t){var n,r,o=t.type,l=t.props,i=t.altKey;switch(o){case he:r={isOpen:K(l,"isOpen"),highlightedIndex:J(l),selectedItem:l.items[t.index],inputValue:l.itemToString(l.items[t.index])};break;case ee:r=e.isOpen?{highlightedIndex:k(e.highlightedIndex,1,l.items,l.isItemDisabled,!0)}:{highlightedIndex:i&&null==e.selectedItem?-1:q(l,e,1),isOpen:l.items.length>=0};break;case te:r=e.isOpen?i?$(l,e.highlightedIndex):{highlightedIndex:k(e.highlightedIndex,-1,l.items,l.isItemDisabled,!0)}:{highlightedIndex:q(l,e,-1),isOpen:l.items.length>=0};break;case ae:r=$(l,e.highlightedIndex);break;case ne:r=d({isOpen:!1,highlightedIndex:-1},!e.isOpen&&{selectedItem:null,inputValue:""});break;case le:r={highlightedIndex:k(e.highlightedIndex,-10,l.items,l.isItemDisabled,!0)};break;case ie:r={highlightedIndex:k(e.highlightedIndex,10,l.items,l.isItemDisabled,!0)};break;case re:r={highlightedIndex:D(0,!1,l.items,l.isItemDisabled)};break;case oe:r={highlightedIndex:D(l.items.length-1,!0,l.items,l.isItemDisabled)};break;case ue:r=d({isOpen:!1,highlightedIndex:-1},e.highlightedIndex>=0&&(null==(n=l.items)?void 0:n.length)&&t.selectItem&&{selectedItem:l.items[e.highlightedIndex],inputValue:l.itemToString(l.items[e.highlightedIndex])});break;case se:r={isOpen:!0,highlightedIndex:J(l),inputValue:t.inputValue};break;case ce:r={isOpen:!e.isOpen,highlightedIndex:e.isOpen?-1:q(l,e,0)};break;case ye:r={selectedItem:t.selectedItem,inputValue:l.itemToString(t.selectedItem)};break;case Se:r={inputValue:t.inputValue};break;default:return function(e,t,n){var r,o=t.type,l=t.props;switch(o){case n.ItemMouseMove:r={highlightedIndex:t.disabled?-1:t.index};break;case n.MenuMouseLeave:r={highlightedIndex:-1};break;case n.ToggleButtonClick:case n.FunctionToggleMenu:r={isOpen:!e.isOpen,highlightedIndex:e.isOpen?-1:q(l,e,0)};break;case n.FunctionOpenMenu:r={isOpen:!0,highlightedIndex:q(l,e,0)};break;case n.FunctionCloseMenu:r={isOpen:!1};break;case n.FunctionSetHighlightedIndex:r={highlightedIndex:l.isItemDisabled(l.items[t.highlightedIndex],t.highlightedIndex)?-1:t.highlightedIndex};break;case n.FunctionSetInputValue:r={inputValue:t.inputValue};break;case n.FunctionReset:r={highlightedIndex:J(l),isOpen:K(l,"isOpen"),selectedItem:K(l,"selectedItem"),inputValue:K(l,"inputValue")};break;default:throw new Error("Reducer called without proper action type.")}return d({},e,r)}(e,t,xe)}return d({},e,r)}var _e=["onMouseLeave","refKey","ref"],ke=["item","index","refKey","ref","onMouseMove","onMouseDown","onClick","onPress","disabled"],De=["onClick","onPress","refKey","ref"],Oe=["onKeyDown","onChange","onInput","onBlur","onChangeText","onClick","refKey","ref"];function Pe(t){void 0===t&&(t={}),Re(t,Pe);var n=d({},Ee,t),r=n.items,o=n.scrollIntoView,l=n.environment,i=n.getA11yStatusMessage,a=function(t,n,r,o){var l=(0,e.useRef)(),i=H(t,n,r,o),a=i[0],s=i[1],u=Y();return(0,e.useEffect)((function(){M(n,"selectedItem")&&(u||n.itemToKey(n.selectedItem)!==n.itemToKey(l.current)&&s({type:Se,inputValue:n.itemToString(n.selectedItem)}),l.current=a.selectedItem===l.current?n.selectedItem:a.selectedItem)}),[a.selectedItem,n.selectedItem]),[E(a,n),s]}(Me,n,Ie,X),s=a[0],u=a[1],g=s.isOpen,h=s.highlightedIndex,p=s.selectedItem,f=s.inputValue,m=(0,e.useRef)(null),v=(0,e.useRef)({}),b=(0,e.useRef)(null),y=(0,e.useRef)(null),w=Y(),S=V(n),x=(0,e.useRef)(),k=B({state:s,props:n}),D=(0,e.useCallback)((function(e){return v.current[S.getItemId(e)]}),[S]);W(i,s,[g,h,p,f],l);var P=function(t){var n=t.highlightedIndex,r=t.isOpen,o=t.itemRefs,l=t.getItemNodeFromIndex,i=t.menuElement,a=t.scrollIntoView,s=(0,e.useRef)(!0);return L((function(){n<0||!r||!Object.keys(o.current).length||(!1===s.current?s.current=!0:a(l(n),i))}),[n]),s}({menuElement:m.current,highlightedIndex:h,isOpen:g,itemRefs:v,scrollIntoView:o,getItemNodeFromIndex:D});Q({props:n,state:s}),(0,e.useEffect)((function(){G(n,"isOpen")&&b.current&&b.current.focus()}),[]),(0,e.useEffect)((function(){w||(x.current=r.length)}));var F=function(t,n,r){var o=(0,e.useRef)({isMouseDown:!1,isTouchMove:!1,isTouchEnd:!1});return(0,e.useEffect)((function(){if(!t)return C;var e=n.map((function(e){return e.current}));function l(){o.current.isTouchEnd=!1,o.current.isMouseDown=!0}function i(n){o.current.isMouseDown=!1,O(n.target,e,t)||r()}function a(){o.current.isTouchEnd=!1,o.current.isTouchMove=!1}function s(){o.current.isTouchMove=!0}function u(n){o.current.isTouchEnd=!0,o.current.isTouchMove||O(n.target,e,t,!1)||r()}return t.addEventListener("mousedown",l),t.addEventListener("mouseup",i),t.addEventListener("touchstart",a),t.addEventListener("touchmove",s),t.addEventListener("touchend",u),function(){t.removeEventListener("mousedown",l),t.removeEventListener("mouseup",i),t.removeEventListener("touchstart",a),t.removeEventListener("touchmove",s),t.removeEventListener("touchend",u)}}),[t,r]),o.current}(l,[y,m,b],(0,e.useCallback)((function(){k.current.state.isOpen&&u({type:ue,selectItem:!1})}),[u,k])),T=U("getInputProps","getMenuProps");(0,e.useEffect)((function(){g||(v.current={})}),[g]),(0,e.useEffect)((function(){var e;g&&null!=l&&l.document&&null!=b&&null!=(e=b.current)&&e.focus&&l.document.activeElement!==b.current&&b.current.focus()}),[g,l]);var N=(0,e.useMemo)((function(){return{ArrowDown:function(e){e.preventDefault(),u({type:ee,altKey:e.altKey})},ArrowUp:function(e){e.preventDefault(),u({type:te,altKey:e.altKey})},Home:function(e){k.current.state.isOpen&&(e.preventDefault(),u({type:re}))},End:function(e){k.current.state.isOpen&&(e.preventDefault(),u({type:oe}))},Escape:function(e){var t=k.current.state;(t.isOpen||t.inputValue||t.selectedItem||t.highlightedIndex>-1)&&(e.preventDefault(),u({type:ne}))},Enter:function(e){k.current.state.isOpen&&229!==e.which&&(e.preventDefault(),u({type:ae}))},PageUp:function(e){k.current.state.isOpen&&(e.preventDefault(),u({type:le}))},PageDown:function(e){k.current.state.isOpen&&(e.preventDefault(),u({type:ie}))}}}),[u,k]),z=(0,e.useCallback)((function(e){return d({id:S.labelId,htmlFor:S.inputId},e)}),[S]),j=(0,e.useCallback)((function(e,t){var n,r=void 0===e?{}:e,o=r.onMouseLeave,l=r.refKey,i=void 0===l?"ref":l,a=r.ref,s=c(r,_e),g=(void 0===t?{}:t).suppressRefError;return T("getMenuProps",void 0!==g&&g,i,m),d(((n={})[i]=R(a,(function(e){m.current=e})),n.id=S.menuId,n.role="listbox",n["aria-labelledby"]=s&&s["aria-label"]?void 0:""+S.labelId,n.onMouseLeave=I(o,(function(){u({type:de})})),n),s)}),[u,T,S]),K=(0,e.useCallback)((function(e){var t,n,r=void 0===e?{}:e,o=r.item,l=r.index,i=r.refKey,a=void 0===i?"ref":i,s=r.ref,g=r.onMouseMove,h=r.onMouseDown,p=r.onClick;r.onPress;var f=r.disabled,m=c(r,ke);void 0!==f&&console.warn('Passing "disabled" as an argument to getItemProps is not supported anymore. Please use the isItemDisabled prop from useCombobox.');var b=k.current,y=b.props,w=b.state,C=A(o,l,y.items,"Pass either item or index to getItemProps!"),x=C[0],E=C[1],M=y.isItemDisabled(x,E),_=p;return d(((t={})[a]=R(s,(function(e){e&&(v.current[S.getItemId(E)]=e)})),t["aria-disabled"]=M,t["aria-selected"]=E===w.highlightedIndex,t.id=S.getItemId(E),t.role="option",t),!M&&((n={}).onClick=I(_,(function(){u({type:he,index:E})})),n),{onMouseMove:I(g,(function(){F.isTouchEnd||E===w.highlightedIndex||(P.current=!1,u({type:ge,index:E,disabled:M}))})),onMouseDown:I(h,(function(e){return e.preventDefault()}))},m)}),[u,S,k,F,P]),q=(0,e.useCallback)((function(e){var t,n=void 0===e?{}:e,r=n.onClick;n.onPress;var o=n.refKey,l=void 0===o?"ref":o,i=n.ref,a=c(n,De),s=k.current.state;return d(((t={})[l]=R(i,(function(e){y.current=e})),t["aria-controls"]=S.menuId,t["aria-expanded"]=s.isOpen,t.id=S.toggleButtonId,t.tabIndex=-1,t),!a.disabled&&d({},{onClick:I(r,(function(){u({type:pe})}))}),a)}),[u,k,S]),$=(0,e.useCallback)((function(e,t){var n,r=void 0===e?{}:e,o=r.onKeyDown,i=r.onChange,a=r.onInput,s=r.onBlur;r.onChangeText;var g=r.onClick,h=r.refKey,p=void 0===h?"ref":h,f=r.ref,m=c(r,Oe),v=(void 0===t?{}:t).suppressRefError;T("getInputProps",void 0!==v&&v,p,b);var y,w=k.current.state,C={};return m.disabled||((y={}).onChange=I(i,a,(function(e){u({type:se,inputValue:e.target.value})})),y.onKeyDown=I(o,(function(e){var t=_(e);t&&N[t]&&N[t](e)})),y.onBlur=I(s,(function(e){if(null!=l&&l.document&&w.isOpen&&!F.isMouseDown){var t=null===e.relatedTarget&&l.document.activeElement!==l.document.body;u({type:ue,selectItem:!t})}})),y.onClick=I(g,(function(){u({type:ce})})),C=y),d(((n={})[p]=R(f,(function(e){b.current=e})),n["aria-activedescendant"]=w.isOpen&&w.highlightedIndex>-1?S.getItemId(w.highlightedIndex):"",n["aria-autocomplete"]="list",n["aria-controls"]=S.menuId,n["aria-expanded"]=w.isOpen,n["aria-labelledby"]=m&&m["aria-label"]?void 0:S.labelId,n.autoComplete="off",n.id=S.inputId,n.role="combobox",n.value=w.inputValue,n),C,m)}),[u,S,l,N,k,F,T]),J=(0,e.useCallback)((function(){u({type:fe})}),[u]),Z=(0,e.useCallback)((function(){u({type:ve})}),[u]),xe=(0,e.useCallback)((function(){u({type:me})}),[u]),Fe=(0,e.useCallback)((function(e){u({type:be,highlightedIndex:e})}),[u]),Te=(0,e.useCallback)((function(e){u({type:ye,selectedItem:e})}),[u]);return{getItemProps:K,getLabelProps:z,getMenuProps:j,getInputProps:$,getToggleButtonProps:q,toggleMenu:J,openMenu:xe,closeMenu:Z,setHighlightedIndex:Fe,setInputValue:(0,e.useCallback)((function(e){u({type:we,inputValue:e})}),[u]),selectItem:Te,reset:(0,e.useCallback)((function(){u({type:Ce})}),[u]),highlightedIndex:h,isOpen:g,selectedItem:p,inputValue:f}}Pe.stateChangeTypes=xe;var Fe={activeIndex:-1,selectedItems:[]};function Te(e,t){return G(e,t,Fe)}function Ne(e,t){return K(e,t,Fe)}function Le(e){return{activeIndex:Te(e,"activeIndex"),selectedItems:Te(e,"selectedItems")}}function Ve(e){if(e.shiftKey||e.metaKey||e.ctrlKey||e.altKey)return!1;var t=e.target;return!(t instanceof HTMLInputElement&&""!==t.value)||0===t.selectionStart&&0===t.selectionEnd}function Ae(e,t){return e.selectedItems===t.selectedItems&&e.activeIndex===t.activeIndex}h().array,h().array,h().array,h().func,h().number,h().number,h().number,h().func,h().func,h().string,h().string;var ze={itemToKey:j.itemToKey,stateReducer:j.stateReducer,environment:j.environment,keyNavigationNext:"ArrowRight",keyNavigationPrevious:"ArrowLeft"},Be=C,He=0,je=1,Ke=2,Ge=3,qe=4,Ue=5,We=6,Qe=7,$e=8,Xe=9,Ye=10,Je=11,Ze=12,et=Object.freeze({__proto__:null,DropdownClick:Qe,DropdownKeyDownBackspace:We,DropdownKeyDownNavigationPrevious:Ue,FunctionAddSelectedItem:$e,FunctionRemoveSelectedItem:Xe,FunctionReset:Ze,FunctionSetActiveIndex:Je,FunctionSetSelectedItems:Ye,SelectedItemClick:He,SelectedItemKeyDownBackspace:Ke,SelectedItemKeyDownDelete:je,SelectedItemKeyDownNavigationNext:Ge,SelectedItemKeyDownNavigationPrevious:qe});function tt(e,t){var n,r=t.type,o=t.index,l=t.props,i=t.selectedItem,a=e.activeIndex,s=e.selectedItems;switch(r){case He:n={activeIndex:o};break;case qe:n={activeIndex:a-1<0?0:a-1};break;case Ge:n={activeIndex:a+1>=s.length?-1:a+1};break;case Ke:case je:if(a<0)break;var u=a;1===s.length?u=-1:a===s.length-1&&(u=s.length-2),n=d({selectedItems:[].concat(s.slice(0,a),s.slice(a+1))},{activeIndex:u});break;case Ue:n={activeIndex:s.length-1};break;case We:n={selectedItems:s.slice(0,s.length-1)};break;case $e:n={selectedItems:[].concat(s,[i])};break;case Qe:n={activeIndex:-1};break;case Xe:var c=a,g=s.findIndex((function(e){return l.itemToKey(e)===l.itemToKey(i)}));if(g<0)break;1===s.length?c=-1:g===s.length-1&&(c=s.length-2),n={selectedItems:[].concat(s.slice(0,g),s.slice(g+1)),activeIndex:c};break;case Ye:n={selectedItems:t.selectedItems};break;case Je:n={activeIndex:t.activeIndex};break;case Ze:n={activeIndex:Ne(l,"activeIndex"),selectedItems:Ne(l,"selectedItems")};break;default:throw new Error("Reducer called without proper action type.")}return d({},e,n)}var nt=["refKey","ref","onClick","onKeyDown","selectedItem","index"],rt=["refKey","ref","onKeyDown","onClick","preventKeyAction"];function ot(t){void 0===t&&(t={}),Be(t,ot);var n=d({},ze,t),r=n.getA11yStatusMessage,o=n.environment,l=n.keyNavigationNext,i=n.keyNavigationPrevious,a=function(e,t,n,r){var o=H(e,t,n,r),l=o[0],i=o[1];return[E(l,t),i]}(tt,n,Le,Ae),s=a[0],u=a[1],g=s.activeIndex,h=s.selectedItems,p=Y(),f=(0,e.useRef)(null),m=(0,e.useRef)();m.current=[];var v=B({state:s,props:n});W(r,s,[g,h],o),(0,e.useEffect)((function(){p||(-1===g&&f.current?f.current.focus():m.current[g]&&m.current[g].focus())}),[g]),Q({props:n,state:s});var b=U("getDropdownProps"),y=(0,e.useMemo)((function(){var e;return(e={})[i]=function(){u({type:qe})},e[l]=function(){u({type:Ge})},e.Delete=function(){u({type:je})},e.Backspace=function(){u({type:Ke})},e}),[u,l,i]),w=(0,e.useMemo)((function(){var e;return(e={})[i]=function(e){Ve(e)&&u({type:Ue})},e.Backspace=function(e){Ve(e)&&u({type:We})},e}),[u,i]);return{getSelectedItemProps:(0,e.useCallback)((function(e){var t,n=void 0===e?{}:e,r=n.refKey,o=void 0===r?"ref":r,l=n.ref,i=n.onClick,a=n.onKeyDown,s=n.selectedItem,g=n.index,h=c(n,nt),p=v.current.state,f=A(s,g,p.selectedItems,"Pass either item or index to getSelectedItemProps!")[1],b=f>-1&&f===p.activeIndex;return d(((t={})[o]=R(l,(function(e){e&&m.current.push(e)})),t.tabIndex=b?0:-1,t.onClick=I(i,(function(){u({type:He,index:f})})),t.onKeyDown=I(a,(function(e){var t=_(e);t&&y[t]&&y[t](e)})),t),h)}),[u,v,y]),getDropdownProps:(0,e.useCallback)((function(e,t){var n,r=void 0===e?{}:e,o=r.refKey,l=void 0===o?"ref":o,i=r.ref,a=r.onKeyDown,s=r.onClick,g=r.preventKeyAction,h=void 0!==g&&g,p=c(r,rt),m=(void 0===t?{}:t).suppressRefError;return b("getDropdownProps",void 0!==m&&m,l,f),d(((n={})[l]=R(i,(function(e){e&&(f.current=e)})),n),!h&&{onKeyDown:I(a,(function(e){var t=_(e);t&&w[t]&&w[t](e)})),onClick:I(s,(function(){u({type:Qe})}))},p)}),[u,w,b]),addSelectedItem:(0,e.useCallback)((function(e){u({type:$e,selectedItem:e})}),[u]),removeSelectedItem:(0,e.useCallback)((function(e){u({type:Xe,selectedItem:e})}),[u]),setSelectedItems:(0,e.useCallback)((function(e){u({type:Ye,selectedItems:e})}),[u]),setActiveIndex:(0,e.useCallback)((function(e){u({type:Je,activeIndex:e})}),[u]),reset:(0,e.useCallback)((function(){u({type:Ze})}),[u]),selectedItems:h,activeIndex:g}}ot.stateChangeTypes=et;const lt=window.ReactDOM;function it(e,t,n){let r,o=n.initialDeps??[];return()=>{var l,i,a,s;let u;n.key&&(null==(l=n.debug)?void 0:l.call(n))&&(u=Date.now());const c=e();if(c.length===o.length&&!c.some(((e,t)=>o[t]!==e)))return r;let d;if(o=c,n.key&&(null==(i=n.debug)?void 0:i.call(n))&&(d=Date.now()),r=t(...c),n.key&&(null==(a=n.debug)?void 0:a.call(n))){const e=Math.round(100*(Date.now()-u))/100,t=Math.round(100*(Date.now()-d))/100,r=t/16,o=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${o(t,5)} /${o(e,5)} ms`,`\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0,Math.min(120-120*r,120))}deg 100% 31%);`,null==n?void 0:n.key)}return null==(s=null==n?void 0:n.onChange)||s.call(n,r),r}}function at(e,t){if(void 0===e)throw new Error("Unexpected undefined"+(t?`: ${t}`:""));return e}const st=(e,t,n)=>{let r;return function(...o){e.clearTimeout(r),r=e.setTimeout((()=>t.apply(this,o)),n)}},ut=e=>e,ct=e=>{const t=Math.max(e.startIndex-e.overscan,0),n=Math.min(e.endIndex+e.overscan,e.count-1),r=[];for(let e=t;e<=n;e++)r.push(e);return r},dt=(e,t)=>{const n=e.scrollElement;if(!n)return;const r=e.targetWindow;if(!r)return;const o=e=>{const{width:n,height:r}=e;t({width:Math.round(n),height:Math.round(r)})};if(o(n.getBoundingClientRect()),!r.ResizeObserver)return()=>{};const l=new r.ResizeObserver((e=>{const t=e[0];if(null==t?void 0:t.borderBoxSize){const e=t.borderBoxSize[0];if(e)return void o({width:e.inlineSize,height:e.blockSize})}o(n.getBoundingClientRect())}));return l.observe(n,{box:"border-box"}),()=>{l.unobserve(n)}},gt={passive:!0},ht="undefined"==typeof window||"onscrollend"in window,pt=(e,t)=>{const n=e.scrollElement;if(!n)return;const r=e.targetWindow;if(!r)return;let o=0;const l=ht?()=>{}:st(r,(()=>{t(o,!1)}),e.options.isScrollingResetDelay),i=r=>()=>{o=n[e.options.horizontal?"scrollLeft":"scrollTop"],l(),t(o,r)},a=i(!0),s=i(!1);return s(),n.addEventListener("scroll",a,gt),n.addEventListener("scrollend",s,gt),()=>{n.removeEventListener("scroll",a),n.removeEventListener("scrollend",s)}},ft=(e,t,n)=>{if(null==t?void 0:t.borderBoxSize){const e=t.borderBoxSize[0];if(e)return Math.round(e[n.options.horizontal?"inlineSize":"blockSize"])}return Math.round(e.getBoundingClientRect()[n.options.horizontal?"width":"height"])},mt=(e,{adjustments:t=0,behavior:n},r)=>{var o,l;const i=e+t;null==(l=null==(o=r.scrollElement)?void 0:o.scrollTo)||l.call(o,{[r.options.horizontal?"left":"top"]:i,behavior:n})};class vt{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollDirection=null,this.scrollAdjustments=0,this.measureElementCache=new Map,this.observer=(()=>{let e=null;const t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver((e=>{e.forEach((e=>{this._measureElement(e.target,e)}))})):null);return{disconnect:()=>{var e;return null==(e=t())?void 0:e.disconnect()},observe:e=>{var n;return null==(n=t())?void 0:n.observe(e,{box:"border-box"})},unobserve:e=>{var n;return null==(n=t())?void 0:n.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach((([t,n])=>{void 0===n&&delete e[t]})),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:ut,rangeExtractor:ct,onChange:()=>{},measureElement:ft,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,...e}},this.notify=(e,t)=>{var n,r;const{startIndex:o,endIndex:l}=this.range??{startIndex:void 0,endIndex:void 0},i=this.calculateRange();(e||o!==(null==i?void 0:i.startIndex)||l!==(null==i?void 0:i.endIndex))&&(null==(r=(n=this.options).onChange)||r.call(n,this,t))},this.cleanup=()=>{this.unsubs.filter(Boolean).forEach((e=>e())),this.unsubs=[],this.scrollElement=null},this._didMount=()=>(this.measureElementCache.forEach(this.observer.observe),()=>{this.observer.disconnect(),this.cleanup()}),this._willUpdate=()=>{var e;const t=this.options.getScrollElement();this.scrollElement!==t&&(this.cleanup(),this.scrollElement=t,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(null==(e=this.scrollElement)?void 0:e.window)??null,this._scrollToOffset(this.scrollOffset,{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,(e=>{this.scrollRect=e,this.notify(!1,!1)}))),this.unsubs.push(this.options.observeElementOffset(this,((e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.scrollOffset<e?"forward":"backward":null,this.scrollOffset=e;const n=this.isScrolling;this.isScrolling=t,this.notify(n!==t,t)}))))},this.getSize=()=>this.scrollRect[this.options.horizontal?"width":"height"],this.getMeasurementOptions=it((()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey]),((e,t,n,r)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:n,getItemKey:r})),{key:!1}),this.getFurthestMeasurement=(e,t)=>{const n=new Map,r=new Map;for(let o=t-1;o>=0;o--){const t=e[o];if(n.has(t.lane))continue;const l=r.get(t.lane);if(null==l||t.end>l.end?r.set(t.lane,t):t.end<l.end&&n.set(t.lane,!0),n.size===this.options.lanes)break}return r.size===this.options.lanes?Array.from(r.values()).sort(((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end))[0]:void 0},this.getMeasurements=it((()=>[this.getMeasurementOptions(),this.itemSizeCache]),(({count:e,paddingStart:t,scrollMargin:n,getItemKey:r},o)=>{const l=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const i=this.measurementsCache.slice(0,l);for(let a=l;a<e;a++){const e=r(a),l=1===this.options.lanes?i[a-1]:this.getFurthestMeasurement(i,a),s=l?l.end+this.options.gap:t+n,u=o.get(e),c="number"==typeof u?u:this.options.estimateSize(a),d=s+c,g=l?l.lane:a%this.options.lanes;i[a]={index:a,start:s,size:c,end:d,key:e,lane:g}}return this.measurementsCache=i,i}),{key:!1,debug:()=>this.options.debug}),this.calculateRange=it((()=>[this.getMeasurements(),this.getSize(),this.scrollOffset]),((e,t,n)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:n}){const r=e.length-1,o=bt(0,r,(t=>e[t].start),n);let l=o;for(;l<r&&e[l].end<n+t;)l++;return{startIndex:o,endIndex:l}}({measurements:e,outerSize:t,scrollOffset:n}):null),{key:!1,debug:()=>this.options.debug}),this.getIndexes=it((()=>[this.options.rangeExtractor,this.calculateRange(),this.options.overscan,this.options.count]),((e,t,n,r)=>null===t?[]:e({startIndex:t.startIndex,endIndex:t.endIndex,overscan:n,count:r})),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=e=>{const t=this.options.indexAttribute,n=e.getAttribute(t);return n?parseInt(n,10):(console.warn(`Missing attribute name '${t}={index}' on measured element.`),-1)},this._measureElement=(e,t)=>{const n=this.measurementsCache[this.indexFromElement(e)];if(!n||!e.isConnected)return void this.measureElementCache.forEach(((t,n)=>{t===e&&(this.observer.unobserve(e),this.measureElementCache.delete(n))}));const r=this.measureElementCache.get(n.key);r!==e&&(r&&this.observer.unobserve(r),this.observer.observe(e),this.measureElementCache.set(n.key,e));const o=this.options.measureElement(e,t,this);this.resizeItem(n,o)},this.resizeItem=(e,t)=>{const n=t-(this.itemSizeCache.get(e.key)??e.size);0!==n&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(e,n,this):e.start<this.scrollOffset+this.scrollAdjustments)&&this._scrollToOffset(this.scrollOffset,{adjustments:this.scrollAdjustments+=n,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(e.index),this.itemSizeCache=new Map(this.itemSizeCache.set(e.key,t)),this.notify(!0,!1))},this.measureElement=e=>{e&&this._measureElement(e,void 0)},this.getVirtualItems=it((()=>[this.getIndexes(),this.getMeasurements()]),((e,t)=>{const n=[];for(let r=0,o=e.length;r<o;r++){const o=t[e[r]];n.push(o)}return n}),{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{const t=this.getMeasurements();return at(t[bt(0,t.length-1,(e=>at(t[e]).start),e)])},this.getOffsetForAlignment=(e,t)=>{const n=this.getSize();"auto"===t&&(t=e<=this.scrollOffset?"start":e>=this.scrollOffset+n?"end":"start"),"start"===t||("end"===t?e-=n:"center"===t&&(e-=n/2));const r=this.options.horizontal?"scrollWidth":"scrollHeight",o=(this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[r]:this.scrollElement[r]:0)-this.getSize();return Math.max(Math.min(o,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));const n=at(this.getMeasurements()[e]);if("auto"===t)if(n.end>=this.scrollOffset+this.getSize()-this.options.scrollPaddingEnd)t="end";else{if(!(n.start<=this.scrollOffset+this.options.scrollPaddingStart))return[this.scrollOffset,t];t="start"}const r="end"===t?n.end+this.options.scrollPaddingEnd:n.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(r,t),t]},this.isDynamicMode=()=>this.measureElementCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:n}={})=>{this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:n})},this.scrollToIndex=(e,{align:t="auto",behavior:n}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const[r,o]=this.getOffsetForIndex(e,t);this._scrollToOffset(r,{adjustments:void 0,behavior:n}),"smooth"!==n&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout((()=>{if(this.scrollToIndexTimeoutId=null,this.measureElementCache.has(this.options.getItemKey(e))){const[t]=this.getOffsetForIndex(e,o);((e,t)=>Math.abs(e-t)<1)(t,this.scrollOffset)||this.scrollToIndex(e,{align:o,behavior:n})}else this.scrollToIndex(e,{align:o,behavior:n})})))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.scrollOffset+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e;const t=this.getMeasurements();let n;return n=0===t.length?this.options.paddingStart:1===this.options.lanes?(null==(e=t[t.length-1])?void 0:e.end)??0:Math.max(...t.slice(-this.options.lanes).map((e=>e.end))),n-this.options.scrollMargin+this.options.paddingEnd},this._scrollToOffset=(e,{adjustments:t,behavior:n})=>{this.options.scrollToFn(e,{behavior:n,adjustments:t},this)},this.measure=()=>{var e,t;this.itemSizeCache=new Map,null==(t=(e=this.options).onChange)||t.call(e,this,!1)},this.setOptions(e),this.scrollRect=this.options.initialRect,this.scrollOffset="function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset,this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach((e=>{this.itemSizeCache.set(e.key,e.size)})),this.notify(!1,!1)}}const bt=(e,t,n,r)=>{for(;e<=t;){const o=(e+t)/2|0,l=n(o);if(l<r)e=o+1;else{if(!(l>r))return o;t=o-1}}return e>0?e-1:0},yt="undefined"!=typeof document?e.useLayoutEffect:e.useEffect;function wt(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=wt(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Ct(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=wt(e))&&(r&&(r+=" "),r+=t);return r}const St=Ct,xt={control:"EI4knPO0ECpXeQhjLORD",wrapper:"DSJXnrt8tJWVsXNrnRTo",showToggle:"qAT6zduYvPeW8j6ds36i",showClear:"MM7tD2BLzjzlIxG0waig",input:"T1lie4t5MRq74lLVCT2W",inputWrapper:"ipIKwj6TOlr4VeChVOXQ",focused:"FtZxbz0GHQnfcek9TaFz",arrow:"uV00taDXfsKrCaLNRxTk",toggle:"uZaNvWrBqCrSR4CP6Wir",menu:"B5pItXYkDzHKhYcytBw5",visible:"crTiVqU_oviiwNLX04BC",hidden:"eCB_KpH4s1EhpiVwX7RA",option:"W5BMaf4uTeT2a9zipuXL",info:"zV9ZZEMAkpr48AiZxwl5",group:"Xtl_DuPCi7Ax4NN7iCHW",clear:"wVnhKuvudIwWKGx07nl8",groupLabel:"r9GaWwkQW173DhZduO2q",nestedMenu:"EbPxrD_Go7rC_H_QLKIP",selectedItem:"_ZXvNEgdT0C5zrWKeg2S",selectedItemButton:"wExZqPhDL7_puTwvbLtv",selectedItems:"Zn85UAz3N1UBDNFeHFzG"},It=(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},(0,e.createElement)("rect",{x:"0",fill:"none",width:"20",height:"20"}),(0,e.createElement)("g",null,(0,e.createElement)("path",{d:"M14.95 6.46L11.41 10l3.54 3.54-1.41 1.41L10 11.42l-3.53 3.53-1.42-1.42L8.58 10 5.05 6.47l1.42-1.42L10 8.58l3.54-3.53z"})));function Rt(e){return e.length>0?e.reduce(((e,t)=>Array.isArray(t.items)?[...e,...t.items]:[...e,t]),[]):[]}function Et(e,t,n){var r;return null!==(r=Rt(e).find((e=>`${e[n]}`==`${t}`)))&&void 0!==r?r:null}const Mt=(0,o.forwardRef)((function({label:t,source:n,onChange:r,onSelect:a,onClear:s,onEnter:c,onVirtualChange:d,tooltipText:g,className:h="",placeholder:p="",filterInputValue:f,error:m="",help:v="",itemLabelKey:b="label",itemValueKey:y="value",toStringKey:w="label",groupItemsKey:C="items",selected:S="",beforeInputWrapper:x=null,afterInputWrapper:I=null,virtualize:R=null,showClear:E=!1,itemFilter:M=null,filterOnSelect:_=!0,noResultsText:k=(0,i.__)("No results.","generateblocks")},D){function O(e){return e?e[b===w?b:y]||e:""}const P=(0,o.useCallback)((function(e,t){return e.filter((e=>{if(""===t)return!0;const n=(e[b]||e.toString()).toLowerCase().trim(),r=(e[y]||e.toString()).toLowerCase().trim();return n.includes(t.toLowerCase())||r.includes(t.toLowerCase())}))}),[]);function F(){A(null),J("")}const T=M||P,[N,L]=(0,o.useState)(n),[V,A]=(0,o.useState)((()=>Et(N,S,y))),[z,B]=(0,o.useState)(!_),H=(0,o.useRef)(null);(0,o.useEffect)((()=>{if(S&&!V&&n.length){const e=Et(n,S,y);A(e)}else!S&&V&&A(null)}),[S,V,n]);const j=(K={count:N.length,getScrollElement:()=>H.current,estimateSize:(0,o.useCallback)((()=>33),[]),onChange:d},function(t){const n=e.useReducer((()=>({})),{})[1],r={...t,onChange:(e,r)=>{var o;r?(0,lt.flushSync)(n):n(),null==(o=t.onChange)||o.call(t,e,r)}},[o]=e.useState((()=>new vt(r)));return o.setOptions(r),e.useEffect((()=>o._didMount()),[]),yt((()=>o._willUpdate())),o}({observeElementRect:dt,observeElementOffset:pt,scrollToFn:mt,...K}));var K;const{isOpen:G,getToggleButtonProps:q,getLabelProps:U,getMenuProps:W,getInputProps:Q,highlightedIndex:$,getItemProps:X,inputValue:Y,setInputValue:J}=Pe({selectedItem:V,itemToString:O,initialInputValue:V?O(V):S,itemToKey:e=>{var t,n;return null!==(t=null!==(n=e?.id)&&void 0!==n?n:e?.[y])&&void 0!==t?t:e},items:Rt(N),onInputValueChange({inputValue:e}){L(z||!e?n:T(n,e,O,N)),r&&r(e)},onSelectedItemChange({selectedItem:e}){A(e),_||L(n),a&&a(e,F)},onHighlightedIndexChange({highlightedIndex:e,type:t}){R&&t!==Pe.stateChangeTypes.MenuMouseLeave&&j.scrollToIndex(e)},stateReducer:(0,o.useCallback)(((e,t)=>{const{type:n,changes:r}=t;switch(n){case Pe.stateChangeTypes.InputKeyDownArrowDown:case Pe.stateChangeTypes.InputClick:return _||!1!==z||B(!0),r;case Pe.stateChangeTypes.InputChange:var o;return!_&&z&&B(!1),f?{...r,inputValue:null!==(o=f(r.inputValue))&&void 0!==o?o:r.inputValue}:r;case Pe.stateChangeTypes.ItemClick:case Pe.stateChangeTypes.InputKeyDownEnter:case Pe.stateChangeTypes.InputBlur:var l;return f&&V?{...r,inputValue:null!==(l=f(r.inputValue))&&void 0!==l?l:r.inputValue}:r;default:return r}}),[_,z])});var Z,ee,te,ne;Z=()=>{S||J(""),L(n)},ee=[n],ne=(te=(0,e.useRef)(!0)).current?(te.current=!1,!0):te.current,(0,e.useEffect)((function(){if(!ne)return Z()}),ee);const{id:re,...oe}=U();let le=0;const ie=(0,o.useCallback)((function(e){c&&"Enter"===e.key&&$<0&&c(Y)}),[$,c,Y]),ae=N.length>0||0===N.length&&k;return(0,e.createElement)(l.BaseControl,{id:re,label:t,help:v||m,...oe,className:"gb-autocomplete"},(0,e.createElement)(e.Fragment,null,g&&(0,e.createElement)("div",{className:"gb-icon"},(0,e.createElement)(l.Tooltip,{text:g},(0,e.createElement)("div",null,(0,e.createElement)(l.Icon,{icon:u})))),(0,e.createElement)("div",{className:St(xt.control,R&&"is-virtualized",h)},(0,e.createElement)("div",{className:St(xt.wrapper,E&&xt.showClear,ae&&xt.showToggle)},x&&x({inputValue:Y,items:N,selectedItem:V,setSelectedItem:A}),(0,e.createElement)("div",{className:xt.inputWrapper},(0,e.createElement)("input",{placeholder:p,className:St(xt.input),type:"text",onKeyDown:ie,...Q({ref:D,onKeyDown:ie})}),E&&Y&&(0,e.createElement)(l.Button,{className:xt.clear,icon:It,label:(0,i.__)("Clear","generateblocks-pro"),onClick:()=>{s&&s(),F()}}),(N.length>0||k)&&(0,e.createElement)("button",{"aria-label":(0,i.__)("toggle menu","generateblocks-pro"),className:St(xt.toggle,"gb-autocomplete__toggle"),type:"button",...q()},(0,e.createElement)("svg",{className:xt.arrow,viewBox:"0 0 20 20",width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{transform:G?"rotate(180)":void 0,d:"M5 6l5 5 5-5 2 1-7 7-7-7 2-1z",fill:"#555",style:{transformOrigin:"center"}})))),I&&I({inputValue:Y,items:N,selectedItem:V,setSelectedItem:A})),(0,e.createElement)("ul",{...W({ref:H,className:St(xt.menu,(!G||!ae)&&xt.hidden)})},R?(0,e.createElement)(e.Fragment,null,(0,e.createElement)("li",{key:"total-size",style:{height:j.getTotalSize(),marginBottom:0}}),j.getVirtualItems().map((({index:t,size:n,start:r})=>{var o;const l=N[t],i=l[b]||l.value||l,a=null!==(o=l?.className)&&void 0!==o?o:"";return(0,e.createElement)("li",{className:St($===t&&xt.focused,V===l&&xt.selected,xt.option,a),key:`${i}${t}`,...X({index:t,item:l}),style:{position:"absolute",top:0,left:0,width:"100%",height:n,transform:`translateY(${r}px)`}},(0,e.createElement)("span",null,i))}))):G&&N.map(((t,n)=>{const r=Array.isArray(t?.[C]),o=t[b]||t.value||t;return(0,e.createElement)("li",{className:St(!r&&$===n&&["is-focused",xt.focused],!r&&V===t&&["is-selected",xt.selected],r?["gb-autocomplete__group",xt.group]:["gb-autocomplete__option",xt.option],t?.className),key:`${t}${n}`,"data-index":n,...r?{}:X({item:t,index:n})},(0,e.createElement)("div",{className:St(r&&xt.groupLabel)},o),r&&(0,e.createElement)("ul",{className:xt.nestedMenu},t[C].map((t=>{const n=le;return le++,(0,e.createElement)("li",{className:St($===n&&["is-focused",xt.focused],V===t&&["is-selected",xt.selected],xt.option,t?.className,"gb-autocomplete__option"),"data-sub-item":!0,"data-index":n,key:`${t}${n}`,...X({item:t,index:n})},(0,e.createElement)("span",null,t[b]||t.value||t))}))))})),0===N.length&&k&&(0,e.createElement)("li",{className:St(xt.option,"gb-autocomplete__option")},(0,e.createElement)("span",null,k))))))}));Mt.groupItemFilter=function(e,t,n){return Array.isArray(e)?e.map((e=>{const{items:r=[]}=e;return{...e,items:r.filter((e=>n(e).toLowerCase().includes(t.toLowerCase())))}})).filter((e=>e.items.length>0)):[]};const _t=(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},(0,e.createElement)("rect",{x:"0",fill:"none",width:"20",height:"20"}),(0,e.createElement)("g",null,(0,e.createElement)("path",{d:"M14.95 6.46L11.41 10l3.54 3.54-1.41 1.41L10 11.42l-3.53 3.53-1.42-1.42L8.58 10 5.05 6.47l1.42-1.42L10 8.58l3.54-3.53z"})));function kt(e){return e.length>0?e.reduce(((e,t)=>Array.isArray(t.items)?[...e,...t.items]:[...e,t]),[]):[]}function Dt({label:t,placeholder:n,onChange:r,onInputChange:a,className:s,help:u,noResultsText:c=(0,i.__)("No results.","generateblocks"),itemLabelKey:d="label",itemValueKey:g="value",itemInfoKey:h="info",toStringKey:p="label",groupItemsKey:f="items",value:m=[],options:v=[]}){const b=v.some((e=>Array.isArray(e[f])));function y(e){return e?e["label"===p?d:g]||e:""}const w=(0,o.useCallback)((function(e,t){return v.filter((n=>{if(""===t&&0===e.length)return!0;const r=n[d]?n[d].toLowerCase():"",o=n[g]?`${n[g]}`.toLowerCase():"",l=r.includes(t.toLowerCase())||o.includes(t.toLowerCase()),i=e.some((e=>o.includes(`${e}`?.toLowerCase()?.trim())));return!i&&l}))}),[v,d,g]),C=(0,o.useCallback)((function(e,t){return Array.isArray(v)?v.map((n=>{const{items:r=[]}=n;return{...n,items:r.filter((n=>{const r=n[d]?n[d].toLowerCase():"",o=n[g]?`${n[g]}`.toLowerCase():"",l=o.includes(t.toLowerCase())||r.includes(t.toLowerCase()),i=e.some((e=>`${e}`.toLowerCase().includes(o)));return!i&&l}))}})).filter((e=>e.items.length>0)):[]}),[v,d,g]),S=b?C:w,[x,I]=(0,o.useState)(""),[R,E]=(0,o.useState)(Array.isArray(m)?m:[m]),M=(0,o.useMemo)((()=>S(R,x)),[v,R,x]),{getSelectedItemProps:_,getDropdownProps:k,removeSelectedItem:D}=ot({selectedItems:R,onStateChange({selectedItems:e,type:t}){switch(t){case ot.stateChangeTypes.SelectedItemKeyDownBackspace:case ot.stateChangeTypes.SelectedItemKeyDownDelete:case ot.stateChangeTypes.DropdownKeyDownBackspace:case ot.stateChangeTypes.FunctionRemoveSelectedItem:E(e),r&&r(e)}}}),{isOpen:O,getToggleButtonProps:P,getLabelProps:F,getMenuProps:T,getInputProps:N,highlightedIndex:L,getItemProps:V,selectedItem:A}=Pe({itemToKey:e=>{var t,n;return null!==(t=null!==(n=e?.id)&&void 0!==n?n:e?.[g])&&void 0!==t?t:e},items:kt(M),itemToString:y,defaultHighlightedIndex:0,selectedItem:null,inputValue:x,stateReducer(e,t){const{changes:n,type:r}=t;switch(r){case Pe.stateChangeTypes.InputKeyDownEnter:case Pe.stateChangeTypes.ItemClick:return{...n,isOpen:!0,highlightedIndex:0};default:return n}},onStateChange({type:e,selectedItem:t}){switch(e){case Pe.stateChangeTypes.InputKeyDownEnter:case Pe.stateChangeTypes.ItemClick:case Pe.stateChangeTypes.InputBlur:if(t){const e=[...R,t.value];E(e),I(""),r&&r(e)}}}}),{id:z,...B}=F();let H=0;return(0,e.createElement)(l.BaseControl,{...B,id:z,className:"gb-multiselect",help:u,label:t},(0,e.createElement)("div",{className:St(xt.control,s)},R.length>0&&(0,e.createElement)("div",{className:xt.selectedItems},R.map((function(t,n){if(!t)return null;const r=function(e,t,n){var r;return null!==(r=kt(e).find((e=>`${e[n]}`==`${t}`)))&&void 0!==r?r:null}(v,t,g),o=!isNaN(t);let a=y(r);return a||(a=o?`#${t}`:t),(0,e.createElement)("span",{className:St(xt.selectedItem,"gb-multiselect__selected-item"),key:`selected-item-${n}`,..._({selectedItem:t,index:n})},a,(0,e.createElement)(l.Button,{className:xt.selectedItemButton,onClick:e=>{e.stopPropagation(),D(t)},size:"small",icon:_t,label:(0,i.sprintf)(
// Translators: %s is the selected item to be removed.
// Translators: %s is the selected item to be removed.
(0,i.__)("Remove %s","generateblocks-pro"),a)}))}))),(0,e.createElement)("div",{className:St(xt.wrapper)},(0,e.createElement)("div",{className:xt.inputWrapper},(0,e.createElement)("input",{placeholder:n,className:xt.input,type:"text",...N({...k({preventKeyAction:O}),onChange:e=>{const t=e.target.value;I(t),a&&a(t)}})}),(0,e.createElement)("button",{"aria-label":(0,i.__)("toggle menu","generateblocks-pro"),type:"button",className:St(xt.toggle,"gb-multiselect__toggle"),...P()},(0,e.createElement)("svg",{className:xt.arrow,viewBox:"0 0 20 20",width:"20",height:"20",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{transform:O?"rotate(180)":void 0,d:"M5 6l5 5 5-5 2 1-7 7-7-7 2-1z",fill:"#555",style:{transformOrigin:"center"}}))))),(0,e.createElement)("ul",{className:St(xt.menu,!(O&&M.length)&&xt.hidden),...T()},O&&M.map(((t,n)=>{const r=Array.isArray(t?.[f]),o=t[d]||t.value||t;return(0,e.createElement)("li",{className:St(!r&&L===n&&["is-focused",xt.focused],!r&&A===t&&["is-selected",xt.selected],r?["gb-multiselect__group",xt.group]:["gb-multiselect__option",xt.option],t?.className),key:`${t}${n}`,"data-index":n,...r?{}:V({item:t,index:n})},(0,e.createElement)("div",{className:St(r&&xt.groupLabel)},o),t[h]&&(0,e.createElement)("div",{className:xt.info},t[h]),r&&(0,e.createElement)("ul",{className:xt.nestedMenu},t[f].map((t=>{const n=H;return H++,(0,e.createElement)("li",{className:St(L===n&&["is-focused",xt.focused],A===t&&["is-selected",xt.selected],xt.option,t?.className,"gb-multiselect__option"),"data-sub-item":!0,"data-index":n,key:`${t}${n}`,...V({item:t,index:n})},(0,e.createElement)("div",null,t[d]||t.value||t),t[h]&&(0,e.createElement)("div",{className:xt.info},t[h]))}))))})),O&&0===M.length&&c&&(0,e.createElement)("li",{className:St(xt.option,"gb-multiselect__option")},(0,e.createElement)("span",null,c)))))}const Ot={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let Pt;const Ft=new Uint8Array(16);function Tt(){if(!Pt&&(Pt="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Pt))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Pt(Ft)}const Nt=[];for(let e=0;e<256;++e)Nt.push((e+256).toString(16).slice(1));const Lt=function(e,t,n){if(Ot.randomUUID&&!t&&!e)return Ot.randomUUID();const r=(e=e||{}).random||(e.rng||Tt)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(let e=0;e<16;++e)t[n+e]=r[e];return t}return function(e,t=0){return Nt[e[t+0]]+Nt[e[t+1]]+Nt[e[t+2]]+Nt[e[t+3]]+"-"+Nt[e[t+4]]+Nt[e[t+5]]+"-"+Nt[e[t+6]]+Nt[e[t+7]]+"-"+Nt[e[t+8]]+Nt[e[t+9]]+"-"+Nt[e[t+10]]+Nt[e[t+11]]+Nt[e[t+12]]+Nt[e[t+13]]+Nt[e[t+14]]+Nt[e[t+15]]}(r)},Vt=(0,o.forwardRef)((function({id:t,label:n,value:r,checked:l,onChange:a,indeterminate:s,"aria-label":u,className:c,...d},g){const h=t||Lt(),p=(0,o.useRef)(),f=g||p;if((0,o.useLayoutEffect)((()=>{f.current&&(f.current.indeterminate=s)}),[f.current]),!u&&!n)throw new Error((0,i.__)("Checkbox must have an accessible label","generateblocks-pro"));return(0,e.createElement)("div",{className:"components-checkbox-control gb-checkbox"},(0,e.createElement)("span",{className:"components-checkbox-control__input-container"},(0,e.createElement)("input",{type:"checkbox",className:St("components-checkbox-control__input",c),id:h,checked:l,onChange:a,value:r,"aria-label":u,ref:f,...d}),s&&!l&&(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",role:"presentation",className:"components-checkbox-control__indeterminate","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M7 11.5h10V13H7z"})),l&&(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",role:"presentation",className:"components-checkbox-control__checked","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"}))),n&&(0,e.createElement)("label",{htmlFor:h,className:"components-checkbox-control__label"},n))})),At=window.wp.compose,zt=window.wp.blockEditor,Bt=window.wp.hooks;function Ht(){return(Ht=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function jt(e,t){if(null==e)return{};var n,r,o={},l=Object.keys(e);for(r=0;r<l.length;r++)t.indexOf(n=l[r])>=0||(o[n]=e[n]);return o}function Kt(t){var n=(0,e.useRef)(t),r=(0,e.useRef)((function(e){n.current&&n.current(e)}));return n.current=t,r.current}var Gt=function(e,t,n){return void 0===t&&(t=0),void 0===n&&(n=1),e>n?n:e<t?t:e},qt=function(e){return"touches"in e},Ut=function(e){return e&&e.ownerDocument.defaultView||self},Wt=function(e,t,n){var r=e.getBoundingClientRect(),o=qt(t)?function(e,t){for(var n=0;n<e.length;n++)if(e[n].identifier===t)return e[n];return e[0]}(t.touches,n):t;return{left:Gt((o.pageX-(r.left+Ut(e).pageXOffset))/r.width),top:Gt((o.pageY-(r.top+Ut(e).pageYOffset))/r.height)}},Qt=function(e){!qt(e)&&e.preventDefault()},$t=e.memo((function(t){var n=t.onMove,r=t.onKey,o=jt(t,["onMove","onKey"]),l=(0,e.useRef)(null),i=Kt(n),a=Kt(r),s=(0,e.useRef)(null),u=(0,e.useRef)(!1),c=(0,e.useMemo)((function(){var e=function(e){Qt(e),(qt(e)?e.touches.length>0:e.buttons>0)&&l.current?i(Wt(l.current,e,s.current)):n(!1)},t=function(){return n(!1)};function n(n){var r=u.current,o=Ut(l.current),i=n?o.addEventListener:o.removeEventListener;i(r?"touchmove":"mousemove",e),i(r?"touchend":"mouseup",t)}return[function(e){var t=e.nativeEvent,r=l.current;if(r&&(Qt(t),!function(e,t){return t&&!qt(e)}(t,u.current)&&r)){if(qt(t)){u.current=!0;var o=t.changedTouches||[];o.length&&(s.current=o[0].identifier)}r.focus(),i(Wt(r,t,s.current)),n(!0)}},function(e){var t=e.which||e.keyCode;t<37||t>40||(e.preventDefault(),a({left:39===t?.05:37===t?-.05:0,top:40===t?.05:38===t?-.05:0}))},n]}),[a,i]),d=c[0],g=c[1],h=c[2];return(0,e.useEffect)((function(){return h}),[h]),e.createElement("div",Ht({},o,{onTouchStart:d,onMouseDown:d,className:"react-colorful__interactive",ref:l,onKeyDown:g,tabIndex:0,role:"slider"}))})),Xt=function(e){return e.filter(Boolean).join(" ")},Yt=function(t){var n=t.color,r=t.left,o=t.top,l=void 0===o?.5:o,i=Xt(["react-colorful__pointer",t.className]);return e.createElement("div",{className:i,style:{top:100*l+"%",left:100*r+"%"}},e.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:n}}))},Jt=function(e,t,n){return void 0===t&&(t=0),void 0===n&&(n=Math.pow(10,t)),Math.round(n*e)/n},Zt=(Math.PI,function(e){var t=e.s,n=e.v,r=e.a,o=(200-t)*n/100;return{h:Jt(e.h),s:Jt(o>0&&o<200?t*n/100/(o<=100?o:200-o)*100:0),l:Jt(o/2),a:Jt(r,2)}}),en=function(e){var t=Zt(e);return"hsl("+t.h+", "+t.s+"%, "+t.l+"%)"},tn=function(e){var t=Zt(e);return"hsla("+t.h+", "+t.s+"%, "+t.l+"%, "+t.a+")"},nn=function(e){var t=e.h,n=e.s,r=e.v,o=e.a;t=t/360*6,n/=100,r/=100;var l=Math.floor(t),i=r*(1-n),a=r*(1-(t-l)*n),s=r*(1-(1-t+l)*n),u=l%6;return{r:Jt(255*[r,a,i,i,s,r][u]),g:Jt(255*[s,r,r,a,i,i][u]),b:Jt(255*[i,i,s,r,r,a][u]),a:Jt(o,2)}},rn=function(e){var t=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return t?ln({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:void 0===t[7]?1:Number(t[7])/(t[8]?100:1)}):{h:0,s:0,v:0,a:1}},on=rn,ln=function(e){var t=e.r,n=e.g,r=e.b,o=e.a,l=Math.max(t,n,r),i=l-Math.min(t,n,r),a=i?l===t?(n-r)/i:l===n?2+(r-t)/i:4+(t-n)/i:0;return{h:Jt(60*(a<0?a+6:a)),s:Jt(l?i/l*100:0),v:Jt(l/255*100),a:o}},an=e.memo((function(t){var n=t.hue,r=t.onChange,o=Xt(["react-colorful__hue",t.className]);return e.createElement("div",{className:o},e.createElement($t,{onMove:function(e){r({h:360*e.left})},onKey:function(e){r({h:Gt(n+360*e.left,0,360)})},"aria-label":"Hue","aria-valuenow":Jt(n),"aria-valuemax":"360","aria-valuemin":"0"},e.createElement(Yt,{className:"react-colorful__hue-pointer",left:n/360,color:en({h:n,s:100,v:100,a:1})})))})),sn=e.memo((function(t){var n=t.hsva,r=t.onChange,o={backgroundColor:en({h:n.h,s:100,v:100,a:1})};return e.createElement("div",{className:"react-colorful__saturation",style:o},e.createElement($t,{onMove:function(e){r({s:100*e.left,v:100-100*e.top})},onKey:function(e){r({s:Gt(n.s+100*e.left,0,100),v:Gt(n.v-100*e.top,0,100)})},"aria-label":"Color","aria-valuetext":"Saturation "+Jt(n.s)+"%, Brightness "+Jt(n.v)+"%"},e.createElement(Yt,{className:"react-colorful__saturation-pointer",top:1-n.v/100,left:n.s/100,color:en(n)})))})),un=function(e,t){return e.replace(/\s/g,"")===t.replace(/\s/g,"")};function cn(t,n,r){var o=Kt(r),l=(0,e.useState)((function(){return t.toHsva(n)})),i=l[0],a=l[1],s=(0,e.useRef)({color:n,hsva:i});(0,e.useEffect)((function(){if(!t.equal(n,s.current.color)){var e=t.toHsva(n);s.current={hsva:e,color:n},a(e)}}),[n,t]),(0,e.useEffect)((function(){var e;(function(e,t){if(e===t)return!0;for(var n in e)if(e[n]!==t[n])return!1;return!0})(i,s.current.hsva)||t.equal(e=t.fromHsva(i),s.current.color)||(s.current={hsva:i,color:e},o(e))}),[i,t,o]);var u=(0,e.useCallback)((function(e){a((function(t){return Object.assign({},t,e)}))}),[]);return[i,u]}var dn="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,gn=new Map,hn=function(e){dn((function(){var t=e.current?e.current.ownerDocument:document;if(void 0!==t&&!gn.has(t)){var r=t.createElement("style");r.innerHTML='.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>\')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}',gn.set(t,r);var o=n.nc;o&&r.setAttribute("nonce",o),t.head.appendChild(r)}}),[])},pn=function(t){var n=t.className,r=t.colorModel,o=t.color,l=void 0===o?r.defaultColor:o,i=t.onChange,a=jt(t,["className","colorModel","color","onChange"]),s=(0,e.useRef)(null);hn(s);var u=cn(r,l,i),c=u[0],d=u[1],g=Xt(["react-colorful",n]);return e.createElement("div",Ht({},a,{ref:s,className:g}),e.createElement(sn,{hsva:c,onChange:d}),e.createElement(an,{hue:c.h,onChange:d,className:"react-colorful__last-control"}))},fn=function(t){var n=t.className,r=t.hsva,o=t.onChange,l={backgroundImage:"linear-gradient(90deg, "+tn(Object.assign({},r,{a:0}))+", "+tn(Object.assign({},r,{a:1}))+")"},i=Xt(["react-colorful__alpha",n]),a=Jt(100*r.a);return e.createElement("div",{className:i},e.createElement("div",{className:"react-colorful__alpha-gradient",style:l}),e.createElement($t,{onMove:function(e){o({a:e.left})},onKey:function(e){o({a:Gt(r.a+e.left)})},"aria-label":"Alpha","aria-valuetext":a+"%","aria-valuenow":a,"aria-valuemin":"0","aria-valuemax":"100"},e.createElement(Yt,{className:"react-colorful__alpha-pointer",left:r.a,color:tn(r)})))},mn=function(t){var n=t.className,r=t.colorModel,o=t.color,l=void 0===o?r.defaultColor:o,i=t.onChange,a=jt(t,["className","colorModel","color","onChange"]),s=(0,e.useRef)(null);hn(s);var u=cn(r,l,i),c=u[0],d=u[1],g=Xt(["react-colorful",n]);return e.createElement("div",Ht({},a,{ref:s,className:g}),e.createElement(sn,{hsva:c,onChange:d}),e.createElement(an,{hue:c.h,onChange:d}),e.createElement(fn,{hsva:c,onChange:d,className:"react-colorful__last-control"}))},vn={defaultColor:"rgba(0, 0, 0, 1)",toHsva:rn,fromHsva:function(e){var t=nn(e);return"rgba("+t.r+", "+t.g+", "+t.b+", "+t.a+")"},equal:un},bn=function(t){return e.createElement(mn,Ht({},t,{colorModel:vn}))},yn={defaultColor:"rgb(0, 0, 0)",toHsva:on,fromHsva:function(e){var t=nn(e);return"rgb("+t.r+", "+t.g+", "+t.b+")"},equal:un},wn=function(t){return e.createElement(pn,Ht({},t,{colorModel:yn}))},Cn={grad:.9,turn:360,rad:360/(2*Math.PI)},Sn=function(e){return"string"==typeof e?e.length>0:"number"==typeof e},xn=function(e,t,n){return void 0===t&&(t=0),void 0===n&&(n=Math.pow(10,t)),Math.round(n*e)/n+0},In=function(e,t,n){return void 0===t&&(t=0),void 0===n&&(n=1),e>n?n:e>t?e:t},Rn=function(e){return(e=isFinite(e)?e%360:0)>0?e:e+360},En=function(e){return{r:In(e.r,0,255),g:In(e.g,0,255),b:In(e.b,0,255),a:In(e.a)}},Mn=function(e){return{r:xn(e.r),g:xn(e.g),b:xn(e.b),a:xn(e.a,3)}},kn=/^#([0-9a-f]{3,8})$/i,Dn=function(e){var t=e.toString(16);return t.length<2?"0"+t:t},On=function(e){var t=e.r,n=e.g,r=e.b,o=e.a,l=Math.max(t,n,r),i=l-Math.min(t,n,r),a=i?l===t?(n-r)/i:l===n?2+(r-t)/i:4+(t-n)/i:0;return{h:60*(a<0?a+6:a),s:l?i/l*100:0,v:l/255*100,a:o}},Pn=function(e){var t=e.h,n=e.s,r=e.v,o=e.a;t=t/360*6,n/=100,r/=100;var l=Math.floor(t),i=r*(1-n),a=r*(1-(t-l)*n),s=r*(1-(1-t+l)*n),u=l%6;return{r:255*[r,a,i,i,s,r][u],g:255*[s,r,r,a,i,i][u],b:255*[i,i,s,r,r,a][u],a:o}},Fn=function(e){return{h:Rn(e.h),s:In(e.s,0,100),l:In(e.l,0,100),a:In(e.a)}},Tn=function(e){return{h:xn(e.h),s:xn(e.s),l:xn(e.l),a:xn(e.a,3)}},Nn=function(e){return Pn((n=(t=e).s,{h:t.h,s:(n*=((r=t.l)<50?r:100-r)/100)>0?2*n/(r+n)*100:0,v:r+n,a:t.a}));var t,n,r},Ln=function(e){return{h:(t=On(e)).h,s:(o=(200-(n=t.s))*(r=t.v)/100)>0&&o<200?n*r/100/(o<=100?o:200-o)*100:0,l:o/2,a:t.a};var t,n,r,o},Vn=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,An=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,zn=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Bn=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Hn={string:[[function(e){var t=kn.exec(e);return t?(e=t[1]).length<=4?{r:parseInt(e[0]+e[0],16),g:parseInt(e[1]+e[1],16),b:parseInt(e[2]+e[2],16),a:4===e.length?xn(parseInt(e[3]+e[3],16)/255,2):1}:6===e.length||8===e.length?{r:parseInt(e.substr(0,2),16),g:parseInt(e.substr(2,2),16),b:parseInt(e.substr(4,2),16),a:8===e.length?xn(parseInt(e.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(e){var t=zn.exec(e)||Bn.exec(e);return t?t[2]!==t[4]||t[4]!==t[6]?null:En({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:void 0===t[7]?1:Number(t[7])/(t[8]?100:1)}):null},"rgb"],[function(e){var t=Vn.exec(e)||An.exec(e);if(!t)return null;var n,r,o=Fn({h:(n=t[1],r=t[2],void 0===r&&(r="deg"),Number(n)*(Cn[r]||1)),s:Number(t[3]),l:Number(t[4]),a:void 0===t[5]?1:Number(t[5])/(t[6]?100:1)});return Nn(o)},"hsl"]],object:[[function(e){var t=e.r,n=e.g,r=e.b,o=e.a,l=void 0===o?1:o;return Sn(t)&&Sn(n)&&Sn(r)?En({r:Number(t),g:Number(n),b:Number(r),a:Number(l)}):null},"rgb"],[function(e){var t=e.h,n=e.s,r=e.l,o=e.a,l=void 0===o?1:o;if(!Sn(t)||!Sn(n)||!Sn(r))return null;var i=Fn({h:Number(t),s:Number(n),l:Number(r),a:Number(l)});return Nn(i)},"hsl"],[function(e){var t=e.h,n=e.s,r=e.v,o=e.a,l=void 0===o?1:o;if(!Sn(t)||!Sn(n)||!Sn(r))return null;var i=function(e){return{h:Rn(e.h),s:In(e.s,0,100),v:In(e.v,0,100),a:In(e.a)}}({h:Number(t),s:Number(n),v:Number(r),a:Number(l)});return Pn(i)},"hsv"]]},jn=function(e,t){for(var n=0;n<t.length;n++){var r=t[n][0](e);if(r)return[r,t[n][1]]}return[null,void 0]},Kn=function(e,t){var n=Ln(e);return{h:n.h,s:In(n.s+100*t,0,100),l:n.l,a:n.a}},Gn=function(e){return(299*e.r+587*e.g+114*e.b)/1e3/255},qn=function(e,t){var n=Ln(e);return{h:n.h,s:n.s,l:In(n.l+100*t,0,100),a:n.a}},Un=function(){function e(e){this.parsed=function(e){return"string"==typeof e?jn(e.trim(),Hn.string):"object"==typeof e&&null!==e?jn(e,Hn.object):[null,void 0]}(e)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return e.prototype.isValid=function(){return null!==this.parsed},e.prototype.brightness=function(){return xn(Gn(this.rgba),2)},e.prototype.isDark=function(){return Gn(this.rgba)<.5},e.prototype.isLight=function(){return Gn(this.rgba)>=.5},e.prototype.toHex=function(){return t=(e=Mn(this.rgba)).r,n=e.g,r=e.b,l=(o=e.a)<1?Dn(xn(255*o)):"","#"+Dn(t)+Dn(n)+Dn(r)+l;var e,t,n,r,o,l},e.prototype.toRgb=function(){return Mn(this.rgba)},e.prototype.toRgbString=function(){return t=(e=Mn(this.rgba)).r,n=e.g,r=e.b,(o=e.a)<1?"rgba("+t+", "+n+", "+r+", "+o+")":"rgb("+t+", "+n+", "+r+")";var e,t,n,r,o},e.prototype.toHsl=function(){return Tn(Ln(this.rgba))},e.prototype.toHslString=function(){return t=(e=Tn(Ln(this.rgba))).h,n=e.s,r=e.l,(o=e.a)<1?"hsla("+t+", "+n+"%, "+r+"%, "+o+")":"hsl("+t+", "+n+"%, "+r+"%)";var e,t,n,r,o},e.prototype.toHsv=function(){return e=On(this.rgba),{h:xn(e.h),s:xn(e.s),v:xn(e.v),a:xn(e.a,3)};var e},e.prototype.invert=function(){return Wn({r:255-(e=this.rgba).r,g:255-e.g,b:255-e.b,a:e.a});var e},e.prototype.saturate=function(e){return void 0===e&&(e=.1),Wn(Kn(this.rgba,e))},e.prototype.desaturate=function(e){return void 0===e&&(e=.1),Wn(Kn(this.rgba,-e))},e.prototype.grayscale=function(){return Wn(Kn(this.rgba,-1))},e.prototype.lighten=function(e){return void 0===e&&(e=.1),Wn(qn(this.rgba,e))},e.prototype.darken=function(e){return void 0===e&&(e=.1),Wn(qn(this.rgba,-e))},e.prototype.rotate=function(e){return void 0===e&&(e=15),this.hue(this.hue()+e)},e.prototype.alpha=function(e){return"number"==typeof e?Wn({r:(t=this.rgba).r,g:t.g,b:t.b,a:e}):xn(this.rgba.a,3);var t},e.prototype.hue=function(e){var t=Ln(this.rgba);return"number"==typeof e?Wn({h:e,s:t.s,l:t.l,a:t.a}):xn(t.h)},e.prototype.isEqual=function(e){return this.toHex()===Wn(e).toHex()},e}(),Wn=function(e){return e instanceof Un?e:new Un(e)};const Qn={control:"_Dr6qiG33WsQKrtPYXqR",toggleButton:"WYRzcJgioJO1iHXopcmc",toggleIndicator:"CjqbJYOT4NEFI7_G5AVg",content:"EnEDwByR81HDx6WeQRuP",inputWrapper:"IGnJgyaENI58gkiWI7sV",input:"JKrZKpb_FbMBTqdR6V4i",clear:"NwtwLzWTjGCb6CH2SjdB",palette:"YB9B04pq6vQiLU3FBgyA",opacity:"vgWTZDj5PnmAloaJDnoF"},$n="gb-color-picker-palettes";function Xn(e){return e?.color?.startsWith("var(")?{...e,color:e.color}:{...e,color:`var(--wp--preset--color--${e.slug}, ${e.color})`}}function Yn(t){const{value:n,onChange:r,label:a,tooltip:s,"aria-label":u,colors:c,renderToggle:d,onClick:g,onOpacityChange:h,valueOpacity:p=1}=t,[f,m]=(0,o.useState)(n||""),v=(0,o.useRef)(null),b=1!==p?wn:bn,y=(0,o.useMemo)((()=>(e=>{if(String(e).startsWith("var(")){const t=e.match(/\(([^)]+)\)/);if(t){const n=getComputedStyle(document.documentElement).getPropertyValue(t[1]);n&&(e=n)}}return Wn(e).toRgbString()})(n)),[n]),w=(0,At.useDebounce)(r,100);(0,o.useEffect)((()=>{n!==f&&m(n)}),[n]),(0,o.useEffect)((()=>{n!==f&&w(f);const e=setTimeout((()=>{v.current&&v.current.focus()}),10);return()=>clearTimeout(e)}),[f]);const C=function(){const[e=[],t=[]]=(0,zt.useSettings)("color.palette.custom","color.palette.theme");return(0,o.useMemo)((()=>[...e.map(Xn),...t.map(Xn)]),[e,t])}(),S=(0,Bt.applyFilters)("generateblocks.components.colorPalettes",c||C,t,C);(0,o.useEffect)((()=>{sessionStorage.setItem($n,JSON.stringify(S))}),[S]);const x=Lt();return(0,e.createElement)(l.BaseControl,{id:x,label:a,className:St("gb-color-picker",Qn.control),"data-gb-control":"ColorPickerControl"},(0,e.createElement)(l.Dropdown,{className:Qn.toggle,contentClassName:Qn.content,placement:"top left",renderToggle:({isOpen:t,onToggle:r})=>{if(d)return d({isOpen:t,onToggle:r});const o=(0,e.createElement)(l.Button,{className:Qn.toggleButton,onClick:()=>{r(),g&&g()},"aria-expanded":t,"aria-label":u},(0,e.createElement)("span",{className:Qn.toggleIndicator,style:{background:n?(i=n,a=p,i?(a||0===a)&&1!==a&&i.startsWith("#")?Wn(i).alpha(a).toRgbString():i:""):null}}));var i,a;return(0,e.createElement)(e.Fragment,null,s?(0,e.createElement)(l.Tooltip,{text:s},o):o)},renderContent:()=>{const t=sessionStorage.getItem($n),r=sessionStorage?JSON.parse(t):S;return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(b,{color:y,onChange:e=>{Wn(e).isValid()&&(e=1===Wn(e).alpha()?Wn(e).toHex():e),m(e)}}),(0,e.createElement)("div",{className:Qn.inputWrapper},(0,e.createElement)(l.TextControl,{ref:v,className:Qn.input,type:"text",value:f,onChange:e=>{!e.startsWith("#")&&/^([0-9A-F]{3}){1,2}$/i.test(e)&&(e="#"+e),m(e)},onBlur:()=>{Wn(n).isValid()&&1===Wn(n).alpha()&&m(Wn(n).toHex())}}),(0,e.createElement)(l.Button,{size:"small",variant:"secondary",className:Qn.clear,onClick:()=>{m(""),1!==p&&h(1)}},(0,i.__)("Clear","generateblocks"))),1!==p&&(0,e.createElement)("div",{className:Qn.opacity},(0,e.createElement)(l.RangeControl,{label:(0,i.__)("Opacity","generateblocks"),value:p||0,onChange:e=>h(e),min:0,max:1,step:.01,initialPosition:1})),(0,e.createElement)(l.BaseControl,{className:Qn.palette},(0,e.createElement)(zt.ColorPalette,{colors:r,value:n||"",onChange:e=>{m(e)},disableCustomColors:!0,clearable:!1})))}}))}const Jn="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function Zn(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function er(e){return"nodeType"in e}function tr(e){var t,n;return e?Zn(e)?e:er(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function nr(e){const{Document:t}=tr(e);return e instanceof t}function rr(e){return!Zn(e)&&e instanceof tr(e).HTMLElement}function or(e){return e instanceof tr(e).SVGElement}function lr(e){return e?Zn(e)?e.document:er(e)?nr(e)?e:rr(e)||or(e)?e.ownerDocument:document:document:document}const ir=Jn?e.useLayoutEffect:e.useEffect;function ar(t){const n=(0,e.useRef)(t);return ir((()=>{n.current=t})),(0,e.useCallback)((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return null==n.current?void 0:n.current(...t)}),[])}function sr(t,n){void 0===n&&(n=[t]);const r=(0,e.useRef)(t);return ir((()=>{r.current!==t&&(r.current=t)}),n),r}function ur(t,n){const r=(0,e.useRef)();return(0,e.useMemo)((()=>{const e=t(r.current);return r.current=e,e}),[...n])}function cr(t){const n=ar(t),r=(0,e.useRef)(null),o=(0,e.useCallback)((e=>{e!==r.current&&(null==n||n(e,r.current)),r.current=e}),[]);return[r,o]}function dr(t){const n=(0,e.useRef)();return(0,e.useEffect)((()=>{n.current=t}),[t]),n.current}let gr={};function hr(t,n){return(0,e.useMemo)((()=>{if(n)return n;const e=null==gr[t]?0:gr[t]+1;return gr[t]=e,t+"-"+e}),[t,n])}function pr(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[n,o]of r){const r=t[n];null!=r&&(t[n]=r+e*o)}return t}),{...t})}}const fr=pr(1),mr=pr(-1);function vr(e){if(!e)return!1;const{KeyboardEvent:t}=tr(e.target);return t&&e instanceof t}function br(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=tr(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}const yr=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[yr.Translate.toString(e),yr.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),wr="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function Cr(e){return e.matches(wr)?e:e.querySelector(wr)}const Sr={display:"none"};function xr(e){let{id:n,value:r}=e;return t().createElement("div",{id:n,style:Sr},r)}function Ir(e){let{id:n,announcement:r,ariaLiveType:o="assertive"}=e;return t().createElement("div",{id:n,style:{position:"fixed",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":o,"aria-atomic":!0},r)}const Rr=(0,e.createContext)(null),Er={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},Mr={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function _r(n){let{announcements:r=Mr,container:o,hiddenTextDescribedById:l,screenReaderInstructions:i=Er}=n;const{announce:a,announcement:s}=function(){const[t,n]=(0,e.useState)("");return{announce:(0,e.useCallback)((e=>{null!=e&&n(e)}),[]),announcement:t}}(),u=hr("DndLiveRegion"),[c,d]=(0,e.useState)(!1);if((0,e.useEffect)((()=>{d(!0)}),[]),function(t){const n=(0,e.useContext)(Rr);(0,e.useEffect)((()=>{if(!n)throw new Error("useDndMonitor must be used within a children of <DndContext>");return n(t)}),[t,n])}((0,e.useMemo)((()=>({onDragStart(e){let{active:t}=e;a(r.onDragStart({active:t}))},onDragMove(e){let{active:t,over:n}=e;r.onDragMove&&a(r.onDragMove({active:t,over:n}))},onDragOver(e){let{active:t,over:n}=e;a(r.onDragOver({active:t,over:n}))},onDragEnd(e){let{active:t,over:n}=e;a(r.onDragEnd({active:t,over:n}))},onDragCancel(e){let{active:t,over:n}=e;a(r.onDragCancel({active:t,over:n}))}})),[a,r])),!c)return null;const g=t().createElement(t().Fragment,null,t().createElement(xr,{id:l,value:i.draggable}),t().createElement(Ir,{id:u,announcement:s}));return o?(0,lt.createPortal)(g,o):g}var kr;function Dr(){}function Or(t,n){return(0,e.useMemo)((()=>({sensor:t,options:null!=n?n:{}})),[t,n])}function Pr(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return(0,e.useMemo)((()=>[...n].filter((e=>null!=e))),[...n])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(kr||(kr={}));const Fr=Object.freeze({x:0,y:0});function Tr(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function Nr(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function Lr(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function Vr(e){let{left:t,top:n,height:r,width:o}=e;return[{x:t,y:n},{x:t+o,y:n},{x:t,y:n+r},{x:t+o,y:n+r}]}function Ar(e,t){if(!e||0===e.length)return null;const[n]=e;return t?n[t]:n}function zr(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}const Br=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=zr(t,t.left,t.top),l=[];for(const e of r){const{id:t}=e,r=n.get(t);if(r){const n=Tr(zr(r),o);l.push({id:t,data:{droppableContainer:e,value:n}})}}return l.sort(Nr)};function Hr(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),l=Math.min(t.top+t.height,e.top+e.height),i=o-r,a=l-n;if(r<o&&n<l){const n=t.width*t.height,r=e.width*e.height,o=i*a;return Number((o/(n+r-o)).toFixed(4))}return 0}const jr=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const e of r){const{id:r}=e,l=n.get(r);if(l){const n=Hr(l,t);n>0&&o.push({id:r,data:{droppableContainer:e,value:n}})}}return o.sort(Lr)};function Kr(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:Fr}function Gr(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...t})}}const qr=Gr(1);const Ur={ignoreTransform:!1};function Wr(e,t){void 0===t&&(t=Ur);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=tr(e).getComputedStyle(e);t&&(n=function(e,t,n){const r=function(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;const{scaleX:o,scaleY:l,x:i,y:a}=r,s=e.left-i-(1-o)*parseFloat(n),u=e.top-a-(1-l)*parseFloat(n.slice(n.indexOf(" ")+1)),c=o?e.width/o:e.width,d=l?e.height/l:e.height;return{width:c,height:d,top:u,right:s+c,bottom:u+d,left:s}}(n,t,r))}const{top:r,left:o,width:l,height:i,bottom:a,right:s}=n;return{top:r,left:o,width:l,height:i,bottom:a,right:s}}function Qr(e){return Wr(e,{ignoreTransform:!0})}function $r(e,t){const n=[];return e?function r(o){if(null!=t&&n.length>=t)return n;if(!o)return n;if(nr(o)&&null!=o.scrollingElement&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!rr(o)||or(o))return n;if(n.includes(o))return n;const l=tr(e).getComputedStyle(o);return o!==e&&function(e,t){void 0===t&&(t=tr(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some((e=>{const r=t[e];return"string"==typeof r&&n.test(r)}))}(o,l)&&n.push(o),function(e,t){return void 0===t&&(t=tr(e).getComputedStyle(e)),"fixed"===t.position}(o,l)?n:r(o.parentNode)}(e):n}function Xr(e){const[t]=$r(e,1);return null!=t?t:null}function Yr(e){return Jn&&e?Zn(e)?e:er(e)?nr(e)||e===lr(e).scrollingElement?window:rr(e)?e:null:null:null}function Jr(e){return Zn(e)?e.scrollX:e.scrollLeft}function Zr(e){return Zn(e)?e.scrollY:e.scrollTop}function eo(e){return{x:Jr(e),y:Zr(e)}}var to;function no(e){return!(!Jn||!e)&&e===document.scrollingElement}function ro(e){const t={x:0,y:0},n=no(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};return{isTop:e.scrollTop<=t.y,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(to||(to={}));const oo={x:.2,y:.2};function lo(e,t,n,r,o){let{top:l,left:i,right:a,bottom:s}=n;void 0===r&&(r=10),void 0===o&&(o=oo);const{isTop:u,isBottom:c,isLeft:d,isRight:g}=ro(e),h={x:0,y:0},p={x:0,y:0},f=t.height*o.y,m=t.width*o.x;return!u&&l<=t.top+f?(h.y=to.Backward,p.y=r*Math.abs((t.top+f-l)/f)):!c&&s>=t.bottom-f&&(h.y=to.Forward,p.y=r*Math.abs((t.bottom-f-s)/f)),!g&&a>=t.right-m?(h.x=to.Forward,p.x=r*Math.abs((t.right-m-a)/m)):!d&&i<=t.left+m&&(h.x=to.Backward,p.x=r*Math.abs((t.left+m-i)/m)),{direction:h,speed:p}}function io(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function ao(e){return e.reduce(((e,t)=>fr(e,eo(t))),Fr)}const so=[["x",["left","right"],function(e){return e.reduce(((e,t)=>e+Jr(t)),0)}],["y",["top","bottom"],function(e){return e.reduce(((e,t)=>e+Zr(t)),0)}]];class uo{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const n=$r(t),r=ao(n);this.rect={...e},this.width=e.width,this.height=e.height;for(const[e,t,o]of so)for(const l of t)Object.defineProperty(this,l,{get:()=>{const t=o(n),i=r[e]-t;return this.rect[l]+i},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class co{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function go(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}var ho,po;function fo(e){e.preventDefault()}function mo(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(ho||(ho={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter"}(po||(po={}));const vo={start:[po.Space,po.Enter],cancel:[po.Esc],end:[po.Space,po.Enter]},bo=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case po.Right:return{...n,x:n.x+25};case po.Left:return{...n,x:n.x-25};case po.Down:return{...n,y:n.y+25};case po.Up:return{...n,y:n.y-25}}};class yo{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;const{event:{target:t}}=e;this.props=e,this.listeners=new co(lr(t)),this.windowListeners=new co(tr(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(ho.Resize,this.handleCancel),this.windowListeners.add(ho.VisibilityChange,this.handleCancel),setTimeout((()=>this.listeners.add(ho.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=Wr),!e)return;const{top:n,left:r,bottom:o,right:l}=t(e);Xr(e)&&(o<=0||l<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(Fr)}handleKeyDown(e){if(vr(e)){const{active:t,context:n,options:r}=this.props,{keyboardCodes:o=vo,coordinateGetter:l=bo,scrollBehavior:i="smooth"}=r,{code:a}=e;if(o.end.includes(a))return void this.handleEnd(e);if(o.cancel.includes(a))return void this.handleCancel(e);const{collisionRect:s}=n.current,u=s?{x:s.left,y:s.top}:Fr;this.referenceCoordinates||(this.referenceCoordinates=u);const c=l(e,{active:t,context:n.current,currentCoordinates:u});if(c){const t=mr(c,u),r={x:0,y:0},{scrollableAncestors:o}=n.current;for(const n of o){const o=e.code,{isTop:l,isRight:a,isLeft:s,isBottom:u,maxScroll:d,minScroll:g}=ro(n),h=io(n),p={x:Math.min(o===po.Right?h.right-h.width/2:h.right,Math.max(o===po.Right?h.left:h.left+h.width/2,c.x)),y:Math.min(o===po.Down?h.bottom-h.height/2:h.bottom,Math.max(o===po.Down?h.top:h.top+h.height/2,c.y))},f=o===po.Right&&!a||o===po.Left&&!s,m=o===po.Down&&!u||o===po.Up&&!l;if(f&&p.x!==c.x){const e=n.scrollLeft+t.x,l=o===po.Right&&e<=d.x||o===po.Left&&e>=g.x;if(l&&!t.y)return void n.scrollTo({left:e,behavior:i});r.x=l?n.scrollLeft-e:o===po.Right?n.scrollLeft-d.x:n.scrollLeft-g.x,r.x&&n.scrollBy({left:-r.x,behavior:i});break}if(m&&p.y!==c.y){const e=n.scrollTop+t.y,l=o===po.Down&&e<=d.y||o===po.Up&&e>=g.y;if(l&&!t.x)return void n.scrollTo({top:e,behavior:i});r.y=l?n.scrollTop-e:o===po.Down?n.scrollTop-d.y:n.scrollTop-g.y,r.y&&n.scrollBy({top:-r.y,behavior:i});break}}this.handleMove(e,fr(mr(c,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function wo(e){return Boolean(e&&"distance"in e)}function Co(e){return Boolean(e&&"delay"in e)}yo.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=vo,onActivation:o}=t,{active:l}=n;const{code:i}=e.nativeEvent;if(r.start.includes(i)){const t=l.activatorNode.current;return!(t&&e.target!==t||(e.preventDefault(),null==o||o({event:e.nativeEvent}),0))}return!1}}];class So{constructor(e,t,n){var r;void 0===n&&(n=function(e){const{EventTarget:t}=tr(e);return e instanceof t?e:lr(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:o}=e,{target:l}=o;this.props=e,this.events=t,this.document=lr(l),this.documentListeners=new co(this.document),this.listeners=new co(n),this.windowListeners=new co(tr(l)),this.initialCoordinates=null!=(r=br(o))?r:Fr,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),this.windowListeners.add(ho.Resize,this.handleCancel),this.windowListeners.add(ho.DragStart,fo),this.windowListeners.add(ho.VisibilityChange,this.handleCancel),this.windowListeners.add(ho.ContextMenu,fo),this.documentListeners.add(ho.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(Co(t))return void(this.timeoutId=setTimeout(this.handleStart,t.delay));if(wo(t))return}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(ho.Click,mo,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(ho.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:o}=this,{onMove:l,options:{activationConstraint:i}}=o;if(!r)return;const a=null!=(t=br(e))?t:Fr,s=mr(r,a);if(!n&&i){if(wo(i)){if(null!=i.tolerance&&go(s,i.tolerance))return this.handleCancel();if(go(s,i.distance))return this.handleStart()}return Co(i)&&go(s,i.tolerance)?this.handleCancel():void 0}e.cancelable&&e.preventDefault(),l(a)}handleEnd(){const{onEnd:e}=this.props;this.detach(),e()}handleCancel(){const{onCancel:e}=this.props;this.detach(),e()}handleKeydown(e){e.code===po.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const xo={move:{name:"pointermove"},end:{name:"pointerup"}};class Io extends So{constructor(e){const{event:t}=e,n=lr(t.target);super(e,xo,n)}}Io.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!(!n.isPrimary||0!==n.button||(null==r||r({event:n}),0))}}];const Ro={move:{name:"mousemove"},end:{name:"mouseup"}};var Eo;!function(e){e[e.RightClick=2]="RightClick"}(Eo||(Eo={})),class extends So{constructor(e){super(e,Ro,lr(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==Eo.RightClick&&(null==r||r({event:n}),!0)}}];const Mo={move:{name:"touchmove"},end:{name:"touchend"}};var _o,ko;(class extends So{constructor(e){super(e,Mo)}static setup(){return window.addEventListener(Mo.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Mo.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:o}=n;return!(o.length>1||(null==r||r({event:n}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(_o||(_o={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(ko||(ko={}));const Do={x:{[to.Backward]:!1,[to.Forward]:!1},y:{[to.Backward]:!1,[to.Forward]:!1}};var Oo,Po;!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(Oo||(Oo={})),function(e){e.Optimized="optimized"}(Po||(Po={}));const Fo=new Map;function To(e,t){return ur((n=>e?n||("function"==typeof t?t(e):e):null),[t,e])}function No(t){let{callback:n,disabled:r}=t;const o=ar(n),l=(0,e.useMemo)((()=>{if(r||"undefined"==typeof window||void 0===window.ResizeObserver)return;const{ResizeObserver:e}=window;return new e(o)}),[r]);return(0,e.useEffect)((()=>()=>null==l?void 0:l.disconnect()),[l]),l}function Lo(e){return new uo(Wr(e),e)}function Vo(t,n,r){void 0===n&&(n=Lo);const[o,l]=(0,e.useReducer)((function(e){if(!t)return null;var o;if(!1===t.isConnected)return null!=(o=null!=e?e:r)?o:null;const l=n(t);return JSON.stringify(e)===JSON.stringify(l)?e:l}),null),i=function(t){let{callback:n,disabled:r}=t;const o=ar(n),l=(0,e.useMemo)((()=>{if(r||"undefined"==typeof window||void 0===window.MutationObserver)return;const{MutationObserver:e}=window;return new e(o)}),[o,r]);return(0,e.useEffect)((()=>()=>null==l?void 0:l.disconnect()),[l]),l}({callback(e){if(t)for(const n of e){const{type:e,target:r}=n;if("childList"===e&&r instanceof HTMLElement&&r.contains(t)){l();break}}}}),a=No({callback:l});return ir((()=>{l(),t?(null==a||a.observe(t),null==i||i.observe(document.body,{childList:!0,subtree:!0})):(null==a||a.disconnect(),null==i||i.disconnect())}),[t]),o}const Ao=[];function zo(t,n){void 0===n&&(n=[]);const r=(0,e.useRef)(null);return(0,e.useEffect)((()=>{r.current=null}),n),(0,e.useEffect)((()=>{const e=t!==Fr;e&&!r.current&&(r.current=t),!e&&r.current&&(r.current=null)}),[t]),r.current?mr(t,r.current):Fr}function Bo(t){return(0,e.useMemo)((()=>t?function(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(t):null),[t])}const Ho=[];const jo=[{sensor:Io,options:{}},{sensor:yo,options:{}}],Ko={current:{}},Go={draggable:{measure:Qr},droppable:{measure:Qr,strategy:Oo.WhileDragging,frequency:Po.Optimized},dragOverlay:{measure:Wr}};class qo extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}const Uo={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new qo,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:Dr},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:Go,measureDroppableContainers:Dr,windowRect:null,measuringScheduled:!1},Wo={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:Dr,draggableNodes:new Map,over:null,measureDroppableContainers:Dr},Qo=(0,e.createContext)(Wo),$o=(0,e.createContext)(Uo);function Xo(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new qo}}}function Yo(e,t){switch(t.type){case kr.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case kr.DragMove:return e.draggable.active?{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}}:e;case kr.DragEnd:case kr.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case kr.RegisterDroppable:{const{element:n}=t,{id:r}=n,o=new qo(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case kr.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;const i=new qo(e.droppable.containers);return i.set(n,{...l,disabled:o}),{...e,droppable:{...e.droppable,containers:i}}}case kr.UnregisterDroppable:{const{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const l=new qo(e.droppable.containers);return l.delete(n),{...e,droppable:{...e.droppable,containers:l}}}default:return e}}function Jo(t){let{disabled:n}=t;const{active:r,activatorEvent:o,draggableNodes:l}=(0,e.useContext)(Qo),i=dr(o),a=dr(null==r?void 0:r.id);return(0,e.useEffect)((()=>{if(!n&&!o&&i&&null!=a){if(!vr(i))return;if(document.activeElement===i.target)return;const e=l.get(a);if(!e)return;const{activatorNode:t,node:n}=e;if(!t.current&&!n.current)return;requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e)continue;const t=Cr(e);if(t){t.focus();break}}}))}}),[o,n,l,a,i]),null}const Zo=(0,e.createContext)({...Fr,scaleX:1,scaleY:1});var el;!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(el||(el={}));const tl=(0,e.memo)((function(n){var r,o,l,i;let{id:a,accessibility:s,autoScroll:u=!0,children:c,sensors:d=jo,collisionDetection:g=jr,measuring:h,modifiers:p,...f}=n;const m=(0,e.useReducer)(Yo,void 0,Xo),[v,b]=m,[y,w]=function(){const[t]=(0,e.useState)((()=>new Set)),n=(0,e.useCallback)((e=>(t.add(e),()=>t.delete(e))),[t]);return[(0,e.useCallback)((e=>{let{type:n,event:r}=e;t.forEach((e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)}))}),[t]),n]}(),[C,S]=(0,e.useState)(el.Uninitialized),x=C===el.Initialized,{draggable:{active:I,nodes:R,translate:E},droppable:{containers:M}}=v,_=I?R.get(I):null,k=(0,e.useRef)({initial:null,translated:null}),D=(0,e.useMemo)((()=>{var e;return null!=I?{id:I,data:null!=(e=null==_?void 0:_.data)?e:Ko,rect:k}:null}),[I,_]),O=(0,e.useRef)(null),[P,F]=(0,e.useState)(null),[T,N]=(0,e.useState)(null),L=sr(f,Object.values(f)),V=hr("DndDescribedBy",a),A=(0,e.useMemo)((()=>M.getEnabled()),[M]),z=(B=h,(0,e.useMemo)((()=>({draggable:{...Go.draggable,...null==B?void 0:B.draggable},droppable:{...Go.droppable,...null==B?void 0:B.droppable},dragOverlay:{...Go.dragOverlay,...null==B?void 0:B.dragOverlay}})),[null==B?void 0:B.draggable,null==B?void 0:B.droppable,null==B?void 0:B.dragOverlay]));var B;const{droppableRects:H,measureDroppableContainers:j,measuringScheduled:K}=function(t,n){let{dragging:r,dependencies:o,config:l}=n;const[i,a]=(0,e.useState)(null),{frequency:s,measure:u,strategy:c}=l,d=(0,e.useRef)(t),g=function(){switch(c){case Oo.Always:return!1;case Oo.BeforeDragging:return r;default:return!r}}(),h=sr(g),p=(0,e.useCallback)((function(e){void 0===e&&(e=[]),h.current||a((t=>null===t?e:t.concat(e.filter((e=>!t.includes(e))))))}),[h]),f=(0,e.useRef)(null),m=ur((e=>{if(g&&!r)return Fo;if(!e||e===Fo||d.current!==t||null!=i){const e=new Map;for(let n of t){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){e.set(n.id,n.rect.current);continue}const t=n.node.current,r=t?new uo(u(t),t):null;n.rect.current=r,r&&e.set(n.id,r)}return e}return e}),[t,i,r,g,u]);return(0,e.useEffect)((()=>{d.current=t}),[t]),(0,e.useEffect)((()=>{g||p()}),[r,g]),(0,e.useEffect)((()=>{i&&i.length>0&&a(null)}),[JSON.stringify(i)]),(0,e.useEffect)((()=>{g||"number"!=typeof s||null!==f.current||(f.current=setTimeout((()=>{p(),f.current=null}),s))}),[s,g,p,...o]),{droppableRects:m,measureDroppableContainers:p,measuringScheduled:null!=i}}(A,{dragging:x,dependencies:[E.x,E.y],config:z.droppable}),G=function(e,t){const n=null!==t?e.get(t):void 0,r=n?n.node.current:null;return ur((e=>{var n;return null===t?null:null!=(n=null!=r?r:e)?n:null}),[r,t])}(R,I),q=(0,e.useMemo)((()=>T?br(T):null),[T]),U=function(){const e=!1===(null==P?void 0:P.autoScrollEnabled),t="object"==typeof u?!1===u.enabled:!1===u,n=x&&!e&&!t;return"object"==typeof u?{...u,enabled:n}:{enabled:n}}(),W=function(e,t){return To(e,t)}(G,z.draggable.measure);!function(t){let{activeNode:n,measure:r,initialRect:o,config:l=!0}=t;const i=(0,e.useRef)(!1),{x:a,y:s}="boolean"==typeof l?{x:l,y:l}:l;ir((()=>{if(!a&&!s||!n)return void(i.current=!1);if(i.current||!o)return;const e=null==n?void 0:n.node.current;if(!e||!1===e.isConnected)return;const t=Kr(r(e),o);if(a||(t.x=0),s||(t.y=0),i.current=!0,Math.abs(t.x)>0||Math.abs(t.y)>0){const n=Xr(e);n&&n.scrollBy({top:t.y,left:t.x})}}),[n,a,s,o,r])}({activeNode:I?R.get(I):null,config:U.layoutShiftCompensation,initialRect:W,measure:z.draggable.measure});const Q=Vo(G,z.draggable.measure,W),$=Vo(G?G.parentElement:null),X=(0,e.useRef)({activatorEvent:null,active:null,activeNode:G,collisionRect:null,collisions:null,droppableRects:H,draggableNodes:R,draggingNode:null,draggingNodeRect:null,droppableContainers:M,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),Y=M.getNodeFor(null==(r=X.current.over)?void 0:r.id),J=function(t){let{measure:n}=t;const[r,o]=(0,e.useState)(null),l=No({callback:(0,e.useCallback)((e=>{for(const{target:t}of e)if(rr(t)){o((e=>{const r=n(t);return e?{...e,width:r.width,height:r.height}:r}));break}}),[n])}),i=(0,e.useCallback)((e=>{const t=function(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return rr(t)?t:e}(e);null==l||l.disconnect(),t&&(null==l||l.observe(t)),o(t?n(t):null)}),[n,l]),[a,s]=cr(i);return(0,e.useMemo)((()=>({nodeRef:a,rect:r,setRef:s})),[r,a,s])}({measure:z.dragOverlay.measure}),Z=null!=(o=J.nodeRef.current)?o:G,ee=x?null!=(l=J.rect)?l:Q:null,te=Boolean(J.nodeRef.current&&J.rect),ne=Kr(re=te?null:Q,To(re));var re;const oe=Bo(Z?tr(Z):null),le=function(t){const n=(0,e.useRef)(t),r=ur((e=>t?e&&e!==Ao&&t&&n.current&&t.parentNode===n.current.parentNode?e:$r(t):Ao),[t]);return(0,e.useEffect)((()=>{n.current=t}),[t]),r}(x?null!=Y?Y:G:null),ie=function(t,n){void 0===n&&(n=Wr);const[r]=t,o=Bo(r?tr(r):null),[l,i]=(0,e.useReducer)((function(){return t.length?t.map((e=>no(e)?o:new uo(n(e),e))):Ho}),Ho),a=No({callback:i});return t.length>0&&l===Ho&&i(),ir((()=>{t.length?t.forEach((e=>null==a?void 0:a.observe(e))):(null==a||a.disconnect(),i())}),[t]),l}(le),ae=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce(((e,t)=>t({transform:e,...r})),n):n}(p,{transform:{x:E.x-ne.x,y:E.y-ne.y,scaleX:1,scaleY:1},activatorEvent:T,active:D,activeNodeRect:Q,containerNodeRect:$,draggingNodeRect:ee,over:X.current.over,overlayNodeRect:J.rect,scrollableAncestors:le,scrollableAncestorRects:ie,windowRect:oe}),se=q?fr(q,E):null,ue=function(t){const[n,r]=(0,e.useState)(null),o=(0,e.useRef)(t),l=(0,e.useCallback)((e=>{const t=Yr(e.target);t&&r((e=>e?(e.set(t,eo(t)),new Map(e)):null))}),[]);return(0,e.useEffect)((()=>{const e=o.current;if(t!==e){n(e);const i=t.map((e=>{const t=Yr(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,eo(t)]):null})).filter((e=>null!=e));r(i.length?new Map(i):null),o.current=t}return()=>{n(t),n(e)};function n(e){e.forEach((e=>{const t=Yr(e);null==t||t.removeEventListener("scroll",l)}))}}),[l,t]),(0,e.useMemo)((()=>t.length?n?Array.from(n.values()).reduce(((e,t)=>fr(e,t)),Fr):ao(t):Fr),[t,n])}(le),ce=zo(ue),de=zo(ue,[Q]),ge=fr(ae,ce),he=ee?qr(ee,ae):null,pe=D&&he?g({active:D,collisionRect:he,droppableRects:H,droppableContainers:A,pointerCoordinates:se}):null,fe=Ar(pe,"id"),[me,ve]=(0,e.useState)(null),be=function(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}(te?ae:fr(ae,de),null!=(i=null==me?void 0:me.rect)?i:null,Q),ye=(0,e.useCallback)(((e,t)=>{let{sensor:n,options:r}=t;if(null==O.current)return;const o=R.get(O.current);if(!o)return;const l=e.nativeEvent,i=new n({active:O.current,activeNode:o,event:l,options:r,context:X,onStart(e){const t=O.current;if(null==t)return;const n=R.get(t);if(!n)return;const{onDragStart:r}=L.current,o={active:{id:t,data:n.data,rect:k}};(0,lt.unstable_batchedUpdates)((()=>{null==r||r(o),S(el.Initializing),b({type:kr.DragStart,initialCoordinates:e,active:t}),y({type:"onDragStart",event:o})}))},onMove(e){b({type:kr.DragMove,coordinates:e})},onEnd:a(kr.DragEnd),onCancel:a(kr.DragCancel)});function a(e){return async function(){const{active:t,collisions:n,over:r,scrollAdjustedTranslate:o}=X.current;let i=null;if(t&&o){const{cancelDrop:a}=L.current;i={activatorEvent:l,active:t,collisions:n,delta:o,over:r},e===kr.DragEnd&&"function"==typeof a&&await Promise.resolve(a(i))&&(e=kr.DragCancel)}O.current=null,(0,lt.unstable_batchedUpdates)((()=>{b({type:e}),S(el.Uninitialized),ve(null),F(null),N(null);const t=e===kr.DragEnd?"onDragEnd":"onDragCancel";if(i){const e=L.current[t];null==e||e(i),y({type:t,event:i})}}))}}(0,lt.unstable_batchedUpdates)((()=>{F(i),N(e.nativeEvent)}))}),[R]),we=(0,e.useCallback)(((e,t)=>(n,r)=>{const o=n.nativeEvent,l=R.get(r);if(null!==O.current||!l||o.dndKit||o.defaultPrevented)return;const i={active:l};!0===e(n,t.options,i)&&(o.dndKit={capturedBy:t.sensor},O.current=r,ye(n,t))}),[R,ye]),Ce=function(t,n){return(0,e.useMemo)((()=>t.reduce(((e,t)=>{const{sensor:r}=t;return[...e,...r.activators.map((e=>({eventName:e.eventName,handler:n(e.handler,t)})))]}),[])),[t,n])}(d,we);!function(t){(0,e.useEffect)((()=>{if(!Jn)return;const e=t.map((e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()}));return()=>{for(const t of e)null==t||t()}}),t.map((e=>{let{sensor:t}=e;return t})))}(d),ir((()=>{Q&&C===el.Initializing&&S(el.Initialized)}),[Q,C]),(0,e.useEffect)((()=>{const{onDragMove:e}=L.current,{active:t,activatorEvent:n,collisions:r,over:o}=X.current;if(!t||!n)return;const l={active:t,activatorEvent:n,collisions:r,delta:{x:ge.x,y:ge.y},over:o};(0,lt.unstable_batchedUpdates)((()=>{null==e||e(l),y({type:"onDragMove",event:l})}))}),[ge.x,ge.y]),(0,e.useEffect)((()=>{const{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:o}=X.current;if(!e||null==O.current||!t||!o)return;const{onDragOver:l}=L.current,i=r.get(fe),a=i&&i.rect.current?{id:i.id,rect:i.rect.current,data:i.data,disabled:i.disabled}:null,s={active:e,activatorEvent:t,collisions:n,delta:{x:o.x,y:o.y},over:a};(0,lt.unstable_batchedUpdates)((()=>{ve(a),null==l||l(s),y({type:"onDragOver",event:s})}))}),[fe]),ir((()=>{X.current={activatorEvent:T,active:D,activeNode:G,collisionRect:he,collisions:pe,droppableRects:H,draggableNodes:R,draggingNode:Z,draggingNodeRect:ee,droppableContainers:M,over:me,scrollableAncestors:le,scrollAdjustedTranslate:ge},k.current={initial:ee,translated:he}}),[D,G,pe,he,R,Z,ee,H,M,me,le,ge]),function(t){let{acceleration:n,activator:r=_o.Pointer,canScroll:o,draggingRect:l,enabled:i,interval:a=5,order:s=ko.TreeOrder,pointerCoordinates:u,scrollableAncestors:c,scrollableAncestorRects:d,delta:g,threshold:h}=t;const p=function(e){let{delta:t,disabled:n}=e;const r=dr(t);return ur((e=>{if(n||!r||!e)return Do;const o=Math.sign(t.x-r.x),l=Math.sign(t.y-r.y);return{x:{[to.Backward]:e.x[to.Backward]||-1===o,[to.Forward]:e.x[to.Forward]||1===o},y:{[to.Backward]:e.y[to.Backward]||-1===l,[to.Forward]:e.y[to.Forward]||1===l}}}),[n,t,r])}({delta:g,disabled:!i}),[f,m]=function(){const t=(0,e.useRef)(null);return[(0,e.useCallback)(((e,n)=>{t.current=setInterval(e,n)}),[]),(0,e.useCallback)((()=>{null!==t.current&&(clearInterval(t.current),t.current=null)}),[])]}(),v=(0,e.useRef)({x:0,y:0}),b=(0,e.useRef)({x:0,y:0}),y=(0,e.useMemo)((()=>{switch(r){case _o.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case _o.DraggableRect:return l}}),[r,l,u]),w=(0,e.useRef)(null),C=(0,e.useCallback)((()=>{const e=w.current;if(!e)return;const t=v.current.x*b.current.x,n=v.current.y*b.current.y;e.scrollBy(t,n)}),[]),S=(0,e.useMemo)((()=>s===ko.TreeOrder?[...c].reverse():c),[s,c]);(0,e.useEffect)((()=>{if(i&&c.length&&y){for(const e of S){if(!1===(null==o?void 0:o(e)))continue;const t=c.indexOf(e),r=d[t];if(!r)continue;const{direction:l,speed:i}=lo(e,r,y,n,h);for(const e of["x","y"])p[e][l[e]]||(i[e]=0,l[e]=0);if(i.x>0||i.y>0)return m(),w.current=e,f(C,a),v.current=i,void(b.current=l)}v.current={x:0,y:0},b.current={x:0,y:0},m()}else m()}),[n,C,o,m,i,a,JSON.stringify(y),JSON.stringify(p),f,c,S,d,JSON.stringify(h)])}({...U,delta:E,draggingRect:he,pointerCoordinates:se,scrollableAncestors:le,scrollableAncestorRects:ie});const Se=(0,e.useMemo)((()=>({active:D,activeNode:G,activeNodeRect:Q,activatorEvent:T,collisions:pe,containerNodeRect:$,dragOverlay:J,draggableNodes:R,droppableContainers:M,droppableRects:H,over:me,measureDroppableContainers:j,scrollableAncestors:le,scrollableAncestorRects:ie,measuringConfiguration:z,measuringScheduled:K,windowRect:oe})),[D,G,Q,T,pe,$,J,R,M,H,me,j,le,ie,z,K,oe]),xe=(0,e.useMemo)((()=>({activatorEvent:T,activators:Ce,active:D,activeNodeRect:Q,ariaDescribedById:{draggable:V},dispatch:b,draggableNodes:R,over:me,measureDroppableContainers:j})),[T,Ce,D,Q,b,V,R,me,j]);return t().createElement(Rr.Provider,{value:w},t().createElement(Qo.Provider,{value:xe},t().createElement($o.Provider,{value:Se},t().createElement(Zo.Provider,{value:be},c)),t().createElement(Jo,{disabled:!1===(null==s?void 0:s.restoreFocus)})),t().createElement(_r,{...s,hiddenTextDescribedById:V}))})),nl=(0,e.createContext)(null),rl="button",ol="Droppable";const ll="Droppable",il={timeout:25};function al(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function sl(e,t){return e.reduce(((e,n,r)=>{const o=t.get(n);return o&&(e[r]=o),e}),Array(e.length))}function ul(e){return null!==e&&e>=0}const cl=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const l=al(t,r,n),i=t[o],a=l[o];return a&&i?{x:a.left-i.left,y:a.top-i.top,scaleX:a.width/i.width,scaleY:a.height/i.height}:null},dl={scaleX:1,scaleY:1},gl=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:o,rects:l,overIndex:i}=e;const a=null!=(t=l[n])?t:r;if(!a)return null;if(o===n){const e=l[i];return e?{x:0,y:n<i?e.top+e.height-(a.top+a.height):e.top-a.top,...dl}:null}const s=function(e,t,n){const r=e[t],o=e[t-1],l=e[t+1];return r?n<t?o?r.top-(o.top+o.height):l?l.top-(r.top+r.height):0:l?l.top-(r.top+r.height):o?r.top-(o.top+o.height):0:0}(l,o,n);return o>n&&o<=i?{x:0,y:-a.height-s,...dl}:o<n&&o>=i?{x:0,y:a.height+s,...dl}:{x:0,y:0,...dl}},hl="Sortable",pl=t().createContext({activeIndex:-1,containerId:hl,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:cl,disabled:{draggable:!1,droppable:!1}});function fl(n){let{children:r,id:o,items:l,strategy:i=cl,disabled:a=!1}=n;const{active:s,dragOverlay:u,droppableRects:c,over:d,measureDroppableContainers:g}=(0,e.useContext)($o),h=hr(hl,o),p=Boolean(null!==u.rect),f=(0,e.useMemo)((()=>l.map((e=>"object"==typeof e&&"id"in e?e.id:e))),[l]),m=null!=s,v=s?f.indexOf(s.id):-1,b=d?f.indexOf(d.id):-1,y=(0,e.useRef)(f),w=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(f,y.current),C=-1!==b&&-1===v||w,S=function(e){return"boolean"==typeof e?{draggable:e,droppable:e}:e}(a);ir((()=>{w&&m&&g(f)}),[w,f,m,g]),(0,e.useEffect)((()=>{y.current=f}),[f]);const x=(0,e.useMemo)((()=>({activeIndex:v,containerId:h,disabled:S,disableTransforms:C,items:f,overIndex:b,useDragOverlay:p,sortedRects:sl(f,c),strategy:i})),[v,h,S.draggable,S.droppable,C,f,b,c,p,i]);return t().createElement(pl.Provider,{value:x},r)}const ml=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return al(n,r,o).indexOf(t)},vl=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:l,newIndex:i,previousItems:a,previousContainerId:s,transition:u}=e;return!(!u||!r||a!==l&&o===i||!n&&(i===o||t!==s))},bl={duration:200,easing:"ease"},yl="transform",wl=yr.Transition.toString({property:yl,duration:0,easing:"linear"}),Cl={roleDescription:"sortable"};function Sl(t){let{animateLayoutChanges:n=vl,attributes:r,disabled:o,data:l,getNewIndex:i=ml,id:a,strategy:s,resizeObserverConfig:u,transition:c=bl}=t;const{items:d,containerId:g,activeIndex:h,disabled:p,disableTransforms:f,sortedRects:m,overIndex:v,useDragOverlay:b,strategy:y}=(0,e.useContext)(pl),w=function(e,t){var n,r;return"boolean"==typeof e?{draggable:e,droppable:!1}:{draggable:null!=(n=null==e?void 0:e.draggable)?n:t.draggable,droppable:null!=(r=null==e?void 0:e.droppable)?r:t.droppable}}(o,p),C=d.indexOf(a),S=(0,e.useMemo)((()=>({sortable:{containerId:g,index:C,items:d},...l})),[g,l,C,d]),x=(0,e.useMemo)((()=>d.slice(d.indexOf(a))),[d,a]),{rect:I,node:R,isOver:E,setNodeRef:M}=function(t){let{data:n,disabled:r=!1,id:o,resizeObserverConfig:l}=t;const i=hr(ll),{active:a,dispatch:s,over:u,measureDroppableContainers:c}=(0,e.useContext)(Qo),d=(0,e.useRef)({disabled:r}),g=(0,e.useRef)(!1),h=(0,e.useRef)(null),p=(0,e.useRef)(null),{disabled:f,updateMeasurementsFor:m,timeout:v}={...il,...l},b=sr(null!=m?m:o),y=No({callback:(0,e.useCallback)((()=>{g.current?(null!=p.current&&clearTimeout(p.current),p.current=setTimeout((()=>{c(Array.isArray(b.current)?b.current:[b.current]),p.current=null}),v)):g.current=!0}),[v]),disabled:f||!a}),w=(0,e.useCallback)(((e,t)=>{y&&(t&&(y.unobserve(t),g.current=!1),e&&y.observe(e))}),[y]),[C,S]=cr(w),x=sr(n);return(0,e.useEffect)((()=>{y&&C.current&&(y.disconnect(),g.current=!1,y.observe(C.current))}),[C,y]),ir((()=>(s({type:kr.RegisterDroppable,element:{id:o,key:i,disabled:r,node:C,rect:h,data:x}}),()=>s({type:kr.UnregisterDroppable,key:i,id:o}))),[o]),(0,e.useEffect)((()=>{r!==d.current.disabled&&(s({type:kr.SetDroppableDisabled,id:o,key:i,disabled:r}),d.current.disabled=r)}),[o,i,r,s]),{active:a,rect:h,isOver:(null==u?void 0:u.id)===o,node:C,over:u,setNodeRef:S}}({id:a,data:S,disabled:w.droppable,resizeObserverConfig:{updateMeasurementsFor:x,...u}}),{active:_,activatorEvent:k,activeNodeRect:D,attributes:O,setNodeRef:P,listeners:F,isDragging:T,over:N,setActivatorNodeRef:L,transform:V}=function(t){let{id:n,data:r,disabled:o=!1,attributes:l}=t;const i=hr(ol),{activators:a,activatorEvent:s,active:u,activeNodeRect:c,ariaDescribedById:d,draggableNodes:g,over:h}=(0,e.useContext)(Qo),{role:p=rl,roleDescription:f="draggable",tabIndex:m=0}=null!=l?l:{},v=(null==u?void 0:u.id)===n,b=(0,e.useContext)(v?Zo:nl),[y,w]=cr(),[C,S]=cr(),x=function(t,n){return(0,e.useMemo)((()=>t.reduce(((e,t)=>{let{eventName:r,handler:o}=t;return e[r]=e=>{o(e,n)},e}),{})),[t,n])}(a,n),I=sr(r);return ir((()=>(g.set(n,{id:n,key:i,node:y,activatorNode:C,data:I}),()=>{const e=g.get(n);e&&e.key===i&&g.delete(n)})),[g,n]),{active:u,activatorEvent:s,activeNodeRect:c,attributes:(0,e.useMemo)((()=>({role:p,tabIndex:m,"aria-disabled":o,"aria-pressed":!(!v||p!==rl)||void 0,"aria-roledescription":f,"aria-describedby":d.draggable})),[o,p,m,v,f,d.draggable]),isDragging:v,listeners:o?void 0:x,node:y,over:h,setNodeRef:w,setActivatorNodeRef:S,transform:b}}({id:a,data:S,attributes:{...Cl,...r},disabled:w.draggable}),A=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return(0,e.useMemo)((()=>e=>{n.forEach((t=>t(e)))}),n)}(M,P),z=Boolean(_),B=z&&!f&&ul(h)&&ul(v),H=!b&&T,j=H&&B?V:null,K=B?null!=j?j:(null!=s?s:y)({rects:m,activeNodeRect:D,activeIndex:h,overIndex:v,index:C}):null,G=ul(h)&&ul(v)?i({id:a,items:d,activeIndex:h,overIndex:v}):C,q=null==_?void 0:_.id,U=(0,e.useRef)({activeId:q,items:d,newIndex:G,containerId:g}),W=d!==U.current.items,Q=n({active:_,containerId:g,isDragging:T,isSorting:z,id:a,index:C,items:d,newIndex:U.current.newIndex,previousItems:U.current.items,previousContainerId:U.current.containerId,transition:c,wasDragging:null!=U.current.activeId}),$=function(t){let{disabled:n,index:r,node:o,rect:l}=t;const[i,a]=(0,e.useState)(null),s=(0,e.useRef)(r);return ir((()=>{if(!n&&r!==s.current&&o.current){const e=l.current;if(e){const t=Wr(o.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&a(n)}}r!==s.current&&(s.current=r)}),[n,r,o,l]),(0,e.useEffect)((()=>{i&&a(null)}),[i]),i}({disabled:!Q,index:C,node:R,rect:I});return(0,e.useEffect)((()=>{z&&U.current.newIndex!==G&&(U.current.newIndex=G),g!==U.current.containerId&&(U.current.containerId=g),d!==U.current.items&&(U.current.items=d)}),[z,G,g,d]),(0,e.useEffect)((()=>{if(q===U.current.activeId)return;if(q&&!U.current.activeId)return void(U.current.activeId=q);const e=setTimeout((()=>{U.current.activeId=q}),50);return()=>clearTimeout(e)}),[q]),{active:_,activeIndex:h,attributes:O,data:S,rect:I,index:C,newIndex:G,items:d,isOver:E,isSorting:z,isDragging:T,listeners:F,node:R,overIndex:v,over:N,setNodeRef:A,setActivatorNodeRef:L,setDroppableNodeRef:M,setDraggableNodeRef:P,transform:null!=$?$:K,transition:$||W&&U.current.newIndex===C?wl:H&&!vr(k)||!c?void 0:z||Q?yr.Transition.toString({...c,property:yl}):void 0}}function xl(e){if(!e)return!1;const t=e.data.current;return!!(t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable)}const Il=[po.Down,po.Right,po.Up,po.Left],Rl=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:o,droppableContainers:l,over:i,scrollableAncestors:a}}=t;if(Il.includes(e.code)){if(e.preventDefault(),!n||!r)return;const t=[];l.getEnabled().forEach((n=>{if(!n||null!=n&&n.disabled)return;const l=o.get(n.id);if(l)switch(e.code){case po.Down:r.top<l.top&&t.push(n);break;case po.Up:r.top>l.top&&t.push(n);break;case po.Left:r.left>l.left&&t.push(n);break;case po.Right:r.left<l.left&&t.push(n)}}));const s=(e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=Vr(t),l=[];for(const e of r){const{id:t}=e,r=n.get(t);if(r){const n=Vr(r),i=o.reduce(((e,t,r)=>e+Tr(n[r],t)),0),a=Number((i/4).toFixed(4));l.push({id:t,data:{droppableContainer:e,value:a}})}}return l.sort(Nr)})({active:n,collisionRect:r,droppableRects:o,droppableContainers:t,pointerCoordinates:null});let u=Ar(s,"id");if(u===(null==i?void 0:i.id)&&s.length>1&&(u=s[1].id),null!=u){const e=l.get(n.id),t=l.get(u),i=t?o.get(t.id):null,s=null==t?void 0:t.node.current;if(s&&i&&e&&t){const n=$r(s).some(((e,t)=>a[t]!==e)),o=El(e,t),l=function(e,t){return!(!xl(e)||!xl(t))&&!!El(e,t)&&e.data.current.sortable.index<t.data.current.sortable.index}(e,t),u=n||!o?{x:0,y:0}:{x:l?r.width-i.width:0,y:l?r.height-i.height:0},c={x:i.left,y:i.top};return u.x&&u.y?c:mr(c,u)}}}};function El(e,t){return!(!xl(e)||!xl(t))&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}const Ml=e=>{let{containerNodeRect:t,draggingNodeRect:n,transform:r}=e;return n&&t?function(e,t,n){const r={...e};return t.top+e.y<=n.top?r.y=n.top-t.top:t.bottom+e.y>=n.top+n.height&&(r.y=n.top+n.height-t.bottom),t.left+e.x<=n.left?r.x=n.left-t.left:t.right+e.x>=n.left+n.width&&(r.x=n.left+n.width-t.right),r}(r,n,t):r},_l=e=>{let{transform:t}=e;return{...t,x:0}},kl={list:"PQLFkFET2Ni1jF_s7MCG",item:"Rvhv_z0kgo69kd5bpWPK",handle:"dcVbtjqevMaW3_QuzhqE"},Dl=(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256"},(0,e.createElement)("rect",{width:"256",height:"256",fill:"none"}),(0,e.createElement)("circle",{cx:"91",cy:"60",r:"16"}),(0,e.createElement)("circle",{cx:"91",cy:"128",r:"16"}),(0,e.createElement)("circle",{cx:"91",cy:"196",r:"16"}),(0,e.createElement)("circle",{cx:"161",cy:"60",r:"16"}),(0,e.createElement)("circle",{cx:"161",cy:"128",r:"16"}),(0,e.createElement)("circle",{cx:"161",cy:"196",r:"16"}));function Ol({label:t,className:n="",...r}){return(0,e.createElement)(l.Button,{className:St("gb-sortable-listitem__handle",kl.handle,n),variant:"tertiary",showTooltip:!1,icon:Dl,label:t,...r})}function Pl({children:t,id:n,dragHandle:r=!0,dragHandleLabel:o=(0,i.__)("Reorder Item","generateblocks"),as:l="li",className:a="",disabled:s=!1}){const{active:u,attributes:c,listeners:d,setNodeRef:g,transform:h,transition:p,isDragging:f}=Sl({id:n,disabled:s,data:{disabled:s}}),m={transform:yr.Transform.toString(h),transition:p};u&&u.id===n&&(m.zIndex=1);const v=l,b=r?{ref:g,style:m}:{ref:g,style:m,...c,...d};return(0,e.createElement)(v,{className:St("gb-sortable-listitem",kl.item,a,f&&"is-dragging"),"data-component":"SortableListItem",...b},r&&(0,e.createElement)(Ol,{label:o,...c,...d}),t)}const Fl=(0,o.forwardRef)((function({itemComponent:t,onDragStart:n,onDragEnd:r,items:l,setItems:a,dragHandle:s=!0,dragHandleLabel:u=(0,i.__)("Reorder Item","generateblocks-pro"),className:c=""},d){const[g,h]=(0,o.useState)(!1),[p,f]=(0,o.useState)(null),m=Pr(Or(Io),Or(yo,{coordinateGetter:Rl})),v=t,b=(0,o.useMemo)((()=>l.map((e=>(e.id||(e.id=Lt()),e)))),[l]);return(0,e.createElement)(tl,{modifiers:[_l,Ml],sensors:m,collisionDetection:Br,onDragStart:e=>{const{active:t}=e;h(!0),f(t),n&&n(e)},onDragEnd:e=>{h(!1),function(e){var t;const{active:n,over:r}=e,o=null!==(t=r.data.current?.disabled)&&void 0!==t&&t;if(n.id!==r.id&&!o){const e=l.findIndex((e=>e.id===n.id)),t=l.findIndex((e=>e.id===r.id));a(al(l,e,t))}}(e),f(null),r&&r(e,p)}},(0,e.createElement)("ul",{className:St("gb-sortable-list",kl.list,c,g&&"is-dragging"),ref:d},(0,e.createElement)(fl,{items:b,strategy:gl},l.map(((t,n)=>{var r;return(0,e.createElement)(Pl,{key:`${t.id}${n}`,id:t.id,dragHandle:s,dragHandleLabel:u,disabled:null!==(r=t.disabled)&&void 0!==r&&r},(0,e.createElement)(v,{item:t,index:n}))})))))})),Tl={flex:"P5BDj_1ChaJUBmTLYsGF",horizontal:"FhS8COBWju0TLgXJ63fT",flow:"INZNFG0D_K4Wz6Q40uZT",vertical:"w0EWkY2E7_gSCIxyU4ag"};function Nl({children:t,gap:n,layout:r="flow",direction:o="vertical",as:l="div",wrap:i=!0,className:a="",style:s={},...u}){const c=l,d={...s,"--gap":n};return"flex"===r&&(d.flexWrap=i?"wrap":"nowrap"),(0,e.createElement)(c,{...u,className:St("gb-stack",Tl[o],Tl[r],a),style:d},t)}function Ll(e,t){return"function"==typeof e?e(t):e}function Vl(e,t){return n=>{t.setState((t=>({...t,[e]:Ll(n,t[e])})))}}function Al(e){return e instanceof Function}function zl(e,t,n){let r,o=[];return l=>{let i;n.key&&n.debug&&(i=Date.now());const a=e(l);if(a.length===o.length&&!a.some(((e,t)=>o[t]!==e)))return r;let s;if(o=a,n.key&&n.debug&&(s=Date.now()),r=t(...a),null==n||null==n.onChange||n.onChange(r),n.key&&n.debug&&null!=n&&n.debug()){const e=Math.round(100*(Date.now()-i))/100,t=Math.round(100*(Date.now()-s))/100,r=t/16,o=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${o(t,5)} /${o(e,5)} ms`,`\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0,Math.min(120-120*r,120))}deg 100% 31%);`,null==n?void 0:n.key)}return r}}function Bl(e,t,n,r){return{debug:()=>{var n;return null!=(n=null==e?void 0:e.debugAll)?n:e[t]},key:!1,onChange:r}}const Hl="debugHeaders";function jl(e,t,n){var r;let o={id:null!=(r=n.id)?r:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const e=[],t=n=>{n.subHeaders&&n.subHeaders.length&&n.subHeaders.map(t),e.push(n)};return t(o),e},getContext:()=>({table:e,header:o,column:t})};return e._features.forEach((t=>{null==t.createHeader||t.createHeader(o,e)})),o}const Kl={createTable:e=>{e.getHeaderGroups=zl((()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right]),((t,n,r,o)=>{var l,i;const a=null!=(l=null==r?void 0:r.map((e=>n.find((t=>t.id===e)))).filter(Boolean))?l:[],s=null!=(i=null==o?void 0:o.map((e=>n.find((t=>t.id===e)))).filter(Boolean))?i:[];return Gl(t,[...a,...n.filter((e=>!(null!=r&&r.includes(e.id)||null!=o&&o.includes(e.id)))),...s],e)}),Bl(e.options,Hl)),e.getCenterHeaderGroups=zl((()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right]),((t,n,r,o)=>Gl(t,n=n.filter((e=>!(null!=r&&r.includes(e.id)||null!=o&&o.includes(e.id)))),e,"center")),Bl(e.options,Hl)),e.getLeftHeaderGroups=zl((()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left]),((t,n,r)=>{var o;const l=null!=(o=null==r?void 0:r.map((e=>n.find((t=>t.id===e)))).filter(Boolean))?o:[];return Gl(t,l,e,"left")}),Bl(e.options,Hl)),e.getRightHeaderGroups=zl((()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right]),((t,n,r)=>{var o;const l=null!=(o=null==r?void 0:r.map((e=>n.find((t=>t.id===e)))).filter(Boolean))?o:[];return Gl(t,l,e,"right")}),Bl(e.options,Hl)),e.getFooterGroups=zl((()=>[e.getHeaderGroups()]),(e=>[...e].reverse()),Bl(e.options,Hl)),e.getLeftFooterGroups=zl((()=>[e.getLeftHeaderGroups()]),(e=>[...e].reverse()),Bl(e.options,Hl)),e.getCenterFooterGroups=zl((()=>[e.getCenterHeaderGroups()]),(e=>[...e].reverse()),Bl(e.options,Hl)),e.getRightFooterGroups=zl((()=>[e.getRightHeaderGroups()]),(e=>[...e].reverse()),Bl(e.options,Hl)),e.getFlatHeaders=zl((()=>[e.getHeaderGroups()]),(e=>e.map((e=>e.headers)).flat()),Bl(e.options,Hl)),e.getLeftFlatHeaders=zl((()=>[e.getLeftHeaderGroups()]),(e=>e.map((e=>e.headers)).flat()),Bl(e.options,Hl)),e.getCenterFlatHeaders=zl((()=>[e.getCenterHeaderGroups()]),(e=>e.map((e=>e.headers)).flat()),Bl(e.options,Hl)),e.getRightFlatHeaders=zl((()=>[e.getRightHeaderGroups()]),(e=>e.map((e=>e.headers)).flat()),Bl(e.options,Hl)),e.getCenterLeafHeaders=zl((()=>[e.getCenterFlatHeaders()]),(e=>e.filter((e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}))),Bl(e.options,Hl)),e.getLeftLeafHeaders=zl((()=>[e.getLeftFlatHeaders()]),(e=>e.filter((e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}))),Bl(e.options,Hl)),e.getRightLeafHeaders=zl((()=>[e.getRightFlatHeaders()]),(e=>e.filter((e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}))),Bl(e.options,Hl)),e.getLeafHeaders=zl((()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()]),((e,t,n)=>{var r,o,l,i,a,s;return[...null!=(r=null==(o=e[0])?void 0:o.headers)?r:[],...null!=(l=null==(i=t[0])?void 0:i.headers)?l:[],...null!=(a=null==(s=n[0])?void 0:s.headers)?a:[]].map((e=>e.getLeafHeaders())).flat()}),Bl(e.options,Hl))}};function Gl(e,t,n,r){var o,l;let i=0;const a=function(e,t){void 0===t&&(t=1),i=Math.max(i,t),e.filter((e=>e.getIsVisible())).forEach((e=>{var n;null!=(n=e.columns)&&n.length&&a(e.columns,t+1)}),0)};a(e);let s=[];const u=(e,t)=>{const o={depth:t,id:[r,`${t}`].filter(Boolean).join("_"),headers:[]},l=[];e.forEach((e=>{const i=[...l].reverse()[0];let a,s=!1;if(e.column.depth===o.depth&&e.column.parent?a=e.column.parent:(a=e.column,s=!0),i&&(null==i?void 0:i.column)===a)i.subHeaders.push(e);else{const o=jl(n,a,{id:[r,t,a.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:s,placeholderId:s?`${l.filter((e=>e.column===a)).length}`:void 0,depth:t,index:l.length});o.subHeaders.push(e),l.push(o)}o.headers.push(e),e.headerGroup=o})),s.push(o),t>0&&u(l,t-1)},c=t.map(((e,t)=>jl(n,e,{depth:i,index:t})));u(c,i-1),s.reverse();const d=e=>e.filter((e=>e.column.getIsVisible())).map((e=>{let t=0,n=0,r=[0];return e.subHeaders&&e.subHeaders.length?(r=[],d(e.subHeaders).forEach((e=>{let{colSpan:n,rowSpan:o}=e;t+=n,r.push(o)}))):t=1,n+=Math.min(...r),e.colSpan=t,e.rowSpan=n,{colSpan:t,rowSpan:n}}));return d(null!=(o=null==(l=s[0])?void 0:l.headers)?o:[]),s}const ql=(e,t,n,r,o,l,i)=>{let a={id:t,index:r,original:n,depth:o,parentId:i,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(a._valuesCache.hasOwnProperty(t))return a._valuesCache[t];const n=e.getColumn(t);return null!=n&&n.accessorFn?(a._valuesCache[t]=n.accessorFn(a.original,r),a._valuesCache[t]):void 0},getUniqueValues:t=>{if(a._uniqueValuesCache.hasOwnProperty(t))return a._uniqueValuesCache[t];const n=e.getColumn(t);return null!=n&&n.accessorFn?n.columnDef.getUniqueValues?(a._uniqueValuesCache[t]=n.columnDef.getUniqueValues(a.original,r),a._uniqueValuesCache[t]):(a._uniqueValuesCache[t]=[a.getValue(t)],a._uniqueValuesCache[t]):void 0},renderValue:t=>{var n;return null!=(n=a.getValue(t))?n:e.options.renderFallbackValue},subRows:null!=l?l:[],getLeafRows:()=>function(e,t){const n=[],r=e=>{e.forEach((e=>{n.push(e);const o=t(e);null!=o&&o.length&&r(o)}))};return r(e),n}(a.subRows,(e=>e.subRows)),getParentRow:()=>a.parentId?e.getRow(a.parentId,!0):void 0,getParentRows:()=>{let e=[],t=a;for(;;){const n=t.getParentRow();if(!n)break;e.push(n),t=n}return e.reverse()},getAllCells:zl((()=>[e.getAllLeafColumns()]),(t=>t.map((t=>function(e,t,n,r){const o={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(r),renderValue:()=>{var t;return null!=(t=o.getValue())?t:e.options.renderFallbackValue},getContext:zl((()=>[e,n,t,o]),((e,t,n,r)=>({table:e,column:t,row:n,cell:r,getValue:r.getValue,renderValue:r.renderValue})),Bl(e.options,"debugCells"))};return e._features.forEach((r=>{null==r.createCell||r.createCell(o,n,t,e)}),{}),o}(e,a,t,t.id)))),Bl(e.options,"debugRows")),_getAllCellsByColumnId:zl((()=>[a.getAllCells()]),(e=>e.reduce(((e,t)=>(e[t.column.id]=t,e)),{})),Bl(e.options,"debugRows"))};for(let t=0;t<e._features.length;t++){const n=e._features[t];null==n||null==n.createRow||n.createRow(a,e)}return a},Ul={createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},Wl=(e,t,n)=>{var r;const o=n.toLowerCase();return Boolean(null==(r=e.getValue(t))||null==(r=r.toString())||null==(r=r.toLowerCase())?void 0:r.includes(o))};Wl.autoRemove=e=>ri(e);const Ql=(e,t,n)=>{var r;return Boolean(null==(r=e.getValue(t))||null==(r=r.toString())?void 0:r.includes(n))};Ql.autoRemove=e=>ri(e);const $l=(e,t,n)=>{var r;return(null==(r=e.getValue(t))||null==(r=r.toString())?void 0:r.toLowerCase())===(null==n?void 0:n.toLowerCase())};$l.autoRemove=e=>ri(e);const Xl=(e,t,n)=>{var r;return null==(r=e.getValue(t))?void 0:r.includes(n)};Xl.autoRemove=e=>ri(e)||!(null!=e&&e.length);const Yl=(e,t,n)=>!n.some((n=>{var r;return!(null!=(r=e.getValue(t))&&r.includes(n))}));Yl.autoRemove=e=>ri(e)||!(null!=e&&e.length);const Jl=(e,t,n)=>n.some((n=>{var r;return null==(r=e.getValue(t))?void 0:r.includes(n)}));Jl.autoRemove=e=>ri(e)||!(null!=e&&e.length);const Zl=(e,t,n)=>e.getValue(t)===n;Zl.autoRemove=e=>ri(e);const ei=(e,t,n)=>e.getValue(t)==n;ei.autoRemove=e=>ri(e);const ti=(e,t,n)=>{let[r,o]=n;const l=e.getValue(t);return l>=r&&l<=o};ti.resolveFilterValue=e=>{let[t,n]=e,r="number"!=typeof t?parseFloat(t):t,o="number"!=typeof n?parseFloat(n):n,l=null===t||Number.isNaN(r)?-1/0:r,i=null===n||Number.isNaN(o)?1/0:o;if(l>i){const e=l;l=i,i=e}return[l,i]},ti.autoRemove=e=>ri(e)||ri(e[0])&&ri(e[1]);const ni={includesString:Wl,includesStringSensitive:Ql,equalsString:$l,arrIncludes:Xl,arrIncludesAll:Yl,arrIncludesSome:Jl,equals:Zl,weakEquals:ei,inNumberRange:ti};function ri(e){return null==e||""===e}const oi={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:Vl("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{const n=t.getCoreRowModel().flatRows[0],r=null==n?void 0:n.getValue(e.id);return"string"==typeof r?ni.includesString:"number"==typeof r?ni.inNumberRange:"boolean"==typeof r||null!==r&&"object"==typeof r?ni.equals:Array.isArray(r)?ni.arrIncludes:ni.weakEquals},e.getFilterFn=()=>{var n,r;return Al(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(n=null==(r=t.options.filterFns)?void 0:r[e.columnDef.filterFn])?n:ni[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,r,o;return(null==(n=e.columnDef.enableColumnFilter)||n)&&(null==(r=t.options.enableColumnFilters)||r)&&(null==(o=t.options.enableFilters)||o)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return null==(n=t.getState().columnFilters)||null==(n=n.find((t=>t.id===e.id)))?void 0:n.value},e.getFilterIndex=()=>{var n,r;return null!=(n=null==(r=t.getState().columnFilters)?void 0:r.findIndex((t=>t.id===e.id)))?n:-1},e.setFilterValue=n=>{t.setColumnFilters((t=>{const r=e.getFilterFn(),o=null==t?void 0:t.find((t=>t.id===e.id)),l=Ll(n,o?o.value:void 0);var i;if(li(r,l,e))return null!=(i=null==t?void 0:t.filter((t=>t.id!==e.id)))?i:[];const a={id:e.id,value:l};var s;return o?null!=(s=null==t?void 0:t.map((t=>t.id===e.id?a:t)))?s:[]:null!=t&&t.length?[...t,a]:[a]}))}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{const n=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange((e=>{var r;return null==(r=Ll(t,e))?void 0:r.filter((e=>{const t=n.find((t=>t.id===e.id));return!t||!li(t.getFilterFn(),e.value,t)}))}))},e.resetColumnFilters=t=>{var n,r;e.setColumnFilters(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.columnFilters)?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function li(e,t,n){return!(!e||!e.autoRemove)&&e.autoRemove(t,n)||void 0===t||"string"==typeof t&&!t}const ii={sum:(e,t,n)=>n.reduce(((t,n)=>{const r=n.getValue(e);return t+("number"==typeof r?r:0)}),0),min:(e,t,n)=>{let r;return n.forEach((t=>{const n=t.getValue(e);null!=n&&(r>n||void 0===r&&n>=n)&&(r=n)})),r},max:(e,t,n)=>{let r;return n.forEach((t=>{const n=t.getValue(e);null!=n&&(r<n||void 0===r&&n>=n)&&(r=n)})),r},extent:(e,t,n)=>{let r,o;return n.forEach((t=>{const n=t.getValue(e);null!=n&&(void 0===r?n>=n&&(r=o=n):(r>n&&(r=n),o<n&&(o=n)))})),[r,o]},mean:(e,t)=>{let n=0,r=0;if(t.forEach((t=>{let o=t.getValue(e);null!=o&&(o=+o)>=o&&(++n,r+=o)})),n)return r/n},median:(e,t)=>{if(!t.length)return;const n=t.map((t=>t.getValue(e)));if(!function(e){return Array.isArray(e)&&e.every((e=>"number"==typeof e))}(n))return;if(1===n.length)return n[0];const r=Math.floor(n.length/2),o=n.sort(((e,t)=>e-t));return n.length%2!=0?o[r]:(o[r-1]+o[r])/2},unique:(e,t)=>Array.from(new Set(t.map((t=>t.getValue(e)))).values()),uniqueCount:(e,t)=>new Set(t.map((t=>t.getValue(e)))).size,count:(e,t)=>t.length},ai={getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return null!=(t=null==(n=e.getValue())||null==n.toString?void 0:n.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:Vl("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping((t=>null!=t&&t.includes(e.id)?t.filter((t=>t!==e.id)):[...null!=t?t:[],e.id]))},e.getCanGroup=()=>{var n,r;return(null==(n=e.columnDef.enableGrouping)||n)&&(null==(r=t.options.enableGrouping)||r)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const n=t.getCoreRowModel().flatRows[0],r=null==n?void 0:n.getValue(e.id);return"number"==typeof r?ii.sum:"[object Date]"===Object.prototype.toString.call(r)?ii.extent:void 0},e.getAggregationFn=()=>{var n,r;if(!e)throw new Error;return Al(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(n=null==(r=t.options.aggregationFns)?void 0:r[e.columnDef.aggregationFn])?n:ii[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,r;e.setGrouping(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.grouping)?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];const r=t.getColumn(n);return null!=r&&r.columnDef.getGroupingValue?(e._groupingValuesCache[n]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,r)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!(null==(t=n.subRows)||!t.length)}}},si={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:Vl("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=zl((e=>[fi(t,e)]),(t=>t.findIndex((t=>t.id===e.id))),Bl(t.options,"debugColumns")),e.getIsFirstColumn=n=>{var r;return(null==(r=fi(t,n)[0])?void 0:r.id)===e.id},e.getIsLastColumn=n=>{var r;const o=fi(t,n);return(null==(r=o[o.length-1])?void 0:r.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:null!=(n=e.initialState.columnOrder)?n:[])},e._getOrderColumnsFn=zl((()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode]),((e,t,n)=>r=>{let o=[];if(null!=e&&e.length){const t=[...e],n=[...r];for(;n.length&&t.length;){const e=t.shift(),r=n.findIndex((t=>t.id===e));r>-1&&o.push(n.splice(r,1)[0])}o=[...o,...n]}else o=r;return function(e,t,n){if(null==t||!t.length||!n)return e;const r=e.filter((e=>!t.includes(e.id)));if("remove"===n)return r;const o=t.map((t=>e.find((e=>e.id===t)))).filter(Boolean);return[...o,...r]}(o,t,n)}),Bl(e.options,"debugTable"))}},ui={getInitialState:e=>({columnPinning:{left:[],right:[]},...e}),getDefaultOptions:e=>({onColumnPinningChange:Vl("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{const r=e.getLeafColumns().map((e=>e.id)).filter(Boolean);t.setColumnPinning((e=>{var t,o,l,i,a,s;return"right"===n?{left:(null!=(l=null==e?void 0:e.left)?l:[]).filter((e=>!(null!=r&&r.includes(e)))),right:[...(null!=(i=null==e?void 0:e.right)?i:[]).filter((e=>!(null!=r&&r.includes(e)))),...r]}:"left"===n?{left:[...(null!=(a=null==e?void 0:e.left)?a:[]).filter((e=>!(null!=r&&r.includes(e)))),...r],right:(null!=(s=null==e?void 0:e.right)?s:[]).filter((e=>!(null!=r&&r.includes(e))))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter((e=>!(null!=r&&r.includes(e)))),right:(null!=(o=null==e?void 0:e.right)?o:[]).filter((e=>!(null!=r&&r.includes(e))))}}))},e.getCanPin=()=>e.getLeafColumns().some((e=>{var n,r,o;return(null==(n=e.columnDef.enablePinning)||n)&&(null==(r=null!=(o=t.options.enableColumnPinning)?o:t.options.enablePinning)||r)})),e.getIsPinned=()=>{const n=e.getLeafColumns().map((e=>e.id)),{left:r,right:o}=t.getState().columnPinning,l=n.some((e=>null==r?void 0:r.includes(e))),i=n.some((e=>null==o?void 0:o.includes(e)));return l?"left":!!i&&"right"},e.getPinnedIndex=()=>{var n,r;const o=e.getIsPinned();return o?null!=(n=null==(r=t.getState().columnPinning)||null==(r=r[o])?void 0:r.indexOf(e.id))?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=zl((()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right]),((e,t,n)=>{const r=[...null!=t?t:[],...null!=n?n:[]];return e.filter((e=>!r.includes(e.column.id)))}),Bl(t.options,"debugRows")),e.getLeftVisibleCells=zl((()=>[e._getAllVisibleCells(),t.getState().columnPinning.left]),((e,t)=>{const n=(null!=t?t:[]).map((t=>e.find((e=>e.column.id===t)))).filter(Boolean).map((e=>({...e,position:"left"})));return n}),Bl(t.options,"debugRows")),e.getRightVisibleCells=zl((()=>[e._getAllVisibleCells(),t.getState().columnPinning.right]),((e,t)=>{const n=(null!=t?t:[]).map((t=>e.find((e=>e.column.id===t)))).filter(Boolean).map((e=>({...e,position:"right"})));return n}),Bl(t.options,"debugRows"))},createTable:e=>{e.setColumnPinning=t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,r;return e.setColumnPinning(t?{left:[],right:[]}:null!=(n=null==(r=e.initialState)?void 0:r.columnPinning)?n:{left:[],right:[]})},e.getIsSomeColumnsPinned=t=>{var n;const r=e.getState().columnPinning;var o,l;return t?Boolean(null==(n=r[t])?void 0:n.length):Boolean((null==(o=r.left)?void 0:o.length)||(null==(l=r.right)?void 0:l.length))},e.getLeftLeafColumns=zl((()=>[e.getAllLeafColumns(),e.getState().columnPinning.left]),((e,t)=>(null!=t?t:[]).map((t=>e.find((e=>e.id===t)))).filter(Boolean)),Bl(e.options,"debugColumns")),e.getRightLeafColumns=zl((()=>[e.getAllLeafColumns(),e.getState().columnPinning.right]),((e,t)=>(null!=t?t:[]).map((t=>e.find((e=>e.id===t)))).filter(Boolean)),Bl(e.options,"debugColumns")),e.getCenterLeafColumns=zl((()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right]),((e,t,n)=>{const r=[...null!=t?t:[],...null!=n?n:[]];return e.filter((e=>!r.includes(e.id)))}),Bl(e.options,"debugColumns"))}},ci={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},di={getDefaultColumnDef:()=>ci,getInitialState:e=>({columnSizing:{},columnSizingInfo:{startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]},...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:Vl("columnSizing",e),onColumnSizingInfoChange:Vl("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,r,o;const l=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(n=e.columnDef.minSize)?n:ci.minSize,null!=(r=null!=l?l:e.columnDef.size)?r:ci.size),null!=(o=e.columnDef.maxSize)?o:ci.maxSize)},e.getStart=zl((e=>[e,fi(t,e),t.getState().columnSizing]),((t,n)=>n.slice(0,e.getIndex(t)).reduce(((e,t)=>e+t.getSize()),0)),Bl(t.options,"debugColumns")),e.getAfter=zl((e=>[e,fi(t,e),t.getState().columnSizing]),((t,n)=>n.slice(e.getIndex(t)+1).reduce(((e,t)=>e+t.getSize()),0)),Bl(t.options,"debugColumns")),e.resetSize=()=>{t.setColumnSizing((t=>{let{[e.id]:n,...r}=t;return r}))},e.getCanResize=()=>{var n,r;return(null==(n=e.columnDef.enableResizing)||n)&&(null==(r=t.options.enableColumnResizing)||r)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let t=0;const n=e=>{var r;e.subHeaders.length?e.subHeaders.forEach(n):t+=null!=(r=e.column.getSize())?r:0};return n(e),t},e.getStart=()=>{if(e.index>0){const t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=n=>{const r=t.getColumn(e.column.id),o=null==r?void 0:r.getCanResize();return l=>{if(!r||!o)return;if(null==l.persist||l.persist(),hi(l)&&l.touches&&l.touches.length>1)return;const i=e.getSize(),a=e?e.getLeafHeaders().map((e=>[e.column.id,e.column.getSize()])):[[r.id,r.getSize()]],s=hi(l)?Math.round(l.touches[0].clientX):l.clientX,u={},c=(e,n)=>{"number"==typeof n&&(t.setColumnSizingInfo((e=>{var r,o;const l="rtl"===t.options.columnResizeDirection?-1:1,i=(n-(null!=(r=null==e?void 0:e.startOffset)?r:0))*l,a=Math.max(i/(null!=(o=null==e?void 0:e.startSize)?o:0),-.999999);return e.columnSizingStart.forEach((e=>{let[t,n]=e;u[t]=Math.round(100*Math.max(n+n*a,0))/100})),{...e,deltaOffset:i,deltaPercentage:a}})),"onChange"!==t.options.columnResizeMode&&"end"!==e||t.setColumnSizing((e=>({...e,...u}))))},d=e=>c("move",e),g=e=>{c("end",e),t.setColumnSizingInfo((e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]})))},h=n||"undefined"!=typeof document?document:null,p={moveHandler:e=>d(e.clientX),upHandler:e=>{null==h||h.removeEventListener("mousemove",p.moveHandler),null==h||h.removeEventListener("mouseup",p.upHandler),g(e.clientX)}},f={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),d(e.touches[0].clientX),!1),upHandler:e=>{var t;null==h||h.removeEventListener("touchmove",f.moveHandler),null==h||h.removeEventListener("touchend",f.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),g(null==(t=e.touches[0])?void 0:t.clientX)}},m=!!function(){if("boolean"==typeof gi)return gi;let e=!1;try{const t={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,t),window.removeEventListener("test",n)}catch(t){e=!1}return gi=e,gi}()&&{passive:!1};hi(l)?(null==h||h.addEventListener("touchmove",f.moveHandler,m),null==h||h.addEventListener("touchend",f.upHandler,m)):(null==h||h.addEventListener("mousemove",p.moveHandler,m),null==h||h.addEventListener("mouseup",p.upHandler,m)),t.setColumnSizingInfo((e=>({...e,startOffset:s,startSize:i,deltaOffset:0,deltaPercentage:0,columnSizingStart:a,isResizingColumn:r.id})))}}},createTable:e=>{e.setColumnSizing=t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:null!=(n=e.initialState.columnSizing)?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?{startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}:null!=(n=e.initialState.columnSizingInfo)?n:{startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]})},e.getTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getHeaderGroups()[0])?void 0:n.headers.reduce(((e,t)=>e+t.getSize()),0))?t:0},e.getLeftTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getLeftHeaderGroups()[0])?void 0:n.headers.reduce(((e,t)=>e+t.getSize()),0))?t:0},e.getCenterTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getCenterHeaderGroups()[0])?void 0:n.headers.reduce(((e,t)=>e+t.getSize()),0))?t:0},e.getRightTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getRightHeaderGroups()[0])?void 0:n.headers.reduce(((e,t)=>e+t.getSize()),0))?t:0}}};let gi=null;function hi(e){return"touchstart"===e.type}const pi={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:Vl("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility((t=>({...t,[e.id]:null!=n?n:!e.getIsVisible()})))},e.getIsVisible=()=>{var n,r;const o=e.columns;return null==(n=o.length?o.some((e=>e.getIsVisible())):null==(r=t.getState().columnVisibility)?void 0:r[e.id])||n},e.getCanHide=()=>{var n,r;return(null==(n=e.columnDef.enableHiding)||n)&&(null==(r=t.options.enableHiding)||r)},e.getToggleVisibilityHandler=()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=zl((()=>[e.getAllCells(),t.getState().columnVisibility]),(e=>e.filter((e=>e.column.getIsVisible()))),Bl(t.options,"debugRows")),e.getVisibleCells=zl((()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()]),((e,t,n)=>[...e,...t,...n]),Bl(t.options,"debugRows"))},createTable:e=>{const t=(t,n)=>zl((()=>[n(),n().filter((e=>e.getIsVisible())).map((e=>e.id)).join("_")]),(e=>e.filter((e=>null==e.getIsVisible?void 0:e.getIsVisible()))),Bl(e.options,"debugColumns"));e.getVisibleFlatColumns=t(0,(()=>e.getAllFlatColumns())),e.getVisibleLeafColumns=t(0,(()=>e.getAllLeafColumns())),e.getLeftVisibleLeafColumns=t(0,(()=>e.getLeftLeafColumns())),e.getRightVisibleLeafColumns=t(0,(()=>e.getRightLeafColumns())),e.getCenterVisibleLeafColumns=t(0,(()=>e.getCenterLeafColumns())),e.setColumnVisibility=t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:null!=(n=e.initialState.columnVisibility)?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=null!=(n=t)?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce(((e,n)=>({...e,[n.id]:t||!(null!=n.getCanHide&&n.getCanHide())})),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some((e=>!(null!=e.getIsVisible&&e.getIsVisible()))),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some((e=>null==e.getIsVisible?void 0:e.getIsVisible())),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible(null==(n=t.target)?void 0:n.checked)}}};function fi(e,t){return t?"center"===t?e.getCenterVisibleLeafColumns():"left"===t?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const mi={getInitialState:e=>({...e,pagination:{pageIndex:0,pageSize:10,...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:Vl("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var r,o;if(t){if(null!=(r=null!=(o=e.options.autoResetAll)?o:e.options.autoResetPageIndex)?r:!e.options.manualPagination){if(n)return;n=!0,e._queue((()=>{e.resetPageIndex(),n=!1}))}}else e._queue((()=>{t=!0}))},e.setPagination=t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange((e=>Ll(t,e))),e.resetPagination=t=>{var n;e.setPagination(t?{pageIndex:0,pageSize:10}:null!=(n=e.initialState.pagination)?n:{pageIndex:0,pageSize:10})},e.setPageIndex=t=>{e.setPagination((n=>{let r=Ll(t,n.pageIndex);const o=void 0===e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return r=Math.max(0,Math.min(r,o)),{...n,pageIndex:r}}))},e.resetPageIndex=t=>{var n,r;e.setPageIndex(t?0:null!=(n=null==(r=e.initialState)||null==(r=r.pagination)?void 0:r.pageIndex)?n:0)},e.resetPageSize=t=>{var n,r;e.setPageSize(t?10:null!=(n=null==(r=e.initialState)||null==(r=r.pagination)?void 0:r.pageSize)?n:10)},e.setPageSize=t=>{e.setPagination((e=>{const n=Math.max(1,Ll(t,e.pageSize)),r=e.pageSize*e.pageIndex,o=Math.floor(r/n);return{...e,pageIndex:o,pageSize:n}}))},e.setPageCount=t=>e.setPagination((n=>{var r;let o=Ll(t,null!=(r=e.options.pageCount)?r:-1);return"number"==typeof o&&(o=Math.max(-1,o)),{...n,pageCount:o}})),e.getPageOptions=zl((()=>[e.getPageCount()]),(e=>{let t=[];return e&&e>0&&(t=[...new Array(e)].fill(null).map(((e,t)=>t))),t}),Bl(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:t}=e.getState().pagination,n=e.getPageCount();return-1===n||0!==n&&t<n-1},e.previousPage=()=>e.setPageIndex((e=>e-1)),e.nextPage=()=>e.setPageIndex((e=>e+1)),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return null!=(t=e.options.rowCount)?t:e.getPrePaginationRowModel().rows.length}}},vi={getInitialState:e=>({rowPinning:{top:[],bottom:[]},...e}),getDefaultOptions:e=>({onRowPinningChange:Vl("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,r,o)=>{const l=r?e.getLeafRows().map((e=>{let{id:t}=e;return t})):[],i=o?e.getParentRows().map((e=>{let{id:t}=e;return t})):[],a=new Set([...i,e.id,...l]);t.setRowPinning((e=>{var t,r,o,l,i,s;return"bottom"===n?{top:(null!=(o=null==e?void 0:e.top)?o:[]).filter((e=>!(null!=a&&a.has(e)))),bottom:[...(null!=(l=null==e?void 0:e.bottom)?l:[]).filter((e=>!(null!=a&&a.has(e)))),...Array.from(a)]}:"top"===n?{top:[...(null!=(i=null==e?void 0:e.top)?i:[]).filter((e=>!(null!=a&&a.has(e)))),...Array.from(a)],bottom:(null!=(s=null==e?void 0:e.bottom)?s:[]).filter((e=>!(null!=a&&a.has(e))))}:{top:(null!=(t=null==e?void 0:e.top)?t:[]).filter((e=>!(null!=a&&a.has(e)))),bottom:(null!=(r=null==e?void 0:e.bottom)?r:[]).filter((e=>!(null!=a&&a.has(e))))}}))},e.getCanPin=()=>{var n;const{enableRowPinning:r,enablePinning:o}=t.options;return"function"==typeof r?r(e):null==(n=null!=r?r:o)||n},e.getIsPinned=()=>{const n=[e.id],{top:r,bottom:o}=t.getState().rowPinning,l=n.some((e=>null==r?void 0:r.includes(e))),i=n.some((e=>null==o?void 0:o.includes(e)));return l?"top":!!i&&"bottom"},e.getPinnedIndex=()=>{var n,r;const o=e.getIsPinned();if(!o)return-1;const l=null==(n="top"===o?t.getTopRows():t.getBottomRows())?void 0:n.map((e=>{let{id:t}=e;return t}));return null!=(r=null==l?void 0:l.indexOf(e.id))?r:-1}},createTable:e=>{e.setRowPinning=t=>null==e.options.onRowPinningChange?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,r;return e.setRowPinning(t?{top:[],bottom:[]}:null!=(n=null==(r=e.initialState)?void 0:r.rowPinning)?n:{top:[],bottom:[]})},e.getIsSomeRowsPinned=t=>{var n;const r=e.getState().rowPinning;var o,l;return t?Boolean(null==(n=r[t])?void 0:n.length):Boolean((null==(o=r.top)?void 0:o.length)||(null==(l=r.bottom)?void 0:l.length))},e._getPinnedRows=(t,n,r)=>{var o;return(null==(o=e.options.keepPinnedRows)||o?(null!=n?n:[]).map((t=>{const n=e.getRow(t,!0);return n.getIsAllParentsExpanded()?n:null})):(null!=n?n:[]).map((e=>t.find((t=>t.id===e))))).filter(Boolean).map((e=>({...e,position:r})))},e.getTopRows=zl((()=>[e.getRowModel().rows,e.getState().rowPinning.top]),((t,n)=>e._getPinnedRows(t,n,"top")),Bl(e.options,"debugRows")),e.getBottomRows=zl((()=>[e.getRowModel().rows,e.getState().rowPinning.bottom]),((t,n)=>e._getPinnedRows(t,n,"bottom")),Bl(e.options,"debugRows")),e.getCenterRows=zl((()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom]),((e,t,n)=>{const r=new Set([...null!=t?t:[],...null!=n?n:[]]);return e.filter((e=>!r.has(e.id)))}),Bl(e.options,"debugRows"))}},bi={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:Vl("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:null!=(n=e.initialState.rowSelection)?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection((n=>{t=void 0!==t?t:!e.getIsAllRowsSelected();const r={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach((e=>{e.getCanSelect()&&(r[e.id]=!0)})):o.forEach((e=>{delete r[e.id]})),r}))},e.toggleAllPageRowsSelected=t=>e.setRowSelection((n=>{const r=void 0!==t?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach((t=>{yi(o,t.id,r,!0,e)})),o})),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=zl((()=>[e.getState().rowSelection,e.getCoreRowModel()]),((t,n)=>Object.keys(t).length?wi(e,n):{rows:[],flatRows:[],rowsById:{}}),Bl(e.options,"debugTable")),e.getFilteredSelectedRowModel=zl((()=>[e.getState().rowSelection,e.getFilteredRowModel()]),((t,n)=>Object.keys(t).length?wi(e,n):{rows:[],flatRows:[],rowsById:{}}),Bl(e.options,"debugTable")),e.getGroupedSelectedRowModel=zl((()=>[e.getState().rowSelection,e.getSortedRowModel()]),((t,n)=>Object.keys(t).length?wi(e,n):{rows:[],flatRows:[],rowsById:{}}),Bl(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let r=Boolean(t.length&&Object.keys(n).length);return r&&t.some((e=>e.getCanSelect()&&!n[e.id]))&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows.filter((e=>e.getCanSelect())),{rowSelection:n}=e.getState();let r=!!t.length;return r&&t.some((e=>!n[e.id]))&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var t;const n=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter((e=>e.getCanSelect())).some((e=>e.getIsSelected()||e.getIsSomeSelected()))},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,r)=>{const o=e.getIsSelected();t.setRowSelection((l=>{var i;if(n=void 0!==n?n:!o,e.getCanSelect()&&o===n)return l;const a={...l};return yi(a,e.id,n,null==(i=null==r?void 0:r.selectChildren)||i,t),a}))},e.getIsSelected=()=>{const{rowSelection:n}=t.getState();return Ci(e,n)},e.getIsSomeSelected=()=>{const{rowSelection:n}=t.getState();return"some"===Si(e,n)},e.getIsAllSubRowsSelected=()=>{const{rowSelection:n}=t.getState();return"all"===Si(e,n)},e.getCanSelect=()=>{var n;return"function"==typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(n=t.options.enableRowSelection)||n},e.getCanSelectSubRows=()=>{var n;return"function"==typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(n=t.options.enableSubRowSelection)||n},e.getCanMultiSelect=()=>{var n;return"function"==typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(n=t.options.enableMultiRowSelection)||n},e.getToggleSelectedHandler=()=>{const t=e.getCanSelect();return n=>{var r;t&&e.toggleSelected(null==(r=n.target)?void 0:r.checked)}}}},yi=(e,t,n,r,o)=>{var l;const i=o.getRow(t,!0);n?(i.getCanMultiSelect()||Object.keys(e).forEach((t=>delete e[t])),i.getCanSelect()&&(e[t]=!0)):delete e[t],r&&null!=(l=i.subRows)&&l.length&&i.getCanSelectSubRows()&&i.subRows.forEach((t=>yi(e,t.id,n,r,o)))};function wi(e,t){const n=e.getState().rowSelection,r=[],o={},l=function(e,t){return e.map((e=>{var t;const i=Ci(e,n);if(i&&(r.push(e),o[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:l(e.subRows)}),i)return e})).filter(Boolean)};return{rows:l(t.rows),flatRows:r,rowsById:o}}function Ci(e,t){var n;return null!=(n=t[e.id])&&n}function Si(e,t,n){var r;if(null==(r=e.subRows)||!r.length)return!1;let o=!0,l=!1;return e.subRows.forEach((e=>{if((!l||o)&&(e.getCanSelect()&&(Ci(e,t)?l=!0:o=!1),e.subRows&&e.subRows.length)){const n=Si(e,t);"all"===n?l=!0:"some"===n?(l=!0,o=!1):o=!1}})),o?"all":!!l&&"some"}const xi=/([0-9]+)/gm;function Ii(e,t){return e===t?0:e>t?1:-1}function Ri(e){return"number"==typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"==typeof e?e:""}function Ei(e,t){const n=e.split(xi).filter(Boolean),r=t.split(xi).filter(Boolean);for(;n.length&&r.length;){const e=n.shift(),t=r.shift(),o=parseInt(e,10),l=parseInt(t,10),i=[o,l].sort();if(isNaN(i[0])){if(e>t)return 1;if(t>e)return-1}else{if(isNaN(i[1]))return isNaN(o)?-1:1;if(o>l)return 1;if(l>o)return-1}}return n.length-r.length}const Mi={alphanumeric:(e,t,n)=>Ei(Ri(e.getValue(n)).toLowerCase(),Ri(t.getValue(n)).toLowerCase()),alphanumericCaseSensitive:(e,t,n)=>Ei(Ri(e.getValue(n)),Ri(t.getValue(n))),text:(e,t,n)=>Ii(Ri(e.getValue(n)).toLowerCase(),Ri(t.getValue(n)).toLowerCase()),textCaseSensitive:(e,t,n)=>Ii(Ri(e.getValue(n)),Ri(t.getValue(n))),datetime:(e,t,n)=>{const r=e.getValue(n),o=t.getValue(n);return r>o?1:r<o?-1:0},basic:(e,t,n)=>Ii(e.getValue(n),t.getValue(n))},_i={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:Vl("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{const n=t.getFilteredRowModel().flatRows.slice(10);let r=!1;for(const t of n){const n=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(n))return Mi.datetime;if("string"==typeof n&&(r=!0,n.split(xi).length>1))return Mi.alphanumeric}return r?Mi.text:Mi.basic},e.getAutoSortDir=()=>{const n=t.getFilteredRowModel().flatRows[0];return"string"==typeof(null==n?void 0:n.getValue(e.id))?"asc":"desc"},e.getSortingFn=()=>{var n,r;if(!e)throw new Error;return Al(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(n=null==(r=t.options.sortingFns)?void 0:r[e.columnDef.sortingFn])?n:Mi[e.columnDef.sortingFn]},e.toggleSorting=(n,r)=>{const o=e.getNextSortingOrder(),l=null!=n;t.setSorting((i=>{const a=null==i?void 0:i.find((t=>t.id===e.id)),s=null==i?void 0:i.findIndex((t=>t.id===e.id));let u,c=[],d=l?n:"desc"===o;var g;return u=null!=i&&i.length&&e.getCanMultiSort()&&r?a?"toggle":"add":null!=i&&i.length&&s!==i.length-1?"replace":a?"toggle":"replace","toggle"===u&&(l||o||(u="remove")),"add"===u?(c=[...i,{id:e.id,desc:d}],c.splice(0,c.length-(null!=(g=t.options.maxMultiSortColCount)?g:Number.MAX_SAFE_INTEGER))):c="toggle"===u?i.map((t=>t.id===e.id?{...t,desc:d}:t)):"remove"===u?i.filter((t=>t.id!==e.id)):[{id:e.id,desc:d}],c}))},e.getFirstSortDir=()=>{var n,r;return(null!=(n=null!=(r=e.columnDef.sortDescFirst)?r:t.options.sortDescFirst)?n:"desc"===e.getAutoSortDir())?"desc":"asc"},e.getNextSortingOrder=n=>{var r,o;const l=e.getFirstSortDir(),i=e.getIsSorted();return i?!!(i===l||null!=(r=t.options.enableSortingRemoval)&&!r||n&&null!=(o=t.options.enableMultiRemove)&&!o)&&("desc"===i?"asc":"desc"):l},e.getCanSort=()=>{var n,r;return(null==(n=e.columnDef.enableSorting)||n)&&(null==(r=t.options.enableSorting)||r)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,r;return null!=(n=null!=(r=e.columnDef.enableMultiSort)?r:t.options.enableMultiSort)?n:!!e.accessorFn},e.getIsSorted=()=>{var n;const r=null==(n=t.getState().sorting)?void 0:n.find((t=>t.id===e.id));return!!r&&(r.desc?"desc":"asc")},e.getSortIndex=()=>{var n,r;return null!=(n=null==(r=t.getState().sorting)?void 0:r.findIndex((t=>t.id===e.id)))?n:-1},e.clearSorting=()=>{t.setSorting((t=>null!=t&&t.length?t.filter((t=>t.id!==e.id)):[]))},e.getToggleSortingHandler=()=>{const n=e.getCanSort();return r=>{n&&(null==r.persist||r.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(r))))}}},createTable:e=>{e.setSorting=t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,r;e.setSorting(t?[]:null!=(n=null==(r=e.initialState)?void 0:r.sorting)?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},ki=[Kl,pi,si,ui,Ul,oi,{createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},{getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:Vl("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;const r=null==(n=e.getCoreRowModel().flatRows[0])||null==(n=n._getAllCellsByColumnId()[t.id])?void 0:n.getValue();return"string"==typeof r||"number"==typeof r}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,r,o,l;return(null==(n=e.columnDef.enableGlobalFilter)||n)&&(null==(r=t.options.enableGlobalFilter)||r)&&(null==(o=t.options.enableFilters)||o)&&(null==(l=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||l)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>ni.includesString,e.getGlobalFilterFn=()=>{var t,n;const{globalFilterFn:r}=e.options;return Al(r)?r:"auto"===r?e.getGlobalAutoFilterFn():null!=(t=null==(n=e.options.filterFns)?void 0:n[r])?t:ni[r]},e.setGlobalFilter=t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},_i,ai,{getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:Vl("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var r,o;if(t){if(null!=(r=null!=(o=e.options.autoResetAll)?o:e.options.autoResetExpanded)?r:!e.options.manualExpanding){if(n)return;n=!0,e._queue((()=>{e.resetExpanded(),n=!1}))}}else e._queue((()=>{t=!0}))},e.setExpanded=t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var n,r;e.setExpanded(t?{}:null!=(n=null==(r=e.initialState)?void 0:r.expanded)?n:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some((e=>e.getCanExpand())),e.getToggleAllRowsExpandedHandler=()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{const t=e.getState().expanded;return"boolean"==typeof t?!0===t:!!Object.keys(t).length&&!e.getRowModel().flatRows.some((e=>!e.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach((e=>{const n=e.split(".");t=Math.max(t,n.length)})),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded((r=>{var o;const l=!0===r||!(null==r||!r[e.id]);let i={};if(!0===r?Object.keys(t.getRowModel().rowsById).forEach((e=>{i[e]=!0})):i=r,n=null!=(o=n)?o:!l,!l&&n)return{...i,[e.id]:!0};if(l&&!n){const{[e.id]:t,...n}=i;return n}return r}))},e.getIsExpanded=()=>{var n;const r=t.getState().expanded;return!!(null!=(n=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?n:!0===r||(null==r?void 0:r[e.id]))},e.getCanExpand=()=>{var n,r,o;return null!=(n=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?n:(null==(r=t.options.enableExpanding)||r)&&!(null==(o=e.subRows)||!o.length)},e.getIsAllParentsExpanded=()=>{let n=!0,r=e;for(;n&&r.parentId;)r=t.getRow(r.parentId,!0),n=r.getIsExpanded();return n},e.getToggleExpandedHandler=()=>{const t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},mi,vi,bi,di];function Di(e){var t,n;const r=[...ki,...null!=(t=e._features)?t:[]];let o={_features:r};const l=o._features.reduce(((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(o))),{});let i={...null!=(n=e.initialState)?n:{}};o._features.forEach((e=>{var t;i=null!=(t=null==e.getInitialState?void 0:e.getInitialState(i))?t:i}));const a=[];let s=!1;const u={_features:r,options:{...l,...e},initialState:i,_queue:e=>{a.push(e),s||(s=!0,Promise.resolve().then((()=>{for(;a.length;)a.shift()();s=!1})).catch((e=>setTimeout((()=>{throw e})))))},reset:()=>{o.setState(o.initialState)},setOptions:e=>{const t=Ll(e,o.options);o.options=(e=>o.options.mergeOptions?o.options.mergeOptions(l,e):{...l,...e})(t)},getState:()=>o.options.state,setState:e=>{null==o.options.onStateChange||o.options.onStateChange(e)},_getRowId:(e,t,n)=>{var r;return null!=(r=null==o.options.getRowId?void 0:o.options.getRowId(e,t,n))?r:`${n?[n.id,t].join("."):t}`},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(e,t)=>{let n=(t?o.getPrePaginationRowModel():o.getRowModel()).rowsById[e];if(!n&&(n=o.getCoreRowModel().rowsById[e],!n))throw new Error;return n},_getDefaultColumnDef:zl((()=>[o.options.defaultColumn]),(e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{const t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,n;return null!=(t=null==(n=e.renderValue())||null==n.toString?void 0:n.toString())?t:null},...o._features.reduce(((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef())),{}),...e}}),Bl(e,"debugColumns")),_getColumnDefs:()=>o.options.columns,getAllColumns:zl((()=>[o._getColumnDefs()]),(e=>{const t=function(e,n,r){return void 0===r&&(r=0),e.map((e=>{const l=function(e,t,n,r){var o,l;const i={...e._getDefaultColumnDef(),...t},a=i.accessorKey;let s,u=null!=(o=null!=(l=i.id)?l:a?a.replace(".","_"):void 0)?o:"string"==typeof i.header?i.header:void 0;if(i.accessorFn?s=i.accessorFn:a&&(s=a.includes(".")?e=>{let t=e;for(const e of a.split(".")){var n;t=null==(n=t)?void 0:n[e]}return t}:e=>e[i.accessorKey]),!u)throw new Error;let c={id:`${String(u)}`,accessorFn:s,parent:r,depth:n,columnDef:i,columns:[],getFlatColumns:zl((()=>[!0]),(()=>{var e;return[c,...null==(e=c.columns)?void 0:e.flatMap((e=>e.getFlatColumns()))]}),Bl(e.options,"debugColumns")),getLeafColumns:zl((()=>[e._getOrderColumnsFn()]),(e=>{var t;if(null!=(t=c.columns)&&t.length){let t=c.columns.flatMap((e=>e.getLeafColumns()));return e(t)}return[c]}),Bl(e.options,"debugColumns"))};for(const t of e._features)null==t.createColumn||t.createColumn(c,e);return c}(o,e,r,n),i=e;return l.columns=i.columns?t(i.columns,l,r+1):[],l}))};return t(e)}),Bl(e,"debugColumns")),getAllFlatColumns:zl((()=>[o.getAllColumns()]),(e=>e.flatMap((e=>e.getFlatColumns()))),Bl(e,"debugColumns")),_getAllFlatColumnsById:zl((()=>[o.getAllFlatColumns()]),(e=>e.reduce(((e,t)=>(e[t.id]=t,e)),{})),Bl(e,"debugColumns")),getAllLeafColumns:zl((()=>[o.getAllColumns(),o._getOrderColumnsFn()]),((e,t)=>t(e.flatMap((e=>e.getLeafColumns())))),Bl(e,"debugColumns")),getColumn:e=>o._getAllFlatColumnsById()[e]};Object.assign(o,u);for(let e=0;e<o._features.length;e++){const t=o._features[e];null==t||null==t.createTable||t.createTable(o)}return o}function Oi(t,n){return t?function(e){return"function"==typeof e&&(()=>{const t=Object.getPrototypeOf(e);return t.prototype&&t.prototype.isReactComponent})()}(r=t)||"function"==typeof r||function(e){return"object"==typeof e&&"symbol"==typeof e.$$typeof&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}(r)?e.createElement(t,n):t:null;var r}const Pi={table:"ADRxUCGRBaX2k1AZvpXa",striped:"mVpMnlwbnSP8fpKCWKQg"};function Fi({columns:t,onDragStart:n,onDragEnd:r,onRowSelectionChange:l,rowSelection:i={},striped:a=!0,showHeader:s=!0,data:u=[],className:c="",draggableRows:d=!1,getRowId:g=e=>e.id,enableRowSelection:h=!1,"aria-labelledby":p,"aria-label":f}){const m={},v=(0,o.useRef)(null);h&&(m.rowSelection=i);const b=function(t){const n={state:{},onStateChange:()=>{},renderFallbackValue:null,...t},[r]=e.useState((()=>({current:Di(n)}))),[o,l]=e.useState((()=>r.current.initialState));return r.current.setOptions((e=>({...e,...t,state:{...o,...t.state},onStateChange:e=>{l(e),null==t.onStateChange||t.onStateChange(e)}}))),r.current}({data:u,columns:t,getCoreRowModel:e=>zl((()=>[e.options.data]),(t=>{const n={rows:[],flatRows:[],rowsById:{}},r=function(t,o,l){void 0===o&&(o=0);const i=[];for(let s=0;s<t.length;s++){const u=ql(e,e._getRowId(t[s],s,l),t[s],s,o,void 0,null==l?void 0:l.id);var a;n.flatRows.push(u),n.rowsById[u.id]=u,i.push(u),e.options.getSubRows&&(u.originalSubRows=e.options.getSubRows(t[s],s),null!=(a=u.originalSubRows)&&a.length&&(u.subRows=r(u.originalSubRows,o+1,u)))}return i};return n.rows=r(t),n}),Bl(e.options,"debugTable",0,(()=>e._autoResetPageIndex()))),getRowId:g,enableRowSelection:h,onRowSelectionChange:l,state:m}),y=b.getRowModel().rows,w=d&&!b.getIsSomeRowsSelected()&&!b.getIsAllRowsSelected(),C=Pr(Or(Io),Or(yo,{coordinateGetter:Rl}));if(!f&&!p)throw new Error("Table component requires an accessible label. Please add an aria-label or valid aria-labelledby prop.");function S({row:t}){var n,r;const{active:o,setNodeRef:l,transform:i,transition:a,isDragging:s}=Sl({id:t.id,disabled:null!==(n=t?.disabled)&&void 0!==n&&n,data:{disabled:null!==(r=t?.disabled)&&void 0!==r&&r}}),u={ref:l,style:{backgroundColor:s?"#fff":void 0,transform:yr.Transform.toString(i),transition:a,width:t?.getSize?`${t.getSize().width}px`:void 0,zIndex:o&&o.id===t.id?1:void 0},className:St("gb-table__row","gb-table__row--draggable",s&&"is-dragging"),"data-row-id":t.id};return(0,e.createElement)("tr",{...u,key:t.id,"data-row-id":t.id},t.getVisibleCells().map((t=>(0,e.createElement)("td",{key:t.id,className:"gb-table__cell"},Oi(t.column.columnDef.cell,t.getContext())))))}return(0,e.createElement)(tl,{modifiers:[_l,Ml],sensors:C,collisionDetection:Br,onDragStart:n,onDragEnd:r},(0,e.createElement)("table",{className:St("gb-table",Pi.table,a&&Pi.striped,c),cellPadding:0,"aria-label":f,"aria-labelledby":p,ref:v},s&&(0,e.createElement)("thead",null,b.getHeaderGroups().map((t=>(0,e.createElement)("tr",{key:t.id},t.headers.map((t=>(0,e.createElement)("th",{key:t.id,className:"gb-table__header",colSpan:t.colSpan,"data-column-id":t.column.id},t.isPlaceholder?null:Oi(t.column.columnDef.header,t.getContext())))))))),(0,e.createElement)("tbody",null,w?(0,e.createElement)(fl,{items:y,strategy:gl},y.map((t=>(0,e.createElement)(S,{row:t,key:t.id,canDrag:w})))):(0,e.createElement)(e.Fragment,null,y.map((t=>(0,e.createElement)("tr",{key:t.id,className:"gb-table__row","data-row-id":t.id},t.getVisibleCells().map((t=>(0,e.createElement)("td",{key:t.id,className:"gb-table__cell","data-column-id":t.column.id},Oi(t.column.columnDef.cell,t.getContext())))))))))))}Fi.RowHandle=function({row:t,disabled:n=!1}){var r,o;const{attributes:l,listeners:a}=Sl({id:t.id,disabled:n||null!==(r=t?.disabled)&&void 0!==r&&r,data:{disabled:n||null!==(o=t?.disabled)&&void 0!==o&&o}});return(0,e.createElement)(Ol,{className:"gb-table__handle",variant:"tertiary",showTooltip:!1,label:(0,i.__)("Reorder style","generateblocks-pro"),...l,...a})},Fi.HeaderRowCheckbox=function({table:t,onChange:n,...r}){return(0,e.createElement)(Vt,{type:"checkbox",className:"components-checkbox-control__input gb-table__checkbox gb-table__checkbox--all",checked:t.getIsAllRowsSelected(),indeterminate:t.getIsSomeRowsSelected(),onChange:e=>{t.toggleAllRowsSelected(e.target.checked),n&&n(e)},...r})},Fi.RowCheckbox=function({row:t,"aria-label":n=(0,i.__)("Select row","generateblocks-pro"),...r}){return(0,e.createElement)(Vt,{checked:t.getIsSelected(),disabled:!t.getCanSelect(),onChange:t.getToggleSelectedHandler(),"aria-label":n,...r})};const Ti=(0,s.jsx)(a.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(a.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"})}),Ni={panel:"mqRW0YlJ1flzlOez4Mst",panelTitle:"I8ck24MQn_eNBj2zXeZB",panelContent:"VX20qQNa1dTanrRPQ8Iy"};function Li(t){const{title:n,children:r,dropdownOptions:a=[],shouldRender:s=!0,className:u=""}=t,c=(0,o.useRef)(null),[d,g]=(0,o.useState)(!0);return(0,o.useEffect)((()=>{c.current&&""===c.current.innerHTML.trim()&&g(!1)}),[r]),s&&d?(0,e.createElement)("div",{className:Ct(Ni.panel,u)},(0,e.createElement)(l.PanelBody,null,n&&(0,e.createElement)("div",{className:Ni.panelTitle},(0,e.createElement)("h2",null,n),a.length>0&&(0,e.createElement)(l.DropdownMenu,{icon:Ti,label:(0,i.__)("More options","generateblocks"),controls:a})),(0,e.createElement)("div",{className:Ni.panelContent,ref:c},(0,Bt.applyFilters)("generateblocks.blockSettings.openPanel",r,t)))):null}(0,i.__)("More design options","generateblocks");const Vi={library:"xOotMtQd8Jr_qhwVmc_q",icons:"Btqg4QpBGmY2acOG_uQp",categories:"ZktMu885AO0rej3bPUnV",footer:"iyXuZ632x0NMU_1ciH4f",customIcon:"V1F5CRflBf7RkqTtvX2g",insert:"WEYMXbS3VDdUfok2g3T0"};function Ai({icons:t,onInsert:n,category:r="",iconType:i="",icon:a,clearLabel:s="Clear",insertLabel:u="Insert",customLabel:c="Custom SVG"}){var d;const g=(()=>{if(a)for(const[e,n]of Object.entries(t))for(const[,t]of Object.entries(n.svgs))if(t.icon&&(0,o.renderToString)(t.icon)===a)return e;return r||Object.keys(t)[0]})(),[h,p]=(0,o.useState)(g),f=Object.keys(t),m=null!==(d=(0,o.useMemo)((()=>h&&Object.values(t[h].svgs)),[h]))&&void 0!==d?d:[],[v,b]=(0,o.useState)(!1),[y,w]=(0,o.useState)(a);return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:St(Vi.library,{[`gb-icon-library--${i}`]:i})},(0,e.createElement)("div",{className:Vi.categories},f.map(((n,r)=>(0,e.createElement)(l.Button,{key:r,className:Vi.categoryButton,onClick:()=>{p(n),b(!1)},isPressed:n===h},t[n].group))),(0,e.createElement)(l.Button,{className:Vi.categoryButton,onClick:()=>{b(!0),p("")},isPressed:v},c)),(0,e.createElement)("div",{className:Vi.icons},(0,e.createElement)(e.Fragment,null,!!m&&m.map((({icon:t},n)=>{let r=t;return"string"!=typeof r&&(r=(0,o.renderToString)(r)),(0,e.createElement)(l.Button,{key:n,className:"gb-icon-library__icon",onClick:()=>w(r),isPressed:r===y},(0,e.createElement)("span",{dangerouslySetInnerHTML:{__html:r}}))})),!!v&&(0,e.createElement)(l.TextareaControl,{type:"textarea",className:Vi.customIcon,value:y,onChange:e=>w(e),rows:"10"})))),(0,e.createElement)("div",{className:Vi.footer},!!y&&(0,e.createElement)(l.Button,{isDestructive:!0,onClick:()=>n("")},s),(0,e.createElement)("div",{className:Vi.insert},!!y&&(0,e.createElement)("span",{dangerouslySetInnerHTML:{__html:y}}),(0,e.createElement)(l.Button,{variant:"primary",onClick:()=>n(y),disabled:!y},u))))}const zi={control:"kgQZOd0jSMpvCxnePEAA",preview:"odBn0skeqX1A62nOJ45H",modal:"cfpLnipAfrbqcnGYzztA"};function Bi({value:t,title:n,setIsOpen:r,onChange:o,iconType:i="icon",icons:a,clearLabel:s,insertLabel:u,customLabel:c}){return(0,e.createElement)(l.Modal,{title:n,onRequestClose:()=>r(!1),className:St(zi.modal,{"gb-icon-library-modal":!0}),size:"large"},(0,e.createElement)(Ai,{icons:a,onInsert:e=>{o(e),r(!1)},iconType:i,icon:t,clearLabel:s,insertLabel:u,customLabel:c}))}function Hi({value:t,onChange:n,attributes:r,iconType:i,label:a,insertLabel:s="Insert",clearLabel:u="Clear",customLabel:c="Custom SVG",openLabel:d="Open Library",modalTitle:g="Icon Library",icons:h={}}){const[p,f]=(0,o.useState)(!1),m=Lt();return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.BaseControl,{className:zi.control,label:a,id:m},(0,e.createElement)(l.Button,{className:zi.preview,onClick:()=>f(!0)},!!t&&(0,e.createElement)("span",{dangerouslySetInnerHTML:{__html:t}}),d)),!!p&&(0,e.createElement)(Bi,{value:t,onChange:n,setIsOpen:f,attributes:r,iconType:i,icons:h,title:g,clearLabel:u,insertLabel:s,customLabel:c}))}const ji=window.wp.data,Ki=window.wp.coreData;function Gi(e){return e.replaceAll(" ","_").replaceAll(/[^a-zA-Z0-9.\-_]/g,"")}const qi={description:"",display_name:"",first_name:"",last_name:"",nickname:"",user_email:""};function Ui({type:t,post:n,term:r,user:a,source:s,onSelect:u,onEnter:c,onClear:d,onAdd:g,help:h,fallback:p=[],label:f=(0,i.__)("Meta key","generateblocks"),value:m=""}){const v=(0,ji.useSelect)((e=>{const{getCurrentUser:t}=e(Ki.store);return t?t():null})),b=(0,o.useMemo)((()=>{var e;switch(t){case"post":if(!n?.meta)return[];const o=(0,Bt.applyFilters)("generateblocks.editor.SelectMetaKeys.keys",Object.keys(n.meta).map((e=>({label:e,value:e}))),n,t);return 0===o.length?[]:[{id:"post_meta",label:(0,i.__)("Post Meta","generateblocks"),items:o}];case"author":const l={...qi,...a?.meta};if(0===Object.keys(l).length)return[];const s=(0,Bt.applyFilters)("generateblocks.editor.SelectMetaKeys.keys",Object.keys(l).map((e=>({label:e,value:e}))),l,t);return 0===s.length?[]:[{id:"author_meta",label:(0,i.__)("Author Meta","generateblocks"),items:s}];case"user":const u={...qi,...a?.meta};if(0===Object.keys(u).length)return[];const c=(0,Bt.applyFilters)("generateblocks.editor.SelectMetaKeys.keys",Object.keys(u).map((e=>({label:e,value:e}))),u,t);return 0===c.length?[]:[{id:"user_meta",label:(0,i.__)("User Meta","generateblocks"),items:c}];case"term":const d=null!==(e=r?.meta)&&void 0!==e?e:{};if(!d)return[];const g=(0,Bt.applyFilters)("generateblocks.editor.SelectMetaKeys.keys",Object.keys(d).map((e=>({label:e,value:e}))),d,t);return 0===g.length?[]:[{id:"term_meta",label:(0,i.__)("Term Meta","generateblocks"),items:g}];default:return p}}),[n,t,r,v,a,s]),y=(0,Bt.applyFilters)("generateblocks.editor.SelectMetaKeys.options",b,{user:a||v,post:n,term:r,type:t,source:s});return(0,e.createElement)(Mt,{className:"gb-meta-key-select",label:f,selected:m,onSelect:u,source:y,toStringKey:"value",showClear:!0,onEnter:c,onClear:d,itemFilter:Mt.groupItemFilter,help:h,filterOnSelect:!1,filterInputValue:Gi,noResultsText:null,afterInputWrapper:({inputValue:t,items:n})=>(0,e.createElement)(l.Button,{variant:"primary",size:"compact",className:"gb-gc-add__button",disabled:!t||n.length>0||t===m,onClick:()=>g&&g({inputValue:t,items:n})},(0,i.__)("Add","generateblocks"))})}const Wi=window.wp.apiFetch;var Qi=n.n(Wi),$i=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Xi="undefined"==typeof window||"Deno"in globalThis;function Yi(){}function Ji(e){return"number"==typeof e&&e>=0&&e!==1/0}function Zi(e,t){return"function"==typeof e?e(t):e}function ea(e,t){return"function"==typeof e?e(t):e}function ta(e,t){if(e===t)return e;const n=ra(e)&&ra(t);if(n||oa(e)&&oa(t)){const r=n?e:Object.keys(e),o=r.length,l=n?t:Object.keys(t),i=l.length,a=n?[]:{};let s=0;for(let o=0;o<i;o++){const i=n?o:l[o];(!n&&r.includes(i)||n)&&void 0===e[i]&&void 0===t[i]?(a[i]=void 0,s++):(a[i]=ta(e[i],t[i]),a[i]===e[i]&&void 0!==e[i]&&s++)}return o===i&&s===o?e:a}return t}function na(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function ra(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function oa(e){if(!la(e))return!1;const t=e.constructor;if(void 0===t)return!0;const n=t.prototype;return!!la(n)&&!!n.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function la(e){return"[object Object]"===Object.prototype.toString.call(e)}function ia(e,t,n){return"function"==typeof n.structuralSharing?n.structuralSharing(e,t):!1!==n.structuralSharing?ta(e,t):t}function aa(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function sa(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var ua=Symbol(),ca=new class extends $i{#e;#t;#n;constructor(){super(),this.#n=e=>{if(!Xi&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},da=function(){let e=[],t=0,n=e=>{e()},r=e=>{e()},o=e=>setTimeout(e,0);const l=r=>{t?e.push(r):o((()=>{n(r)}))};return{batch:l=>{let i;t++;try{i=l()}finally{t--,t||(()=>{const t=e;e=[],t.length&&o((()=>{r((()=>{t.forEach((e=>{n(e)}))}))}))})()}return i},batchCalls:e=>(...t)=>{l((()=>{e(...t)}))},schedule:l,setNotifyFunction:e=>{n=e},setBatchNotifyFunction:e=>{r=e},setScheduler:e=>{o=e}}}(),ga=new class extends $i{#r=!0;#t;#n;constructor(){super(),this.#n=e=>{if(!Xi&&window.addEventListener){const t=()=>e(!0),n=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",n)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#r!==e&&(this.#r=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#r}};function ha(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(n=t.networkMode,"online"!==(n??"online")||ga.isOnline()?"fetching":"paused"),...void 0===e&&{error:null,status:"pending"}};var n}function pa(){let e,t;const n=new Promise(((n,r)=>{e=n,t=r}));function r(e){Object.assign(n,e),delete n.resolve,delete n.reject}return n.status="pending",n.catch((()=>{})),n.resolve=t=>{r({status:"fulfilled",value:t}),e(t)},n.reject=e=>{r({status:"rejected",reason:e}),t(e)},n}var fa=class extends $i{constructor(e,t){super(),this.options=t,this.#o=e,this.#l=null,this.#i=pa(),this.options.experimental_prefetchInRender||this.#i.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#o;#a=void 0;#s=void 0;#u=void 0;#c;#d;#i;#l;#g;#h;#p;#f;#m;#v;#b=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#a.addObserver(this),ma(this.#a,this.options)?this.#y():this.updateResult(),this.#w())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return va(this.#a,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return va(this.#a,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#C(),this.#S(),this.#a.removeObserver(this)}setOptions(e,t){const n=this.options,r=this.#a;if(this.options=this.#o.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof ea(this.options.enabled,this.#a))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#x(),this.#a.setOptions(this.options),n._defaulted&&!na(this.options,n)&&this.#o.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#a,observer:this});const o=this.hasListeners();o&&ba(this.#a,r,this.options,n)&&this.#y(),this.updateResult(t),!o||this.#a===r&&ea(this.options.enabled,this.#a)===ea(n.enabled,this.#a)&&Zi(this.options.staleTime,this.#a)===Zi(n.staleTime,this.#a)||this.#I();const l=this.#R();!o||this.#a===r&&ea(this.options.enabled,this.#a)===ea(n.enabled,this.#a)&&l===this.#v||this.#E(l)}getOptimisticResult(e){const t=this.#o.getQueryCache().build(this.#o,e),n=this.createResult(t,e);return r=n,!na(this.getCurrentResult(),r)&&(this.#u=n,this.#d=this.options,this.#c=this.#a.state),n;var r}getCurrentResult(){return this.#u}trackResult(e,t){const n={};return Object.keys(e).forEach((r=>{Object.defineProperty(n,r,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(r),t?.(r),e[r])})})),n}trackProp(e){this.#b.add(e)}getCurrentQuery(){return this.#a}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#o.defaultQueryOptions(e),n=this.#o.getQueryCache().build(this.#o,t);return n.fetch().then((()=>this.createResult(n,t)))}fetch(e){return this.#y({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#u)))}#y(e){this.#x();let t=this.#a.fetch(this.options,e);return e?.throwOnError||(t=t.catch(Yi)),t}#I(){this.#C();const e=Zi(this.options.staleTime,this.#a);if(Xi||this.#u.isStale||!Ji(e))return;const t=function(e,t){return Math.max(e+(t||0)-Date.now(),0)}(this.#u.dataUpdatedAt,e),n=t+1;this.#f=setTimeout((()=>{this.#u.isStale||this.updateResult()}),n)}#R(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#a):this.options.refetchInterval)??!1}#E(e){this.#S(),this.#v=e,!Xi&&!1!==ea(this.options.enabled,this.#a)&&Ji(this.#v)&&0!==this.#v&&(this.#m=setInterval((()=>{(this.options.refetchIntervalInBackground||ca.isFocused())&&this.#y()}),this.#v))}#w(){this.#I(),this.#E(this.#R())}#C(){this.#f&&(clearTimeout(this.#f),this.#f=void 0)}#S(){this.#m&&(clearInterval(this.#m),this.#m=void 0)}createResult(e,t){const n=this.#a,r=this.options,o=this.#u,l=this.#c,i=this.#d,a=e!==n?e.state:this.#s,{state:s}=e;let u,c={...s},d=!1;if(t._optimisticResults){const o=this.hasListeners(),l=!o&&ma(e,t),i=o&&ba(e,n,t,r);(l||i)&&(c={...c,...ha(s.data,e.options)}),"isRestoring"===t._optimisticResults&&(c.fetchStatus="idle")}let{error:g,errorUpdatedAt:h,status:p}=c;if(t.select&&void 0!==c.data)if(o&&c.data===l?.data&&t.select===this.#g)u=this.#h;else try{this.#g=t.select,u=t.select(c.data),u=ia(o?.data,u,t),this.#h=u,this.#l=null}catch(e){this.#l=e}else u=c.data;if(void 0!==t.placeholderData&&void 0===u&&"pending"===p){let e;if(o?.isPlaceholderData&&t.placeholderData===i?.placeholderData)e=o.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#p?.state.data,this.#p):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#l=null}catch(e){this.#l=e}void 0!==e&&(p="success",u=ia(o?.data,e,t),d=!0)}this.#l&&(g=this.#l,u=this.#h,h=Date.now(),p="error");const f="fetching"===c.fetchStatus,m="pending"===p,v="error"===p,b=m&&f,y=void 0!==u,w={status:p,fetchStatus:c.fetchStatus,isPending:m,isSuccess:"success"===p,isError:v,isInitialLoading:b,isLoading:b,data:u,dataUpdatedAt:c.dataUpdatedAt,error:g,errorUpdatedAt:h,failureCount:c.fetchFailureCount,failureReason:c.fetchFailureReason,errorUpdateCount:c.errorUpdateCount,isFetched:c.dataUpdateCount>0||c.errorUpdateCount>0,isFetchedAfterMount:c.dataUpdateCount>a.dataUpdateCount||c.errorUpdateCount>a.errorUpdateCount,isFetching:f,isRefetching:f&&!m,isLoadingError:v&&!y,isPaused:"paused"===c.fetchStatus,isPlaceholderData:d,isRefetchError:v&&y,isStale:ya(e,t),refetch:this.refetch,promise:this.#i};if(this.options.experimental_prefetchInRender){const t=e=>{"error"===w.status?e.reject(w.error):void 0!==w.data&&e.resolve(w.data)},r=()=>{const e=this.#i=w.promise=pa();t(e)},o=this.#i;switch(o.status){case"pending":e.queryHash===n.queryHash&&t(o);break;case"fulfilled":"error"!==w.status&&w.data===o.value||r();break;case"rejected":"error"===w.status&&w.error===o.reason||r()}}return w}updateResult(e){const t=this.#u,n=this.createResult(this.#a,this.options);if(this.#c=this.#a.state,this.#d=this.options,void 0!==this.#c.data&&(this.#p=this.#a),na(n,t))return;this.#u=n;const r={};!1!==e?.listeners&&(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,n="function"==typeof e?e():e;if("all"===n||!n&&!this.#b.size)return!0;const r=new Set(n??this.#b);return this.options.throwOnError&&r.add("error"),Object.keys(this.#u).some((e=>{const n=e;return this.#u[n]!==t[n]&&r.has(n)}))})()&&(r.listeners=!0),this.#M({...r,...e})}#x(){const e=this.#o.getQueryCache().build(this.#o,this.options);if(e===this.#a)return;const t=this.#a;this.#a=e,this.#s=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#w()}#M(e){da.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#u)})),this.#o.getQueryCache().notify({query:this.#a,type:"observerResultsUpdated"})}))}};function ma(e,t){return function(e,t){return!1!==ea(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&va(e,t,t.refetchOnMount)}function va(e,t,n){if(!1!==ea(t.enabled,e)){const r="function"==typeof n?n(e):n;return"always"===r||!1!==r&&ya(e,t)}return!1}function ba(e,t,n,r){return(e!==t||!1===ea(r.enabled,e))&&(!n.suspense||"error"!==e.state.status)&&ya(e,n)}function ya(e,t){return!1!==ea(t.enabled,e)&&e.isStaleByTime(Zi(t.staleTime,e))}function wa(e){return{onFetch:(t,n)=>{const r=t.options,o=t.fetchOptions?.meta?.fetchMore?.direction,l=t.state.data?.pages||[],i=t.state.data?.pageParams||[];let a={pages:[],pageParams:[]},s=0;const u=async()=>{let n=!1;const u=function(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==ua?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}(t.options,t.fetchOptions),c=async(e,r,o)=>{if(n)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);const l={queryKey:t.queryKey,pageParam:r,direction:o?"backward":"forward",meta:t.options.meta};var i;i=l,Object.defineProperty(i,"signal",{enumerable:!0,get:()=>(t.signal.aborted?n=!0:t.signal.addEventListener("abort",(()=>{n=!0})),t.signal)});const a=await u(l),{maxPages:s}=t.options,c=o?sa:aa;return{pages:c(e.pages,a,s),pageParams:c(e.pageParams,r,s)}};if(o&&l.length){const e="backward"===o,t={pages:l,pageParams:i},n=(e?Sa:Ca)(r,t);a=await c(t,n,e)}else{const t=e??l.length;do{const e=0===s?i[0]??r.initialPageParam:Ca(r,a);if(s>0&&null==e)break;a=await c(a,e),s++}while(s<t)}return a};t.options.persister?t.fetchFn=()=>t.options.persister?.(u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n):t.fetchFn=u}}}function Ca(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function Sa(e,{pages:t,pageParams:n}){return t.length>0?e.getPreviousPageParam?.(t[0],t,n[0],n):void 0}function xa(e,t){return!!t&&null!=Ca(e,t)}function Ia(e,t){return!(!t||!e.getPreviousPageParam)&&null!=Sa(e,t)}var Ra=class extends fa{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:wa()},t)}getOptimisticResult(e){return e.behavior=wa(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){const{state:n}=e,r=super.createResult(e,t),{isFetching:o,isRefetching:l,isError:i,isRefetchError:a}=r,s=n.fetchMeta?.fetchMore?.direction,u=i&&"forward"===s,c=o&&"forward"===s,d=i&&"backward"===s,g=o&&"backward"===s;return{...r,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:xa(t,n.data),hasPreviousPage:Ia(t,n.data),isFetchNextPageError:u,isFetchingNextPage:c,isFetchPreviousPageError:d,isFetchingPreviousPage:g,isRefetchError:a&&!u&&!d,isRefetching:l&&!c&&!g}}},Ea=e.createContext(void 0),Ma=t=>{const n=e.useContext(Ea);if(t)return t;if(!n)throw new Error("No QueryClient set, use QueryClientProvider to set one");return n};var _a=e.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),ka=()=>e.useContext(_a);function Da(){}var Oa=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},Pa=t=>{e.useEffect((()=>{t.clearReset()}),[t])},Fa=({result:e,errorResetBoundary:t,throwOnError:n,query:r})=>{return e.isError&&!t.isReset()&&!e.isFetching&&r&&(o=n,l=[e.error,r],"function"==typeof o?o(...l):!!o);var o,l},Ta=e.createContext(!1),Na=()=>e.useContext(Ta),La=(Ta.Provider,e=>{e.suspense&&(void 0===e.staleTime&&(e.staleTime=1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))}),Va=(e,t)=>e.isLoading&&e.isFetching&&!t,Aa=(e,t)=>e?.suspense&&t.isPending,za=(e,t,n)=>t.fetchOptimistic(e).catch((()=>{n.clearReset()}));function Ba(t,n){return function(t,n,r){const o=Ma(r),l=Na(),i=ka(),a=o.defaultQueryOptions(t);o.getDefaultOptions().queries?._experimental_beforeQuery?.(a),a._optimisticResults=l?"isRestoring":"optimistic",La(a),Oa(a,i),Pa(i);const s=!o.getQueryCache().get(a.queryHash),[u]=e.useState((()=>new n(o,a))),c=u.getOptimisticResult(a);if(e.useSyncExternalStore(e.useCallback((e=>{const t=l?()=>{}:u.subscribe(da.batchCalls(e));return u.updateResult(),t}),[u,l]),(()=>u.getCurrentResult()),(()=>u.getCurrentResult())),e.useEffect((()=>{u.setOptions(a,{listeners:!1})}),[a,u]),Aa(a,c))throw za(a,u,i);if(Fa({result:c,errorResetBoundary:i,throwOnError:a.throwOnError,query:o.getQueryCache().get(a.queryHash)}))throw c.error;if(o.getDefaultOptions().queries?._experimental_afterQuery?.(a,c),a.experimental_prefetchInRender&&!Xi&&Va(c,l)){const e=s?za(a,u,i):o.getQueryCache().get(a.queryHash)?.promise;e?.catch(Da).finally((()=>{u.updateResult()}))}return a.notifyOnChangeProps?c:u.trackResult(c)}(t,Ra,n)}function Ha(e="array"){return(0,ji.useSelect)((t=>{const{getPostTypes:n}=t(Ki.store),r=n({per_page:-1});if("object"===e){const e={};return Array.isArray(r)?(r.forEach((t=>{e[t.slug]=t})),e):e}return r}),[])}function ja({value:t,onChange:n,onInputChange:r,onClear:a,help:s,placeholder:u,queryClient:c,multiple:d=!1,postType:g=["any"],postStatus:h=["publish"],currentPostId:p=0,includeCurrent:f=!0,label:m=(0,i.__)("Select Post","generateblocks-pro")}){const v=Ha("object"),b=(0,o.useRef)(0),{status:y,data:w=[],error:C,isFetching:S,fetchNextPage:x,hasNextPage:I}=Ba({queryKey:["getPosts",{postType:g,currentPostId:p,value:t}],queryFn:async({pageParam:e})=>{const t={post_type:g,posts_per_page:150,post__not_in:[p],post_status:h,paged:e,ignore_sticky_posts:!0},n=await Qi()({path:"/generateblocks/v1/get-wp-query",method:"POST",data:{args:t,attributes:{},block:{}}});return n?.max_num_pages!==b&&(b.current=n.max_num_pages),n.posts},initialPageParam:1,getNextPageParam:(e,t,n)=>{const r=n+1;return r>b.current?void 0:r},maxPages:b.current,staleTime:6e4},c),R="error"===y;I&&x();let E=s;S&&(E=(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.Spinner,{style:{margin:"0 8px 0 0",width:"12px",height:"12px"}}),(0,i.__)("Loading more posts…","generateblocks-pro")));const M=(0,o.useMemo)((()=>Array.isArray(w?.pages)?function(e,t,n){const r={};e.forEach((e=>{const t={label:`#${e.ID} ${""!==e.post_title?e.post_title:e.post_name}`,value:e.ID};Array.isArray(r?.[e.post_type])?r[e.post_type].push(t):r[e.post_type]=[t]}));const o=Object.entries(r).map((([e,n])=>{var r;return{id:e,label:null!==(r=t?.[e]?.labels?.name)&&void 0!==r?r:e,items:n}})),{isGbProActive:l=!1}=gbPermissions||{};return l&&n?[{label:(0,i.__)("Current","generateblocks-pro"),items:[{label:(0,i.__)("Current Post","generateblocks-pro"),value:"current"}]},...o]:o}(w.pages.flat(),v,f):[]),[w?.pages,v]);return d?(0,e.createElement)(Dt,{label:m,value:0===t?[]:t,onChange:n,onInputChange:r,help:R?C.message:E,placeholder:u,options:M}):(0,e.createElement)(Mt,{label:m,selected:t,onSelect:n,onChange:r,source:M,showClear:!!a,onClear:a,help:R?C.message:E,toStringKey:"label",itemFilter:Mt.groupItemFilter,filterOnSelect:!1,placeholder:u})}function Ka({value:t,onChange:n,onInputChange:r,onClear:a,help:s,placeholder:u,queryClient:c,multiple:d=!1,currentLabel:g=(0,i.__)("Current User","generateblocks-pro"),label:h=(d?(0,i.__)("Select Users","generateblocks-pro"):(0,i.__)("Select User","generateblocks-pro")),includeCurrent:p=!0}){const f=(0,o.useRef)(0),{status:m,data:v=[],error:b,isFetching:y,fetchNextPage:w,hasNextPage:C}=Ba({queryKey:["getUsers",{value:t}],queryFn:async({pageParam:e})=>{const t={number:150,paged:e},n=await Qi()({path:"/generateblocks/v1/get-user-query",method:"POST",data:{args:t}});return n?.max_pages!==f&&(f.current=n.max_pages),n.users},initialPageParam:1,getNextPageParam:(e,t,n)=>{const r=n+1;return r>f.current?void 0:r},maxPages:f.current,staleTime:6e4},c),S="error"===m;C&&w();let x=s;y&&(x=(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.Spinner,{style:{margin:"0 8px 0 0",width:"12px",height:"12px"}}),(0,i.__)("Loading more users…","generateblocks-pro")));const I=(0,o.useMemo)((()=>Array.isArray(v?.pages)?function(t,n,r){const o=t.map((t=>{const{data:n={},roles:r=[]}=t,{display_name:o,user_login:a}=n;return{label:`#${t.ID} ${o||a}`,info:(0,e.createElement)(e.Fragment,null,(0,e.createElement)(l.VisuallyHidden,null,(0,i.__)("Roles:","generateblocks-pro")),r.join(", ")),value:t.ID,id:t.ID}})),{isGbProActive:a=!1}=gbPermissions||{};return a&&r?[{label:n,value:"current"},...o]:o}(v.pages.flat(),g,p):[]),[v?.pages]);return d?(0,e.createElement)(Dt,{label:h,value:0===t?[]:t,onChange:n,onInputChange:r,help:S?b.message:x,placeholder:u,options:I}):(0,e.createElement)(Mt,{label:h,selected:t,onSelect:n,onChange:r,source:I,showClear:!!a,onClear:a,help:S?b.message:x,toStringKey:"label",itemFilter:Mt.groupItemFilter,filterOnSelect:!1,placeholder:u})}function Ga({id:t,onChange:n,value:r,help:l,onClear:a,placeholder:s,multiple:u=!1,label:c=(0,i.__)("Select post type","generateblocks"),excludePostTypes:d=[],filter:g=e=>!d.includes(e?.slug)}){const h=Ha(),p=(0,o.useMemo)((()=>Array.isArray(h)?h.filter((e=>e.viewable&&"attachment"!==e.slug&&!d.includes(e.slug))):[]),[h]),f=(0,o.useMemo)((()=>p.filter(g).reduce(((e,t)=>(e.push({value:t.slug,label:t.name}),e)),[])),[p]);return u?(0,e.createElement)(Dt,{id:t,label:c,help:l,placeholder:s,options:f,value:r,onChange:n}):(0,e.createElement)(Mt,{id:t,label:c,help:l,placeholder:s,source:f,toStringKey:"label",filterOnSelect:!1,selected:r,onSelect:n,showClear:!!a,onClear:a})}function qa({value:t,taxonomy:n,onChange:r,postId:o,placeholder:l,help:a,noResultsText:s,multiple:u=!1,currentLabel:c=(0,i.__)("Current post terms","generateblocks-pro"),label:d=(u?(0,i.__)("Select Terms","generateblocks-pro"):(0,i.__)("Select Term","generateblocks-pro")),includeCurrent:g=!0,onClear:h}){const p=Za({taxonomy:n,postId:o}),f=Array.isArray(p)?p.map((e=>({label:e.name,value:e.id.toString(),id:e.id}))):[],{isGbProActive:m=!1}=gbPermissions||{};return m&&g&&f.unshift({label:c,value:"current",id:"current"}),u?(0,e.createElement)(Dt,{id:"gblocks-select-term",label:d,value:t,onChange:r,placeholder:l,help:a,options:f,noResultsText:s}):(0,e.createElement)(Mt,{id:"gblocks-select-term",label:d,placeholder:l,selected:t,source:f,onSelect:r,toStringKey:"label",showClear:!!h,filterOnSelect:!1,help:a,noResultsText:s,onClear:h})}function Ua({id:t,onChange:n,value:r,help:o,postType:a}){var s;const u=es(a),c=[{value:"",label:(0,i.__)("Select Taxonomy…","generateblocks")},...u?u.map((e=>({value:e.slug,label:e.name}))):[]];return(0,e.createElement)(l.ComboboxControl,{id:t,label:(0,i.__)("Select taxonomy","generateblocks"),help:o,placeholder:(0,i.__)("Select Taxonomy…","generateblocks"),options:c,value:r||(null!==(s=c[0]?.value)&&void 0!==s?s:""),onChange:n})}function Wa({postId:e,load:t=[],options:n={}}){const[r,l]=(0,o.useState)(null),[i,a]=(0,o.useState)(!0);return(0,o.useEffect)((()=>{e&&t.length&&async function(){a(!0);try{const r=await Qi()({path:`/generateblocks/v1/post-record?postId=${e}&load=${t.join()}&options=${JSON.stringify(n)}`});l(r)}catch(e){console.error("Error fetching post record:",e)}finally{a(!1)}}()}),[e,t,n]),{record:r,isLoading:i}}function Qa(e){const[t,n]=(0,o.useState)(null),[r,l]=(0,o.useState)(!0);return(0,o.useEffect)((()=>{e&&async function(){l(!0);try{const t=await Qi()({path:`/generateblocks/v1/get-user-record?id=${e}`});n(t)}catch(e){console.error("Error fetching user record:",e)}finally{l(!1)}}()}),[e]),{record:t,isLoading:r}}function $a({termId:e,taxonomy:t}){return(0,ji.useSelect)((n=>{const{isResolving:r,getEntityRecord:o,hasFinishedResolution:l}=n(Ki.store);if(!e||!t)return{record:null,isLoading:!1};const i=(0,Bt.applyFilters)("generateblocks.editor.dynamicTags.term-request-params",["taxonomy",t,e]),a=o(...i),s=!l("getEntityRecord",i)||r("getEntityRecord",i);return{record:(0,Bt.applyFilters)("generateblocks.editor.dynamicTags.termRecord",a),isLoading:s}}))}function Xa({shouldRequest:e=!0,id:t,key:n,singleOnly:r=!1}){const[l,i]=(0,o.useState)({data:null,isResolvingData:!0,hasResolvedData:!1});return(0,o.useEffect)((()=>{e&&t&&n&&(async()=>{const e=await Qi()({path:`/generateblocks/v1/meta/get-post-meta?id=${t}&key=${n}&singleOnly=${r}`});i({data:e,isResolvingData:!1,hasResolvedData:!0})})()}),[e,t,n,r]),l}function Ya({shouldRequest:e=!0,key:t,singleOnly:n=!1}){const[r,l]=(0,o.useState)({data:null,isResolvingData:!0,hasResolvedData:!1});return(0,o.useEffect)((()=>{e&&t&&(async()=>{const e=await Qi()({path:`/generateblocks/v1/meta/get-option?key=${t}&singleOnly=${n}`});l({data:e,isResolvingData:!1,hasResolvedData:!0})})()}),[e,t,n]),r}function Ja(e=!0){return(0,ji.useSelect)((t=>{const{isResolving:n,getEntityRecords:r,hasFinishedResolution:o}=t(Ki.store);if(!e)return{record:null,isLoading:!1};const l=["root","user",{per_page:-1}];return{records:r(...l),isLoading:!o("getEntityRecord",l)||n("getEntityRecord",l)}}),[e])}function Za({taxonomy:e,postId:t}){return(0,ji.useSelect)((n=>{const{getEntityRecords:r}=n(Ki.store),o={per_page:-1};return t&&(o.post=t),r("taxonomy",e,o)}),[e,t])}function es(e){return(0,ji.useSelect)((t=>{const{getTaxonomies:n}=t(Ki.store),r={per_page:-1};return e&&!["gp_elements","wp_block"].includes(e)&&(r.types=e),n(r)||[]}),[e])}})(),r})(),e.exports=t()}},t={},n=function n(r){var o=t[r];if(void 0!==o)return o.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}(9224);(window.gbp=window.gbp||{}).components=n})();