(()=>{var e={5758:e=>{var t;globalThis,t=()=>(()=>{"use strict";var e={17:e=>{e.exports=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var r,o,s;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(o=r;0!==o--;)if(!e(t[o],n[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(s=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(o=r;0!==o--;)if(!Object.prototype.hasOwnProperty.call(n,s[o]))return!1;for(o=r;0!==o--;){var c=s[o];if(!e(t[c],n[c]))return!1}return!0}return t!=t&&n!=n}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};return(()=>{n.r(r),n.d(r,{BlockStyles:()=>b,Style:()=>w,buildChangedStylesObject:()=>I,getPreviewDevice:()=>h,getSelector:()=>E,useAtRuleEffect:()=>A,useCurrentAtRule:()=>D,useDecodeStyleKeys:()=>G,useDeviceType:()=>q,useGenerateCSSEffect:()=>O,useSetStyles:()=>C,useStyleSelectorEffect:()=>x,withUniqueId:()=>k});const e=window.React,t=window.wp.components,o=window.wp.primitives,s=(0,e.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(o.Path,{fillRule:"evenodd",d:"M10.289 4.836A1 1 0 0111.275 4h1.306a1 1 0 01.987.836l.244 1.466c.787.26 1.503.679 2.108 1.218l1.393-.522a1 1 0 011.216.437l.653 1.13a1 1 0 01-.23 1.273l-1.148.944a6.025 6.025 0 010 2.435l1.149.946a1 1 0 01.23 1.272l-.653 1.13a1 1 0 01-1.216.437l-1.394-.522c-.605.54-1.32.958-2.108 1.218l-.244 1.466a1 1 0 01-.987.836h-1.306a1 1 0 01-.986-.836l-.244-1.466a5.995 5.995 0 01-2.108-1.218l-1.394.522a1 1 0 01-1.217-.436l-.653-1.131a1 1 0 01.23-1.272l1.149-.946a6.026 6.026 0 010-2.435l-1.148-.944a1 1 0 01-.23-1.272l.653-1.131a1 1 0 011.217-.437l1.393.522a5.994 5.994 0 012.108-1.218l.244-1.466zM14.929 12a3 3 0 11-6 0 3 3 0 016 0z",clipRule:"evenodd"})),c=(0,e.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)(o.Path,{d:"M12 4c-4.4 0-8 3.6-8 8v.1c0 4.1 3.2 7.5 7.2 7.9h.8c4.4 0 8-3.6 8-8s-3.6-8-8-8zm0 15V5c3.9 0 7 3.1 7 7s-3.1 7-7 7z"})),l=window.wp.element,i=window.wp.i18n;function u(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=u(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}const a=function(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=u(e))&&(r&&(r+=" "),r+=t);return r},d={settings:"WdIRmBXejeCYBngUrOJ8",panel:"xRZoEmKFu6Mw_ASNGux1",styles:"DYzfrZbGDh6xueb47Tga"},f="gb-block-styles-tab";function b({settingsTab:n=()=>null,stylesTab:r=()=>null,onTabSelect:o=()=>null}){var u;const b=null!==(u=sessionStorage.getItem(f))&&void 0!==u?u:"settings",[g,p]=(0,l.useState)(b),m=(0,l.useMemo)((()=>[{name:"settings",icon:s,title:(0,i.__)("Settings","generateblocks")},{name:"styles",icon:c,title:(0,i.__)("Styles","generateblocks")}]),[]);return(0,e.createElement)(t.TabPanel,{className:a(d.panel,"gb-block-styles-tab-panel",d[g]),activeClass:"is-active",onSelect:e=>{!function(e){sessionStorage.setItem(f,e)}(e),p(e),o(e)},initialTabName:g,tabs:m},(()=>(0,e.createElement)(e.Fragment,null,"settings"===g&&(0,e.createElement)(e.Fragment,null,n),"styles"===g&&(0,e.createElement)(e.Fragment,null,r))))}const g=window.wp.data,p=e=>e.reduce(((e,t)=>{if(t.name&&t.name.includes("generateblocks")&&t.attributes&&t.attributes.uniqueId&&(e.uniqueIds.push(t.attributes.uniqueId),e.clientIds.push(t.clientId)),t.innerBlocks){const{uniqueIds:n,clientIds:r}=p(t.innerBlocks);e.uniqueIds=e.uniqueIds.concat(n),e.clientIds=e.clientIds.concat(r)}return e}),{uniqueIds:[],clientIds:[]}),m=e=>e.substr(2,9).replace("-",""),y=(e,t,n)=>e.filter((e=>e===t)).length>1&&n===e.lastIndexOf(t);function k(t){return n=>{const{clientId:r,attributes:o,setAttributes:s}=n;return(0,l.useEffect)((()=>{const e=function(){const e=(0,g.select)("core/block-editor").getBlocks(),t=e=>e.map((e=>{if("core/widget-area"===e.name){const n=(0,g.select)("core/block-editor").getBlocks(e.clientId);return{...e,innerBlocks:t(n)}}return{...e,innerBlocks:t(e.innerBlocks||[])}}));return t(e)}(),{uniqueIds:t,clientIds:n}=p(e);if(!o.uniqueId||y(t,o.uniqueId,n.indexOf(r))){const e=m(r);s({uniqueId:e})}}),[r]),(0,e.createElement)(t,{...n})}}const v=window.wp.hooks;function S({editorCss:t,id:n}){return t?(0,e.createElement)("style",{id:n},t):null}function w({selector:t,styles:n,getCss:r,clientId:o,name:s}){const c=(0,l.useMemo)((()=>t.replace(".","")),[t]),[i,u]=(0,l.useState)(""),[a,d]=(0,l.useState)(null),[f,b]=(0,l.useState)(!1);(0,l.useEffect)((()=>{const e=document.querySelector('iframe[name="editor-canvas"]')?.contentDocument||document,t=e.querySelector(".is-root-container");let n=null;t&&(n=e.getElementById("generateblocks-block-styles"),n||(n=e.createElement("div"),n.style.display="none",n.id="generateblocks-block-styles",t.insertBefore(n,t.firstChild)),d(n),b(null!==n.closest(".block-editor-block-preview__content-iframe")))}),[]),(0,l.useEffect)((()=>{if(!t)return;let e=!0;return(async()=>{try{const o=await r(t,n,"editor");e&&u(o)}catch(e){console.error("Failed to fetch CSS:",e)}})(),()=>{e=!1}}),[n,t,r]);const g=(0,v.applyFilters)("generateblocks.editor.blockCss",i,{clientId:o,name:s});return!a||f?(0,e.createElement)(S,{editorCss:g,id:c}):(0,l.createPortal)((0,e.createElement)(S,{editorCss:g,id:c}),a)}function h(e){let t="Desktop";return t=e>1024?"Desktop":e>767?"Tablet":"Mobile",t}function I(e,t,n){return t&&n?{[n]:{[t]:e}}:t?{[t]:e}:n?{[n]:e}:e}function E(e,t){const n={"generateblocks/text":"text","generateblocks/element":"element","generateblocks/loop-item":"loop-item","generateblocks/looper":"looper","generateblocks/media":"media","generateblocks/query":"query","generateblocks/query-page-numbers":"query-page-numbers","generateblocks/shape":"shape","generateblocks-pro/accordion":"accordion","generateblocks-pro/accordion-item":"accordion__item","generateblocks-pro/accordion-toggle":"accordion__toggle","generateblocks-pro/accordion-toggle-icon":"accordion__toggle-icon","generateblocks-pro/accordion-content":"accordion__content","generateblocks-pro/tab-item":"tabs__item","generateblocks-pro/tab-items":"tabs__items","generateblocks-pro/tab-menu-item":"tabs__menu-item","generateblocks-pro/tabs":"tabs","generateblocks-pro/tabs-menu":"tabs__menu","generateblocks-pro/navigation":"navigation","generateblocks-pro/menu-container":"menu-container","generateblocks-pro/classic-menu":"menu","generateblocks-pro/classic-menu-item":"menu-item","generateblocks-pro/classic-sub-menu":"sub-menu","generateblocks-pro/menu-toggle":"menu-toggle","generateblocks-pro/site-header":"site-header"};if(n[e])return`.gb-${n[e]}-${t}`}function _(e,t=[]){var n;return null!==(n=t?.find((t=>t.value===e))?.id)&&void 0!==n?n:"all"}function j(e,t){var n;return null!==(n=t.find((t=>t.id===e))?.value)&&void 0!==n?n:""}function A({deviceType:e,atRule:t,setAtRule:n,defaultAtRules:r=[],isSelected:o,getPreviewWidth:s=()=>null}){(0,l.useEffect)((()=>{if(!o)return;let c="";switch(e){case"Desktop":c="";break;case"Tablet":c="mediumSmallWidth";break;case"Mobile":c="smallWidth";break;default:return}if(_(t,r)!==c){const o=h(s(t));if(e===o)return;const l=j(c,r);n(l)}}),[e,t,n,_,j,r,o])}function O({selector:e,styles:t,setAttributes:n,getCss:r,getSelector:o,isSelected:s,blockCss:c="",clientId:i=""}){const u=(0,g.useSelect)((e=>e("core/block-editor")?.getMultiSelectedBlocks()||[]),[]),{updateBlockAttributes:a}=(0,g.useDispatch)("core/block-editor"),d=(0,l.useMemo)((()=>t),[JSON.stringify(t)]),f=(0,l.useMemo)((()=>u.some((e=>e.clientId===i))),[u,i]);(0,l.useEffect)((()=>{if(null===e||""===e)return;const l=function(e,t,n,r,o){const s=Object.keys(r||{}).length>0,c=o?.trim().length>0||!1;return e||t||s&&!c||!s&&c||c&&!o.includes(n)}(s,f,e,d,c);if(!l)return;let i=!0;return async function(){if(f&&u.length>0){const e=u.map((async e=>{const t=o(e?.name,e?.attributes?.uniqueId);if(!t)return;const n=await r(t,e?.attributes?.styles);return n===(e?.attributes?.css||"")?null:{clientId:e.clientId,blockAttrs:{css:n}}})),t=(await Promise.all(e)).filter(Boolean);if(i&&t.length>0){const e=t.map((e=>e.clientId)),n=t.reduce(((e,t)=>(e[t.clientId]=t.blockAttrs,e)),{});a(e,n,!0)}return}const s=await r(e,t);i&&s!==c&&n({css:s})}(),()=>{i=!1}}),[e,d,n,r,o,s,f,c,u,a,i])}function x({isSelected:e,currentStyle:t,selector:n,setCurrentStyle:r,setNestedRule:o}){(0,l.useEffect)((()=>{e&&(t?.selector&&n===t?.selector||(r({selector:n}),o("")))}),[e,t?.selector,n,r,o])}function q(){const{setDeviceType:e=()=>null}=(0,g.useDispatch)("core/editor");return{deviceType:(0,g.useSelect)((e=>{const{getDeviceType:t=()=>"Desktop"}=e("core/editor");return t()}),[]),setDeviceType:e}}const B={Desktop:"all",Tablet:"mediumSmallWidth",Mobile:"smallWidth"};function D(e=[]){const{deviceType:t}=q();return(0,l.useMemo)((()=>{var n;if(!t||"Desktop"===t)return"";const r=B[t];return null!==(n=e.find((e=>e.id===r))?.value)&&void 0!==n?n:""}),[t])}function T(e,...t){if(!t.length)return e;const n=t.shift();if(M(e)&&M(n))for(const t in n)M(n[t])?(e[t]||Object.assign(e,{[t]:{}}),T(e[t],n[t])):Object.assign(e,{[t]:n[t]});return T(e,...t)}function M(e){return e&&"object"==typeof e&&!Array.isArray(e)}function C(e,{cleanStylesObject:t}){const{setAttributes:n,clientId:r}=e,o=(0,g.useSelect)((e=>e("core/block-editor")?.getMultiSelectedBlocks()||[]),[]),s=(0,g.useSelect)((e=>e("core/block-editor")?.getBlock),[]),{updateBlockAttributes:c}=(0,g.useDispatch)("core/block-editor");return function(e){if(Array.isArray(o)&&o.length>0){const n=o.map((n=>{const r=s(n?.clientId)?.attributes?.styles,o=t(T({...r},e));return{clientId:n.clientId,blockAttrs:{styles:o}}})),r=n.map((e=>e.clientId)),l=n.reduce(((e,t)=>(e[t.clientId]=t.blockAttrs,e)),{});return void c(r,l,!0)}const l=s(r)?.attributes?.styles,i=t(T({...l},e));n({styles:i})}}const P=window.wp.blockEditor,R=window.wp.htmlEntities,N=new Set(["amp;","lt;","gt;","quot;","apos;","#39;"]);function F(e){return"object"!=typeof e||null===e?e:Object.entries(e).reduce(((e,[t,n])=>(e[(0,R.decodeEntities)(t)]="object"==typeof n?F(n):n,e)),{})}var W=n(17),z=n.n(W);function G({styles:e,setAttributes:t}){const{__unstableMarkNextChangeAsNotPersistent:n}=(0,g.useDispatch)(P.store);(0,l.useEffect)((()=>{if(e&&"object"==typeof e&&function(e){if("object"!=typeof e||null===e)return!1;const t=[e];for(;t.length>0;){const e=t.pop();for(const n in e){for(const e of N)if(n.includes(e))return!0;"object"==typeof e[n]&&null!==e[n]&&t.push(e[n])}}return!1}(e)){const r=F(e);z()(r,e)||("function"==typeof n&&n(),t({styles:r}))}}),[e,t])}})(),r})(),e.exports=t()}},t={},n=function n(r){var o=t[r];if(void 0!==o)return o.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}(5758);(window.gbp=window.gbp||{}).blockStyles=n})();