# Translation of GenerateBlocks Pro in Czech
# This file is distributed under the same license as the GenerateBlocks Pro package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-06-10 02:59:28+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n >= 2 && n <= 4) ? 1 : 2);\n"
"X-Generator: GlotPress/3.0.0\n"
"Language: cs_CZ\n"
"Project-Id-Version: GenerateBlocks Pro\n"

#. Plugin Name of the plugin
#. translators: GenerateBlocks Pro
#: includes/class-rest.php:440
msgid "GenerateBlocks Pro"
msgstr "GenerateBlocks Pro"

#. Plugin URI of the plugin
msgid "https://generateblocks.com"
msgstr "https://generateblocks.com"

#. Description of the plugin
msgid "GenerateBlocks Pro adds more great features to GenerateBlocks without sacrificing usability or performance."
msgstr "GenerateBlocks Pro rozšiřuje GenerateBlocks o dalš<PERSON> skvě<PERSON> funk<PERSON>, aniž by tím utrpěla použitelnost nebo výkon."

#. Author of the plugin
msgid "Tom Usborne"
msgstr "Tom Usborne"

#. Author URI of the plugin
msgid "https://tomusborne.com"
msgstr "https://tomusborne.com"

#: includes/class-asset-library.php:57 includes/class-asset-library.php:58
#: includes/class-asset-library.php:75
msgid "Asset Library"
msgstr "Knihovna assatů"

#: includes/class-global-styles.php:57
msgctxt "Post Type General Name"
msgid "Global Styles"
msgstr "Globální styly"

#: includes/class-global-styles.php:58
msgctxt "Post Type Singular Name"
msgid "Global Style"
msgstr "Globální styl"

#: includes/class-global-styles.php:59 includes/class-global-styles.php:61
#: includes/class-global-styles.php:111
msgid "Global Styles"
msgstr "Globální styly"

#: includes/class-global-styles.php:60
msgid "Parent Global Style"
msgstr "Nadřazený globální styl"

#: includes/class-global-styles.php:62
msgid "View Global Style"
msgstr "Zobrazit globální styl"

#: includes/class-global-styles.php:63
msgid "Add New Global Style"
msgstr "Přidat nový globální styl"

#: includes/class-global-styles.php:64 includes/class-local-templates.php:70
msgid "Add New"
msgstr "Přidat nový"

#: includes/class-global-styles.php:65
msgid "Edit Global Style"
msgstr "Upravit globální styl"

#: includes/class-global-styles.php:66
msgid "Update Global Style"
msgstr "Aktualizovat globální styl"

#: includes/class-global-styles.php:67
msgid "Search Global Style"
msgstr "Vyhledat globální styl"

#: includes/class-global-styles.php:68 includes/class-local-templates.php:74
msgid "Not Found"
msgstr "Nenalezeno"

#: includes/class-global-styles.php:69 includes/class-local-templates.php:75
msgid "Not found in Trash"
msgstr "V koši nebylo nic nalezeno"

#: includes/class-local-templates.php:63
msgctxt "Post Type General Name"
msgid "Local Patterns"
msgstr "Lokální vzory"

#: includes/class-local-templates.php:64
msgctxt "Post Type Singular Name"
msgid "Local Pattern"
msgstr "Lokální vzor"

#: includes/class-local-templates.php:65 includes/class-local-templates.php:67
#: includes/class-local-templates.php:117 dist/blocks.js:17
msgid "Local Patterns"
msgstr "Lokální vzory"

#: includes/class-local-templates.php:66
msgid "Parent Local Pattern"
msgstr "Nadřazený lokální vzor"

#: includes/class-local-templates.php:68
msgid "View Local Pattern"
msgstr "Zobrazit lokální vzor"

#: includes/class-local-templates.php:69
msgid "Add New Local Pattern"
msgstr "Přidat nový lokální vzor"

#: includes/class-local-templates.php:71
msgid "Edit Local Pattern"
msgstr "Upravit lokální vzor"

#: includes/class-local-templates.php:72
msgid "Update Local Pattern"
msgstr "Aktualizovat lokální vzor"

#: includes/class-local-templates.php:73
msgid "Search Local Pattern"
msgstr "Vyhledat lokální vzor"

#: includes/class-rest.php:256
msgid "Templates not found."
msgstr "Šablony nenalezeny."

#: includes/class-rest.php:322
msgid "Template data not found."
msgstr "Data šablony nebyla nalezena."

#: includes/class-rest.php:343 includes/class-rest.php:468
msgid "Settings saved."
msgstr "Nastavení uloženo."

#: includes/class-rest.php:409 includes/class-rest.php:448
msgid "An error occurred, please try again."
msgstr "Došlo k chybě, zkuste to prosím znovu."

#. translators: License key expiration date.
#: includes/class-rest.php:419
msgid "Your license key expired on %s."
msgstr "Platnost vašeho licenčního klíče vypršela dne %s."

#: includes/class-rest.php:426
msgid "Your license key has been disabled."
msgstr "Váš licenční klíč byl zablokován."

#: includes/class-rest.php:430
msgid "Invalid license."
msgstr "Neplatná licence."

#: includes/class-rest.php:435
msgid "Your license is not active for this URL."
msgstr "Vaše licence není pro tuto adresu URL aktivní."

#. translators: GenerateBlocks Pro
#: includes/class-rest.php:440
msgid "This appears to be an invalid license key for %s."
msgstr "Zdá se, že se jedná o neplatný licenční klíč pro %s."

#: includes/class-rest.php:444
msgid "Your license key has reached its activation limit."
msgstr "Váš licenční klíč dosáhl svého aktivačního limitu."

#: includes/class-rest.php:538
msgid "Shapes saved."
msgstr "Tvary uloženy."

#: includes/class-rest.php:600
msgid "Icons saved."
msgstr "Ikony uloženy."

#: plugin.php:74
msgid "GenerateBlocks Pro is not working because you need to activate the GenerateBlocks plugin."
msgstr "GenerateBlocks Pro nefunguje, protože je třeba aktivovat plugin GenerateBlocks."

#: plugin.php:75
msgid "Activate GenerateBlocks Now"
msgstr "Aktivovat GenerateBlocks"

#: plugin.php:83
msgid "GenerateBlocks Pro is not working because you need to install the GenerateBlocks plugin."
msgstr "GenerateBlocks Pro nefunguje, protože je třeba nainstalovat plugin GenerateBlocks."

#: plugin.php:84
msgid "Install GenerateBlocks Now"
msgstr "Nainstalovat GenerateBlocks"

#: plugin.php:103
msgid "GenerateBlocks Pro is not working because you are using an old version of GenerateBlocks."
msgstr "GenerateBlocks Pro nefunguje, protože používáte zastaralou verzi GenerateBlocks."

#: plugin.php:104
msgid "Update GenerateBlocks Now"
msgstr "Aktualizovat GenerateBlocks"

#: dist/asset-library.js:1
msgid "Shapes are dynamic elements that will update automatically on your website if altered/removed here."
msgstr "Tvary jsou dynamické prvky, které se na vašich webových stránkách automaticky aktualizují, pokud je zde změníte nebo odstraníte."

#: dist/asset-library.js:1
msgid "Group"
msgstr "Skupina"

#: dist/asset-library.js:1
msgid "Group Name"
msgstr "Jméno skupiny"

#: dist/asset-library.js:1
msgid "This will permanently delete all shapes in this group and remove them from the front-end of your website."
msgstr "Tím se trvale odstraní všechny tvary v této skupině a odstraní se z webu."

#: dist/asset-library.js:1
msgid "Delete Group"
msgstr "Odstranit skupinu"

#: dist/asset-library.js:1
msgid "Export Group"
msgstr "Exportovat skupinu"

#: dist/asset-library.js:1
msgid "Name"
msgstr "Jméno"

#: dist/asset-library.js:1
msgid "Upload an SVG file"
msgstr "Nahrát soubor SVG"

#: dist/asset-library.js:1
msgid "Upload SVG"
msgstr "Nahrát SVG"

#: dist/asset-library.js:1
msgid "Insert SVG"
msgstr "Vložit SVG"

#: dist/asset-library.js:1 dist/blocks.js:15
msgid "Browse"
msgstr "Procházet"

#: dist/asset-library.js:1
msgid "SVG HTML"
msgstr "SVG HTML"

#: dist/asset-library.js:1
msgid "Delete Shape"
msgstr "Odstranit tvar"

#: dist/asset-library.js:1
msgid "This will permanently delete this shape and remove it from the front-end of your website."
msgstr "Tím se tento tvar trvale odstraní a bude smazán z webové stránky."

#: dist/asset-library.js:1
msgid "Add Shape"
msgstr "Přidat tvar"

#: dist/asset-library.js:1
msgid "Add Group"
msgstr "Přidat skupinu"

#: dist/asset-library.js:1
msgid "Import Group"
msgstr "Importovat skupinu"

#: dist/asset-library.js:1
msgid "Group imported."
msgstr "Skupina importována."

#: dist/asset-library.js:1
msgid "File not valid."
msgstr "Soubor je neplatný."

#: dist/asset-library.js:1
msgid "Wrong asset type."
msgstr "Chybný typ assetu."

#: dist/asset-library.js:1
msgid "Icons are static elements that are added to your content. Changes here will not affect your existing icons."
msgstr "Ikony jsou statické prvky, které se přidávají do obsahu. Změny zde nebudou mít vliv na vaše stávající ikony."

#: dist/asset-library.js:1
msgid "This will permanently delete all icons in this group."
msgstr "Tím se trvale odstraní všechny ikony v této skupině."

#: dist/asset-library.js:1
msgid "Delete Icon"
msgstr "Odstranit ikonu"

#: dist/asset-library.js:1
msgid "This will permanently delete this icon."
msgstr "Tím tuto ikonu trvale odstraníte."

#: dist/asset-library.js:1
msgid "Add Icon"
msgstr "Přidat ikonu"

#: dist/blocks.js:1
msgid "Custom Attributes"
msgstr "Vlastní atributy"

#: dist/blocks.js:1
msgid "Delete attribute"
msgstr "Odstranit atribut"

#: dist/blocks.js:1
msgid "Add Attribute"
msgstr "Přidat atribut"

#: dist/blocks.js:1
msgid "Allowed attributes"
msgstr "Povolené atributy"

#: dist/blocks.js:1
msgid "The following HTML attributes are allowed to be used in the Custom Attributes feature:"
msgstr "Pro vlastní atributy je povoleno používat následující atributy HTML:"

#: dist/blocks.js:1 dist/blocks.js:5 dist/blocks.js:7 dist/blocks.js:9
#: dist/blocks.js:11 dist/blocks.js:13 dist/blocks.js:15 dist/blocks.js:17
msgid "Close"
msgstr "Zavřít"

#: dist/blocks.js:1
msgid "Select Units"
msgstr "Vybrat jednotky"

#: dist/blocks.js:1
msgctxt "A size unit for CSS markup"
msgid "Pixel"
msgstr "Pixel"

#: dist/blocks.js:1
msgctxt "A size unit for CSS markup"
msgid "Em"
msgstr "Em"

#: dist/blocks.js:1
msgctxt "A size unit for CSS markup"
msgid "Percentage"
msgstr "Procenta"

#: dist/blocks.js:1
msgctxt "A size unit for CSS markup"
msgid "Degree"
msgstr "Stupně"

#: dist/blocks.js:2 dist/blocks.js:3
msgid "%s Units"
msgstr "%s Jednotky"

#: dist/blocks.js:3
msgid "Delete Effect"
msgstr "Odstranit efekt"

#: dist/blocks.js:3
msgid "This will permanently delete this transform."
msgstr "Tímto tuto transformaci trvale odstraníte."

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Type"
msgstr "Typ"

#: dist/blocks.js:3
msgid "Choose.."
msgstr "Vybrat.."

#: dist/blocks.js:3
msgid "Translate"
msgstr "Posunutí"

#: dist/blocks.js:3
msgid "Rotate"
msgstr "Rotace"

#: dist/blocks.js:3
msgid "Skew"
msgstr "Zkosení"

#: dist/blocks.js:3
msgid "Scale"
msgstr "Měřítko"

#: dist/blocks.js:3
msgid "Blur"
msgstr "Rozmazání"

#: dist/blocks.js:3
msgid "Brightness"
msgstr "Jas"

#: dist/blocks.js:3
msgid "Contrast"
msgstr "Kontrast"

#: dist/blocks.js:3
msgid "Grayscale"
msgstr "Stupně šedi"

#: dist/blocks.js:3
msgid "Hue-Rotate"
msgstr "Otáčení odstínu"

#: dist/blocks.js:3
msgid "Invert"
msgstr "Invertování"

#: dist/blocks.js:3
msgid "Saturate"
msgstr "Saturace"

#: dist/blocks.js:3
msgid "Sepia"
msgstr "Sépie"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Device"
msgstr "Zařízení"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "All"
msgstr "Vše"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Desktop"
msgstr "Desktop"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Tablet"
msgstr "Tablet"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Tablet + Mobile"
msgstr "Tablet + Mobil"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Mobile"
msgstr "Mobil"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "State"
msgstr "Stav"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Normal"
msgstr "Normální"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Hover"
msgstr "Najetí myši"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Target"
msgstr "Cíl"

#: dist/blocks.js:3
msgid "Your background image must be set to Pseudo Element for effects to work."
msgstr "Aby efekty fungovaly, musí být obrázek na pozadí nastaven na hodnotu Pseudo Element."

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Custom Selector"
msgstr "Vlastní selektor"

#: dist/blocks.js:3 dist/blocks.js:5
msgid "Opacity"
msgstr "Viditelnost"

#: dist/blocks.js:3
msgid "Mix Blend Mode"
msgstr "Režim prolnutí Mix"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Choose…"
msgstr "Vybrat…"

#: dist/blocks.js:3
msgid "Multiply"
msgstr "Násobit"

#: dist/blocks.js:3
msgid "Screen"
msgstr "Závoj"

#: dist/blocks.js:3
msgid "Overlay"
msgstr "Překrýt"

#: dist/blocks.js:3
msgid "Darken"
msgstr "Ztmavit"

#: dist/blocks.js:3
msgid "Lighten"
msgstr "Zesvětlit"

#: dist/blocks.js:3
msgid "Color Dodge"
msgstr "Zesvětlit barvy"

#: dist/blocks.js:3
msgid "Color Burn"
msgstr "Ztmavit barvy"

#: dist/blocks.js:3
msgid "Hard Light"
msgstr "Tvrdé světlo"

#: dist/blocks.js:3
msgid "Soft Light"
msgstr "Měkké světlo"

#: dist/blocks.js:3
msgid "Difference"
msgstr "Rozdíl"

#: dist/blocks.js:3
msgid "Exclusion"
msgstr "Vyloučit"

#: dist/blocks.js:3
msgid "Hue"
msgstr "Odstín"

#: dist/blocks.js:3
msgid "Saturation"
msgstr "Sytost"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Color"
msgstr "Barva"

#: dist/blocks.js:3
msgid "Luminosity"
msgstr "Světlost"

#: dist/blocks.js:3
msgid "Timing Function"
msgstr "Funkce časování"

#: dist/blocks.js:3
msgid "CSS Property"
msgstr "CSS hodnota"

#: dist/blocks.js:3
msgid "Transition Duration"
msgstr "Trvání přechodu"

#: dist/blocks.js:3
msgid "Delay"
msgstr "Prodleva"

#: dist/blocks.js:3
msgid "Inset"
msgstr "Inset"

#: dist/blocks.js:3
msgid "Horizontal Offset"
msgstr "Horizontální posun"

#: dist/blocks.js:3
msgid "Vertical Offset"
msgstr "Vertikální posun"

#: dist/blocks.js:3
msgid "Spread"
msgstr "Rozptyl"

#: dist/blocks.js:3
msgid "Translate X"
msgstr "Posunutí X"

#: dist/blocks.js:3
msgid "Translate Y"
msgstr "Posunutí Y"

#: dist/blocks.js:3
msgid "Skew X"
msgstr "Zkosení X"

#: dist/blocks.js:3
msgid "Skew Y"
msgstr "Zkosení Y"

#: dist/blocks.js:3
msgid "Automatically add a smooth transition to this effect."
msgstr "Automaticky přidat plynulý přechod k tomuto efektu."

#: dist/blocks.js:3
msgid "Add Transition"
msgstr "Přidat přechod"

#: dist/blocks.js:3 dist/blocks.js:17
msgid "Self"
msgstr "sebe"

#: dist/blocks.js:3
msgid "Inner Container"
msgstr "Vnitřní kontejner"

#: dist/blocks.js:3
msgid "Background Image"
msgstr "Obrázek na pozadí"

#: dist/blocks.js:3
msgid "Icon"
msgstr "Ikona"

#: dist/blocks.js:3
msgid "Effects"
msgstr "Efekty"

#. translators: Number of transforms.
#: dist/blocks.js:5
msgid "Opacity & Blend (%s)"
msgstr "Viditelnost a prolínání (%s)"

#: dist/blocks.js:5
msgid "Opacity & Blend"
msgstr "Viditelnost a prolínání"

#: dist/blocks.js:5 dist/blocks.js:7 dist/blocks.js:9 dist/blocks.js:11
msgid "Add Effect"
msgstr "Přidat efekt"

#: dist/blocks.js:5 dist/blocks.js:13
msgid "Disable in editor"
msgstr "Zakázat v editoru"

#: dist/blocks.js:5
msgid "Disable these effects in the editor when this block is selected."
msgstr "Zakázat tyto efekty v editoru, když je tento blok vybrán."

#. translators: Number of transforms.
#: dist/blocks.js:7
msgid "Transition (%s)"
msgstr "Přechod (%s)"

#: dist/blocks.js:7
msgid "Transition"
msgstr "Přechod"

#. translators: Number of transforms.
#: dist/blocks.js:9
msgid "Box Shadow (%s)"
msgstr "Stín boxu (%s)"

#: dist/blocks.js:9
msgid "Box Shadow"
msgstr "Stín boxu"

#. translators: Number of transforms.
#: dist/blocks.js:11
msgid "Text Shadow (%s)"
msgstr "Stín textu (%s)"

#: dist/blocks.js:11
msgid "Text Shadow"
msgstr "Stín textu"

#. translators: Number of transforms.
#: dist/blocks.js:13
msgid "Transform (%s)"
msgstr "Transformace (%s)"

#: dist/blocks.js:13
msgid "Transform"
msgstr "Transformace"

#: dist/blocks.js:13
msgid "Add Transform"
msgstr "Přidat transformaci"

#: dist/blocks.js:13
msgid "Disable transforms in the editor when this block is selected."
msgstr "Zakázat transformace v editoru, když je tento blok vybrán."

#. translators: Number of transforms.
#: dist/blocks.js:15
msgid "Filter (%s)"
msgstr "Filtry (%s)"

#: dist/blocks.js:15
msgid "Filter"
msgstr "Filtry"

#: dist/blocks.js:15
msgid "Add Filter"
msgstr "Přidat filtr"

#: dist/blocks.js:15
msgid "Hide on desktop"
msgstr "Skrýt na desktopu"

#: dist/blocks.js:15
msgid "Hide on tablet"
msgstr "Skrýt na tabletu"

#: dist/blocks.js:15
msgid "Hide on mobile"
msgstr "Skrýt na mobilu"

#: dist/blocks.js:15
msgid "This block is hidden on this device."
msgstr "Tento blok je na tomto zařízení skrytý."

#: dist/blocks.js:15
msgid "Styles"
msgstr "Styly"

#: dist/blocks.js:15
msgid "Copy Styles"
msgstr "Kopírovat styly"

#: dist/blocks.js:15
msgid "Paste Styles"
msgstr "Vložit styly"

#: dist/blocks.js:15
msgid "Clear Styles"
msgstr "Vyčistit styly"

#: dist/blocks.js:15
msgid "This will remove all styling from these blocks."
msgstr "Tím se z těchto bloků odstraní všechny styly."

#: dist/blocks.js:15
msgid "This will remove all styling from this block."
msgstr "Tím se z tohoto bloku odstraní všechny styly."

#: dist/blocks.js:15
msgid "Global Style"
msgstr "Globální styl"

#: dist/blocks.js:15
msgid "Name your global style something short and unique to this type of block."
msgstr "Pojmenujte svůj globální styl krátce a jedinečně pro tento typ bloku."

#: dist/blocks.js:15
msgid "Change Global Style ID"
msgstr "Změnit ID globálního stylu"

#: dist/blocks.js:15
msgid "Changing this ID will remove the styling from existing blocks using this Global Style."
msgstr "Změna tohoto ID odstraní stylování z existujících bloků používajících tento globální styl."

#: dist/blocks.js:15
msgid "Label"
msgstr "Štítek"

#: dist/blocks.js:15
msgid "The label shown when choosing a Global Style in the editor."
msgstr "Štítek zobrazený při výběru globálního stylu v editoru."

#: dist/blocks.js:15
msgid "Use Global Style"
msgstr "Použít globální styl"

#: dist/blocks.js:15
msgid "This will remove all local styling from this block."
msgstr "Tento krok odstraní z tohoto bloku všechny lokální styly."

#: dist/blocks.js:15
msgid "Clear local styles"
msgstr "Vyčistit lokální styly"

#: dist/blocks.js:15
msgid "Colors"
msgstr "Barvy"

#: dist/blocks.js:15
msgid "Background Color"
msgstr "Barva pozadí"

#: dist/blocks.js:15
msgid "Text Color"
msgstr "Barva textu"

#: dist/blocks.js:15
msgid "Link Color"
msgstr "Barva odkazu"

#: dist/blocks.js:15
msgid "Border Color"
msgstr "Barva ohraničení"

#: dist/blocks.js:15
msgid "This makes your Element Tag a link element. It uses valid HTML5 coding but will break if you add interative elements (links or buttons) inside the container."
msgstr "Tímto se ze značky Element Tag stane element odkazu. Používá platné kódování HTML5, ale pokud do kontejneru přidáte interaktivní prvky (odkazy nebo tlačítka), dojde k jeho porušení."

#: dist/blocks.js:15
msgid "This adds a hidden link inside your container and tells it to cover the entire element. It is less prone to breakage, but is not as clean as the wrapper method."
msgstr "Tím přidáte skrytý odkaz do kontejneru a určíte mu, že má pokrýt celý prvek. Je méně náchylný k poruše, ale není tak čistý jako metoda wrapper."

#: dist/blocks.js:15
msgid "Change Container Link"
msgstr "Změnit odkaz kontejneru"

#: dist/blocks.js:15
msgid "Set Container Link"
msgstr "Nastavit odkaz kontejneru"

#: dist/blocks.js:15
msgid "This container is using a dynamic link."
msgstr "Tento kontejner používá dynamický odkaz."

#: dist/blocks.js:15
msgid "Open link in a new tab"
msgstr "Otevřít odkaz na nové kartě"

#: dist/blocks.js:15
msgid "Add rel=\"nofollow\""
msgstr "Přidat rel=\"nofollow\""

#: dist/blocks.js:15
msgid "Add rel=\"sponsored\""
msgstr "Přidat rel=\"sponzorováno\""

#: dist/blocks.js:15
msgid "Link Type"
msgstr "Typ odkazu"

#: dist/blocks.js:15
msgid "Hidden Link"
msgstr "Skrytý odkaz"

#: dist/blocks.js:15
msgid "Wrapper"
msgstr "Wrapper"

#: dist/blocks.js:15
msgid "Aria Label"
msgstr "Aria štítek"

#: dist/blocks.js:15
msgid "Help screen readers understand what this link does."
msgstr "Pomozte čtečkám obrazovky pochopit, co tento odkaz dělá."

#: dist/blocks.js:15
msgid "Image"
msgstr "Obrázek"

#: dist/blocks.js:15
msgid "Gradient"
msgstr "Barevný přechod"

#: dist/blocks.js:15
msgid "Delete Background"
msgstr "Odstranit pozadí"

#: dist/blocks.js:15
msgid "This will permanently delete this background."
msgstr "Tím se toto pozadí trvale odstraní."

#: dist/blocks.js:15
msgid "Direction"
msgstr "Směr"

#: dist/blocks.js:15
msgid "Stop One"
msgstr "Zastavit první"

#: dist/blocks.js:15
msgid "Stop Two"
msgstr "Zastavit druhou"

#: dist/blocks.js:15
msgid "Image URL"
msgstr "URL obrázku"

#: dist/blocks.js:15
msgid "Background image preview"
msgstr "Náhled obrázku na pozadí"

#: dist/blocks.js:15
msgid "Set background image"
msgstr "Nastavení obrázku na pozadí"

#: dist/blocks.js:15
msgid "Open the Media Library"
msgstr "Otevřít knihovnu médií"

#: dist/blocks.js:15
msgid "Image Size"
msgstr "Rozměry obrázku"

#: dist/blocks.js:15
msgid "Size"
msgstr "Rozměr"

#: dist/blocks.js:15
msgid "Position"
msgstr "Pozice"

#: dist/blocks.js:15
msgid "Repeat"
msgstr "Opakovat"

#: dist/blocks.js:15
msgid "Attachment"
msgstr "Příloha"

#: dist/blocks.js:15
msgid "Current post terms"
msgstr "Podmínky aktuálního příspěvku"

#: dist/blocks.js:15
msgid "Current post"
msgstr "Aktuální příspěvek"

#: dist/blocks.js:15
msgid "Current post author"
msgstr "Autor aktuálního příspěvku"

#. translators: Number of gradients.
#: dist/blocks.js:17
msgid "Advanced (%s)"
msgstr "Pokročilé (%s)"

#: dist/blocks.js:17
msgid "Advanced"
msgstr "Pokročilé"

#: dist/blocks.js:17
msgid "Pseudo Element"
msgstr "Pseudo Element"

#: dist/blocks.js:17
msgid "Add Background"
msgstr "Přidat pozadí"

#: dist/blocks.js:17
msgid "Random"
msgstr "Náhodně"

#: dist/blocks.js:17
msgid "Exclude current post"
msgstr "Vyloučit aktuální příspěvek"

#: dist/blocks.js:17
msgid "Post meta sub field"
msgstr "Podpole Post meta"

#: dist/blocks.js:17
msgid "Select value property"
msgstr "Vybrat vlastnost hodnoty"

#: dist/blocks.js:17
msgid "Select Category"
msgstr "Vybrat kategorii"

#: dist/blocks.js:17
msgid "Blocks"
msgstr "Bloky"

#: dist/blocks.js:17
msgid "Patterns"
msgstr "Vzory"

#: dist/blocks.js:17
msgid "No patterns found."
msgstr "Nebyly nalezeny žádné vzory"

#: dist/blocks.js:17 dist/blocks.js:19
msgid "Add Template"
msgstr "Přidat šablonu"

#. translators: Number of templates.
#: dist/blocks.js:19
msgid "Patterns: %s"
msgstr "Vzory: %s"

#: dist/blocks.js:19
msgid "Pattern Library"
msgstr "Knihovna vzorů"

#: dist/blocks.js:19
msgid "Insert pre-built patterns directly into your content."
msgstr "Vkládejte předpřipravené vzory přímo do obsahu."

#: dist/blocks.js:19
msgid "Open Pattern Library"
msgstr "Otevřít knihovnu vzorů"

#: dist/dashboard.js:1
msgid "License key activated."
msgstr "Licenční klíč je aktivován."

#: dist/dashboard.js:1
msgid "License key deactivated."
msgstr "Licenční klíč je deaktivován."

#: dist/dashboard.js:1
msgid "Receiving updates"
msgstr "Přijímání aktualizací"

#: dist/dashboard.js:1
msgid "Not receiving updates"
msgstr "Nepřijímá aktualizace"

#: dist/dashboard.js:1
msgid "Enter your license key here…"
msgstr "Zde zadejte licenční klíč..."

#: dist/dashboard.js:1
msgid "Get alpha and beta updates directly to your Dashboard."
msgstr "Získávejte aktualizace alfa a beta verzí přímo na nástěnku."

#: dist/dashboard.js:1
msgid "Enable Local Patterns"
msgstr "Povolit lokální vzory"

#: dist/dashboard.js:1
msgid "Enable Remote Patterns"
msgstr "Povolit vzdálené vzory"

#: dist/dashboard.js:1
msgid "The pattern library syncs once a day by default. Clicking this button will force it to re-sync."
msgstr "Knihovna vzorů se ve výchozím nastavení synchronizuje jednou denně. Kliknutím na toto tlačítko vynutíte její opětovnou synchronizaci."

#: dist/dashboard.js:1
msgid "Remote patterns synced."
msgstr "Vzdálené vzory synchronizovány."

#: dist/dashboard.js:1
msgid "Sync Remote Patterns"
msgstr "Synchronizovat vzdálené vzory"

#: dist/dashboard.js:1
msgid "Save"
msgstr "Uložit"