# Translation of GenerateBlocks Pro in Spanish (Spain)
# This file is distributed under the same license as the GenerateBlocks Pro package.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-10-03 06:31:24+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/3.0.0\n"
"Language: es\n"
"Project-Id-Version: GenerateBlocks Pro\n"

#. Plugin Name of the plugin
#. translators: GenerateBlocks Pro
#: includes/class-rest.php:440
msgid "GenerateBlocks Pro"
msgstr "GenerateBlocks Pro"

#. Plugin URI of the plugin
msgid "https://generateblocks.com"
msgstr "https://generateblocks.com"

#. Description of the plugin
msgid "GenerateBlocks Pro adds more great features to GenerateBlocks without sacrificing usability or performance."
msgstr "GenerateBlocks Pro añade más funciones geniales a GenerateBlocks sin sacrificar la usabilidad o el rendimiento."

#. Author of the plugin
msgid "Tom Usborne"
msgstr "Tom Usborne"

#. Author URI of the plugin
msgid "https://tomusborne.com"
msgstr "https://tomusborne.com"

#: includes/class-asset-library.php:57 includes/class-asset-library.php:58
#: includes/class-asset-library.php:75
msgid "Asset Library"
msgstr "Biblioteca de recursos"

#: includes/class-global-styles.php:57
msgctxt "Post Type General Name"
msgid "Global Styles"
msgstr "Estilos globales"

#: includes/class-global-styles.php:58
msgctxt "Post Type Singular Name"
msgid "Global Style"
msgstr "Estilo Global"

#: includes/class-global-styles.php:59 includes/class-global-styles.php:61
#: includes/class-global-styles.php:111
msgid "Global Styles"
msgstr "Estilos globales"

#: includes/class-global-styles.php:60
msgid "Parent Global Style"
msgstr "Estilo global de los superiores"

#: includes/class-global-styles.php:62
msgid "View Global Style"
msgstr "Ver el estilo global"

#: includes/class-global-styles.php:63
msgid "Add New Global Style"
msgstr "Añadir nuevo estilo global"

#: includes/class-global-styles.php:64 includes/class-local-templates.php:70
msgid "Add New"
msgstr "Añadir nuevo"

#: includes/class-global-styles.php:65
msgid "Edit Global Style"
msgstr "Editar el estilo global"

#: includes/class-global-styles.php:66
msgid "Update Global Style"
msgstr "Actualizar el estilo global"

#: includes/class-global-styles.php:67
msgid "Search Global Style"
msgstr "Buscar estilo global"

#: includes/class-global-styles.php:68 includes/class-local-templates.php:74
msgid "Not Found"
msgstr "No encontrado"

#: includes/class-global-styles.php:69 includes/class-local-templates.php:75
msgid "Not found in Trash"
msgstr "No se encontró en la papelera"

#: includes/class-local-templates.php:63
msgctxt "Post Type General Name"
msgid "Local Patterns"
msgstr "Patrones locales"

#: includes/class-local-templates.php:64
msgctxt "Post Type Singular Name"
msgid "Local Pattern"
msgstr "Patrón local"

#: includes/class-local-templates.php:65 includes/class-local-templates.php:67
#: includes/class-local-templates.php:117 dist/blocks.js:17
msgid "Local Patterns"
msgstr "Patrones locales"

#: includes/class-local-templates.php:66
msgid "Parent Local Pattern"
msgstr "Patrón local de los superiores"

#: includes/class-local-templates.php:68
msgid "View Local Pattern"
msgstr "Ver patrón local"

#: includes/class-local-templates.php:69
msgid "Add New Local Pattern"
msgstr "Añadir nuevo patrón local"

#: includes/class-local-templates.php:71
msgid "Edit Local Pattern"
msgstr "Editar patrón local"

#: includes/class-local-templates.php:72
msgid "Update Local Pattern"
msgstr "Actualizar el patrón local"

#: includes/class-local-templates.php:73
msgid "Search Local Pattern"
msgstr "Buscar el patrón local"

#: includes/class-rest.php:256
msgid "Templates not found."
msgstr "Plantillas no encontradas."

#: includes/class-rest.php:322
msgid "Template data not found."
msgstr "Datos de la plantilla no encontrados."

#: includes/class-rest.php:343 includes/class-rest.php:468
msgid "Settings saved."
msgstr "Ajustes guardados."

#: includes/class-rest.php:409 includes/class-rest.php:448
msgid "An error occurred, please try again."
msgstr "Ha ocurrido un error. Por favor intentalo nuevamente."

#. translators: License key expiration date.
#: includes/class-rest.php:419
msgid "Your license key expired on %s."
msgstr "Tu licencia caducó el %s."

#: includes/class-rest.php:426
msgid "Your license key has been disabled."
msgstr "Tu licencia ha sido deshabilitada."

#: includes/class-rest.php:430
msgid "Invalid license."
msgstr "Licencia no válida."

#: includes/class-rest.php:435
msgid "Your license is not active for this URL."
msgstr "Tu licencia no está activa para esta URL."

#. translators: GenerateBlocks Pro
#: includes/class-rest.php:440
msgid "This appears to be an invalid license key for %s."
msgstr "Esto parece ser una clave de licencia no válida para %s."

#: includes/class-rest.php:444
msgid "Your license key has reached its activation limit."
msgstr "Tu clave de licencia ha alcanzado el límite de activaciones."

#: includes/class-rest.php:538
msgid "Shapes saved."
msgstr "Formas guardadas."

#: includes/class-rest.php:600
msgid "Icons saved."
msgstr "Iconos guardados."

#: plugin.php:74
msgid "GenerateBlocks Pro is not working because you need to activate the GenerateBlocks plugin."
msgstr "GenerateBlocks Pro no funciona porque es necesario activar el plugin GenerateBlocks."

#: plugin.php:75
msgid "Activate GenerateBlocks Now"
msgstr "Activar GenerateBlocks ahora"

#: plugin.php:83
msgid "GenerateBlocks Pro is not working because you need to install the GenerateBlocks plugin."
msgstr "GenerateBlocks Pro no funciona porque necesita instalar el plugin GenerateBlocks."

#: plugin.php:84
msgid "Install GenerateBlocks Now"
msgstr "Instalar GenerateBlocks ahora"

#: plugin.php:103
msgid "GenerateBlocks Pro is not working because you are using an old version of GenerateBlocks."
msgstr "GenerateBlocks Pro no funciona porque está utilizando una versión antigua de GenerateBlocks."

#: plugin.php:104
msgid "Update GenerateBlocks Now"
msgstr "Actualizar ahora GenerateBlocks"

#: dist/asset-library.js:1
msgid "Shapes are dynamic elements that will update automatically on your website if altered/removed here."
msgstr "Las formas son elementos dinámicos que se actualizarán automáticamente en su sitio web si se modifican o eliminan aquí."

#: dist/asset-library.js:1
msgid "Group"
msgstr "Grupo"

#: dist/asset-library.js:1
msgid "Group Name"
msgstr "Nombre del grupo"

#: dist/asset-library.js:1
msgid "This will permanently delete all shapes in this group and remove them from the front-end of your website."
msgstr "Esto eliminará permanentemente todas las formas de este grupo y las eliminará del front-end de su sitio web."

#: dist/asset-library.js:1
msgid "Delete Group"
msgstr "Eliminar grupo"

#: dist/asset-library.js:1
msgid "Export Group"
msgstr "Exportar grupo"

#: dist/asset-library.js:1
msgid "Name"
msgstr "Nombre"

#: dist/asset-library.js:1
msgid "Upload an SVG file"
msgstr "Subir un archivo SVG"

#: dist/asset-library.js:1
msgid "Upload SVG"
msgstr "Subir SVG"

#: dist/asset-library.js:1
msgid "Insert SVG"
msgstr "Insertar SVG"

#: dist/asset-library.js:1 dist/blocks.js:15
msgid "Browse"
msgstr "Navegar"

#: dist/asset-library.js:1
msgid "SVG HTML"
msgstr "SVG HTML"

#: dist/asset-library.js:1
msgid "Delete Shape"
msgstr "Eliminar la forma"

#: dist/asset-library.js:1
msgid "This will permanently delete this shape and remove it from the front-end of your website."
msgstr "Esto borrará permanentemente esta forma y la eliminará del front-end de su sitio web."

#: dist/asset-library.js:1
msgid "Add Shape"
msgstr "Añadir forma"

#: dist/asset-library.js:1
msgid "Add Group"
msgstr "Añadir grupo"

#: dist/asset-library.js:1
msgid "Import Group"
msgstr "Importar grupo"

#: dist/asset-library.js:1
msgid "Group imported."
msgstr "Grupo importado."

#: dist/asset-library.js:1
msgid "File not valid."
msgstr "Archivo no válido."

#: dist/asset-library.js:1
msgid "Wrong asset type."
msgstr "Tipo de activo incorrecto."

#: dist/asset-library.js:1
msgid "Icons are static elements that are added to your content. Changes here will not affect your existing icons."
msgstr "Los iconos son elementos estáticos que se añaden a su contenido. Los cambios aquí no afectarán a sus iconos existentes."

#: dist/asset-library.js:1
msgid "This will permanently delete all icons in this group."
msgstr "Esto eliminará permanentemente todos los iconos de este grupo."

#: dist/asset-library.js:1
msgid "Delete Icon"
msgstr "Eliminar icono"

#: dist/asset-library.js:1
msgid "This will permanently delete this icon."
msgstr "Esto eliminará permanentemente este icono."

#: dist/asset-library.js:1
msgid "Add Icon"
msgstr "Agregar icono"

#: dist/blocks.js:1
msgid "Custom Attributes"
msgstr "Atributos personalizados"

#: dist/blocks.js:1
msgid "Delete attribute"
msgstr "Eliminar atributo"

#: dist/blocks.js:1
msgid "Add Attribute"
msgstr "Añadir atributo"

#: dist/blocks.js:1
msgid "Allowed attributes"
msgstr "Atributos permitidos"

#: dist/blocks.js:1
msgid "The following HTML attributes are allowed to be used in the Custom Attributes feature:"
msgstr "Los siguientes atributos HTML se pueden utilizar en la función de atributos personalizados:"

#: dist/blocks.js:1 dist/blocks.js:5 dist/blocks.js:7 dist/blocks.js:9
#: dist/blocks.js:11 dist/blocks.js:13 dist/blocks.js:15 dist/blocks.js:17
msgid "Close"
msgstr "Cerrar"

#: dist/blocks.js:1
msgid "Select Units"
msgstr "Seleccionar unidades"

#: dist/blocks.js:1
msgctxt "A size unit for CSS markup"
msgid "Pixel"
msgstr "Píxel"

#: dist/blocks.js:1
msgctxt "A size unit for CSS markup"
msgid "Em"
msgstr "Em"

#: dist/blocks.js:1
msgctxt "A size unit for CSS markup"
msgid "Percentage"
msgstr "Porcentaje"

#: dist/blocks.js:1
msgctxt "A size unit for CSS markup"
msgid "Degree"
msgstr "Grado"

#: dist/blocks.js:2 dist/blocks.js:3
msgid "%s Units"
msgstr "Unidades de %s"

#: dist/blocks.js:3
msgid "Delete Effect"
msgstr "Eliminar efecto"

#: dist/blocks.js:3
msgid "This will permanently delete this transform."
msgstr "Esto eliminará permanentemente esta transformación."

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Type"
msgstr "Tipo"

#: dist/blocks.js:3
msgid "Choose.."
msgstr "Selecciona.."

#: dist/blocks.js:3
msgid "Translate"
msgstr "Traducir"

#: dist/blocks.js:3
msgid "Rotate"
msgstr "Girar"

#: dist/blocks.js:3
msgid "Skew"
msgstr "Inclinar"

#: dist/blocks.js:3
msgid "Scale"
msgstr "Escala"

#: dist/blocks.js:3
msgid "Blur"
msgstr "Difuminado"

#: dist/blocks.js:3
msgid "Brightness"
msgstr "Brillo"

#: dist/blocks.js:3
msgid "Contrast"
msgstr "Contraste"

#: dist/blocks.js:3
msgid "Grayscale"
msgstr "Escala de grises"

#: dist/blocks.js:3
msgid "Hue-Rotate"
msgstr "Girar hacia adentro"

#: dist/blocks.js:3
msgid "Invert"
msgstr "Invertir"

#: dist/blocks.js:3
msgid "Saturate"
msgstr "Saturar"

#: dist/blocks.js:3
msgid "Sepia"
msgstr "Sepia"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Device"
msgstr "Dispositivo"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "All"
msgstr "Todos"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Desktop"
msgstr "Escritorio"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Tablet"
msgstr "Tableta"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Tablet + Mobile"
msgstr "Tableta + móvil"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Mobile"
msgstr "Móvil"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "State"
msgstr "Provincia"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Normal"
msgstr "Normal"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Hover"
msgstr "Al pasar el cursor"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Target"
msgstr "Destino"

#: dist/blocks.js:3
msgid "Your background image must be set to Pseudo Element for effects to work."
msgstr "Tu imagen de fondo debe estar configurada como Pseudo Elemento para que los efectos funcionen."

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Custom Selector"
msgstr "Selector personalizado"

#: dist/blocks.js:3 dist/blocks.js:5
msgid "Opacity"
msgstr "Opacidad"

#: dist/blocks.js:3
msgid "Mix Blend Mode"
msgstr "Modo mezclar mezcla"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Choose…"
msgstr "Selecciona…"

#: dist/blocks.js:3
msgid "Multiply"
msgstr "Multiplicar"

#: dist/blocks.js:3
msgid "Screen"
msgstr "Pantalla"

#: dist/blocks.js:3
msgid "Overlay"
msgstr "Superposición"

#: dist/blocks.js:3
msgid "Darken"
msgstr "Oscurecer"

#: dist/blocks.js:3
msgid "Lighten"
msgstr "Aclarar"

#: dist/blocks.js:3
msgid "Color Dodge"
msgstr "Sobreexposición del color"

#: dist/blocks.js:3
msgid "Color Burn"
msgstr "Subexposición del color"

#: dist/blocks.js:3
msgid "Hard Light"
msgstr "Luz intensa"

#: dist/blocks.js:3
msgid "Soft Light"
msgstr "Luz suave"

#: dist/blocks.js:3
msgid "Difference"
msgstr "Diferencia"

#: dist/blocks.js:3
msgid "Exclusion"
msgstr "Exclusión"

#: dist/blocks.js:3
msgid "Hue"
msgstr "Matiz"

#: dist/blocks.js:3
msgid "Saturation"
msgstr "Saturación"

#: dist/blocks.js:3 dist/blocks.js:15
msgid "Color"
msgstr "Color"

#: dist/blocks.js:3
msgid "Luminosity"
msgstr "Luminosidad"

#: dist/blocks.js:3
msgid "Timing Function"
msgstr "Función de temporización"

#: dist/blocks.js:3
msgid "CSS Property"
msgstr "Propiedad CSS"

#: dist/blocks.js:3
msgid "Transition Duration"
msgstr "Duración de la transición"

#: dist/blocks.js:3
msgid "Delay"
msgstr "Retraso"

#: dist/blocks.js:3
msgid "Inset"
msgstr "Recuadro"

#: dist/blocks.js:3
msgid "Horizontal Offset"
msgstr "Desplazamiento horizontal"

#: dist/blocks.js:3
msgid "Vertical Offset"
msgstr "Desplazamiento vertical"

#: dist/blocks.js:3
msgid "Spread"
msgstr "Separado"

#: dist/blocks.js:3
msgid "Translate X"
msgstr "Traducir X"

#: dist/blocks.js:3
msgid "Translate Y"
msgstr "Traducir Y"

#: dist/blocks.js:3
msgid "Skew X"
msgstr "Distorsión X"

#: dist/blocks.js:3
msgid "Skew Y"
msgstr "Distorsión Y"

#: dist/blocks.js:3
msgid "Automatically add a smooth transition to this effect."
msgstr "Añade automáticamente una transición suave a este efecto."

#: dist/blocks.js:3
msgid "Add Transition"
msgstr "Añadir transición"

#: dist/blocks.js:3 dist/blocks.js:17
msgid "Self"
msgstr "El mismo"

#: dist/blocks.js:3
msgid "Inner Container"
msgstr "Contenedor interno"

#: dist/blocks.js:3
msgid "Background Image"
msgstr "Imagen de fondo"

#: dist/blocks.js:3
msgid "Icon"
msgstr "Icono"

#: dist/blocks.js:3
msgid "Effects"
msgstr "Efectos"

#. translators: Number of transforms.
#: dist/blocks.js:5
msgid "Opacity & Blend (%s)"
msgstr "Opacidad y mezcla (%s)"

#: dist/blocks.js:5
msgid "Opacity & Blend"
msgstr "Opacidad y mezcla"

#: dist/blocks.js:5 dist/blocks.js:7 dist/blocks.js:9 dist/blocks.js:11
msgid "Add Effect"
msgstr "Añadir efecto"

#: dist/blocks.js:5 dist/blocks.js:13
msgid "Disable in editor"
msgstr "Desactivar en el editor"

#: dist/blocks.js:5
msgid "Disable these effects in the editor when this block is selected."
msgstr "Desactivar estos efectos en el editor cuando se selecciona este bloque."

#. translators: Number of transforms.
#: dist/blocks.js:7
msgid "Transition (%s)"
msgstr "Transición (%s)"

#: dist/blocks.js:7
msgid "Transition"
msgstr "Transición"

#. translators: Number of transforms.
#: dist/blocks.js:9
msgid "Box Shadow (%s)"
msgstr "Sombra de caja (%s)"

#: dist/blocks.js:9
msgid "Box Shadow"
msgstr "Sombra de la caja"

#. translators: Number of transforms.
#: dist/blocks.js:11
msgid "Text Shadow (%s)"
msgstr "Sombra de texto (%s)"

#: dist/blocks.js:11
msgid "Text Shadow"
msgstr "Sombra de texto"

#. translators: Number of transforms.
#: dist/blocks.js:13
msgid "Transform (%s)"
msgstr "Transformar (%s)"

#: dist/blocks.js:13
msgid "Transform"
msgstr "Transformar"

#: dist/blocks.js:13
msgid "Add Transform"
msgstr "Añadir transformación"

#: dist/blocks.js:13
msgid "Disable transforms in the editor when this block is selected."
msgstr "Desactivar las transformaciones en el editor cuando se selecciona este bloque."

#. translators: Number of transforms.
#: dist/blocks.js:15
msgid "Filter (%s)"
msgstr "Filtro (%s)"

#: dist/blocks.js:15
msgid "Filter"
msgstr "Filtrar"

#: dist/blocks.js:15
msgid "Add Filter"
msgstr "Añadir filtro"

#: dist/blocks.js:15
msgid "Hide on desktop"
msgstr "Ocultar en el escritorio"

#: dist/blocks.js:15
msgid "Hide on tablet"
msgstr "Ocultar en tablet"

#: dist/blocks.js:15
msgid "Hide on mobile"
msgstr "Ocultar en dispositivos móviles"

#: dist/blocks.js:15
msgid "This block is hidden on this device."
msgstr "Este bloque está oculto en este dispositivo."

#: dist/blocks.js:15
msgid "Styles"
msgstr "Estilos"

#: dist/blocks.js:15
msgid "Copy Styles"
msgstr "Copiar estilos"

#: dist/blocks.js:15
msgid "Paste Styles"
msgstr "Pegar estilos"

#: dist/blocks.js:15
msgid "Clear Styles"
msgstr "Limpiar estilos"

#: dist/blocks.js:15
msgid "This will remove all styling from these blocks."
msgstr "Esto eliminará todo el estilo de estos bloques."

#: dist/blocks.js:15
msgid "This will remove all styling from this block."
msgstr "Esto eliminará todos los estilos de este bloque."

#: dist/blocks.js:15
msgid "Global Style"
msgstr "Estilo Global"

#: dist/blocks.js:15
msgid "Name your global style something short and unique to this type of block."
msgstr "Nombra a tu estilo global algo corto y único para este tipo de bloque."

#: dist/blocks.js:15
msgid "Change Global Style ID"
msgstr "Cambiar el ID de estilo global"

#: dist/blocks.js:15
msgid "Changing this ID will remove the styling from existing blocks using this Global Style."
msgstr "Al cambiar este ID se eliminará el estilo de los bloques existentes que utilizan este Estilo Global."

#: dist/blocks.js:15
msgid "Label"
msgstr "Etiqueta"

#: dist/blocks.js:15
msgid "The label shown when choosing a Global Style in the editor."
msgstr "La etiqueta que se muestra al elegir un estilo global en el editor."

#: dist/blocks.js:15
msgid "Use Global Style"
msgstr "Utilizar el estilo global"

#: dist/blocks.js:15
msgid "This will remove all local styling from this block."
msgstr "Esto eliminará todo el estilo local de este bloque."

#: dist/blocks.js:15
msgid "Clear local styles"
msgstr "Limpiar los estilos locales"

#: dist/blocks.js:15
msgid "Colors"
msgstr "Colores"

#: dist/blocks.js:15
msgid "Background Color"
msgstr "Color de fondo"

#: dist/blocks.js:15
msgid "Text Color"
msgstr "Color del texto"

#: dist/blocks.js:15
msgid "Link Color"
msgstr "Color del Link"

#: dist/blocks.js:15
msgid "Border Color"
msgstr "Color del borde"

#: dist/blocks.js:15
msgid "This makes your Element Tag a link element. It uses valid HTML5 coding but will break if you add interative elements (links or buttons) inside the container."
msgstr "Esto hace que tu etiqueta de elemento sea un elemento de enlace. Utiliza codificación HTML5 válida, pero puedes romperlo si añades elementos interativos (enlaces o botones) dentro del contenedor."

#: dist/blocks.js:15
msgid "This adds a hidden link inside your container and tells it to cover the entire element. It is less prone to breakage, but is not as clean as the wrapper method."
msgstr "Esto añade un enlace oculto dentro del contenedor y le dices que cubra todo el elemento. Es menos propenso a romperse, pero no es tan limpio como el método de envoltura."

#: dist/blocks.js:15
msgid "Change Container Link"
msgstr "Cambiar el enlace del contenedor"

#: dist/blocks.js:15
msgid "Set Container Link"
msgstr "Establecer enlace de contenedor"

#: dist/blocks.js:15
msgid "This container is using a dynamic link."
msgstr "Este contenedor está utilizando un enlace dinámico."

#: dist/blocks.js:15
msgid "Open link in a new tab"
msgstr "Abre el enlace en una nueva pestaña"

#: dist/blocks.js:15
msgid "Add rel=\"nofollow\""
msgstr "Añadir rel=\"nofollow\""

#: dist/blocks.js:15
msgid "Add rel=\"sponsored\""
msgstr "Añadir rel=\"sponsored\""

#: dist/blocks.js:15
msgid "Link Type"
msgstr "Tipo de enlace"

#: dist/blocks.js:15
msgid "Hidden Link"
msgstr "Enlace oculto"

#: dist/blocks.js:15
msgid "Wrapper"
msgstr "El contenedor"

#: dist/blocks.js:15
msgid "Aria Label"
msgstr "Etiqueta Aria"

#: dist/blocks.js:15
msgid "Help screen readers understand what this link does."
msgstr "Ayuda a los lectores de pantalla a entender lo que hace este enlace."

#: dist/blocks.js:15
msgid "Image"
msgstr "Imagen"

#: dist/blocks.js:15
msgid "Gradient"
msgstr "Degradado"

#: dist/blocks.js:15
msgid "Delete Background"
msgstr "Eliminar fondo"

#: dist/blocks.js:15
msgid "This will permanently delete this background."
msgstr "Esto eliminará permanentemente este fondo."

#: dist/blocks.js:15
msgid "Direction"
msgstr "Dirección"

#: dist/blocks.js:15
msgid "Stop One"
msgstr "Primera parada"

#: dist/blocks.js:15
msgid "Stop Two"
msgstr "Segunda parada"

#: dist/blocks.js:15
msgid "Image URL"
msgstr "URL de la imagen"

#: dist/blocks.js:15
msgid "Background image preview"
msgstr "Vista previa de imagen de fondo"

#: dist/blocks.js:15
msgid "Set background image"
msgstr "Establecer imagen de fondo"

#: dist/blocks.js:15
msgid "Open the Media Library"
msgstr "Abrir la biblioteca de medios"

#: dist/blocks.js:15
msgid "Image Size"
msgstr "Tamaño de la imagen"

#: dist/blocks.js:15
msgid "Size"
msgstr "Tamaño"

#: dist/blocks.js:15
msgid "Position"
msgstr "Posición"

#: dist/blocks.js:15
msgid "Repeat"
msgstr "Repetir"

#: dist/blocks.js:15
msgid "Attachment"
msgstr "Adjuntos"

#: dist/blocks.js:15
msgid "Current post terms"
msgstr "Términos actuales de la entrada"

#: dist/blocks.js:15
msgid "Current post"
msgstr "Entrada actual"

#: dist/blocks.js:15
msgid "Current post author"
msgstr "Autor actual de la entrada"

#. translators: Number of gradients.
#: dist/blocks.js:17
msgid "Advanced (%s)"
msgstr "Avanzado (%s)"

#: dist/blocks.js:17
msgid "Advanced"
msgstr "Avanzado"

#: dist/blocks.js:17
msgid "Pseudo Element"
msgstr "Pseudoelemento"

#: dist/blocks.js:17
msgid "Add Background"
msgstr "Añadir fondo"

#: dist/blocks.js:17
msgid "Random"
msgstr "Aleatorio"

#: dist/blocks.js:17
msgid "Exclude current post"
msgstr "Excluye la entrada actual"

#: dist/blocks.js:17
msgid "Post meta sub field"
msgstr "Subcampo de la entrada"

#: dist/blocks.js:17
msgid "Select value property"
msgstr "Selecciona la propiedad del valor"

#: dist/blocks.js:17
msgid "Select Category"
msgstr "Elegir categoría"

#: dist/blocks.js:17
msgid "Blocks"
msgstr "Bloques"

#: dist/blocks.js:17
msgid "Patterns"
msgstr "Patrones"

#: dist/blocks.js:17
msgid "No patterns found."
msgstr "No se han encontrado patrones."

#: dist/blocks.js:17 dist/blocks.js:19
msgid "Add Template"
msgstr "Añadir plantilla"

#. translators: Number of templates.
#: dist/blocks.js:19
msgid "Patterns: %s"
msgstr "Patrones: %s"

#: dist/blocks.js:19
msgid "Pattern Library"
msgstr "Biblioteca de patrones"

#: dist/blocks.js:19
msgid "Insert pre-built patterns directly into your content."
msgstr "Inserta patrones pre-construidos directamente en tu contenido."

#: dist/blocks.js:19
msgid "Open Pattern Library"
msgstr "Abrir librería de patrones"

#: dist/dashboard.js:1
msgid "License key activated."
msgstr "Clave de licencia activada."

#: dist/dashboard.js:1
msgid "License key deactivated."
msgstr "Clave de licencia desactivada."

#: dist/dashboard.js:1
msgid "Receiving updates"
msgstr "Recibiendo actualizaciones"

#: dist/dashboard.js:1
msgid "Not receiving updates"
msgstr "No recibiendo actualizaciones"

#: dist/dashboard.js:1
msgid "Enter your license key here…"
msgstr "Introduce tu clave de licencia aquí.."

#: dist/dashboard.js:1
msgid "Get alpha and beta updates directly to your Dashboard."
msgstr "Recibe las actualizaciones de las versiones alfa y beta directamente en tu panel de control."

#: dist/dashboard.js:1
msgid "Enable Local Patterns"
msgstr "Activar patrones locales"

#: dist/dashboard.js:1
msgid "Enable Remote Patterns"
msgstr "Activar patrones remotos"

#: dist/dashboard.js:1
msgid "The pattern library syncs once a day by default. Clicking this button will force it to re-sync."
msgstr "La biblioteca de patrones se sincroniza por defecto una vez al día. Al hacer clic en este botón, se forzará la resincronización."

#: dist/dashboard.js:1
msgid "Remote patterns synced."
msgstr "Patrones remotos sincronizados."

#: dist/dashboard.js:1
msgid "Sync Remote Patterns"
msgstr "Sincronizar con patrones remotos"

#: dist/dashboard.js:1
msgid "Save"
msgstr "Guardar"