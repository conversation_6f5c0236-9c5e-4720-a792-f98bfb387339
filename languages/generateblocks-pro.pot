# Copyright (C) 2023 <PERSON>
# This file is distributed under the GPL2+.
msgid ""
msgstr ""
"Project-Id-Version: GenerateBlocks Pro 1.6.0-alpha.1\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/generateblocks-pro\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2023-06-10T03:01:21+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.6.0\n"
"X-Domain: generateblocks-pro\n"

#. Plugin Name of the plugin
#. translators: GenerateBlocks Pro
#: includes/class-rest.php:440
msgid "GenerateBlocks Pro"
msgstr ""

#. Plugin URI of the plugin
msgid "https://generateblocks.com"
msgstr ""

#. Description of the plugin
msgid "GenerateBlocks Pro adds more great features to GenerateBlocks without sacrificing usability or performance."
msgstr ""

#. Author of the plugin
msgid "Tom Usborne"
msgstr ""

#. Author URI of the plugin
msgid "https://tomusborne.com"
msgstr ""

#: includes/class-asset-library.php:57
#: includes/class-asset-library.php:58
#: includes/class-asset-library.php:75
msgid "Asset Library"
msgstr ""

#: includes/class-global-styles.php:57
msgctxt "Post Type General Name"
msgid "Global Styles"
msgstr ""

#: includes/class-global-styles.php:58
msgctxt "Post Type Singular Name"
msgid "Global Style"
msgstr ""

#: includes/class-global-styles.php:59
#: includes/class-global-styles.php:61
#: includes/class-global-styles.php:111
msgid "Global Styles"
msgstr ""

#: includes/class-global-styles.php:60
msgid "Parent Global Style"
msgstr ""

#: includes/class-global-styles.php:62
msgid "View Global Style"
msgstr ""

#: includes/class-global-styles.php:63
msgid "Add New Global Style"
msgstr ""

#: includes/class-global-styles.php:64
#: includes/class-local-templates.php:70
msgid "Add New"
msgstr ""

#: includes/class-global-styles.php:65
msgid "Edit Global Style"
msgstr ""

#: includes/class-global-styles.php:66
msgid "Update Global Style"
msgstr ""

#: includes/class-global-styles.php:67
msgid "Search Global Style"
msgstr ""

#: includes/class-global-styles.php:68
#: includes/class-local-templates.php:74
msgid "Not Found"
msgstr ""

#: includes/class-global-styles.php:69
#: includes/class-local-templates.php:75
msgid "Not found in Trash"
msgstr ""

#: includes/class-local-templates.php:63
msgctxt "Post Type General Name"
msgid "Local Patterns"
msgstr ""

#: includes/class-local-templates.php:64
msgctxt "Post Type Singular Name"
msgid "Local Pattern"
msgstr ""

#: includes/class-local-templates.php:65
#: includes/class-local-templates.php:67
#: includes/class-local-templates.php:117
#: dist/blocks.js:17
msgid "Local Patterns"
msgstr ""

#: includes/class-local-templates.php:66
msgid "Parent Local Pattern"
msgstr ""

#: includes/class-local-templates.php:68
msgid "View Local Pattern"
msgstr ""

#: includes/class-local-templates.php:69
msgid "Add New Local Pattern"
msgstr ""

#: includes/class-local-templates.php:71
msgid "Edit Local Pattern"
msgstr ""

#: includes/class-local-templates.php:72
msgid "Update Local Pattern"
msgstr ""

#: includes/class-local-templates.php:73
msgid "Search Local Pattern"
msgstr ""

#: includes/class-rest.php:256
msgid "Templates not found."
msgstr ""

#: includes/class-rest.php:322
msgid "Template data not found."
msgstr ""

#: includes/class-rest.php:343
#: includes/class-rest.php:468
msgid "Settings saved."
msgstr ""

#: includes/class-rest.php:409
#: includes/class-rest.php:448
msgid "An error occurred, please try again."
msgstr ""

#. translators: License key expiration date.
#: includes/class-rest.php:419
msgid "Your license key expired on %s."
msgstr ""

#: includes/class-rest.php:426
msgid "Your license key has been disabled."
msgstr ""

#: includes/class-rest.php:430
msgid "Invalid license."
msgstr ""

#: includes/class-rest.php:435
msgid "Your license is not active for this URL."
msgstr ""

#. translators: GenerateBlocks Pro
#: includes/class-rest.php:440
msgid "This appears to be an invalid license key for %s."
msgstr ""

#: includes/class-rest.php:444
msgid "Your license key has reached its activation limit."
msgstr ""

#: includes/class-rest.php:538
msgid "Shapes saved."
msgstr ""

#: includes/class-rest.php:600
msgid "Icons saved."
msgstr ""

#: plugin.php:74
msgid "GenerateBlocks Pro is not working because you need to activate the GenerateBlocks plugin."
msgstr ""

#: plugin.php:75
msgid "Activate GenerateBlocks Now"
msgstr ""

#: plugin.php:83
msgid "GenerateBlocks Pro is not working because you need to install the GenerateBlocks plugin."
msgstr ""

#: plugin.php:84
msgid "Install GenerateBlocks Now"
msgstr ""

#: plugin.php:103
msgid "GenerateBlocks Pro is not working because you are using an old version of GenerateBlocks."
msgstr ""

#: plugin.php:104
msgid "Update GenerateBlocks Now"
msgstr ""

#: dist/asset-library.js:1
msgid "Shapes are dynamic elements that will update automatically on your website if altered/removed here."
msgstr ""

#: dist/asset-library.js:1
msgid "Group"
msgstr ""

#: dist/asset-library.js:1
msgid "Group Name"
msgstr ""

#: dist/asset-library.js:1
msgid "This will permanently delete all shapes in this group and remove them from the front-end of your website."
msgstr ""

#: dist/asset-library.js:1
msgid "Delete Group"
msgstr ""

#: dist/asset-library.js:1
msgid "Export Group"
msgstr ""

#: dist/asset-library.js:1
msgid "Name"
msgstr ""

#: dist/asset-library.js:1
msgid "Upload an SVG file"
msgstr ""

#: dist/asset-library.js:1
msgid "Upload SVG"
msgstr ""

#: dist/asset-library.js:1
msgid "Insert SVG"
msgstr ""

#: dist/asset-library.js:1
#: dist/blocks.js:13
msgid "Browse"
msgstr ""

#: dist/asset-library.js:1
msgid "SVG HTML"
msgstr ""

#: dist/asset-library.js:1
msgid "Delete Shape"
msgstr ""

#: dist/asset-library.js:1
msgid "This will permanently delete this shape and remove it from the front-end of your website."
msgstr ""

#: dist/asset-library.js:1
msgid "Add Shape"
msgstr ""

#: dist/asset-library.js:1
msgid "Add Group"
msgstr ""

#: dist/asset-library.js:1
msgid "Import Group"
msgstr ""

#: dist/asset-library.js:1
msgid "Group imported."
msgstr ""

#: dist/asset-library.js:1
msgid "File not valid."
msgstr ""

#: dist/asset-library.js:1
msgid "Wrong asset type."
msgstr ""

#: dist/asset-library.js:1
msgid "Icons are static elements that are added to your content. Changes here will not affect your existing icons."
msgstr ""

#: dist/asset-library.js:1
msgid "This will permanently delete all icons in this group."
msgstr ""

#: dist/asset-library.js:1
msgid "Delete Icon"
msgstr ""

#: dist/asset-library.js:1
msgid "This will permanently delete this icon."
msgstr ""

#: dist/asset-library.js:1
msgid "Add Icon"
msgstr ""

#: dist/blocks.js:1
msgid "Custom Attributes"
msgstr ""

#: dist/blocks.js:1
msgid "Delete attribute"
msgstr ""

#: dist/blocks.js:1
msgid "Add Attribute"
msgstr ""

#: dist/blocks.js:1
msgid "Allowed attributes"
msgstr ""

#: dist/blocks.js:1
msgid "The following HTML attributes are allowed to be used in the Custom Attributes feature:"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:3
#: dist/blocks.js:5
#: dist/blocks.js:7
#: dist/blocks.js:9
#: dist/blocks.js:11
#: dist/blocks.js:13
#: dist/blocks.js:15
msgid "Close"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Type"
msgstr ""

#: dist/blocks.js:1
msgid "Choose.."
msgstr ""

#: dist/blocks.js:1
msgid "Translate"
msgstr ""

#: dist/blocks.js:1
msgid "Rotate"
msgstr ""

#: dist/blocks.js:1
msgid "Skew"
msgstr ""

#: dist/blocks.js:1
msgid "Scale"
msgstr ""

#: dist/blocks.js:1
msgid "Blur"
msgstr ""

#: dist/blocks.js:1
msgid "Brightness"
msgstr ""

#: dist/blocks.js:1
msgid "Contrast"
msgstr ""

#: dist/blocks.js:1
msgid "Grayscale"
msgstr ""

#: dist/blocks.js:1
msgid "Hue-Rotate"
msgstr ""

#: dist/blocks.js:1
msgid "Invert"
msgstr ""

#: dist/blocks.js:1
msgid "Saturate"
msgstr ""

#: dist/blocks.js:1
msgid "Sepia"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Device"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "All"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Desktop"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Tablet"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Tablet + Mobile"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Mobile"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "State"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Normal"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Hover"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Target"
msgstr ""

#: dist/blocks.js:1
msgid "Your background image must be set to Pseudo Element for effects to work."
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Custom Selector"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:3
msgid "Opacity"
msgstr ""

#: dist/blocks.js:1
msgid "Mix Blend Mode"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Choose…"
msgstr ""

#: dist/blocks.js:1
msgid "Multiply"
msgstr ""

#: dist/blocks.js:1
msgid "Screen"
msgstr ""

#: dist/blocks.js:1
msgid "Overlay"
msgstr ""

#: dist/blocks.js:1
msgid "Darken"
msgstr ""

#: dist/blocks.js:1
msgid "Lighten"
msgstr ""

#: dist/blocks.js:1
msgid "Color Dodge"
msgstr ""

#: dist/blocks.js:1
msgid "Color Burn"
msgstr ""

#: dist/blocks.js:1
msgid "Hard Light"
msgstr ""

#: dist/blocks.js:1
msgid "Soft Light"
msgstr ""

#: dist/blocks.js:1
msgid "Difference"
msgstr ""

#: dist/blocks.js:1
msgid "Exclusion"
msgstr ""

#: dist/blocks.js:1
msgid "Hue"
msgstr ""

#: dist/blocks.js:1
msgid "Saturation"
msgstr ""

#: dist/blocks.js:1
msgid "Color"
msgstr ""

#: dist/blocks.js:1
msgid "Luminosity"
msgstr ""

#: dist/blocks.js:1
msgid "Timing Function"
msgstr ""

#: dist/blocks.js:1
msgid "CSS Property"
msgstr ""

#: dist/blocks.js:1
msgid "Transition Duration"
msgstr ""

#: dist/blocks.js:1
msgid "Delay"
msgstr ""

#: dist/blocks.js:1
msgid "Inset"
msgstr ""

#: dist/blocks.js:1
msgid "Horizontal Offset"
msgstr ""

#: dist/blocks.js:1
msgid "Vertical Offset"
msgstr ""

#: dist/blocks.js:1
msgid "Spread"
msgstr ""

#: dist/blocks.js:1
msgid "Translate X"
msgstr ""

#: dist/blocks.js:1
msgid "Translate Y"
msgstr ""

#: dist/blocks.js:1
msgid "Skew X"
msgstr ""

#: dist/blocks.js:1
msgid "Skew Y"
msgstr ""

#: dist/blocks.js:1
msgid "Automatically add a smooth transition to this effect."
msgstr ""

#: dist/blocks.js:1
msgid "Add Transition"
msgstr ""

#: dist/blocks.js:1
msgid "Delete this effect."
msgstr ""

#: dist/blocks.js:1
msgid "This will permanently delete this effect."
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:13
msgid "Delete"
msgstr ""

#: dist/blocks.js:1
#: dist/blocks.js:15
msgid "Self"
msgstr ""

#: dist/blocks.js:1
msgid "Inner Container"
msgstr ""

#: dist/blocks.js:1
msgid "Background Image"
msgstr ""

#: dist/blocks.js:1
msgid "Icon"
msgstr ""

#: dist/blocks.js:1
msgid "Accordion Content"
msgstr ""

#: dist/blocks.js:1
msgid "Effects"
msgstr ""

#. translators: Number of transforms.
#: dist/blocks.js:3
msgid "Opacity & Blend (%s)"
msgstr ""

#: dist/blocks.js:3
msgid "Opacity & Blend"
msgstr ""

#: dist/blocks.js:3
#: dist/blocks.js:5
#: dist/blocks.js:7
#: dist/blocks.js:9
msgid "Add Effect"
msgstr ""

#: dist/blocks.js:3
#: dist/blocks.js:11
msgid "Disable in editor"
msgstr ""

#. translators: Number of transforms.
#: dist/blocks.js:5
msgid "Transition (%s)"
msgstr ""

#: dist/blocks.js:5
#: dist/blocks.js:17
msgid "Transition"
msgstr ""

#. translators: Number of transforms.
#: dist/blocks.js:7
msgid "Box Shadow (%s)"
msgstr ""

#: dist/blocks.js:7
msgid "Box Shadow"
msgstr ""

#. translators: Number of transforms.
#: dist/blocks.js:9
msgid "Text Shadow (%s)"
msgstr ""

#: dist/blocks.js:9
msgid "Text Shadow"
msgstr ""

#. translators: Number of transforms.
#: dist/blocks.js:11
msgid "Transform (%s)"
msgstr ""

#: dist/blocks.js:11
msgid "Transform"
msgstr ""

#: dist/blocks.js:11
msgid "Add Transform"
msgstr ""

#. translators: Number of transforms.
#: dist/blocks.js:13
msgid "Filter (%s)"
msgstr ""

#: dist/blocks.js:13
msgid "Filter"
msgstr ""

#: dist/blocks.js:13
msgid "Add Filter"
msgstr ""

#: dist/blocks.js:13
msgid "Hide on desktop"
msgstr ""

#: dist/blocks.js:13
msgid "Hide on tablet"
msgstr ""

#: dist/blocks.js:13
msgid "Hide on mobile"
msgstr ""

#: dist/blocks.js:13
msgid "This block is hidden on this device."
msgstr ""

#: dist/blocks.js:13
msgid "Styles"
msgstr ""

#: dist/blocks.js:13
msgid "Copy Styles"
msgstr ""

#: dist/blocks.js:13
msgid "Paste Styles"
msgstr ""

#: dist/blocks.js:13
msgid "Clear Styles"
msgstr ""

#: dist/blocks.js:13
msgid "This will remove all styling from these blocks."
msgstr ""

#: dist/blocks.js:13
msgid "This will remove all styling from this block."
msgstr ""

#: dist/blocks.js:13
msgid "This Global Style is a Button block inside the \"Buttons\" block and may not work with this standalone Button."
msgstr ""

#: dist/blocks.js:13
msgid "This Global Style is a standalone Button and may not work with this Button block inside the \"Buttons\" block."
msgstr ""

#: dist/blocks.js:13
msgid "This Global Style is using the legacy layout system and may not work with this Container block."
msgstr ""

#: dist/blocks.js:13
msgid "This Global Style is using the new layout system and may not work with this Container block."
msgstr ""

#: dist/blocks.js:13
msgid "Global Style"
msgstr ""

#: dist/blocks.js:13
msgid "Name your global style something short and unique to this type of block."
msgstr ""

#: dist/blocks.js:13
msgid "Change Global Style ID"
msgstr ""

#: dist/blocks.js:13
msgid "Changing this ID will remove the styling from existing blocks using this Global Style."
msgstr ""

#: dist/blocks.js:13
msgid "Label"
msgstr ""

#: dist/blocks.js:13
msgid "The label shown when choosing a Global Style in the editor."
msgstr ""

#: dist/blocks.js:13
msgid "Use Global Style"
msgstr ""

#: dist/blocks.js:13
msgid "This will remove all local styling from this block."
msgstr ""

#: dist/blocks.js:13
msgid "Clear local styles"
msgstr ""

#: dist/blocks.js:13
msgid "Colors"
msgstr ""

#: dist/blocks.js:13
msgid "Background Color"
msgstr ""

#: dist/blocks.js:13
msgid "Text Color"
msgstr ""

#: dist/blocks.js:13
msgid "Link Color"
msgstr ""

#: dist/blocks.js:13
msgid "Border Color"
msgstr ""

#: dist/blocks.js:13
msgid "Current"
msgstr ""

#: dist/blocks.js:13
msgid "This makes your Element Tag a link element. It uses valid HTML5 coding but will break if you add interactive elements (links or buttons) inside the container."
msgstr ""

#: dist/blocks.js:13
msgid "This adds a hidden link inside your container and tells it to cover the entire element. It is less prone to breakage, but is not as clean as the wrapper method."
msgstr ""

#: dist/blocks.js:13
msgid "Change Container Link"
msgstr ""

#: dist/blocks.js:13
msgid "Set Container Link"
msgstr ""

#: dist/blocks.js:13
msgid "This container is using a dynamic link."
msgstr ""

#: dist/blocks.js:13
msgid "Open link in a new tab"
msgstr ""

#: dist/blocks.js:13
msgid "Add rel=\"nofollow\""
msgstr ""

#: dist/blocks.js:13
msgid "Add rel=\"sponsored\""
msgstr ""

#: dist/blocks.js:13
msgid "Link Type"
msgstr ""

#: dist/blocks.js:13
msgid "Hidden Link"
msgstr ""

#: dist/blocks.js:13
msgid "Wrapper"
msgstr ""

#: dist/blocks.js:13
msgid "Aria Label"
msgstr ""

#: dist/blocks.js:13
msgid "Help screen readers understand what this link does."
msgstr ""

#: dist/blocks.js:13
msgid "Image"
msgstr ""

#: dist/blocks.js:13
msgid "Gradient"
msgstr ""

#: dist/blocks.js:13
msgid "Direction"
msgstr ""

#: dist/blocks.js:13
msgid "Stop One"
msgstr ""

#: dist/blocks.js:13
msgid "Stop Two"
msgstr ""

#: dist/blocks.js:13
msgid "Image URL"
msgstr ""

#: dist/blocks.js:13
msgid "Background image preview"
msgstr ""

#: dist/blocks.js:13
msgid "Set background image"
msgstr ""

#: dist/blocks.js:13
msgid "Open the Media Library"
msgstr ""

#: dist/blocks.js:13
msgid "Image Size"
msgstr ""

#: dist/blocks.js:13
msgid "Size"
msgstr ""

#: dist/blocks.js:13
msgid "Position"
msgstr ""

#: dist/blocks.js:13
msgid "Repeat"
msgstr ""

#: dist/blocks.js:13
msgid "Attachment"
msgstr ""

#: dist/blocks.js:13
msgid "Delete this background."
msgstr ""

#: dist/blocks.js:13
msgid "This will permanently delete this background."
msgstr ""

#: dist/blocks.js:13
msgid "Current post terms"
msgstr ""

#: dist/blocks.js:13
msgid "Current post"
msgstr ""

#: dist/blocks.js:13
msgid "Current post author"
msgstr ""

#. translators: Number of gradients.
#: dist/blocks.js:15
msgid "Advanced (%s)"
msgstr ""

#: dist/blocks.js:15
msgid "Advanced"
msgstr ""

#: dist/blocks.js:15
msgid "Pseudo Element"
msgstr ""

#: dist/blocks.js:15
msgid "Add Background"
msgstr ""

#: dist/blocks.js:15
msgid "Random"
msgstr ""

#: dist/blocks.js:15
msgid "Exclude current post"
msgstr ""

#: dist/blocks.js:15
msgid "Post meta sub field"
msgstr ""

#: dist/blocks.js:15
msgid "Select value property"
msgstr ""

#: dist/blocks.js:15
msgid "Remove Tab Item & Button"
msgstr ""

#: dist/blocks.js:15
msgid "Remove Tab Button & Item"
msgstr ""

#: dist/blocks.js:15
msgid "Add Tab Item"
msgstr ""

#: dist/blocks.js:15
msgid "You can use this button to add new tab items."
msgstr ""

#: dist/blocks.js:15
msgid "Select…"
msgstr ""

#. translators: Tab number
#: dist/blocks.js:17
msgid "Tab %s"
msgstr ""

#: dist/blocks.js:17
msgid "Default opened tab"
msgstr ""

#: dist/blocks.js:17
msgid "None"
msgstr ""

#: dist/blocks.js:17
msgid "Fade"
msgstr ""

#: dist/blocks.js:17
msgid "Sync tab item styles"
msgstr ""

#: dist/blocks.js:17
msgid "Tab button type"
msgstr ""

#: dist/blocks.js:17
msgid "Button"
msgstr ""

#: dist/blocks.js:17
msgid "Container"
msgstr ""

#: dist/blocks.js:17
msgid "Tabs"
msgstr ""

#: dist/blocks.js:17
msgid "Choose a tab layout to start with."
msgstr ""

#: dist/blocks.js:17
msgid "Horizontal Tabs"
msgstr ""

#: dist/blocks.js:17
msgid "Vertical Tabs"
msgstr ""

#: dist/blocks.js:17
msgid "Button Tabs"
msgstr ""

#: dist/blocks.js:17
msgid "Build a series of tabs using our Container and Button blocks."
msgstr ""

#: dist/blocks.js:17
msgid "Add Accordion Item"
msgstr ""

#: dist/blocks.js:17
msgid "You can use this button to add new accordion items."
msgstr ""

#: dist/blocks.js:17
msgid "Sync accordion item styles"
msgstr ""

#: dist/blocks.js:17
msgid "Slide"
msgstr ""

#: dist/blocks.js:17
msgid "Accordion title type"
msgstr ""

#: dist/blocks.js:17
msgid "Item open by default"
msgstr ""

#: dist/blocks.js:17
msgid "Build accordions using our Container and Button blocks."
msgstr ""

#: dist/blocks.js:17
msgid "Select Category"
msgstr ""

#: dist/blocks.js:17
msgid "Blocks"
msgstr ""

#: dist/blocks.js:17
msgid "Patterns"
msgstr ""

#: dist/blocks.js:17
msgid "No patterns found."
msgstr ""

#: dist/blocks.js:17
#: dist/blocks.js:23
msgid "Add Template"
msgstr ""

#. translators: Number of templates.
#: dist/blocks.js:19
msgid "Patterns: %s"
msgstr ""

#. translators: GenerateBlocks version number
#: dist/blocks.js:21
msgid "Requires GenerateBlocks %s"
msgstr ""

#. translators: GenerateBlocks Pro version number
#: dist/blocks.js:23
msgid "Requires GenerateBlocks Pro %s"
msgstr ""

#: dist/blocks.js:23
msgid "Pattern Library"
msgstr ""

#: dist/blocks.js:23
msgid "Insert pre-built patterns directly into your content."
msgstr ""

#: dist/blocks.js:23
msgid "Open Pattern Library"
msgstr ""

#: dist/dashboard.js:1
msgid "License key activated."
msgstr ""

#: dist/dashboard.js:1
msgid "License key deactivated."
msgstr ""

#: dist/dashboard.js:1
msgid "Receiving updates"
msgstr ""

#: dist/dashboard.js:1
msgid "Not receiving updates"
msgstr ""

#: dist/dashboard.js:1
msgid "Enter your license key here…"
msgstr ""

#: dist/dashboard.js:1
msgid "Receive alpha, beta, and release candidate updates. This should only be used on staging/development websites."
msgstr ""

#: dist/dashboard.js:1
msgid "Enable Local Patterns"
msgstr ""

#: dist/dashboard.js:1
msgid "Enable Remote Patterns"
msgstr ""

#: dist/dashboard.js:1
msgid "The pattern library syncs once a day by default. Clicking this button will force it to re-sync."
msgstr ""

#: dist/dashboard.js:1
msgid "Remote patterns synced."
msgstr ""

#: dist/dashboard.js:1
msgid "Sync Remote Patterns"
msgstr ""

#: dist/dashboard.js:1
msgid "Save"
msgstr ""
